{"data": [{"id": "78_1750898474.949", "type": "feed", "offset": 78, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898474, "updated_time": 1750898474, "target": {"id": "1902773959205844973", "type": "answer", "url": "https://api.zhihu.com/answers/1902773959205844973", "author": {"id": "8b9c7cf41cf264b7f185eac8b5c24ec0", "url": "https://api.zhihu.com/people/8b9c7cf41cf264b7f185eac8b5c24ec0", "user_type": "people", "url_token": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>", "headline": "🚀 deepractice.ai 创始人 & CEO", "avatar_url": "https://picx.zhimg.com/50/v2-0c24eea9d68f3c1fe1f229f7b4562fd2_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 1381, "is_following": false, "is_followed": false}, "created_time": 1746436663, "updated_time": 1748601385, "voteup_count": 58, "thanks_count": 4, "comment_count": 21, "is_copyable": true, "question": {"id": "14694918319", "type": "question", "url": "https://api.zhihu.com/questions/14694918319", "author": {"id": "a1fcc8257912a20c093e55903eb8ffc3", "url": "https://api.zhihu.com/people/a1fcc8257912a20c093e55903eb8ffc3", "user_type": "people", "url_token": "zang-73-50", "name": "像阳光那样", "headline": "", "avatar_url": "https://pic1.zhimg.com/50/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 1, "is_following": false, "is_followed": false}, "title": "哪个ai写代码最强？", "created": 1741689259, "answer_count": 0, "follower_count": 0, "comment_count": 3, "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "我认为是 Cursor + Claude 3.7，我的开源项目 99.9% 代码完全由 AI 完成，我认为 AI 已经有能力代替绝大部分程序员了，大家感兴趣可以去代码仓库给 AI 挑挑刺。 https://github.com/Deepractice/dpml 1. 截止 2025 年 5 月 5 日，发布 可用版本到 npm 仓库，总共投入人力 1 人，耗时 1 个月。 2. 整个项目 99.9% 的代码由 AI 编写，使用的是 Cursor + Claude 3.7 Thinking ， 我负责做架构设计模块设计还有监督开发过程（ PS： 在…", "excerpt_new": "我认为是 Cursor + Claude 3.7，我的开源项目 99.9% 代码完全由 AI 完成，我认为 AI 已经有能力代替绝大部分程序员了，大家感兴趣可以去代码仓库给 AI 挑挑刺。 https://github.com/Deepractice/dpml 1. 截止 2025 年 5 月 5 日，发布 可用版本到 npm 仓库，总共投入人力 1 人，耗时 1 个月。 2. 整个项目 99.9% 的代码由 AI 编写，使用的是 Cursor + Claude 3.7 Thinking ， 我负责做架构设计模块设计还有监督开发过程（ PS： 在…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"oStZDgIs\">我认为是 Cursor + Claude 3.7，我的开源项目 99.9% 代码完全由 AI 完成，我认为 AI 已经有能力代替绝大部分程序员了，大家感兴趣可以去代码仓库给 AI 挑挑刺。</p><p data-pid=\"9fkMHgeb\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/Deepractice/dpml\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/Deepractice/</span><span class=\"invisible\">dpml</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"tRs8sco7\">1. 截止 2025 年 5 月 5 日，发布 可用版本到 npm 仓库，总共投入人力 1 人，耗时 1 个月。</p><p data-pid=\"uq0QGGgB\">2. 整个项目 99.9% 的代码由 AI 编写，使用的是 Cursor + Claude 3.7 Thinking ， 我负责做架构设计模块设计还有监督开发过程（ PS： 在开发这个项目之前我从来没学过也没写过 TypeScript ）。</p><p data-pid=\"M80H2bD8\">3. 截止目前项目花费大约 5000 RMB。</p><p data-pid=\"FwvveN1R\">4. 让 AI 用人类视角预估项目的工时投入为 2.5 人， 5 个月的工期。效率提高了 10 倍不止。</p><p data-pid=\"Ozd4udNw\">这个项目涉及的都是一些偏技术向的需求，同时工程量和复杂度都是超出一般项目的，涉及了基础编译器，SDK ，CLI 命令行等方向。这个项目本身证明了 AI 编程大有可为。</p><h2>[开源项目] DPML, 像写 HTML 一样开发 AI 应用</h2><p data-pid=\"eRpddk6_\"><b>需求</b></p><p data-pid=\"neJyiZ2Q\">我个人在研究 AI agent 的过程中，发现几个痛点。</p><ul><li data-pid=\"FZYLld0g\">当 Prompt 的量级上来以后，慢慢的就需要工程化，结构化管理了，目前这个领域还缺乏一些开发工具支撑。</li><li data-pid=\"67ySWRVF\">目前开发 Agent 都是基于正统编程语言的比如 Langchain ，Spring AI 等等，门槛比较高。同时对于我自己来说，虽然是懂编程的，但是实际上开发 AI 应用大部分时候都是在调提示词，和测试各种模型，编程本身的工作量并不大，这个时候如果每次都要搭建环境，编写代码，启动应用就有点太麻烦了。但是像 coze 这种平台又对于我们开发者来说灵活性不高。</li><li data-pid=\"wETk2jyU\">AI agent 目前编码能力还是很强的，但是如果代码量越少，封装度越高，AI 的执行速度，精准度和成本都会更好，其实这个就是 DSL 对 AI 本身就是非常友好的存在。</li><li data-pid=\"7EMdpmyU\">共享，目前 AI 工程领域还没有特别好的开源共享平台出现，比如提示词共享，智能体共享等等。</li></ul><h2>项目</h2><p data-pid=\"ezYFskH4\">基于上面的一些问题，我做了一个开源项目 DPML ， 核心目标是引入声明式 DSL ，即标记语言来实现 AI 领域方方面面的开发搭建。一句话概括就是“像写 HTML 一样开发 AI 应用”。</p><p data-pid=\"PnTTtM9T\">项目地址： <a href=\"https://link.zhihu.com/?target=https%3A//github.com/Deepractice/dpml\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/Deepractice/</span><span class=\"invisible\">dpml</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"2s6HnBD1\">目前项目还是在最初期阶段，仅仅实现了核心编译器模块，CLI 界面，和 agent 领域很基础的功能。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"zjOZhoLE\">我们 Deepractice 团队组织了一个AI 学习交流微信群（开源向），主要用于学习，交流 AI 技术，包括不限于 AI 编程，AI 写作，AI 视频。欢迎小白踊跃提问，也欢迎大佬分享。同时也欢迎创业者在群里分享和推广自己的 AI 方向的产品，项目，技术（非销售）。</p><p data-pid=\"PtnNEz5P\">感兴趣的朋友可以添加我的微信，备注【知乎】</p><p data-pid=\"Y9DklGAE\">微信：deepracticex</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 9375, "favorite_count": 143, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1902773959205844973}", "attached_info": "CtEGCJ+W9fzOl6rRPBAEGgk3MjU5NTk1NjYgt4TiwAYoOjAVQE5KSAofVFNfU09VUkNFX1pSRUNBTExfSVRFTUNGX1VQVk9URRIfZG9jX3R5cGU6IEFuc3dlcgppZDogNzI2NzU3NTI1ChgAIAA6AFoJMTEzOTE3NTUyYiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3ITMTkwMjc3Mzk1OTIwNTg0NDk3M4oBCzE0Njk0OTE4MzE5qgEJcmVjb21tZW5kwgEgOGI5YzdjZjQxY2YyNjRiN2YxODVlYWM4YjVjMjRlYzDyAQoIDBIGTm9ybWFs8gEoCAoSJDM2YmE1ZTliLWQ1ZWEtNGQ5YS04NGVhLTE4M2Q4YzI3N2YzMvIBBggLEgIxNIICAIgCzve5zfoykgIgOGI5YzdjZjQxY2YyNjRiN2YxODVlYWM4YjVjMjRlYzCaAgDKAhZTaG9ySW50ZXJlc3RXZWlnaHRSdWxlygIWQWN0aW9uU2hvckludGVyZXN0UnVsZcoCG0ludGVyYWN0aW9uU2hvckludGVyZXN0UnVsZcoCGFBlcmlvZEludGVyZXN0V2VpZ2h0UnVsZcoCFVVzZXJMY25FeGl0V2VpZ2h0UnVsZcoCFENvbnRlbnRBZ2VXZWlnaHRSdWxlygIVUXVlc3Rpb25Jc29sYXRpb25SdWxl2gIfVFNfU09VUkNFX1pSRUNBTExfSVRFTUNGX1VQVk9URegCA/oCC05PUk1BTF9GTE9XigMgYzI3MDU3NTgwZTlmNDRhYjkzMWJjNjE2YzY1YTlhMDWaAw0KAnYyEAAaBW90aGVyqAOfSdgDAOoDGXRleHRBbGxTaXRlQWN0aW9uSXRlbUNGVjL6Ax8SDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFgAQAiAQAkgQGTm9ybWFsmgQBM6AEAKgEALAEALoEBm1hbnVhbMIEAzE3MMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAwJaayT+BBQAAAAAAAAAAiQUFqAKugr3SP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUOkAYAoAZSqAYBkgIuCgk3MjU5NTk1NjYSEzE5MDI3NzM5NTkyMDU4NDQ5NzMYBCIKSU1BR0VfVEVYVA==", "action_card": false}, {"id": "79_1750898474.801", "type": "feed", "offset": 79, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898474, "updated_time": 1750898474, "target": {"id": "1920932791178233427", "type": "article", "url": "https://api.zhihu.com/articles/1920932791178233427", "author": {"id": "e4c70c8966ac6433a8d8ab9087891dca", "url": "https://api.zhihu.com/people/e4c70c8966ac6433a8d8ab9087891dca", "user_type": "people", "url_token": "shan-<PERSON><PERSON>-yan-cheng-10", "name": "JioNLP", "headline": "开源JioNLP、ffio作者，公众号JioNLP", "avatar_url": "https://picx.zhimg.com/50/v2-a581add09700866bd26d38b3971fd239_l.jpg?source=b6762063", "is_org": false, "gender": 0, "followers_count": 11732, "is_following": false, "is_followed": false}, "title": "<PERSON><PERSON>-Researcher一使手腕，又重得AI届盛宠", "comment_permission": "all", "created": 1750766332, "updated": 1750766332, "voteup_count": 6, "voting": 0, "comment_count": 0, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "无限猴子定理（Infinite Monkey Theorem）：如果有无数多的猴子在无数多的打字机上随机地打字，并持续无限久的时间，那么在某个时候，它们必然会打出莎士比亚的全部著作。     Hello，大家好，我是 JioNLP。 今天来聊聊 Kimi 月之暗面 新发布的 agentic 超级AI模型，Kimi-Researcher。那么文章的开头我为啥要提到那个 无限猴子定理 呢？等我介绍完 Kimi 的新产品，你就明白了。对于这个模型，我愿称之为 领跑未来AI基座模型训练的存…", "excerpt_new": "无限猴子定理（Infinite Monkey Theorem）：如果有无数多的猴子在无数多的打字机上随机地打字，并持续无限久的时间，那么在某个时候，它们必然会打出莎士比亚的全部著作。     Hello，大家好，我是 JioNLP。 今天来聊聊 Kimi 月之暗面 新发布的 agentic 超级AI模型，Kimi-Researcher。那么文章的开头我为啥要提到那个 无限猴子定理 呢？等我介绍完 Kimi 的新产品，你就明白了。对于这个模型，我愿称之为 领跑未来AI基座模型训练的存…", "preview_type": "default", "preview_text": "", "column": {"id": "c_1210567565799829504", "type": "column", "url": "https://api.zhihu.com/columns/c_1210567565799829504", "author": {"id": "", "url": "", "user_type": "people", "url_token": "", "name": "匿名用户", "headline": "", "avatar_url": "https://picx.zhimg.com/v2-d41c2ceaed8f51999522f903672a521f_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "JioNLP的AI论坛", "imageUrl": "https://picx.zhimg.com/v2-f111d7ee1c41944859e975a712c0883b_720w.jpg?source=d16d100b", "comment_permission": "public", "intro": "", "updated": 1748345923, "is_following": false}, "content": "<p></p><blockquote data-pid=\"uxzAF_3X\"><b>无限猴子定理（Infinite Monkey Theorem）</b>：如果有无数多的猴子在无数多的打字机上随机地打字，并持续无限久的时间，那么在某个时候，它们必然会打出莎士比亚的全部著作。<br/> </blockquote><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-4ce8390a30f1db95a596f7773ceab11d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"705\" data-rawheight=\"460\" data-original-token=\"v2-e3655bb8bc76d22571a45cd213fba20f\" class=\"origin_image zh-lightbox-thumb\" width=\"705\" data-original=\"https://pic4.zhimg.com/v2-4ce8390a30f1db95a596f7773ceab11d_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-d0d9c6002430d8e60b177f27719c9dfb_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"816\" data-rawheight=\"434\" data-original-token=\"v2-99e26337aa7f4da0d292324d30117930\" class=\"origin_image zh-lightbox-thumb\" width=\"816\" data-original=\"https://pic4.zhimg.com/v2-d0d9c6002430d8e60b177f27719c9dfb_r.jpg\"/></figure><p data-pid=\"IR8qA7Ff\">Hello，大家好，我是 JioNLP。</p><p data-pid=\"ReW9Y4Ni\">今天来聊聊 <b>Kimi 月之暗面</b> 新发布的 agentic 超级AI模型，<b>Kimi-Researcher</b>。那么文章的开头我为啥要提到那个 <b>无限猴子定理</b> 呢？等我介绍完 Kimi 的新产品，你就明白了。</p><p data-pid=\"oIxJ589e\">对于这个模型，我愿称之为<b>领跑未来AI基座模型训练</b>的存在。</p><p data-pid=\"5yFjxEhC\">这个话听起来很大。</p><h2>容我从头捋一下</h2><p data-pid=\"eokJomsM\">这两年 AI agent 概念很火。不过也有不少人觉得，所谓 AI agent，就是一堆 prompt 的堆砌，然后搜索数据，执行AI写的代码，没什么新鲜玩意。</p><p data-pid=\"teM-PRbh\">许多公司出品的 AI agent 软件，说白了就是在搭建 workflow。这个搭建过程，还是人来干的。就像下图这样。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-071537970e21cfd66897e231b67d9dcb_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"506\" data-rawheight=\"334\" data-original-token=\"v2-141a08063bbe49ec394f8c96c9a374c7\" class=\"origin_image zh-lightbox-thumb\" width=\"506\" data-original=\"https://pic2.zhimg.com/v2-071537970e21cfd66897e231b67d9dcb_r.jpg\"/></figure><p data-pid=\"UaVbaak2\">大白话讲，就是工程师来写 prompt，第一步完成什么工作，利用工具得到什么结果，然后第二步，完成什么工作，得到什么结果。最后，输出一个最终的成品结果来。下图就是一个典型的 AI workflow：</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-eb6892915aa8ccb81d5bd34e2580e5de_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"828\" data-rawheight=\"258\" data-original-token=\"v2-8982a6fcf9dc9d7063bd8947b8ff88ff\" class=\"origin_image zh-lightbox-thumb\" width=\"828\" data-original=\"https://pica.zhimg.com/v2-eb6892915aa8ccb81d5bd34e2580e5de_r.jpg\"/></figure><p data-pid=\"9Y7CdCpx\">这种 workflow 利用 AI 和各种工具来解决问题。你可以看到，整个过程，就是思考一步，执行一步，如果复杂点，还会进行反思，反思过去的执行和思考有没有错误。这就像有句老话说的：</p><blockquote data-pid=\"DoJOXXyS\"> 既在干中学，又在学中干。<br/> <br/> 学而不思则罔，思而不学则殆。<br/> </blockquote><p data-pid=\"EpZQiC4M\">但是我要说：上述这些依然不是最终形式。</p><p data-pid=\"MklU9mwZ\">真正的 AI，应该是：<b>当我向 AI 提出问题之后，就不需要我再去告诉它第一步干什么，第二步干什么；而是 AI 自己就能够去规划，去执行，去检验结果正确与否。</b></p><p data-pid=\"0b_IJsI6\">让 AI 来模拟人类真正在实践与思考中的全过程。那就是当下最火热的方向：<b>端到端 agentic AI 模型</b>。</p><p data-pid=\"9sRAoLhd\">这也就是 Kimi-Researcher 所做的工作，<b>End-to-End RL Training for Emerging Agentic Capabilities 端到端，利用强化学习来训练 AI 模型的 agentic 涌现能力</b>。用函数的形式呈现，就是下图这样。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-84cf362bcdfbe8402a58255f258cb309_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"835\" data-rawheight=\"313\" data-original-token=\"v2-f91c8294b48acfb75b5e04d57b6461ff\" class=\"origin_image zh-lightbox-thumb\" width=\"835\" data-original=\"https://picx.zhimg.com/v2-84cf362bcdfbe8402a58255f258cb309_r.jpg\"/></figure><p data-pid=\"v5sCbxjm\">这就是我为何说，Kimi-Researcher 这一手 agentic 端到端模型优势之处。</p><p data-pid=\"-LRa1-VO\">前面文章开头提到的 <b>无限猴子定理</b>，就是对这种类型 模型训练的一个最好的注解。</p><blockquote data-pid=\"uJeqpiW3\"> 我们现在就规定好 AI 模型需要完成的任务（让猴子打出莎士比亚的诗歌），不论这个任务需要经历多少轮困难，需要执行多少轮任务（让猴子打字）。只要最终能够完成任务，就算是 AI 模型的正向反馈。<br/> </blockquote><p data-pid=\"3R4oZnOS\">通过这种方式，对于 AI 模型自主搜集资料，自主执行任务的强化学习，已经十分接近人类从实践中学习和反思的过程。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-cfef9cedcb6d4f1318905c1d0915d4ee_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"736\" data-rawheight=\"738\" data-original-token=\"v2-94d483d6e49a4f705e6cb2f44c18a875\" class=\"origin_image zh-lightbox-thumb\" width=\"736\" data-original=\"https://pic3.zhimg.com/v2-cfef9cedcb6d4f1318905c1d0915d4ee_r.jpg\"/></figure><h2>评测 Kimi-Researcher</h2><p data-pid=\"9PJpnu5z\">评测 AI 模型这件事，随着 AI 模型能力一代更比一代强，许许多多老数据集问题都比较简单，也就逐渐失去了意义，大家都能考试考 90多分，其实也就没什么区分性。</p><p data-pid=\"CeurEwY5\">所以，想要区分 AI 模型能力，就得上难题。数据集 <b>人类最后的考试（Humans&#39; Last Exam，HLE）</b> 就相当有区分性了。这个数据集里面基本上几千道题，都是难题，解决起来，不仅需要 AI 模型能够分析和思考，还得利用各种 tools 来完成工作。</p><p data-pid=\"8_8TDp_u\">如下图所示，各种大模型在 HLE 数据集上的得分大都不超过 20% 的准确率。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-e32642cbf4923f6837b1022a984f9c1d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"796\" data-rawheight=\"435\" data-original-token=\"v2-2b24f10933bce4398fcda7ab615a6e89\" class=\"origin_image zh-lightbox-thumb\" width=\"796\" data-original=\"https://picx.zhimg.com/v2-e32642cbf4923f6837b1022a984f9c1d_r.jpg\"/></figure><p data-pid=\"HCsMzJzl\">而 Kimi-Researcher 在这个数据集上的 准确率做到了和 Gemini-Deep-Research 持平，达到了当下深度研究 AI 模型的第一梯队水平。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-080c35a9f58d2be61b2fe16950649159_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"681\" data-rawheight=\"452\" data-original-token=\"v2-8f31b39ef050fa57758aa99d4e3f6945\" class=\"origin_image zh-lightbox-thumb\" width=\"681\" data-original=\"https://pic2.zhimg.com/v2-080c35a9f58d2be61b2fe16950649159_r.jpg\"/></figure><p data-pid=\"ZTj6xufy\">要我说，Kimi-Researcher 这一手利用强化学习实现端到端模型方向选得对。接下来几年之内的 AI 模型竞争，就是在这个方向上进行。</p><p data-pid=\"qKrKlsfa\">最近时不时有网上的信息，说什么 AI 六小龙网上信息少了。好像没存在感了。</p><p data-pid=\"QyGeJdZT\">我的观点是，<b>人类距离完全超级 AI 智能体还差得很远，中间要走的路非常长，你完全不需要在短短几个月、一两年的时间里，就急着给一家公司下定论。</b></p><p data-pid=\"bXseIGZU\">这不嘛，Kimi-Researcher 一使手腕，就重得 AI 届的盛宠。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-9b70cdf9499450adfe4c9ae3cc936ee6_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"485\" data-rawheight=\"336\" data-original-token=\"v2-3a13df437d41b7de56b92c28bf7d7c80\" class=\"origin_image zh-lightbox-thumb\" width=\"485\" data-original=\"https://pica.zhimg.com/v2-9b70cdf9499450adfe4c9ae3cc936ee6_r.jpg\"/></figure><p data-pid=\"yQ-qDMWK\">不光是在 HLE 数据集上，Kimi-Researcher 也在xbench、seal-0多个数据集上超越或追平了其它基座模型。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-1128ed1c55b71452c4fb66a7f65db249_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1388\" data-rawheight=\"840\" data-original-token=\"v2-7f339edfedaca44ec99892906cad9890\" class=\"origin_image zh-lightbox-thumb\" width=\"1388\" data-original=\"https://pic4.zhimg.com/v2-1128ed1c55b71452c4fb66a7f65db249_r.jpg\"/></figure><h2>内测效果</h2><p data-pid=\"i5Xzxsaf\">我拿到了 Kimi-Researcher 的内测码，并且做了不少测试。</p><p data-pid=\"pTZ_ldu0\">实事求是讲，HLE 数据集任务难度很大，对于当下 AI 模型挑战极大。即便 Kimi 和 Gemini 达到了当前最高水平 <b>26.9%</b> 准确率，其实放在实战环境中，这个准确率也并不算好，可以说，端到端 agentic AI 模型的大战还有很长的战役需要进行。</p><p data-pid=\"GT9U8-J5\">看指标不如看内测效果。这是我在 Kimi-Researcher 上尝试的几个比较难的问题。希望对你也有所参考。</p><h2>日本养老难题</h2><p data-pid=\"Gy4Pu59o\">由于最近社会经济形势下滑，许多人都担心失业与养老问题。所以我就让 Kimi-Researcher 给出一个日本过去三十年如何应对养老与失业的报告。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-6ce2f7a198a3cf6314cb3b6704c8fd7d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"777\" data-rawheight=\"688\" data-original-token=\"v2-3a7507cd6755dc1f524f5593bc15d90e\" class=\"origin_image zh-lightbox-thumb\" width=\"777\" data-original=\"https://picx.zhimg.com/v2-6ce2f7a198a3cf6314cb3b6704c8fd7d_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-3f6ba3325fbc2ebdf3b3cefe02ef35b5_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"783\" data-rawheight=\"720\" data-original-token=\"v2-9574bc35f128e67c128017e054a94840\" class=\"origin_image zh-lightbox-thumb\" width=\"783\" data-original=\"https://pic4.zhimg.com/v2-3f6ba3325fbc2ebdf3b3cefe02ef35b5_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-dfdb154fd1ef9316f3027d0ed7c056e3_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1092\" data-rawheight=\"852\" data-original-token=\"v2-371475733f68695145932acaeeb15948\" class=\"origin_image zh-lightbox-thumb\" width=\"1092\" data-original=\"https://pic2.zhimg.com/v2-dfdb154fd1ef9316f3027d0ed7c056e3_r.jpg\"/></figure><p data-pid=\"VmUIx1jn\">对于这类查资料，总结汇总信息的文科类课题，不得不说，对这个 AI 模型来说真不是啥难事。这份报告通读下来，我就基本上对日本过去三十年养老和就业方面工作有了一手清晰的认知。数据详实，资料丰富，我真的是可以拿着这份报告去做课题交作业了。</p><h2>Twitter自动发帖程序</h2><p data-pid=\"HzZ1hR7u\">我还想利用程序在 Twitter 上为我的产品打广告。所以就让 Kimi-Researcher 来帮我写一份代码，实现自动发帖。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-9ec25d6132df0094696bab7dca24394e_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"792\" data-rawheight=\"721\" data-original-token=\"v2-d13eed79bc9f1b393056215a82b495c1\" class=\"origin_image zh-lightbox-thumb\" width=\"792\" data-original=\"https://pica.zhimg.com/v2-9ec25d6132df0094696bab7dca24394e_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-5467a82839ae939961840dc94937fe6a_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"781\" data-rawheight=\"735\" data-original-token=\"v2-b1bef7cb7b669d5c071d0d070a4af1da\" class=\"origin_image zh-lightbox-thumb\" width=\"781\" data-original=\"https://pica.zhimg.com/v2-5467a82839ae939961840dc94937fe6a_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-bf00586ba42226d57640b92c8b418d7c_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1079\" data-rawheight=\"855\" data-original-token=\"v2-e231ae248425d3bb3226c5cd2456fb77\" class=\"origin_image zh-lightbox-thumb\" width=\"1079\" data-original=\"https://pic3.zhimg.com/v2-bf00586ba42226d57640b92c8b418d7c_r.jpg\"/></figure><p data-pid=\"JY0GuLy9\">这个程序完成度相当好（当然，这个编程任务其实不算非常难），我去 twitter 上面注册了 API_KEY 和 API_SECRET 之后，就能够直接拿着这个程序运行了。而且给出了详细的在服务器上如何运行的信息。</p><h2>罗夏墨迹心理学测试指标说明与统计</h2><p data-pid=\"u_vnLqju\">我目前还在推进一个罗夏墨迹心理学测试项目，里面涉及到大量的统计指标与含义分析。我想让 AI 模型帮我完成这项工作。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-c316eaae3efb509279c9e4c2491f2ebc_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"780\" data-rawheight=\"684\" data-original-token=\"v2-74c8998019bb4498e969dd53d81fbf67\" class=\"origin_image zh-lightbox-thumb\" width=\"780\" data-original=\"https://pic1.zhimg.com/v2-c316eaae3efb509279c9e4c2491f2ebc_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-b4a2aafd314d8f895ac17c5bb6dadca0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"778\" data-rawheight=\"730\" data-original-token=\"v2-a035427499b0aaf90de9547a6df2d75e\" class=\"origin_image zh-lightbox-thumb\" width=\"778\" data-original=\"https://pic1.zhimg.com/v2-b4a2aafd314d8f895ac17c5bb6dadca0_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-e43cf8ea8e960d6c11e57b549a914d74_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1086\" data-rawheight=\"891\" data-original-token=\"v2-ae4edec0c5cace0f32c0b1f5d54b2f3e\" class=\"origin_image zh-lightbox-thumb\" width=\"1086\" data-original=\"https://pic3.zhimg.com/v2-e43cf8ea8e960d6c11e57b549a914d74_r.jpg\"/></figure><p data-pid=\"xZDrA896\">这个问题，我详细看了 Kimi-Researcher 的分析结果。需要按好坏两方面来评价：</p><ul><li data-pid=\"hZL36G7B\">AI 模型查找资料是真的全面，各种我过去没看过的国外的罗夏墨迹论文和测量指标信息都具备。</li><li data-pid=\"2i1lBeyN\">AI 模型思考过程也是详细的，在输出时，指标统计 与含义都详尽。</li><li data-pid=\"vomc2ynp\">缺点是在最终总结，呈现可视化报告时，没有把所有的指标统计全部汇总到输出报告里面，信息简陋，不够详实。我希望得到的是一份详细的汇总，而非简单的关键字组成的 PPT。</li></ul><h2>股票量化交易方法分析与汇总</h2><p data-pid=\"L4vC-lR_\">我对股票量化交易也感兴趣，所以希望能够让 Kimi-Researcher 把市面上各个机构、私募常用的量化方法告诉我，分析利弊，并给出来代码。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-f0fa84291c02297325cdc67a7a83fe4b_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"569\" data-rawheight=\"740\" data-original-token=\"v2-74ad8c9d0d138dbabb4cc2c843b19c77\" class=\"origin_image zh-lightbox-thumb\" width=\"569\" data-original=\"https://picx.zhimg.com/v2-f0fa84291c02297325cdc67a7a83fe4b_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-70481244b3ba262502412dc3899fb850_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1089\" data-rawheight=\"649\" data-original-token=\"v2-b7d076b22a7748adfef8ccd929eeed9a\" class=\"origin_image zh-lightbox-thumb\" width=\"1089\" data-original=\"https://pic1.zhimg.com/v2-70481244b3ba262502412dc3899fb850_r.jpg\"/></figure><p data-pid=\"MFbbq1eh\">怎么说呢，对于这个资料查找，我觉得完成度很不错，同样的问题在于，程序编写代码上，还是不够详细，也就是说，AI 写大型的，错综复杂的程序，能力还不足，还需要进一步提升。</p><p data-pid=\"cLXyL0wR\">综合内测结果，<b>对于文科类的议题，我对 Kimi-Researcher 比较满意，而涉及到数理、统计方面的信息还需要进一步加强。</b></p><p data-pid=\"LuAjv7Ez\">不过，我对此结果总体上是可接受的，毕竟，端到端 agentic AI 模型的竞争才刚刚开始。HLE 数据集里还有大量的难题需要 AI 模型攻克。</p><h2>结尾说个观点</h2><p data-pid=\"R-wpTdxW\">不论是 Kimi-Researcher，还是 Gemini Deep Research，<b>等到 AI 模型能够在 HLE 数据集上做到 80%~90% 的准确率的时候，我想市面上大部分的 AI agent 公司都没什么存在的必要了。</b></p><p></p>", "is_labeled": false, "visited_count": 559, "thumbnails": ["https://pic1.zhimg.com/50/v2-f9a722e63b0f3444faaf047a78d75e81_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-1577232fa17f4c4f9a4301059d0eafcd_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-e3b7d3351d5f8fc818d0f5ddfb68b600_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-880684c8e0dc76e45bb1a7186346e982_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-e4b16c6de30660750746fe7e71eedfe6_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-846e93c2f1596133f68b1ecea5f675df_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-05708db23871599a9d4054f0e5580b39_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-0c7f88ef4c1abc6c829be901aba1eddd_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-4d92e71a3890bf596cfb961d14abf1b1_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-a8f9c1c7c0417a416140a7f7420678e8_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-54c33a58d35431c76a58f2a07491b0ba_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-c7aa98df8aa30847a888d71c5c39239b_720w.jpg?source=b6762063"], "favorite_count": 13, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": 1920932791178233427}", "attached_info": "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", "action_card": false}, {"id": "80_1750898474.275", "type": "feed", "offset": 80, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898474, "updated_time": 1750898474, "target": {"id": "1992302172", "type": "answer", "url": "https://api.zhihu.com/answers/1992302172", "author": {"id": "26ad33fc8a2501ddb13ce7e1517dfdd6", "url": "https://api.zhihu.com/people/26ad33fc8a2501ddb13ce7e1517dfdd6", "user_type": "people", "url_token": "chen-yue-lao-lao", "name": "陈越姥姥", "headline": "攀拓PAT，IT业的托福", "avatar_url": "https://picx.zhimg.com/50/v2-49e5a5d914d76e92ee6331377b639b02_l.jpg?source=b6762063", "is_org": false, "gender": 0, "followers_count": 133040, "is_following": false, "is_followed": false}, "created_time": 1626014110, "updated_time": 1626014110, "voteup_count": 14930, "thanks_count": 2079, "comment_count": 264, "is_copyable": true, "question": {"id": "435258463", "type": "question", "url": "https://api.zhihu.com/questions/435258463", "author": {"id": "", "url": "", "user_type": "people", "url_token": "", "name": "匿名用户", "headline": "", "avatar_url": "https://picx.zhimg.com/v2-d41c2ceaed8f51999522f903672a521f_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "为什么一些程序员很傲慢？", "created": 1608190611, "answer_count": 0, "follower_count": 0, "comment_count": 208, "bound_topic_ids": [707, 7897, 159445], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "以下为浙大沟通技巧课的部分课件：             你都做到了，还没能得到帮助，那是……真的令人同情。", "excerpt_new": "以下为浙大沟通技巧课的部分课件：             你都做到了，还没能得到帮助，那是……真的令人同情。", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"TWPcM0OX\">以下为浙大沟通技巧课的部分课件：</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-566c0540c037413964849ec7e13ce43f_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"716\" data-rawheight=\"491\" data-original-token=\"v2-fe0b3dcb6c8e254287d04e07150f465c\" data-default-watermark-src=\"https://pic1.zhimg.com/v2-6ab49ae0579089fa41d128f44e357d80_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"716\" data-original=\"https://pic4.zhimg.com/v2-566c0540c037413964849ec7e13ce43f_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-87beb60f7d6812418a56e6b0e5adea1d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"719\" data-rawheight=\"495\" data-original-token=\"v2-b15a509745326400e10f2ae85ab8a874\" data-default-watermark-src=\"https://pic1.zhimg.com/v2-ada7c92aa30dd4bf6990a63215bc27ea_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"719\" data-original=\"https://pic4.zhimg.com/v2-87beb60f7d6812418a56e6b0e5adea1d_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-0530ba62ca95a4f0ad8c6b7509e201e1_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"719\" data-rawheight=\"491\" data-original-token=\"v2-a9b63ae5be3b217c449ea0d97ccdecae\" data-default-watermark-src=\"https://pica.zhimg.com/v2-d939707e1c80b6479d0867d0f6481256_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"719\" data-original=\"https://pic4.zhimg.com/v2-0530ba62ca95a4f0ad8c6b7509e201e1_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-2b21f309e7591eaf113895aca09f5f03_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"718\" data-rawheight=\"492\" data-original-token=\"v2-a9bf59c1b3f2a16399c3f6450859f327\" data-default-watermark-src=\"https://pic2.zhimg.com/v2-7b89dc766d5607cde5da358257f86479_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"718\" data-original=\"https://picx.zhimg.com/v2-2b21f309e7591eaf113895aca09f5f03_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-a2cf2e72963d8104644e54181d65a86d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"715\" data-rawheight=\"492\" data-original-token=\"v2-e72a37cc12b1fbeb38fb02326d8bb396\" data-default-watermark-src=\"https://pica.zhimg.com/v2-fa38e52e00d6628a99a1332678518d72_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"715\" data-original=\"https://pic4.zhimg.com/v2-a2cf2e72963d8104644e54181d65a86d_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-499cee69407b64368c2b823aed9427dd_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"717\" data-rawheight=\"491\" data-original-token=\"v2-e650420ff65871726735cd0a76a3a026\" data-default-watermark-src=\"https://pic4.zhimg.com/v2-b84fdb4cba3140824261509c60261593_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"717\" data-original=\"https://pic4.zhimg.com/v2-499cee69407b64368c2b823aed9427dd_r.jpg\"/></figure><p data-pid=\"DEkNdzCn\">你都做到了，还没能得到帮助，那是……真的令人同情。</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 867490, "favorite_count": 19738, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1992302172}", "attached_info": "CpkJCJ+W9fzOl6rRPBAEGgkzOTIzMTgyNjUgnoOshwYo0nQwiAJAUEowChtUU19TT1VSQ0VfQkFTSUNfSU5GT19SRUNBTEwSATAYACAAOgp7InJhdyI6IiJ9Sj8KKlRTX1NPVVJDRV9aUkVDQUxMX0ZFRURSRV9ORVdCSUVfSE9VUkxZX1JVTRIBMBgAIAA6CnsicmF3IjoiIn1aCDU5MDAzMDI5YiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3IKMTk5MjMwMjE3MooBCTQzNTI1ODQ2M6oBCXJlY29tbWVuZMIBIDI2YWQzM2ZjOGEyNTAxZGRiMTNjZTdlMTUxN2RmZGQ28gEKCAwSBk5vcm1hbPIBKAgKEiQzZTNhZWQ4Yi1iNTA1LTQ1MjctYjdhMy1iZWI1Yjc2ZjVmMmLyAQYICxICMTSCAgCIAs73uc36MpICIDI2YWQzM2ZjOGEyNTAxZGRiMTNjZTdlMTUxN2RmZGQ2mgIAygIWU2hvckludGVyZXN0V2VpZ2h0UnVsZcoCFkFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhtJbnRlcmFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhZSZXZpc2l0VmFsdWVXZWlnaHRSdWxlygIYUGVyaW9kSW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxlygIUQ29udGVudEFnZVdlaWdodFJ1bGXKAhVRdWVzdGlvbklzb2xhdGlvblJ1bGXaAhtUU19TT1VSQ0VfQkFTSUNfSU5GT19SRUNBTEzoAgL6AgtOT1JNQUxfRkxPV4oDIGMyNzA1NzU4MGU5ZjQ0YWI5MzFiYzYxNmM2NWE5YTA1mgMNCgJ2MhAAGgVvdGhlcqgDovk02AMA6gMRYmFzaWNfaW5mb19yZWNhbGz6A7kCEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERTotCAIQzAUY6wMiI3YyLWZlMGIzZGNiNmM4ZTI1NDI4N2QwNGUwNzE1MGY0NjVjOi0IAhDPBRjvAyIjdjItYjE1YTUwOTc0NTMyNjQwMGUxMGYyYWU4NWFiOGE4NzQ6LQgCEM8FGOsDIiN2Mi1hOWI2M2FlNWJlM2IyMTdjNDQ5ZWEwZDk3Y2NkZWNhZTotCAIQzgUY7AMiI3YyLWE5YmY1OWMxYjNmMmExNjM5OWMzZjY0NTA4NTlmMzI3Oi0IAhDLBRjsAyIjdjItZTcyYTM3Y2MxMmIxZmJlYjM4ZmIwMjMyNmQ4YmIzOTY6LQgCEM0FGOsDIiN2Mi1lNjUwNDIwZmY2NTg3MTcyNjczNWNkMGE3NmEzYTAyNoAEAIgEAJIEBk5vcm1hbJoEATKgBACoBACwBAC6BAZtYW51YWzCBAMxNzDIBADSBA/mjqjojZDlt7Lmm7TmlrDYBADwBAD5BAAAAMD6/78/gQUAAAAAAAAAAIkFBagCroK90j+SBQCaBQNkZnSiBQNkZnSyBQExuQUAAAAAAAAAANAFAOAFAOgFAPAFDpAGAKAGVKgGAJICJQoJMzkyMzE4MjY1EgoxOTkyMzAyMTcyGAQiCklNQUdFX1RFWFQ=", "action_card": false}, {"id": "81_1750898474.953", "type": "feed", "offset": 81, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898474, "updated_time": 1750898474, "target": {"id": "1902528927043401152", "type": "answer", "url": "https://api.zhihu.com/answers/1902528927043401152", "author": {"id": "1cd4bb6db8ac9af8b109d9fe9578f336", "url": "https://api.zhihu.com/people/1cd4bb6db8ac9af8b109d9fe9578f336", "user_type": "people", "url_token": "reseted1711172514000", "name": "印度肢解巴基斯坦", "headline": "310，金融业，地域歧视爱好者", "avatar_url": "https://pica.zhimg.com/50/v2-c30925baa1df22d840728ff2cbb2ae2a_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 2368, "is_following": false, "is_followed": false}, "created_time": 1746378243, "updated_time": 1746518051, "voteup_count": 5713, "thanks_count": 247, "comment_count": 613, "is_copyable": false, "question": {"id": "664151570", "type": "question", "url": "https://api.zhihu.com/questions/664151570", "author": {"id": "8790832f21eaf996831dbe961b38dd73", "url": "https://api.zhihu.com/people/8790832f21eaf996831dbe961b38dd73", "user_type": "people", "url_token": "aaaaace-88", "name": "aaaaace", "headline": "", "avatar_url": "https://pic1.zhimg.com/50/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "为什么山姆这么受欢迎？", "created": 1723504406, "answer_count": 0, "follower_count": 0, "comment_count": 113, "bound_topic_ids": [843, 50603], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "作为资深爱国人士，我一向遵循如下原则 凡是和人命直接相关的，无脑选洋货，无论价格差多少都要远离made in CN。比如飞机，汽车，婴儿奶粉，药品，医疗器械，疫苗…… 凡是和提升生活品质相关的，有条件的选洋货，实在囊中羞涩选国货。比如烟，酒，冰激凌，牛肉，牛奶，海鲜，水果，避Y套，电子产品…… 对于低值易耗品，不支持国货是汉奸。比如打火机，餐巾纸，一次性餐具，一次性筷子…… 为啥山姆受欢迎？因为山姆货架上的产…", "excerpt_new": "作为资深爱国人士，我一向遵循如下原则 凡是和人命直接相关的，无脑选洋货，无论价格差多少都要远离made in CN。比如飞机，汽车，婴儿奶粉，药品，医疗器械，疫苗…… 凡是和提升生活品质相关的，有条件的选洋货，实在囊中羞涩选国货。比如烟，酒，冰激凌，牛肉，牛奶，海鲜，水果，避Y套，电子产品…… 对于低值易耗品，不支持国货是汉奸。比如打火机，餐巾纸，一次性餐具，一次性筷子…… 为啥山姆受欢迎？因为山姆货架上的产…", "preview_type": "default", "preview_text": "", "reshipment_settings": "disallowed", "content": "<p data-pid=\"tapnhTHX\">作为资深爱国人士，我一向遵循如下原则</p><p data-pid=\"4b4jDlOO\">凡是和人命直接相关的，无脑选洋货，无论价格差多少都要远离made in CN。比如飞机，汽车，婴儿奶粉，药品，医疗器械，疫苗……</p><p data-pid=\"8SrWEtAT\">凡是和提升生活品质相关的，有条件的选洋货，实在囊中羞涩选国货。比如烟，酒，冰激凌，牛肉，牛奶，海鲜，水果，避Y套，电子产品……</p><p data-pid=\"15tl1qOZ\">对于低值易耗品，不支持国货是汉奸。比如打火机，餐巾纸，一次性餐具，一次性筷子……</p><p data-pid=\"fDpCJYMX\">为啥山姆受欢迎？因为山姆货架上的产品的产地严格遵循我这个原则。</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 399403, "favorite_count": 2132, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1902528927043401152}", "attached_info": "CpwGCJ+W9fzOl6rRPBAEGgk3MjU4Nzc5MDggg7zewAYo0Sww5QRAUUovCgZJdGVtQ0YSH2RvY190eXBlOiBBbnN3ZXIKaWQ6IDY5NzIzMDIxMwoYACAAOgBaCTEwOTg2NjkxMmIgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyEzE5MDI1Mjg5MjcwNDM0MDExNTKKAQk2NjQxNTE1NzCqAQlyZWNvbW1lbmTCASAxY2Q0YmI2ZGI4YWM5YWY4YjEwOWQ5ZmU5NTc4ZjMzNvIBCggMEgZOb3JtYWzyASgIChIkMGVlNDM0MTYtYTNhMi00NjNkLWI0ZDEtMjYzZmQ5MGEwMDI08gEGCAsSAjE0ggIAiALO97nN+jKSAiAxY2Q0YmI2ZGI4YWM5YWY4YjEwOWQ5ZmU5NTc4ZjMzNpoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhZBY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIbSW50ZXJhY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIYUGVyaW9kSW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxlygIUQ29udGVudEFnZVdlaWdodFJ1bGXKAhVRdWVzdGlvbklzb2xhdGlvblJ1bGXaAgZJdGVtQ0boAgL6AgtOT1JNQUxfRkxPV4oDIGMyNzA1NzU4MGU5ZjQ0YWI5MzFiYzYxNmM2NWE5YTA1mgMNCgJ2MhAAGgVvdGhlcqgDq7AY2AMA6gMVdGV4dEFsbFNpdGVNdkl0ZW1DRlYy+gMfEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERYAEAIgEAJIEBk5vcm1hbJoEATKgBACoBACwBAC6BAZtYW51YWzCBAMxNjDIBADSBA/mjqjojZDlt7Lmm7TmlrDYBADwBAD5BAAAAIDsW8I/gQUAAAAAAAAAAIkFBagCroK90j+SBQCaBQNkZnSiBQNkZnSyBQExuQUAAAAAAAAAANAFAOAFAOgFAPAFDpAGAKAGVagGAJICLgoJNzI1ODc3OTA4EhMxOTAyNTI4OTI3MDQzNDAxMTUyGAQiCklNQUdFX1RFWFQ=", "action_card": false}, {"id": "82_1750898474.944", "type": "feed", "offset": 82, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898474, "updated_time": 1750898474, "target": {"id": "1921268233002005180", "type": "article", "url": "https://api.zhihu.com/articles/1921268233002005180", "author": {"id": "aa47fdea99cdcbd9ca3ffda31f7b0a50", "url": "https://api.zhihu.com/people/aa47fdea99cdcbd9ca3ffda31f7b0a50", "user_type": "people", "url_token": "3377-24", "name": "3377", "headline": "幸运二花同学", "avatar_url": "https://pic1.zhimg.com/50/1e7bc1e2d391483f5558ef8c4242ce07_l.jpg?source=b6762063", "is_org": false, "gender": 0, "followers_count": 16, "is_following": false, "is_followed": false}, "title": "UNet特征向量融合过程伪代码", "comment_permission": "all", "created": 1750846076, "updated": 1750846076, "voteup_count": 1, "voting": 0, "comment_count": 0, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "def unet_forward( noisy_latent: Tensor, # [B, 4, H, W] timestep: Tensor, # [B] text_embeddings: Tensor, # [B, max_len, D] control_images: Dict[str, Tensor], # 控制图 {&#39;canny&#39;: [B, 3, H, W], ...} mask: Optional[Tensor] = None, # [B, 1, H, W] original_latent: Optional[Tensor] = None # [B, 4, H, W] (img2img) ) -&gt; Tensor: # 1. 时间嵌入 t_emb = timestep_embedding(timestep) # 2. 处理控制条件 control_f…", "excerpt_new": "def unet_forward( noisy_latent: Tensor, # [B, 4, H, W] timestep: Tensor, # [B] text_embeddings: Tensor, # [B, max_len, D] control_images: Dict[str, Tensor], # 控制图 {&#39;canny&#39;: [B, 3, H, W], ...} mask: Optional[Tensor] = None, # [B, 1, H, W] original_latent: Optional[Tensor] = None # [B, 4, H, W] (img2img) ) -&gt; Tensor: # 1. 时间嵌入 t_emb = timestep_embedding(timestep) # 2. 处理控制条件 control_f…", "preview_type": "default", "preview_text": "", "content": "<div class=\"highlight\"><pre><code class=\"language-text\">def unet_forward(\n    noisy_latent: Tensor,          # [B, 4, H, W]\n    timestep: Tensor,              # [B]\n    text_embeddings: Tensor,       # [B, max_len, D]\n    control_images: Dict[str, Tensor],  # 控制图 {&#39;canny&#39;: [B, 3, H, W], ...}\n    mask: Optional[Tensor] = None, # [B, 1, H, W]\n    original_latent: Optional[Tensor] = None # [B, 4, H, W] (img2img)\n) -&gt; Tensor:\n    \n    # 1. 时间嵌入\n    t_emb = timestep_embedding(timestep)\n    \n    # 2. 处理控制条件\n    control_features = []\n    for name, image in control_images.items():\n        # 通过对应的ControlNet分支\n        feat = controlnet_branches[name](image, t_emb, text_embeddings)\n        control_features.append(feat)\n    \n    # 3. 处理mask条件\n    if mask is not None:\n        # 拼接为额外通道\n        noisy_latent = torch.cat([noisy_latent, mask], dim=1)\n    \n    # 4. UNet主干处理\n    x = noisy_latent\n    down_samples = []\n    \n    # 下采样过程\n    for i, down_block in enumerate(down_blocks):\n        # 残差块（包含时间融合）\n        x = down_block.resnets[0](x, t_emb)\n        \n        # 注入控制特征\n        if control_features:\n            x = x + control_scale * control_features[i]\n        \n        # 注意力层（文本融合）\n        x = down_block.attentions[0](x, text_embeddings)\n        \n        down_samples.append(x)\n        x = down_block.downsampler(x)\n    \n    # 中间块\n    x = mid_block.resnets[0](x, t_emb)\n    x = mid_block.attentions[0](x, text_embeddings)\n    x = mid_block.resnets[1](x, t_emb)\n    \n    # 上采样过程\n    for i, up_block in enumerate(up_blocks):\n        # 跳跃连接\n        x = torch.cat([x, down_samples.pop()], dim=1)\n        \n        # 残差块\n        x = up_block.resnets[0](x, t_emb)\n        \n        # 注入控制特征\n        if control_features:\n            x = x + control_scale * control_features[len(down_blocks)+i]\n        \n        # 注意力层\n        x = up_block.attentions[0](x, text_embeddings)\n        \n        # 上采样\n        x = up_block.upsamplers[0](x)\n    \n    # 5. 输出层\n    noise_pred = out_conv(x)\n    \n    # 6. 掩码混合 (inpainting)\n    if mask is not None and original_latent is not None:\n        # 计算混合权重\n        blend_factor = alphas_cumprod[timestep].sqrt()\n        # 保留已知区域\n        known_part = original_latent * mask\n        # 生成未知区域\n        generated_part = noisy_latent - noise_pred * (1 - alphas_cumprod[timestep]).sqrt()\n        # 混合结果\n        noise_pred = blend_factor * known_part + (1 - blend_factor) * generated_part\n    \n    return noise_pred</code></pre></div><p></p>", "is_labeled": false, "visited_count": 7, "favorite_count": 0, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": 1921268233002005180}", "attached_info": "CqkFCJ+W9fzOl6rRPBAHGgkyNTk1NTAxNzUg/JTvwgYoATAAQFJKJAoZVFNfU09VUkNFX1dBUk1fVVBfTk9STUFMMhIBMBgAIAA6AGIgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyEzE5MjEyNjgyMzMwMDIwMDUxODCqAQlyZWNvbW1lbmTCASBhYTQ3ZmRlYTk5Y2RjYmQ5Y2EzZmZkYTMxZjdiMGE1MPIBCggMEgZOb3JtYWzyASgIChIkMGU3MmY4YWQtMTc5ZC00NWRmLWI3ODQtNjM0ZGFjYjJmMDYx8gEGCAsSAjE0ggIAiALO97nN+jKSAiBhYTQ3ZmRlYTk5Y2RjYmQ5Y2EzZmZkYTMxZjdiMGE1MJoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhVVc2VyTGNuRXhpdFdlaWdodFJ1bGXKAhhDb250ZW50V2FybVVwQnJlYWtJblJ1bGXaAhlUU19TT1VSQ0VfV0FSTV9VUF9OT1JNQUwy6AIC+gILTk9STUFMX0ZMT1eKAyBjMjcwNTc1ODBlOWY0NGFiOTMxYmM2MTZjNjVhOWEwNZoDDQoCdjIQABoFb3RoZXKoAwfYAwDqAx90ZXh0XzEyaG91cl91bmlmaW5zaGVkX3JlY2FsbGVy+gMfEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERYAEAIgEAJIEBk5vcm1hbJoEATKgBACoBACwBAC6BAJhacIEAzQwMMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAYO2qhD+BBQAAAAAAAAAAiQUFqAKugr3SP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUOkAYAoAZWqAYBkgIuCgkyNTk1NTAxNzUSEzE5MjEyNjgyMzMwMDIwMDUxODAYByIKSU1BR0VfVEVYVA==", "action_card": false}, {"id": "83_1750898474.780", "type": "feed", "offset": 83, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898474, "updated_time": 1750898474, "target": {"id": "1915736469101871409", "type": "answer", "url": "https://api.zhihu.com/answers/1915736469101871409", "author": {"id": "dfd18f312d5211bc280bddc5188a8ff2", "url": "https://api.zhihu.com/people/dfd18f312d5211bc280bddc5188a8ff2", "user_type": "people", "url_token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "韦海生", "headline": "公众号「韦海生」，一个比较正经的读书号。", "avatar_url": "https://pic1.zhimg.com/50/v2-cc25c65d30efc8cad9823e870ad35fda_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 10480, "is_following": false, "is_followed": false}, "created_time": 1749527166, "updated_time": 1749527166, "voteup_count": 101, "thanks_count": 4, "comment_count": 11, "is_copyable": false, "question": {"id": "360490763", "type": "question", "url": "https://api.zhihu.com/questions/360490763", "author": {"id": "", "url": "", "user_type": "people", "url_token": "", "name": "匿名用户", "headline": "", "avatar_url": "https://picx.zhimg.com/v2-d41c2ceaed8f51999522f903672a521f_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "你读过后劲最大的书是哪本？", "created": 1576072111, "answer_count": 0, "follower_count": 0, "comment_count": 2, "bound_topic_ids": [53, 1164, 82495], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "thumbnail": "https://picx.zhimg.com/50/v2-2b58d644ad481fa3aa862161e7e80600_720w.jpg?source=b6762063", "excerpt": "看到这个问题，我第一时间就想到了纳西姆·尼古拉斯·塔勒布的《反脆弱》，这是我读过后劲最大的一本书。 这本书的内容我就不多介绍了，你可以去豆瓣页面看内容简介，或者在网上搜一搜就能了解。我只说《反脆弱》这本书中的一个核心词，也是给我很大后劲的词： 反脆弱性。脆弱是什么？玻璃杯是脆弱的。比如你邮寄一个玻璃相框，怕它在路上被摔坏，就会写上「易碎」两个字。脆弱的东西不喜欢波动、不喜欢随机性、不喜欢不确定性、…", "excerpt_new": "看到这个问题，我第一时间就想到了纳西姆·尼古拉斯·塔勒布的《反脆弱》，这是我读过后劲最大的一本书。 这本书的内容我就不多介绍了，你可以去豆瓣页面看内容简介，或者在网上搜一搜就能了解。我只说《反脆弱》这本书中的一个核心词，也是给我很大后劲的词： 反脆弱性。脆弱是什么？玻璃杯是脆弱的。比如你邮寄一个玻璃相框，怕它在路上被摔坏，就会写上「易碎」两个字。脆弱的东西不喜欢波动、不喜欢随机性、不喜欢不确定性、…", "preview_type": "default", "preview_text": "", "reshipment_settings": "disallowed", "content": "<p data-pid=\"BiVI-_cH\">看到这个问题，我第一时间就想到了纳西姆·尼古拉斯·塔勒布的《反脆弱》，这是我读过后劲最大的一本书。</p><p data-pid=\"b39flCHN\">这本书的内容我就不多介绍了，你可以去豆瓣页面看内容简介，或者在网上搜一搜就能了解。我只说《反脆弱》这本书中的一个核心词，也是给我很大后劲的词：<b>反脆弱性</b>。</p><p data-pid=\"dgvc_6_C\">脆弱是什么？玻璃杯是脆弱的。比如你邮寄一个玻璃相框，怕它在路上被摔坏，就会写上「易碎」两个字。脆弱的东西不喜欢波动、不喜欢随机性、不喜欢不确定性、不喜欢混乱、不喜欢错误、不喜欢压力。遇到这些，它们就会受损。</p><p data-pid=\"lFH2gbA8\">脆弱的反面是强韧，强韧是什么呢？摔不坏的铁锤就是强韧的。它不会受损，但也不会因为被摔打而变得更好。</p><p data-pid=\"FoRtkR-d\">那反脆弱性呢？反脆弱的东西喜欢波动性、喜欢随机性、喜欢不确定性、喜欢混乱、喜欢错误、喜欢压力。遇到这些，它们不但不会受损，还会受益，还会变得更好。</p><p data-pid=\"8hk4rs4Z\">就像神话里的九头蛇怪，你砍掉它的一个头，它就会长出两个。再举刚才那个例子，你邮寄一个反脆弱的东西，应在包裹上写什么？写「请乱扔乱放！」因为摔打对它有好处。</p><p data-pid=\"wQnoZfyH\">这本书要告诉我们的是，在充满不确定性、随机性、不可预测的世界里，<b>我们与其去预测未来，去避免一切波动和错误，不如让自己变得反脆弱</b>。</p><p data-pid=\"BaBeaAas\">很多我们以为「稳定」或「安全」的系统，比如一些经济理论专家管理的对冲基金，或者中心化的国家，其实是脆弱的。</p><p data-pid=\"B1Fv8sBP\">而那些我们以为「混乱」或「有风险」的东西，比如街头生活，自由探索和反复试错，甚至一些自然的系统（进化、有机体），却具有反脆弱性。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-639fdc86faa76933526fe4f9b85d3366_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"711\" data-original-token=\"v2-c87fcb677c2772cafa63aa20570b9576\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-6a09a8699e998e87f05ac8ec7ca28ebc_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-639fdc86faa76933526fe4f9b85d3366_r.jpg\"/></figure><p data-pid=\"LYy3dk6M\">这本书挑战了我们习以为常的想法。它说预测很难，特别是预测那些影响很大、发生频率不高的「黑天鹅」事件。这些事件，对那些毫无准备的人（书里叫「火鸡」）来说是灾难。</p><p data-pid=\"rGRfllpK\">与其去预测它们什么时候来，<b>不如让自己在面对这些冲击时不受伤害，甚至能从中受益</b>。这本书就是要提供一个非预测性的指导，帮助我们在不确定的世界里，做出更有智慧的决策。</p><p data-pid=\"YIaIvSwc\">书里还讲了很多其他有意思的概念，比如医源性损伤——就是医生或干预者为了「帮忙」，结果却造成了伤害。这在医疗、政治、经济领域都存在。</p><p data-pid=\"YSlXuGTk\">还有杠铃策略，就是把你的风险和机会放在两个极端，避免脆弱的中间地带。这些想法，都围绕着如何在一个充满波动和未知的世界里，让自己活得更好，甚至变得更强大。</p><p data-pid=\"mBOzEIKW\">在读这本书时，有一句话我曾打印出来贴在办公桌上，提醒自己要做一个反脆弱的人：「<b>我宁愿做愚钝但具有反脆弱性的人，也不做极其聪明但脆弱的人。</b>」</p><p data-pid=\"OuYdEKIo\">这句话一下子就打破了我过去对「聪明」的理解。我总觉得，一个人聪明，有知识，脑子转得快，就能把事情做好。在学校里也同样看重成绩，看重考试分数，觉得这些代表了聪明。可这本书却说不是这样的。</p><p data-pid=\"P3oaYX9a\">塔勒布在书里举了一些例子。比如，他认为很多大学教授和那些做预测的分析师，虽然可能在纸上看起来很「聪明」，会用各种复杂的模型和理论，可他们在真实世界里，在面对那些意料之外的事情时，往往是非常脆弱的。</p><p data-pid=\"fsVetTGk\">他们的模型可能在「平均斯坦」（变化不大，风险可计算的环境）管用，但在「极端斯坦」（大变化影响主导的环境）就完全失效，还经常低估了随机性和不确定性带来的危害。</p><p data-pid=\"NRckI6kK\">相反，那些街头出身的实践者，比如书里的胖子托尼，他没有受过高等教育，不会那些复杂的理论，但他靠直觉，靠经验，靠着对真实世界风险的敏感，却能在市场里活下来，甚至赚到大钱。</p><p data-pid=\"EFBeoiTG\"><b>他关注的不是「真与假」，而是「谁是愚蠢的人，谁不是」</b>。他不是去预测未来，而是去识别脆弱性，然后在它崩溃的时候下注。这是一种从错误中受益，从市场波动中获利的方式，是一种反脆弱的做法。</p><p data-pid=\"uOxW3XE_\">在打印这句话时，我就开始反思自己。我过去是不是也把「聪明」看得太重了？是不是也以为学了很多理论，脑子反应快，就万事大吉了？可生活中真正的挑战，往往不是考试里的那种标准问题。</p><p data-pid=\"Kw2jn9WL\">那些意料之外的事情，那些控制不了的波动，才是真正考验我的时候。<b>如果我的结构是脆弱的，即使我再聪明，一次大的冲击就可能让我一蹶不振</b>。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-a1ce6a59853a41cff4c4f9eab4478a6e_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"720\" data-rawheight=\"410\" data-original-token=\"v2-5484a17058bad22603c25e574699bd97\" data-default-watermark-src=\"https://picx.zhimg.com/v2-f13d74e17b2ab93ec9f6e7b9c6e50f09_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"720\" data-original=\"https://pic1.zhimg.com/v2-a1ce6a59853a41cff4c4f9eab4478a6e_r.jpg\"/></figure><p data-pid=\"p87lIxDG\">我后知后觉地明白，与其花时间去追求那种「看起来很聪明」的状态，去学习那些可能在真实世界里很脆弱的理论，不如把精力放在提升自己的反脆弱性上。</p><p data-pid=\"AK97RVj2\">那怎么做呢？就是要去实践，在事上磨练，从错误中学习，不害怕波动和压力。就像书里说的，偶尔的饥饿和压力对身体有好处。</p><p data-pid=\"yIQjMdJv\">这让我更看重那些能够让我变得更强大，更不容易被外界伤害的行动，即使这些行动看起来没有那么「聪明」，甚至有点「笨」。</p><p data-pid=\"JPwdVK7H\">比如，我开始愿意去尝试那些有失败可能的事情，因为小错误的累积能让我更不容易犯大错误。前段时间我还写了一篇文章，叫《读了&lt;反脆弱&gt;，我开始主动吃苦》。</p><p data-pid=\"2mqT6hw_\">为什么这本《反脆弱》给我的后劲最大？因为它彻底改变了我看待风险和不确定性的方式。以前我总觉得，不确定性是坏事，错误是坏事，波动是坏事，应该尽量避免。</p><p data-pid=\"MbLIoJyW\">可这本书却说，<b>不确定性不一定是坏事，错误不一定是坏事</b>。对于反脆弱的事物来说，它们是机会。</p><p data-pid=\"7qmd8_np\">你看很多时候我们追求的「稳定」和「效率」，其实是以牺牲长期的强韧性为代价的。就像系统性地防止小的森林火灾，最后可能导致一场无法控制的大火。还有市场长期缺乏波动，隐藏的风险反而会越积越多。</p><p data-pid=\"qwbZaPqb\">这让我懂得，我们很多人为的干预，为了让事情看起来更「好」，更「可预测」，结果却适得其反，让系统变得更脆弱。</p><p data-pid=\"45yRKSwL\">书里还有不少比喻，让你深入了解反脆弱。比如把轮子安到旅行箱上，这个简单得不得了的想法，竟然在轮子发明后过了几千年才出现。这说明很多时候，真正的进步不是来自复杂的理论，而是来自简单的实践和看得见的需求。</p><p data-pid=\"poueP3uE\">还有「教鸟儿飞行」的比喻。很多我们以为是理论指导实践的，其实恰恰相反，是实践中摸索出来的东西，后来才有了理论来解释。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-5132c229aa427a3da25e979276f0d35a_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"727\" data-rawheight=\"329\" data-original-token=\"v2-f655a283da050dfebc310eaf686d2f84\" data-default-watermark-src=\"https://pica.zhimg.com/v2-bcd29ebf70bea9124f4becfa6cfd06d2_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"727\" data-original=\"https://pica.zhimg.com/v2-5132c229aa427a3da25e979276f0d35a_r.jpg\"/></figure><p data-pid=\"u9SU2SjG\">就像书中第14章说到的「绿色木材谬误」。你以为卖绿色木材的人是因为懂它的化学成分，其实他只知道怎么买卖赚钱，根本不知道木材为什么是绿的。这些都让我对那些看起来很厉害的理论和专家，多了一份清醒。</p><p data-pid=\"O7fBRLyi\">再延伸说下去，比如「少即是多」的道理，不仅在生活里管用，在决策中也一样。有时候知道什么是不该做的（通过减法），比知道什么是对的更有力。去掉那些让你脆弱的东西，很多事情自然而然就会变好。<b>这是一种通过否定来构建反脆弱的方式</b>。</p><p data-pid=\"0EEiOB8l\">这本《反脆弱》不是一本读完就放在书架上的书。它会悄悄地改变你的思维习惯，让你在面对生活中的各种不确定和挑战时，多一份底气。或者说它像一个随时可以拿起的工具，帮你时刻审视自己遇到的事情，做出更稳妥，也让你受益的决定。</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 6572, "thumbnails": ["https://pica.zhimg.com/50/v2-2b58d644ad481fa3aa862161e7e80600_720w.jpg?source=b6762063"], "favorite_count": 238, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1915736469101871409}", "attached_info": "CrQHCJ+W9fzOl6rRPBAEGgk3MzE0NzYzNDYg/tSewgYoZTALQFNKQQosVFNfU09VUkNFX1RXT1RPV0VSX1NIT1JUSU5URVJFU1RfUkVDQUxMX1RFWFQSATAYACAAOgp7InJhdyI6IiJ9Wgg0MjM4NzQyOGIgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyEzE5MTU3MzY0NjkxMDE4NzE0MDmKAQkzNjA0OTA3NjOqAQlyZWNvbW1lbmTCASBkZmQxOGYzMTJkNTIxMWJjMjgwYmRkYzUxODhhOGZmMvIBCggMEgZOb3JtYWzyASgIChIkMzBlZGJhMGYtYmRhZS00MTI3LWIwYjMtMDA2MjBlY2JjOWI08gEGCAsSAjE0ggIAiALO97nN+jKSAiBkZmQxOGYzMTJkNTIxMWJjMjgwYmRkYzUxODhhOGZmMpoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhZBY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIbSW50ZXJhY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIYUGVyaW9kSW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxl2gIsVFNfU09VUkNFX1RXT1RPV0VSX1NIT1JUSU5URVJFU1RfUkVDQUxMX1RFWFToAgT6AgtOT1JNQUxfRkxPV4oDIGMyNzA1NzU4MGU5ZjQ0YWI5MzFiYzYxNmM2NWE5YTA1mgMNCgJ2MhAAGgVvdGhlcqgDrDPYAwDqAxpmZWVkX2F0dG1fdHdvdG93ZXJfdjJfdGV4dPoDrAESDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFOi0IBBC4CBjHBSIjdjItYzg3ZmNiNjc3YzI3NzJjYWZhNjNhYTIwNTcwYjk1NzY6LQgDENAFGJoDIiN2Mi01NDg0YTE3MDU4YmFkMjI2MDNjMjVlNTc0Njk5YmQ5NzotCAIQ1wUYyQIiI3YyLWY2NTVhMjgzZGEwNTBkZmViYzMxMGVhZjY4NmQyZjg0gAQAiAQAkgQGTm9ybWFsmgQBNKAEAKgEALAEALoEBm1hbnVhbMIEAzE3MMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAoAbguj+BBQAAAAAAAAAAiQUFqAKugr3SP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUOkAYAoAZXqAYAkgIuCgk3MzE0NzYzNDYSEzE5MTU3MzY0NjkxMDE4NzE0MDkYBCIKSU1BR0VfVEVYVA==", "action_card": false}], "paging": {"is_end": false, "is_start": false, "next": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=down&ad_interval=-10&after_id=83&desktop=true&end_offset=87&page_number=15&session_token=5901993897b2b671534b41dfe1703947", "previous": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=pull&ad_interval=-10&before_id=83&desktop=true&end_offset=87&page_number=15&session_token=5901993897b2b671534b41dfe1703947", "totals": 0}, "fresh_text": "推荐已更新"}