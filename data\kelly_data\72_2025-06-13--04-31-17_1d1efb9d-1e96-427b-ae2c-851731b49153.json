{"data": [{"id": "1516512178335", "type": "feed", "target": {"id": "281642849", "type": "answer", "url": "https://api.zhihu.com/answers/281642849", "voteup_count": 2978, "thanks_count": 704, "question": {"id": "59683332", "title": "应届硕士毕业生如何拿到知名互联网公司算法岗（机器学习、数据挖掘、深度学习） offer？", "url": "https://api.zhihu.com/questions/59683332", "type": "question", "question_type": "normal", "created": 1494464739, "answer_count": 63, "comment_count": 21, "follower_count": 5489, "detail": "<p>最近人工智能岗位很火，而且目测这一场革命会持续很久。近两年，很多想学习机器学习，想找相关算法岗的同学越来越多，也就面临着更多的竞争压力。不管是名校还是双非（非 985/211）的同学，正确的学习方向都是至关重要的。计算机相关专业，编程用 Python，研二出去实习，有半年图像类深度学习相关实习经验。想知道都需要具备何种知识，何种方式拿到算法岗位相关 offer？诚谢。</p>", "excerpt": "最近人工智能岗位很火，而且目测这一场革命会持续很久。近两年，很多想学习机器学习…", "bound_topic_ids": [350, 3084, 3279, 89794, 161089], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "9e9d2b6154a16b97e752c0ea96199b4f", "name": "蔷薇", "headline": "六年内，咨询500+计算机学生关于简历修改，项目辅导等", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/yi-yi-34-89-41", "url_token": "yi-yi-34-89-41", "avatar_url": "https://picx.zhimg.com/v2-59706ab989cc851ecfa611dd1e4e872a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1514478395, "created_time": 1514020495, "author": {"id": "987e0850b0d6ccc148db84ba60c48341", "name": "熊风", "headline": "喜欢热血和燃的故事", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xiongfengcherish", "url_token": "xiongfengcherish", "avatar_url": "https://picx.zhimg.com/v2-d634c676b1135dcef464e3a44f2dae43_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 183, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"mEQHmhne\">最近投了一堆机器学习/深度学习/计算机视觉方向的公司，分享一下自己的经验，希望对大家有帮助。</p><p data-pid=\"G37PUHop\">个人背景： 华科本科 + 港科大硕士（MPhil）</p><p data-pid=\"Wx1o3GBo\">拿到的offer有腾讯优图，阿里AI lab，今日头条，滴滴研究院，商汤科技，旷视（face++），大疆，快手。绝大部分是ssp（super special），给到了普通硕士能给到的最高档。</p><p data-pid=\"_jhzCxvh\">最新修改：经人提醒，我意识到直接po公司的原题是非常不好的行为。所以我修改一下答案，只能指明大概的考察范围。在此对我的所有面试官表示抱歉，未经同意贴出了所有的面试题。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"rfogHslD\">写在前面的话</p><p data-pid=\"sr0MWFXM\">这个回答的适用对象主要还是本科和硕士。PhD找工作的套路跟硕士还是很不一样的，所以这个回答的经验对于手握几篇一作顶会的PhD大神并没啥参考意义。</p><p data-pid=\"GtV0tD0k\">我也和我们实验室几个找工作的PhD学长学姐聊过，他们的面试主要是讲自己的research，有的甚至就是去公司给个talk，跟本科硕士的校招流程完全不同。现在也是AI方向PhD的黄金时代，没毕业就被各大公司主动联系，待遇也比我这种硕士高很多很多。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"e-9-UBvz\">一.  整体建议</p><p data-pid=\"-2KihzYa\">一定要找内推。</p><p data-pid=\"PC2Idzz2\">内推一般有两种，第一种力度比较弱，在公司的内推系统上填一下你的名字，加快一下招聘流程；第二种力度比较强，直接把简历送到部门负责人手上。个人比较建议第二种，会省事很多。</p><p data-pid=\"ARgsQZ4G\">原因如下：</p><p data-pid=\"8N1nRPSn\">（1）现在做机器学习的人实在太多了，在不找内推的情况下，流程会特别特别慢。即使你的简历比较优秀，也可能淹没在茫茫大海中，不一定能被懂行的人看到。</p><p data-pid=\"bAdYgnvi\">（2）现在很多公司的笔试其实挺有难度的，就算是大神也有翻车的可能性。</p><p data-pid=\"da-fIaic\">（3）对于大公司而言，即使通过了简历筛选、笔试那一关，你也很难保证你的简历被合适的部门挑中。很可能过关斩将后，发现给你安排的面试官并不是太对口。尤其是深度学习这样比较新的领域，一般部门的面试官多半也是近期自学的，对这个也是一知半解。所以如果是想去BAT这些大公司里面专门做AI的部门，按照正常校招流程走是不合适的，一定要找到那些部门的员工内推。</p><p data-pid=\"KZNYF96d\">在我看来，如果是跪在简历筛选、笔试这些上面，连面试官都没见到，就实在太可惜了。为了避免这一点，请认真找内推。最好能联系到你想去的公司部门里的负责人，直接安排面试。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"CXO9tY-p\">二. 面试经验</p><p data-pid=\"2X_0mLtc\">面试遇到的题目，可以分为几个大类：</p><p data-pid=\"tgc1acXU\">（1）代码题（leetcode类型），主要考察数据结构和基础算法，以及代码基本功 </p><p data-pid=\"Wg7-0DWI\">虽然这部分跟机器学习，深度学习关系不大，但也是面试的重中之重。基本每家公司的面试都问了大量的算法题和代码题，即使是商汤、face++这样的深度学习公司，考察这部分的时间也占到了我很多轮面试的60%甚至70%以上。我去face++面试的时候，面试官是residual net，shuffle net的作者；但他们的面试中，写代码题依旧是主要的部分。</p><p data-pid=\"fOXS9TDZ\">大部分题目都不难，基本是leetcode medium的难度。但是要求在现场白板编程，思路要流畅，能做到一次性Bug-free.  并且，一般都是要给出时间复杂度和空间复杂度最优的做法。对于少数难度很大的题，也不要慌张。一般也不会一点思路也没有，尽力给面试官展现自己的思考过程。面试官也会引导你，给一点小提示，沿着提示把题目慢慢做出来也是可以通过面试的。</p><p data-pid=\"r5HkrX5k\">以下是我所遇到的一些需要当场写出完整代码的题目：</p><p data-pid=\"tEahTFds\">/*******************************************************************</p><p data-pid=\"VEcsSHwc\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"bhOgrbLU\">*******************************************************************/</p><p data-pid=\"sjYWlOYh\">不过这部分有些是LeetCode原题，在这里我简单地举几个例子，附上LeetCode题目链接：</p><p data-pid=\"Zl3nJK7R\"><a href=\"https://link.zhihu.com/?target=https%3A//leetcode.com/problems/maximum-product-subarray/description/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Maximum Product Subarray</a></p><p data-pid=\"yI_BMFKK\"><a href=\"https://link.zhihu.com/?target=https%3A//leetcode.com/problems/maximal-square/description/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Maximal Square</a></p><p data-pid=\"Hovko9cr\"><a href=\"https://link.zhihu.com/?target=https%3A//leetcode.com/problems/subsets/description/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Subsets</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"MMqhvFs1\">（2）数学题或者&#34;智力&#34;题。</p><p data-pid=\"Lmn4B1WO\">不会涉及特别高深的数学知识，一般就是工科数学（微积分，概率论，线性代数）和一些组合数学的问题。</p><p data-pid=\"Jz6xRYR6\">下面是我在面试中被问到过的问题：</p><p data-pid=\"f7qpyhxS\">/*******************************************************************</p><p data-pid=\"b6I-8uEB\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"W38oWzSb\">*******************************************************************/</p><p data-pid=\"T5XBxwP0\">这部分有些题也在知乎上被讨论过，这里附上相应的知乎链接</p><p data-pid=\"pOJIOjss\"><a href=\"https://www.zhihu.com/question/38331955\" class=\"internal\">如果一个女生说，她集齐了十二个星座的前男友，我们应该如何估计她前男友的数量？</a></p><p data-pid=\"WXmVyaTc\"><a href=\"https://www.zhihu.com/question/21605094\" class=\"internal\">如何理解矩阵的「秩」？</a></p><p data-pid=\"8usl8Vih\"><a href=\"https://www.zhihu.com/question/28630628\" class=\"internal\">矩阵低秩的意义?</a></p><p data-pid=\"muznzPBu\"><a href=\"https://www.zhihu.com/question/21874816\" class=\"internal\">如何理解矩阵特征值？</a></p><p data-pid=\"Z97Jl39Q\"><a href=\"https://www.zhihu.com/question/22237507\" class=\"internal\">奇异值的物理意义是什么？</a></p><p data-pid=\"LyyWCjfh\"><a href=\"https://zhuanlan.zhihu.com/p/24913912\" class=\"internal\">为什么梯度反方向是函数值下降最快的方向？</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"_5NV3OrQ\">（3）机器学习基础</p><p data-pid=\"ktWdpMSx\">这部分建议参考周志华老师的《机器学习》。</p><p data-pid=\"28vMHEuL\">下面是我在面试中被问到过的问题：</p><p data-pid=\"BP46dMNF\">/*******************************************************************</p><p data-pid=\"hxa0w_kI\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"d0tsqDtz\">*******************************************************************/</p><p data-pid=\"SsI4ZZOU\">列一下考察的知识点，并附上相关的优质知乎讨论。</p><p data-pid=\"bfaYF95P\">逻辑回归，SVM，决策树</p><p data-pid=\"EdcSr-5I\"><a href=\"https://www.zhihu.com/question/24904422\" class=\"internal\">逻辑回归和SVM的区别是什么？各适用于解决什么问题？</a></p><p data-pid=\"-cGd5efE\"><a href=\"https://www.zhihu.com/question/26768865\" class=\"internal\">Linear SVM 和 LR 有什么异同？</a></p><p data-pid=\"x9Zvl2pU\"><a href=\"https://www.zhihu.com/question/22290096\" class=\"internal\">SVM（支持向量机）属于神经网络范畴吗？</a></p><p data-pid=\"LDBx-7fS\"><a href=\"https://www.zhihu.com/question/34075616\" class=\"internal\">如何理解决策树的损失函数?</a></p><p data-pid=\"XiExbHWm\"><a href=\"https://www.zhihu.com/question/26726794\" class=\"internal\">各种机器学习的应用场景分别是什么？例如，k近邻,贝叶斯，决策树，svm，逻辑斯蒂回归和最大熵模型。</a></p><p data-pid=\"rovCvda4\">主成分分析，奇异值分解</p><p data-pid=\"RmV-aTqM\"><a href=\"https://www.zhihu.com/question/34143886\" class=\"internal\">SVD 降维体现在什么地方？</a></p><p data-pid=\"l-c36ena\"><a href=\"https://www.zhihu.com/question/47121788\" class=\"internal\">为什么PCA不被推荐用来避免过拟合？</a></p><p data-pid=\"dHOolpRn\">随机森林，GBDT, 集成学习</p><p data-pid=\"NhhxEX8g\"><a href=\"https://www.zhihu.com/question/26760839\" class=\"internal\">为什么说bagging是减少variance，而boosting是减少bias?</a></p><p data-pid=\"erhOMtMf\"><a href=\"https://www.zhihu.com/question/46784781\" class=\"internal\">基于树的adaboost和Gradient Tree Boosting区别？</a></p><p data-pid=\"E3d5GVVD\"><a href=\"https://www.zhihu.com/question/41354392\" class=\"internal\">机器学习算法中GBDT和XGBOOST的区别有哪些？</a></p><p data-pid=\"J2uF15Ve\"><a href=\"https://www.zhihu.com/question/51818176\" class=\"internal\">为什么在实际的 kaggle 比赛中 gbdt 和 random forest 效果非常好？</a></p><p data-pid=\"mO8m7fZ7\">过拟合</p><p data-pid=\"bNCGJkW4\"><a href=\"https://www.zhihu.com/question/59201590\" class=\"internal\">机器学习中用来防止过拟合的方法有哪些？</a></p><p data-pid=\"_o8gcRVN\"><a href=\"https://www.zhihu.com/question/20700829\" class=\"internal\">机器学习中使用「正则化来防止过拟合」到底是一个什么原理？为什么正则化项就可以防止过拟合？</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"mwP9JSMA\">（4）深度学习基础</p><p data-pid=\"EwfVQ6YD\">这部分的准备，我推荐花书（Bengio的Deep learning）和 <a class=\"member_mention\" href=\"https://www.zhihu.com/people/b716bc76c2990cd06dae2f9c1f984e6d\" data-hash=\"b716bc76c2990cd06dae2f9c1f984e6d\" data-hovercard=\"p$b$b716bc76c2990cd06dae2f9c1f984e6d\">@魏秀参</a> 学长的<a href=\"https://link.zhihu.com/?target=http%3A//210.28.132.67/weixs/book/CNN_book.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《解析卷积神经网络-深度学习实践手册》</a></p><p data-pid=\"nxe-Rzsg\">/*******************************************************************</p><p data-pid=\"JbhLG70A\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"_RUUq5y-\">*******************************************************************/</p><p data-pid=\"IO3H5H7c\">列一下大概的考察点和相关的知乎讨论。</p><p data-pid=\"2hbAZu2v\">卷积神经网络，循环神经网络，LSTM与GRU，梯度消失与梯度爆炸，激活函数，防止过拟合的方法，dropout，batch normalization，各类经典的网络结构，各类优化方法</p><p data-pid=\"H5c52f-d\"><a href=\"https://www.zhihu.com/question/39022858\" class=\"internal\">卷积神经网络工作原理直观的解释？</a></p><p data-pid=\"06ZybLAN\"><a href=\"https://zhuanlan.zhihu.com/p/31575074\" class=\"internal\">卷积神经网络的复杂度分析</a></p><p data-pid=\"w7qpqi0J\"><a href=\"https://www.zhihu.com/question/34681168\" class=\"internal\">CNN(卷积神经网络)、RNN(循环神经网络)、DNN(深度神经网络)的内部网络结构有什么区别？</a></p><p data-pid=\"_dwNugQS\"><a href=\"https://www.zhihu.com/question/49812013\" class=\"internal\">bp算法中为什么会产生梯度消失？</a></p><p data-pid=\"jgDTeRCd\"><a href=\"https://www.zhihu.com/question/38677354\" class=\"internal\">梯度下降法是万能的模型训练算法吗？</a></p><p data-pid=\"3ulgj7Pt\"><a href=\"https://www.zhihu.com/question/34878706\" class=\"internal\">LSTM如何来避免梯度弥散和梯度爆炸？</a></p><p data-pid=\"BUokaHa7\"><a href=\"https://www.zhihu.com/question/42115548\" class=\"internal\">sgd有多种改进的形式(rmsprop,adadelta等),为什么大多数论文中仍然用sgd?</a></p><p data-pid=\"DO4Z0Vzs\"><a href=\"https://www.zhihu.com/question/41631631\" class=\"internal\">你有哪些deep learning（rnn、cnn）调参的经验？</a></p><p data-pid=\"7WVVJ0lA\"><a href=\"https://zhuanlan.zhihu.com/p/32230623\" class=\"internal\">Adam那么棒，为什么还对SGD念念不忘 (1)</a></p><p data-pid=\"l5HzKJG0\"><a href=\"https://zhuanlan.zhihu.com/p/32262540\" class=\"internal\">Adam那么棒，为什么还对SGD念念不忘 (2)</a></p><p data-pid=\"ojP-uBJm\"><a href=\"https://www.zhihu.com/question/41037974\" class=\"internal\">全连接层的作用是什么？</a></p><p data-pid=\"xsIXV4u6\"><a href=\"https://www.zhihu.com/question/38102762\" class=\"internal\">深度学习中 Batch Normalization为什么效果好？</a></p><p data-pid=\"7NNhyiDC\"><a href=\"https://www.zhihu.com/question/43370067\" class=\"internal\">为什么现在的CNN模型都是在GoogleNet、VGGNet或者AlexNet上调整的？</a></p><p data-pid=\"FQ4aoovv\"><a href=\"https://www.zhihu.com/question/28720729\" class=\"internal\">Krizhevsky等人是怎么想到在CNN里用Dropout和ReLu的?</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"DJ6Uomm-\">（5）科研上的开放性问题</p><p data-pid=\"ZMAX_eU8\">这部分的问题没有固定答案，也没法很好地针对性准备。功在平时，多读paper多思考，注意培养自己的insight和intuition</p><p data-pid=\"2J2OM4vk\">下面是我在面试中被问到过的问题：</p><p data-pid=\"NwTonqrR\">/*******************************************************************</p><p data-pid=\"M5qK27uT\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"iqd23RgW\">*******************************************************************/</p><p data-pid=\"m3wBWKHM\">这部分在知乎上也有很多讨论，不具体列了。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"24eB_FrC\">（6） 编程语言、操作系统等方面的一些问题。</p><p data-pid=\"29WbcVfU\">C++， Python， 操作系统，Linux命令等等。这部分问得比较少，但还是有的，不具体列了</p><p data-pid=\"bAQLDvSa\">（7）针对简历里项目/论文 / 实习的一些问题。</p><p data-pid=\"bUV3Bpnd\">这部分因人而异，我个人的对大家也没参考价值，也不列了。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TZR2ySy0\">三. 平时应该怎么准备</p><p data-pid=\"X_Frm8yH\">在大多数情况下，你能拿到什么样的offer，其实已经被你的简历决定了。如果平时没有积累相关的经历和成果，很难只靠面试表现就拿到非常好的offer。所以建议大家平时积累算法岗所看重的一些干货。</p><p data-pid=\"yD_44tRP\">下面几点算是找AI相关工作的加分项：</p><p data-pid=\"kmcJ4NP_\">（1）一作的顶级会议论文</p><p data-pid=\"pnA-0Pme\">（2）AI领域知名公司的实习经历（长期实习更好）</p><p data-pid=\"ZT3LWv2Q\">（3）相关方向有含金量的项目经历</p><p data-pid=\"4xF5F7rt\">（4）计算机视觉竞赛，数据挖掘竞赛的获奖或者优秀名次。现在这类竞赛太多了，就不具体列了。</p><p data-pid=\"LCLFYG1O\">（5）程序设计竞赛的获奖（例如OI/ACM/topcoder之类的）</p><p data-pid=\"PVMToew9\">当然，名校、高GPA这些是针对所有领域都有用的加分项，同样也是适用于这个领域的。</p><p data-pid=\"vMMZopSI\">所以我的建议就是，如果自己所在的实验室很厉害，资源丰富，就专心做科研，发paper； 如果所在的实验室一般，没法产出相关的优秀成果，可以考虑自己做比赛和找实习。有一份知名公司的实习经历之后，找工作难度会下降很多。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"nPqLGQ_2\">最后，祝有志于AI这个领域的人都能拿到满意的offer.</p>", "excerpt": "最近投了一堆机器学习/深度学习/计算机视觉方向的公司，分享一下自己的经验，希望对大家有帮助。 个人背景： 华科本科 + 港科大硕士（MPhil） 拿到的offer有腾讯优图，阿里AI lab，今日头条，滴滴研究院，商汤科技，旷视（face++），大疆，快手。绝大部分是ssp（super special），给到了普通硕士能给到的最高档。 最新修改：经人提醒，我意识到直接po公司的原题是非常不好的行为。所以我修改一下答案，只能指明大概的考察范围。…", "excerpt_new": "最近投了一堆机器学习/深度学习/计算机视觉方向的公司，分享一下自己的经验，希望对大家有帮助。 个人背景： 华科本科 + 港科大硕士（MPhil） 拿到的offer有腾讯优图，阿里AI lab，今日头条，滴滴研究院，商汤科技，旷视（face++），大疆，快手。绝大部分是ssp（super special），给到了普通硕士能给到的最高档。 最新修改：经人提醒，我意识到直接po公司的原题是非常不好的行为。所以我修改一下答案，只能指明大概的考察范围。…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_COLLECT_ANSWER", "created_time": 1516512178, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "收藏了回答", "is_sticky": false}, {"id": "1516512169632", "type": "feed", "target": {"id": "281642849", "type": "answer", "url": "https://api.zhihu.com/answers/281642849", "voteup_count": 2978, "thanks_count": 704, "question": {"id": "59683332", "title": "应届硕士毕业生如何拿到知名互联网公司算法岗（机器学习、数据挖掘、深度学习） offer？", "url": "https://api.zhihu.com/questions/59683332", "type": "question", "question_type": "normal", "created": 1494464739, "answer_count": 63, "comment_count": 21, "follower_count": 5489, "detail": "<p>最近人工智能岗位很火，而且目测这一场革命会持续很久。近两年，很多想学习机器学习，想找相关算法岗的同学越来越多，也就面临着更多的竞争压力。不管是名校还是双非（非 985/211）的同学，正确的学习方向都是至关重要的。计算机相关专业，编程用 Python，研二出去实习，有半年图像类深度学习相关实习经验。想知道都需要具备何种知识，何种方式拿到算法岗位相关 offer？诚谢。</p>", "excerpt": "最近人工智能岗位很火，而且目测这一场革命会持续很久。近两年，很多想学习机器学习…", "bound_topic_ids": [350, 3084, 3279, 89794, 161089], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "9e9d2b6154a16b97e752c0ea96199b4f", "name": "蔷薇", "headline": "六年内，咨询500+计算机学生关于简历修改，项目辅导等", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/yi-yi-34-89-41", "url_token": "yi-yi-34-89-41", "avatar_url": "https://picx.zhimg.com/v2-59706ab989cc851ecfa611dd1e4e872a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1514478395, "created_time": 1514020495, "author": {"id": "987e0850b0d6ccc148db84ba60c48341", "name": "熊风", "headline": "喜欢热血和燃的故事", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xiongfengcherish", "url_token": "xiongfengcherish", "avatar_url": "https://picx.zhimg.com/v2-d634c676b1135dcef464e3a44f2dae43_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 183, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"mEQHmhne\">最近投了一堆机器学习/深度学习/计算机视觉方向的公司，分享一下自己的经验，希望对大家有帮助。</p><p data-pid=\"G37PUHop\">个人背景： 华科本科 + 港科大硕士（MPhil）</p><p data-pid=\"Wx1o3GBo\">拿到的offer有腾讯优图，阿里AI lab，今日头条，滴滴研究院，商汤科技，旷视（face++），大疆，快手。绝大部分是ssp（super special），给到了普通硕士能给到的最高档。</p><p data-pid=\"_jhzCxvh\">最新修改：经人提醒，我意识到直接po公司的原题是非常不好的行为。所以我修改一下答案，只能指明大概的考察范围。在此对我的所有面试官表示抱歉，未经同意贴出了所有的面试题。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"rfogHslD\">写在前面的话</p><p data-pid=\"sr0MWFXM\">这个回答的适用对象主要还是本科和硕士。PhD找工作的套路跟硕士还是很不一样的，所以这个回答的经验对于手握几篇一作顶会的PhD大神并没啥参考意义。</p><p data-pid=\"GtV0tD0k\">我也和我们实验室几个找工作的PhD学长学姐聊过，他们的面试主要是讲自己的research，有的甚至就是去公司给个talk，跟本科硕士的校招流程完全不同。现在也是AI方向PhD的黄金时代，没毕业就被各大公司主动联系，待遇也比我这种硕士高很多很多。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"e-9-UBvz\">一.  整体建议</p><p data-pid=\"-2KihzYa\">一定要找内推。</p><p data-pid=\"PC2Idzz2\">内推一般有两种，第一种力度比较弱，在公司的内推系统上填一下你的名字，加快一下招聘流程；第二种力度比较强，直接把简历送到部门负责人手上。个人比较建议第二种，会省事很多。</p><p data-pid=\"ARgsQZ4G\">原因如下：</p><p data-pid=\"8N1nRPSn\">（1）现在做机器学习的人实在太多了，在不找内推的情况下，流程会特别特别慢。即使你的简历比较优秀，也可能淹没在茫茫大海中，不一定能被懂行的人看到。</p><p data-pid=\"bAdYgnvi\">（2）现在很多公司的笔试其实挺有难度的，就算是大神也有翻车的可能性。</p><p data-pid=\"da-fIaic\">（3）对于大公司而言，即使通过了简历筛选、笔试那一关，你也很难保证你的简历被合适的部门挑中。很可能过关斩将后，发现给你安排的面试官并不是太对口。尤其是深度学习这样比较新的领域，一般部门的面试官多半也是近期自学的，对这个也是一知半解。所以如果是想去BAT这些大公司里面专门做AI的部门，按照正常校招流程走是不合适的，一定要找到那些部门的员工内推。</p><p data-pid=\"KZNYF96d\">在我看来，如果是跪在简历筛选、笔试这些上面，连面试官都没见到，就实在太可惜了。为了避免这一点，请认真找内推。最好能联系到你想去的公司部门里的负责人，直接安排面试。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"CXO9tY-p\">二. 面试经验</p><p data-pid=\"2X_0mLtc\">面试遇到的题目，可以分为几个大类：</p><p data-pid=\"tgc1acXU\">（1）代码题（leetcode类型），主要考察数据结构和基础算法，以及代码基本功 </p><p data-pid=\"Wg7-0DWI\">虽然这部分跟机器学习，深度学习关系不大，但也是面试的重中之重。基本每家公司的面试都问了大量的算法题和代码题，即使是商汤、face++这样的深度学习公司，考察这部分的时间也占到了我很多轮面试的60%甚至70%以上。我去face++面试的时候，面试官是residual net，shuffle net的作者；但他们的面试中，写代码题依旧是主要的部分。</p><p data-pid=\"fOXS9TDZ\">大部分题目都不难，基本是leetcode medium的难度。但是要求在现场白板编程，思路要流畅，能做到一次性Bug-free.  并且，一般都是要给出时间复杂度和空间复杂度最优的做法。对于少数难度很大的题，也不要慌张。一般也不会一点思路也没有，尽力给面试官展现自己的思考过程。面试官也会引导你，给一点小提示，沿着提示把题目慢慢做出来也是可以通过面试的。</p><p data-pid=\"r5HkrX5k\">以下是我所遇到的一些需要当场写出完整代码的题目：</p><p data-pid=\"tEahTFds\">/*******************************************************************</p><p data-pid=\"VEcsSHwc\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"bhOgrbLU\">*******************************************************************/</p><p data-pid=\"sjYWlOYh\">不过这部分有些是LeetCode原题，在这里我简单地举几个例子，附上LeetCode题目链接：</p><p data-pid=\"Zl3nJK7R\"><a href=\"https://link.zhihu.com/?target=https%3A//leetcode.com/problems/maximum-product-subarray/description/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Maximum Product Subarray</a></p><p data-pid=\"yI_BMFKK\"><a href=\"https://link.zhihu.com/?target=https%3A//leetcode.com/problems/maximal-square/description/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Maximal Square</a></p><p data-pid=\"Hovko9cr\"><a href=\"https://link.zhihu.com/?target=https%3A//leetcode.com/problems/subsets/description/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Subsets</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"MMqhvFs1\">（2）数学题或者&#34;智力&#34;题。</p><p data-pid=\"Lmn4B1WO\">不会涉及特别高深的数学知识，一般就是工科数学（微积分，概率论，线性代数）和一些组合数学的问题。</p><p data-pid=\"Jz6xRYR6\">下面是我在面试中被问到过的问题：</p><p data-pid=\"f7qpyhxS\">/*******************************************************************</p><p data-pid=\"b6I-8uEB\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"W38oWzSb\">*******************************************************************/</p><p data-pid=\"T5XBxwP0\">这部分有些题也在知乎上被讨论过，这里附上相应的知乎链接</p><p data-pid=\"pOJIOjss\"><a href=\"https://www.zhihu.com/question/38331955\" class=\"internal\">如果一个女生说，她集齐了十二个星座的前男友，我们应该如何估计她前男友的数量？</a></p><p data-pid=\"WXmVyaTc\"><a href=\"https://www.zhihu.com/question/21605094\" class=\"internal\">如何理解矩阵的「秩」？</a></p><p data-pid=\"8usl8Vih\"><a href=\"https://www.zhihu.com/question/28630628\" class=\"internal\">矩阵低秩的意义?</a></p><p data-pid=\"muznzPBu\"><a href=\"https://www.zhihu.com/question/21874816\" class=\"internal\">如何理解矩阵特征值？</a></p><p data-pid=\"Z97Jl39Q\"><a href=\"https://www.zhihu.com/question/22237507\" class=\"internal\">奇异值的物理意义是什么？</a></p><p data-pid=\"LyyWCjfh\"><a href=\"https://zhuanlan.zhihu.com/p/24913912\" class=\"internal\">为什么梯度反方向是函数值下降最快的方向？</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"_5NV3OrQ\">（3）机器学习基础</p><p data-pid=\"ktWdpMSx\">这部分建议参考周志华老师的《机器学习》。</p><p data-pid=\"28vMHEuL\">下面是我在面试中被问到过的问题：</p><p data-pid=\"BP46dMNF\">/*******************************************************************</p><p data-pid=\"hxa0w_kI\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"d0tsqDtz\">*******************************************************************/</p><p data-pid=\"SsI4ZZOU\">列一下考察的知识点，并附上相关的优质知乎讨论。</p><p data-pid=\"bfaYF95P\">逻辑回归，SVM，决策树</p><p data-pid=\"EdcSr-5I\"><a href=\"https://www.zhihu.com/question/24904422\" class=\"internal\">逻辑回归和SVM的区别是什么？各适用于解决什么问题？</a></p><p data-pid=\"-cGd5efE\"><a href=\"https://www.zhihu.com/question/26768865\" class=\"internal\">Linear SVM 和 LR 有什么异同？</a></p><p data-pid=\"x9Zvl2pU\"><a href=\"https://www.zhihu.com/question/22290096\" class=\"internal\">SVM（支持向量机）属于神经网络范畴吗？</a></p><p data-pid=\"LDBx-7fS\"><a href=\"https://www.zhihu.com/question/34075616\" class=\"internal\">如何理解决策树的损失函数?</a></p><p data-pid=\"XiExbHWm\"><a href=\"https://www.zhihu.com/question/26726794\" class=\"internal\">各种机器学习的应用场景分别是什么？例如，k近邻,贝叶斯，决策树，svm，逻辑斯蒂回归和最大熵模型。</a></p><p data-pid=\"rovCvda4\">主成分分析，奇异值分解</p><p data-pid=\"RmV-aTqM\"><a href=\"https://www.zhihu.com/question/34143886\" class=\"internal\">SVD 降维体现在什么地方？</a></p><p data-pid=\"l-c36ena\"><a href=\"https://www.zhihu.com/question/47121788\" class=\"internal\">为什么PCA不被推荐用来避免过拟合？</a></p><p data-pid=\"dHOolpRn\">随机森林，GBDT, 集成学习</p><p data-pid=\"NhhxEX8g\"><a href=\"https://www.zhihu.com/question/26760839\" class=\"internal\">为什么说bagging是减少variance，而boosting是减少bias?</a></p><p data-pid=\"erhOMtMf\"><a href=\"https://www.zhihu.com/question/46784781\" class=\"internal\">基于树的adaboost和Gradient Tree Boosting区别？</a></p><p data-pid=\"E3d5GVVD\"><a href=\"https://www.zhihu.com/question/41354392\" class=\"internal\">机器学习算法中GBDT和XGBOOST的区别有哪些？</a></p><p data-pid=\"J2uF15Ve\"><a href=\"https://www.zhihu.com/question/51818176\" class=\"internal\">为什么在实际的 kaggle 比赛中 gbdt 和 random forest 效果非常好？</a></p><p data-pid=\"mO8m7fZ7\">过拟合</p><p data-pid=\"bNCGJkW4\"><a href=\"https://www.zhihu.com/question/59201590\" class=\"internal\">机器学习中用来防止过拟合的方法有哪些？</a></p><p data-pid=\"_o8gcRVN\"><a href=\"https://www.zhihu.com/question/20700829\" class=\"internal\">机器学习中使用「正则化来防止过拟合」到底是一个什么原理？为什么正则化项就可以防止过拟合？</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"mwP9JSMA\">（4）深度学习基础</p><p data-pid=\"EwfVQ6YD\">这部分的准备，我推荐花书（Bengio的Deep learning）和 <a class=\"member_mention\" href=\"https://www.zhihu.com/people/b716bc76c2990cd06dae2f9c1f984e6d\" data-hash=\"b716bc76c2990cd06dae2f9c1f984e6d\" data-hovercard=\"p$b$b716bc76c2990cd06dae2f9c1f984e6d\">@魏秀参</a> 学长的<a href=\"https://link.zhihu.com/?target=http%3A//210.28.132.67/weixs/book/CNN_book.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《解析卷积神经网络-深度学习实践手册》</a></p><p data-pid=\"nxe-Rzsg\">/*******************************************************************</p><p data-pid=\"JbhLG70A\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"_RUUq5y-\">*******************************************************************/</p><p data-pid=\"IO3H5H7c\">列一下大概的考察点和相关的知乎讨论。</p><p data-pid=\"2hbAZu2v\">卷积神经网络，循环神经网络，LSTM与GRU，梯度消失与梯度爆炸，激活函数，防止过拟合的方法，dropout，batch normalization，各类经典的网络结构，各类优化方法</p><p data-pid=\"H5c52f-d\"><a href=\"https://www.zhihu.com/question/39022858\" class=\"internal\">卷积神经网络工作原理直观的解释？</a></p><p data-pid=\"06ZybLAN\"><a href=\"https://zhuanlan.zhihu.com/p/31575074\" class=\"internal\">卷积神经网络的复杂度分析</a></p><p data-pid=\"w7qpqi0J\"><a href=\"https://www.zhihu.com/question/34681168\" class=\"internal\">CNN(卷积神经网络)、RNN(循环神经网络)、DNN(深度神经网络)的内部网络结构有什么区别？</a></p><p data-pid=\"_dwNugQS\"><a href=\"https://www.zhihu.com/question/49812013\" class=\"internal\">bp算法中为什么会产生梯度消失？</a></p><p data-pid=\"jgDTeRCd\"><a href=\"https://www.zhihu.com/question/38677354\" class=\"internal\">梯度下降法是万能的模型训练算法吗？</a></p><p data-pid=\"3ulgj7Pt\"><a href=\"https://www.zhihu.com/question/34878706\" class=\"internal\">LSTM如何来避免梯度弥散和梯度爆炸？</a></p><p data-pid=\"BUokaHa7\"><a href=\"https://www.zhihu.com/question/42115548\" class=\"internal\">sgd有多种改进的形式(rmsprop,adadelta等),为什么大多数论文中仍然用sgd?</a></p><p data-pid=\"DO4Z0Vzs\"><a href=\"https://www.zhihu.com/question/41631631\" class=\"internal\">你有哪些deep learning（rnn、cnn）调参的经验？</a></p><p data-pid=\"7WVVJ0lA\"><a href=\"https://zhuanlan.zhihu.com/p/32230623\" class=\"internal\">Adam那么棒，为什么还对SGD念念不忘 (1)</a></p><p data-pid=\"l5HzKJG0\"><a href=\"https://zhuanlan.zhihu.com/p/32262540\" class=\"internal\">Adam那么棒，为什么还对SGD念念不忘 (2)</a></p><p data-pid=\"ojP-uBJm\"><a href=\"https://www.zhihu.com/question/41037974\" class=\"internal\">全连接层的作用是什么？</a></p><p data-pid=\"xsIXV4u6\"><a href=\"https://www.zhihu.com/question/38102762\" class=\"internal\">深度学习中 Batch Normalization为什么效果好？</a></p><p data-pid=\"7NNhyiDC\"><a href=\"https://www.zhihu.com/question/43370067\" class=\"internal\">为什么现在的CNN模型都是在GoogleNet、VGGNet或者AlexNet上调整的？</a></p><p data-pid=\"FQ4aoovv\"><a href=\"https://www.zhihu.com/question/28720729\" class=\"internal\">Krizhevsky等人是怎么想到在CNN里用Dropout和ReLu的?</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"DJ6Uomm-\">（5）科研上的开放性问题</p><p data-pid=\"ZMAX_eU8\">这部分的问题没有固定答案，也没法很好地针对性准备。功在平时，多读paper多思考，注意培养自己的insight和intuition</p><p data-pid=\"2J2OM4vk\">下面是我在面试中被问到过的问题：</p><p data-pid=\"NwTonqrR\">/*******************************************************************</p><p data-pid=\"M5qK27uT\">经人提醒，意识到直接贴具体的面试原题是非常不好的行为，该部分不在回答里放出来了。</p><p data-pid=\"iqd23RgW\">*******************************************************************/</p><p data-pid=\"m3wBWKHM\">这部分在知乎上也有很多讨论，不具体列了。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"24eB_FrC\">（6） 编程语言、操作系统等方面的一些问题。</p><p data-pid=\"29WbcVfU\">C++， Python， 操作系统，Linux命令等等。这部分问得比较少，但还是有的，不具体列了</p><p data-pid=\"bAQLDvSa\">（7）针对简历里项目/论文 / 实习的一些问题。</p><p data-pid=\"bUV3Bpnd\">这部分因人而异，我个人的对大家也没参考价值，也不列了。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TZR2ySy0\">三. 平时应该怎么准备</p><p data-pid=\"X_Frm8yH\">在大多数情况下，你能拿到什么样的offer，其实已经被你的简历决定了。如果平时没有积累相关的经历和成果，很难只靠面试表现就拿到非常好的offer。所以建议大家平时积累算法岗所看重的一些干货。</p><p data-pid=\"yD_44tRP\">下面几点算是找AI相关工作的加分项：</p><p data-pid=\"kmcJ4NP_\">（1）一作的顶级会议论文</p><p data-pid=\"pnA-0Pme\">（2）AI领域知名公司的实习经历（长期实习更好）</p><p data-pid=\"ZT3LWv2Q\">（3）相关方向有含金量的项目经历</p><p data-pid=\"4xF5F7rt\">（4）计算机视觉竞赛，数据挖掘竞赛的获奖或者优秀名次。现在这类竞赛太多了，就不具体列了。</p><p data-pid=\"LCLFYG1O\">（5）程序设计竞赛的获奖（例如OI/ACM/topcoder之类的）</p><p data-pid=\"PVMToew9\">当然，名校、高GPA这些是针对所有领域都有用的加分项，同样也是适用于这个领域的。</p><p data-pid=\"vMMZopSI\">所以我的建议就是，如果自己所在的实验室很厉害，资源丰富，就专心做科研，发paper； 如果所在的实验室一般，没法产出相关的优秀成果，可以考虑自己做比赛和找实习。有一份知名公司的实习经历之后，找工作难度会下降很多。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"nPqLGQ_2\">最后，祝有志于AI这个领域的人都能拿到满意的offer.</p>", "excerpt": "最近投了一堆机器学习/深度学习/计算机视觉方向的公司，分享一下自己的经验，希望对大家有帮助。 个人背景： 华科本科 + 港科大硕士（MPhil） 拿到的offer有腾讯优图，阿里AI lab，今日头条，滴滴研究院，商汤科技，旷视（face++），大疆，快手。绝大部分是ssp（super special），给到了普通硕士能给到的最高档。 最新修改：经人提醒，我意识到直接po公司的原题是非常不好的行为。所以我修改一下答案，只能指明大概的考察范围。…", "excerpt_new": "最近投了一堆机器学习/深度学习/计算机视觉方向的公司，分享一下自己的经验，希望对大家有帮助。 个人背景： 华科本科 + 港科大硕士（MPhil） 拿到的offer有腾讯优图，阿里AI lab，今日头条，滴滴研究院，商汤科技，旷视（face++），大疆，快手。绝大部分是ssp（super special），给到了普通硕士能给到的最高档。 最新修改：经人提醒，我意识到直接po公司的原题是非常不好的行为。所以我修改一下答案，只能指明大概的考察范围。…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1516512169, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1516422116107", "type": "feed", "target": {"id": "37717958", "type": "answer", "url": "https://api.zhihu.com/answers/37717958", "voteup_count": 438, "thanks_count": 138, "question": {"id": "24570155", "title": "如何评价 Georgia Tech 的 OMSCS (Online Master of Science in Computer Science Degree)?", "url": "https://api.zhihu.com/questions/24570155", "type": "question", "question_type": "normal", "created": 1405993923, "answer_count": 51, "comment_count": 4, "follower_count": 1994, "detail": "<a href=\"https://link.zhihu.com/?target=http%3A//www.omscs.gatech.edu/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">OMSCS - Georgia Institute of Technology</a><br/>据说是目前最好最完善的Online Master Degree in CS, 大约要耗时两年完成, 申请和申请普通Master Degree一样，申请文书，GPA，推荐信都不能少。课程设计也和正常的On Campus MS in CS 一样。大概两年需要6600刀。", "excerpt": "<a href=\"https://link.zhihu.com/?target=http%3A//www.omscs.gatech.edu/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">OMSCS - Georgia Institute of Technology</a> 据说是目前最好最完善的Online Master De…", "bound_topic_ids": [1354, 4228, 6756, 10033, 60324], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "updated_time": 1489723249, "created_time": 1421821443, "author": {"id": "d1ff3578c807ae770b86d9c028bc6cd3", "name": "宋英男", "headline": "苹果公司 人工智能 技术经理", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/yingnansong", "url_token": "<PERSON><PERSON><PERSON>song", "avatar_url": "https://picx.zhimg.com/33df43c3ec46335ebbb9b18bee0f76f3_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "identity", "description": "清华大学 工程管理硕士在读", "topic_names": [], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "清华大学 工程管理硕士在读", "detail_type": "identity", "icon": "", "night_icon": "", "sources": [], "title": "认证", "type": "identity", "url": "https://zhuanlan.zhihu.com/p/96956163"}], "title": "清华大学 工程管理硕士在读"}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 123, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"rio-PM05\">作为OMSCS项目的首批毕业生，怒答一记。</p><p data-pid=\"d2GBNYx3\">2015年12月亲赴 Georgia Tech 校园和所有 on-campus 的学生一起参加了毕业典礼，记录在此：<a href=\"https://link.zhihu.com/?target=http%3A//www.wesleysong.com/blog/2015/12/24/georgia-tech-commencement/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Georgia Tech 毕业典礼见闻</a></p><p data-pid=\"igX1Nruj\">很久之前写了一篇与该项目相关的经验总结， <a href=\"https://link.zhihu.com/?target=http%3A//www.wesleysong.com/blog/2014/04/22/gatech-omscs-application/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Georgia Tech Online Master of Science in Computer Science 项目申请经验分享</a> ，不过由于课业负担很重，没有过多更新。</p><p data-pid=\"ImHFEHNo\">OMSCS项目交流QQ群ID为 <b>53440680（入群请注明来自知乎）。</b>截止到2017年4月该群已有近500人，欢迎对该项目该兴趣的同学，或者已经在读的同学一起交流。</p><p data-pid=\"s3Ti9zce\">话说回来，应该如何评价该项目呢？</p><p data-pid=\"egT5Ix2_\"><b>1. 教学质量高，学校名气大，校友资源广</b></p><br/><p data-pid=\"Z0l3InVZ\">GATech在US属于工科非常有名的学校，在 2015年 US News Rankings 中排名世界第64位(见<a href=\"https://link.zhihu.com/?target=http%3A//www.usnews.com/education/best-global-universities/georgia-institute-of-technology-139755\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Georgia Institute of Technology</a>)，与国内的北大、清华处于同一梯队。GATech的MSCS项目位居全美第9(见<a href=\"https://link.zhihu.com/?target=http%3A//grad-schools.usnews.rankingsandreviews.com/best-graduate-schools/top-science-schools/computer-science-rankings\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Best Computer Science Programs</a>)，仅次于CMU、MIT、Berkely、Cornell、Princeton等如雷贯耳的牛校。</p><p data-pid=\"heirV-X_\">论校友资源，各大互联网公司均有很多GATech的毕业生，每年在GATech校招很多人，尤其是CS的。如果有志于在美国互联网行业打拼，那么你会发现GATech的校友很多。</p><p data-pid=\"IDiiOkjT\">在中国大陆也有很多GATech的杰出校友，比如携程创始人梁建章先生，百度副总裁曾良先生(见<a href=\"https://link.zhihu.com/?target=http%3A//baike.baidu.com/subview/1528323/12823842.htm\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">曾良（百度公司副总裁）</a>)，小米副总裁周光平先生(见<a href=\"https://link.zhihu.com/?target=http%3A//baike.baidu.com/subview/4107796/6483243.htm\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">周光平（小米科技联合创始人、副总裁）</a>)，球星马布里(见<a href=\"https://link.zhihu.com/?target=https%3A//zh.wikipedia.org/wiki/%25E6%2596%25AF%25E8%2592%2582%25E8%258A%25AC%25C2%25B7%25E9%25A9%25AC%25E5%25B8%2583%25E9%2587%258C\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">zh.wikipedia.org/wiki/%</span><span class=\"invisible\">E6%96%AF%E8%92%82%E8%8A%AC%C2%B7%E9%A9%AC%E5%B8%83%E9%87%8C</span><span class=\"ellipsis\"></span></a>)等。</p><p data-pid=\"4j761uBP\">此外，针对OMSCS项目而言，在读的学生大多数都是已经在职场多年的专业人士。第一批的毕业生里，就有来自Google、AT&amp;T等公司的资深员工。这些都是不可多得的人脉。</p><p data-pid=\"n5aInBQA\">不可否认的是，GATech在国内的知名度并不高，还闹出过学生家长认为是野鸡大学的笑话，可能是与工科学校大家比较喜欢闷头做事、不擅长宣传有关吧。</p><p data-pid=\"Rf7ZojIx\"><b>2. 项目性价比高</b></p><br/><p data-pid=\"_-ZjaBGP\">远程教育并非是这几年才有的，很多高校都有类似的项目。然而，几乎所有的远程教育项目在学费上与在校项目没有任何区别（有的还稍微贵一些）。比如，Harvard、Berkely等学校的远程MSCS项目学费是50000美元左右。</p><p data-pid=\"wUbkxt1m\">在这一点上，OMSCS项目具有无可比拟的优势：<b>完成整个项目只需要7000美元左右</b>。</p><p data-pid=\"mbkfBVpU\">就我个人而言，完成整个项目的花费为5849美元，如下图所示：</p><figure><noscript><img src=\"https://pic3.zhimg.com/84ad6f2e0faa973c86e2660ec6e75728_b.png\" data-rawwidth=\"1734\" data-rawheight=\"1366\" data-original-token=\"84ad6f2e0faa973c86e2660ec6e75728\" class=\"origin_image zh-lightbox-thumb\" width=\"1734\" data-original=\"https://pic3.zhimg.com/84ad6f2e0faa973c86e2660ec6e75728_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1734&#39; height=&#39;1366&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1734\" data-rawheight=\"1366\" data-original-token=\"84ad6f2e0faa973c86e2660ec6e75728\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1734\" data-original=\"https://pic3.zhimg.com/84ad6f2e0faa973c86e2660ec6e75728_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/84ad6f2e0faa973c86e2660ec6e75728_b.png\"/></figure><br/><br/><p data-pid=\"bXzSrEZ_\">参加毕业典礼的时候与旁边的在校MSCS学生聊天，当他们知道我拿到和他们完全一样的学位只花了不到6000刀的时候，表情是扭曲的…（在校读完整个项目至少需要10万刀，含生活费和学费）</p><br/><p data-pid=\"5lRcKRFO\"><b>3. 课业负担重</b></p><p data-pid=\"0WEepNfm\">Georgia Tech的课业负担很重，但对于中国学生而言，个人感觉不会有任何问题。</p><p data-pid=\"apYaOBDv\">在与该项目的 Academic Advisor 的交流中得知，在校MSCS项目的学生一般每人每学期选4门课，而OMSCS项目的学生最多只允许选3门课。如果你像我一样狠心选择一学期上3门的话，相信我，你也会没有时间更新任何博客的……</p><p data-pid=\"PeGpW-Mb\">平均而言，每门课需要每周投入10个小时左右。</p><p data-pid=\"jsPrZvtZ\"><b>4. 改革现代教育模式的先驱</b></p><p data-pid=\"4B4YpnOU\">这是历史上第一个基于 MOOC 的硕士学位，前无古人。\n奥巴马不只一次提到了 OMSCS 项目，期待它为现代高等教育带来的创新。(见<a href=\"https://link.zhihu.com/?target=http%3A//www.cc.gatech.edu/news/presidential-double-down-obama-praises-oms-cs-2nd-time\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Presidential Double-Down: Obama Praises OMS CS for 2nd Time</a>)\n一切的历史正等待你我共同创造。</p><p data-pid=\"ez3yFgwF\">这个硕士学位是远程的，所以：\n- 没有F1身份，没有OPT，不能以学生身份直接工作\n- 中国教育部目前无法认证该类学历 (见<a href=\"https://link.zhihu.com/?target=http%3A//www.gov.cn/banshi/2005-08/15/content_23123.htm\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">教育部11日公布国外学历学位认证须知</a>)</p><p data-pid=\"vEHtHfYY\">但是：\n- 美国H1B抽签有硕士待遇 (此点存疑，还需证实，请等我进一步更新)</p><p data-pid=\"CddPHBXZ\"><b>综上所述，这个项目最适合的人群大概有如下几类：\n</b> <b>- 单纯的热爱CS，想拥有硕士学历；</b> <b>- 已经在CS领域工作的职场人士，想通过更高的学位提升待遇、获取晋升的机会；</b> <b>- 其他非CS专业MS或PHD在读，兼职读此项目获取转行的机会</b></p><p data-pid=\"8fMRVywm\">感兴趣了？请关注 OMSCS 项目官方网站获取更多信息吧：<a href=\"https://link.zhihu.com/?target=http%3A//www.omscs.gatech.edu/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">OMSCS - Georgia Institute of Technology</a></p>", "excerpt": "作为OMSCS项目的首批毕业生，怒答一记。 2015年12月亲赴 Georgia Tech 校园和所有 on-campus 的学生一起参加了毕业典礼，记录在此： <a href=\"https://link.zhihu.com/?target=http%3A//www.wesleysong.com/blog/2015/12/24/georgia-tech-commencement/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Georgia Tech 毕业典礼见闻</a>很久之前写了一篇与该项目相关的经验总结， <a href=\"https://link.zhihu.com/?target=http%3A//www.wesleysong.com/blog/2014/04/22/gatech-omscs-application/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Georgia Tech Online Master of Science in Computer Science 项目申请经验分享</a> ，不过由于课业负担很重，没有过多更新。OMSCS项目交流QQ群ID为 <b>53440680（入群请注明来自知乎）。</b>截止到2017年4月该群已有近500人，欢迎对该…", "excerpt_new": "作为OMSCS项目的首批毕业生，怒答一记。 2015年12月亲赴 Georgia Tech 校园和所有 on-campus 的学生一起参加了毕业典礼，记录在此： <a href=\"https://link.zhihu.com/?target=http%3A//www.wesleysong.com/blog/2015/12/24/georgia-tech-commencement/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Georgia Tech 毕业典礼见闻</a>很久之前写了一篇与该项目相关的经验总结， <a href=\"https://link.zhihu.com/?target=http%3A//www.wesleysong.com/blog/2014/04/22/gatech-omscs-application/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Georgia Tech Online Master of Science in Computer Science 项目申请经验分享</a> ，不过由于课业负担很重，没有过多更新。OMSCS项目交流QQ群ID为 <b>53440680（入群请注明来自知乎）。</b>截止到2017年4月该群已有近500人，欢迎对该…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1516422116, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1516421992654", "type": "feed", "target": {"id": "24570155", "title": "如何评价 Georgia Tech 的 OMSCS (Online Master of Science in Computer Science Degree)?", "url": "https://api.zhihu.com/questions/24570155", "type": "question", "question_type": "normal", "created": 1405993923, "answer_count": 51, "comment_count": 4, "follower_count": 1994, "detail": "<a href=\"https://link.zhihu.com/?target=http%3A//www.omscs.gatech.edu/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">OMSCS - Georgia Institute of Technology</a><br/>据说是目前最好最完善的Online Master Degree in CS, 大约要耗时两年完成, 申请和申请普通Master Degree一样，申请文书，GPA，推荐信都不能少。课程设计也和正常的On Campus MS in CS 一样。大概两年需要6600刀。", "excerpt": "<a href=\"https://link.zhihu.com/?target=http%3A//www.omscs.gatech.edu/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">OMSCS - Georgia Institute of Technology</a> 据说是目前最好最完善的Online Master De…", "bound_topic_ids": [1354, 4228, 6756, 10033, 60324], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1516421992, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1515992155686", "type": "feed", "target": {"id": "102681833", "type": "answer", "url": "https://api.zhihu.com/answers/102681833", "voteup_count": 3942, "thanks_count": 302, "question": {"id": "38331955", "title": "如果人说他/她集齐了十二个星座的前任，我们应该如何估计他/她前任的数量？", "url": "https://api.zhihu.com/questions/38331955", "type": "question", "question_type": "normal", "created": 1449593137, "answer_count": 399, "comment_count": 63, "follower_count": 4594, "detail": "大家请从纯数学的角度探讨本题噢。<br/>而且，估计数量也不仅仅应当考虑期望，评论中@<a href=\"https://www.zhihu.com/people/chenjunrui\" class=\"internal\">chenjunrui</a>提到谁能给一个分布函数，计算一下95%条件下的置信区间呢？<br/>另外一种思路，是使“集齐了十二个星座”这个可能性最高的n，对于极其，极值肯定是正无穷，那么对于“刚刚有九个星座”的情况呢，n过低肯定达到九个星座的可能性小，n过大，随着十个星座等可能性增大，九个星座的可能性也要减少，所以一定会有个极大值的。", "excerpt": "大家请从纯数学的角度探讨本题噢。 而且，估计数量也不仅仅应当考虑期望，评论中@<a href=\"https://www.zhihu.com/people/chenjunrui\" class=\"internal\">ch…</a>", "bound_topic_ids": [8145, 8745, 10507, 11265, 11966], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "7e0890af4eea759e8c89a6e64b19ad00", "name": "三水", "headline": "我", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/miao-qing-27", "url_token": "miao-qing-27", "avatar_url": "https://picx.zhimg.com/63df4531d3667efba6e5820b60bd7c60_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1464226112, "created_time": 1464226112, "author": {"id": "a183b32632088ad4cb0ebf944e555eed", "name": "赵轩昂", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/sd0061", "url_token": "sd0061", "avatar_url": "https://picx.zhimg.com/v2-8fea49a0a8b9071c908558eb4a76541d_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 218, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<img src=\"https://www.zhihu.com/equation?tex=%5Csum_%7Bi%3D1%7D%5E%7B12%7D%5Cfrac%7B12%7D%7Bi%7D+%3D+37.23852813852814591655...\" alt=\"\\sum_{i=1}^{12}\\frac{12}{i} = 37.23852813852814591655...\" eeimg=\"1\"/>", "excerpt": "\\sum_{i=1}^{12}\\frac{12}{i} = 37.23852813852814591655...", "excerpt_new": "\\sum_{i=1}^{12}\\frac{12}{i} = 37.23852813852814591655...", "preview_type": "expand", "preview_text": "<img src=\"https://www.zhihu.com/equation?tex=%5Csum_%7Bi%3D1%7D%5E%7B12%7D%5Cfrac%7B12%7D%7Bi%7D+%3D+37.23852813852814591655...\" alt=\"\\sum_{i=1}^{12}\\frac{12}{i} = 37.23852813852814591655...\" eeimg=\"1\"/>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1515992155, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1515989444436", "type": "feed", "target": {"id": "295995785", "type": "answer", "url": "https://api.zhihu.com/answers/295995785", "voteup_count": 48, "thanks_count": 13, "question": {"id": "60790313", "title": "你有哪些必需的互联网订阅服务？", "url": "https://api.zhihu.com/questions/60790313", "type": "question", "question_type": "normal", "created": 1496819289, "answer_count": 10, "comment_count": 0, "follower_count": 114, "detail": "比如音乐订阅，app会员订阅等", "excerpt": "比如音乐订阅，app会员订阅等", "bound_topic_ids": [75, 386, 595, 1309, 1739], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "3acbda511e81e18daaeaf144689fc45e", "name": "不瞌睡的小八", "headline": "985研究生一枚 做云计算和机器学习的 有点文艺 又有点较真", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/bearbare", "url_token": "bearbare", "avatar_url": "https://pic1.zhimg.com/v2-0cbdb0338801680c98a5fdfa1d055634_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1515994147, "created_time": 1515982438, "author": {"id": "", "name": "", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/", "url_token": "", "avatar_url": "https://pic3.zhimg.com/da8e974dc.jpg", "gender": -1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "censor", "is_copyable": true, "comment_count": 7, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"tYxCOejx\">写一下美国的吧：</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"zz_b6drj\"><b>Amazon（亚马逊）Prime</b>：这个不需要解释了，接近三分之二(64%)的美国家庭都是亚马逊Prime会员。财报上的数字是最好的广告——亚马逊每年72亿美元的物流<b><u>净损失</u></b>就是Jeff Bezos送给Prime会员们的福利。当然了，发福利也不影响Bezos成为全球新首富（<a href=\"https://www.zhihu.com/question/263944203/answer/287281134\" class=\"internal\">纽约老闻：2017 年你所在的行业和领域发生了哪些大事？</a>）。</p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-6a989539b2537e04b84dc87b0d7cb31b_b.jpg\" data-size=\"normal\" data-rawwidth=\"1266\" data-rawheight=\"704\" data-original-token=\"v2-6a989539b2537e04b84dc87b0d7cb31b\" class=\"origin_image zh-lightbox-thumb\" width=\"1266\" data-original=\"https://picx.zhimg.com/v2-6a989539b2537e04b84dc87b0d7cb31b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1266&#39; height=&#39;704&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1266\" data-rawheight=\"704\" data-original-token=\"v2-6a989539b2537e04b84dc87b0d7cb31b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1266\" data-original=\"https://picx.zhimg.com/v2-6a989539b2537e04b84dc87b0d7cb31b_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-6a989539b2537e04b84dc87b0d7cb31b_b.jpg\"/><figcaption>亚马逊2016年的物流净损失达到72亿美元</figcaption></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"-Pw1U7M_\"><b>Evernote（印象笔记）PREMIUM</b>：印象笔记是我的个人知识库：日记，课堂笔记，读书笔记，日常灵感，名片扫描，OCR。。。作为七年的老用户，已经习惯了她的文件夹和标签系统。</p><p data-pid=\"POq3D7hk\">付费理由：除了安全性以及每个月10G的上传流量外，</p><ul><li data-pid=\"Y5zFa4ez\">免费的版本不可离线使用！纽约的地铁里，餐馆里经常没有信号，不能离线用=不能用。</li><li data-pid=\"IuqCx2je\">PREMIUM允许把笔记直接变成幻灯片放映,对重度用户来说是必须的功能：读MBA时，几乎每天都有group meeting或presentation，能从印象笔记里直接放幻灯片，即插即讲，可节省大量时间。</li></ul><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-7c8f2f9422f88b8e9ce41bb8540d9688_b.jpg\" data-size=\"normal\" data-rawwidth=\"719\" data-rawheight=\"462\" data-original-token=\"v2-7c8f2f9422f88b8e9ce41bb8540d9688\" class=\"origin_image zh-lightbox-thumb\" width=\"719\" data-original=\"https://pic3.zhimg.com/v2-7c8f2f9422f88b8e9ce41bb8540d9688_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;719&#39; height=&#39;462&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"719\" data-rawheight=\"462\" data-original-token=\"v2-7c8f2f9422f88b8e9ce41bb8540d9688\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"719\" data-original=\"https://pic3.zhimg.com/v2-7c8f2f9422f88b8e9ce41bb8540d9688_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-7c8f2f9422f88b8e9ce41bb8540d9688_b.jpg\"/><figcaption>我校创业中心Leslie eLab的一角</figcaption></figure><p data-pid=\"XvugKsDX\"><b>Linkedin（领英）PREMIUM</b>：网络时代的个人名片，猎头与企业HR的首选招聘途径，拓展个人职场人脉的最佳平台。我在另外一个帖子写过如何更好地利用Linkedin：<a href=\"https://www.zhihu.com/question/21308578/answer/150998636\" class=\"internal\">纽约老闻：如何有效利用 LinkedIn？</a></p><p data-pid=\"XDtOUD5g\">付费理由：</p><ul><li data-pid=\"Up0IOf-M\">更细化的招聘信息：搜索职位时，付费账号可以添加更多的filter（比如级别，薪水等）来精细化搜索。无论是否在找工作，都有必要时刻关注人才市场的动态，对自己的身价有个准确的估计。</li><li data-pid=\"YguImK5Y\">更高的站内搜索与InMail限额：可以更方便地在Linkedin上找人和约饭约咖啡。</li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"3eBpzta7\"><b><a href=\"https://link.zhihu.com/?target=http%3A//Audible.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">Audible.com</span><span class=\"invisible\"></span></a>（有声书）Gold Annual</b>：年费会员，一年可以下载12本有声读物，同时赠送New York Times或Wall Street Journal有声版。2017年我听了144小时，16本有声读物，考虑升级到Platinum Annual，一年可以下载24本。</p><figure data-size=\"small\"><noscript><img src=\"https://pic1.zhimg.com/v2-ffe5c41847892fafcb3e8ca7980b22d2_b.jpg\" data-size=\"small\" data-rawwidth=\"946\" data-rawheight=\"2048\" data-original-token=\"v2-ffe5c41847892fafcb3e8ca7980b22d2\" class=\"origin_image zh-lightbox-thumb\" width=\"946\" data-original=\"https://pic1.zhimg.com/v2-ffe5c41847892fafcb3e8ca7980b22d2_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;946&#39; height=&#39;2048&#39;&gt;&lt;/svg&gt;\" data-size=\"small\" data-rawwidth=\"946\" data-rawheight=\"2048\" data-original-token=\"v2-ffe5c41847892fafcb3e8ca7980b22d2\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"946\" data-original=\"https://pic1.zhimg.com/v2-ffe5c41847892fafcb3e8ca7980b22d2_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-ffe5c41847892fafcb3e8ca7980b22d2_b.jpg\"/><figcaption>老闻最近读（听）的书</figcaption></figure><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"NTm8xfcx\"><b>Youtube Red（油管红）</b>：网易云音乐，虾米等音乐软件因为版权问题不能使用。Youtube Red则是youtube的音乐版，中英文歌曲都很全，可以离线歌曲。付费用户还有一个好处是允许手机锁屏播放Youtube——国内电视剧情节超级慢，《琅琊榜》，《人民的名义》，《大军师司马懿》等剧，我都是电脑上看前两集，知道主要人物长啥样，之后几十集全是在上下班路上锁屏播放<b><u>听</u></b>完的，把电视剧当评书听就不用忍受鲜肉们面瘫演技的折磨了。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"fMWyKO5w\"><b>Youtube TV（油管电视）</b>：习惯回家就打开电视，但是cable TV的愚蠢实在让我忍无可忍。换成Youtube TV + Apple TV，使用体验得到极大提高，而且可以把来不及看的新闻/节目存下来。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-c01d06bd4d9e43aa7da88939be767cef_b.jpg\" data-size=\"normal\" data-rawwidth=\"2634\" data-rawheight=\"722\" data-original-token=\"v2-c01d06bd4d9e43aa7da88939be767cef\" class=\"origin_image zh-lightbox-thumb\" width=\"2634\" data-original=\"https://pic4.zhimg.com/v2-c01d06bd4d9e43aa7da88939be767cef_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;2634&#39; height=&#39;722&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"2634\" data-rawheight=\"722\" data-original-token=\"v2-c01d06bd4d9e43aa7da88939be767cef\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"2634\" data-original=\"https://pic4.zhimg.com/v2-c01d06bd4d9e43aa7da88939be767cef_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-c01d06bd4d9e43aa7da88939be767cef_b.jpg\"/><figcaption>Youtube TV</figcaption></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"sJzZyY4J\">最后，<b>Google Drive，OneDrive，iCould，</b>你总得用一样，用多了总得买空间，逃不掉的。</p><hr/><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"f6RqrESn\">欢迎关注我的公众号“纽约老闻”。</p><figure data-size=\"small\"><noscript><img src=\"https://pic4.zhimg.com/v2-b9eef80290f3d691c9456649b0c00a23_b.jpg\" data-caption=\"\" data-size=\"small\" data-rawwidth=\"1125\" data-rawheight=\"528\" data-original-token=\"v2-b9eef80290f3d691c9456649b0c00a23\" class=\"origin_image zh-lightbox-thumb\" width=\"1125\" data-original=\"https://pic4.zhimg.com/v2-b9eef80290f3d691c9456649b0c00a23_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1125&#39; height=&#39;528&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"small\" data-rawwidth=\"1125\" data-rawheight=\"528\" data-original-token=\"v2-b9eef80290f3d691c9456649b0c00a23\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1125\" data-original=\"https://pic4.zhimg.com/v2-b9eef80290f3d691c9456649b0c00a23_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-b9eef80290f3d691c9456649b0c00a23_b.jpg\"/></figure>", "excerpt": "写一下美国的吧： <b>Amazon（亚马逊）Prime</b>：这个不需要解释了，接近三分之二(64%)的美国家庭都是亚马逊Prime会员。财报上的数字是最好的广告——亚马逊每年72亿美元的物流<b><u>净损失</u></b>就是Jeff Bezos送给Prime会员们的福利。当然了，发福利也不影响Bezos成为全球新首富（<a href=\"https://www.zhihu.com/question/263944203/answer/287281134\" class=\"internal\">纽约老闻：2017 年你所在的行业和领域发生了哪些大事？</a>）。 <b>Evernote（印象笔记）PREMIUM</b>：印象笔记是我的个人知识库：日记，课堂笔记，读书笔记，日常灵感，名片扫…", "excerpt_new": "写一下美国的吧： <b>Amazon（亚马逊）Prime</b>：这个不需要解释了，接近三分之二(64%)的美国家庭都是亚马逊Prime会员。财报上的数字是最好的广告——亚马逊每年72亿美元的物流<b><u>净损失</u></b>就是Jeff Bezos送给Prime会员们的福利。当然了，发福利也不影响Bezos成为全球新首富（<a href=\"https://www.zhihu.com/question/263944203/answer/287281134\" class=\"internal\">纽约老闻：2017 年你所在的行业和领域发生了哪些大事？</a>）。 <b>Evernote（印象笔记）PREMIUM</b>：印象笔记是我的个人知识库：日记，课堂笔记，读书笔记，日常灵感，名片扫…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1515989444, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1515989442403", "type": "feed", "target": {"id": "150998636", "type": "answer", "url": "https://api.zhihu.com/answers/150998636", "voteup_count": 243, "thanks_count": 84, "question": {"id": "21308578", "title": "如何有效利用 LinkedIn？", "url": "https://api.zhihu.com/questions/21308578", "type": "question", "question_type": "normal", "created": 1373291295, "answer_count": 121, "comment_count": 9, "follower_count": 19444, "detail": "", "excerpt": "", "bound_topic_ids": [99, 140, 880, 1738, 2566], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "2e4a4b87cdfaf28b27f170091d15a6ae", "name": "<PERSON><PERSON><PERSON>", "headline": "站在风口，只有猪才能飞起来。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/linjunjie1103", "url_token": "linjunjie1103", "avatar_url": "https://picx.zhimg.com/e5ba6b785_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "reaction_instruction": null}, "updated_time": 1590545263, "created_time": 1489212143, "author": {"id": "", "name": "", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/", "url_token": "", "avatar_url": "https://pic3.zhimg.com/da8e974dc.jpg", "gender": -1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 14, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p></p><ol><li data-pid=\"N4YBx_lB\"><b>换张好照片</b>：这是个看脸的世界，在职业社交网站请用专业摄影师拍的照片。</li><li data-pid=\"wUyLw10X\"><b>上传简历</b>：帮猎头和HR省事，就等于帮你自己增加获得面试的概率。</li><li data-pid=\"jhzSDxfj\"><b>和微信账号绑定</b>：道理同上。</li><li data-pid=\"3Ky2EUOu\"><b>写一段summary（个人概况）</b>：两三句话，让猎头10秒内能对你的经验/技能/求职意向有个大概的了解。比如：“我叫小札，脸书创始人，15年编程经验，想找一份NGO工作”</li><li data-pid=\"XhD6XtHe\"><b>加入校友/公司群</b>：不要错过和求职相关的新闻与活动，充分利用校友人脉。</li><li data-pid=\"F7dTu79V\"><b>设置职位提醒</b>：在Linkedin上搜索职位的条件（比如&#34;纽约地区&#34;，&#34;债券研究员&#34;）可以保存下来，以后每当有符合条件的职位出现后，你都会收到邮件提醒。</li><li data-pid=\"NFWv59XU\"><b>发表文章</b>：对于行业的见解与感悟应该写下来并在Linkedin上发表，这个习惯一方面敦促自己把想法落到笔头理清思路，另一方面也可以打造自己personal brand（个人品牌）。不要害怕自己的观点可能看起来很蠢——你每天在twitter和微博上发表的言论肯定比这更蠢：）</li><li data-pid=\"wI66HiYI\"><b>辅助写简历</b>：office 365用户可以直接在word里打开Review-&gt;Resume Assistant，然后依据职位/行业/技能/关键字在Linkedin上搜索相应的profile给你做参考。<br/></li></ol><a href=\"https://zhuanlan.zhihu.com/p/30919150\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pica.zhimg.com/v2-9af164ad8f0ab65ecdffc4f92f795a5e_180x120.jpg\" data-image-width=\"600\" data-image-height=\"338\" class=\"internal\">极客公园：微软推出简历制作神器，让你在 Word 中直接使用 LinkedIn 的资料</a><p data-pid=\"9_rG6uTc\"><b>9. 约吗？</b>2016年的new year resolution（新年决心）就是每周在Linkedin上约出一个陌生人。刚才数了一下，2016年总共从linkedin约出30个人喝咖啡或者吃饭（大约打了5折吧）：其中大多数的人都是和我在不同的行业，或者同行业但更加资深的前辈。不同专业之间灵感的碰撞和业内前辈的经验，都让我受益良多。其中一些成为了我的&#34;邮件好友&#34;——彼此不时交换对感兴趣领域新闻动态的看法；有两个甚至成为了每周都要一起喝酒的朋友。这种有意识有目的的社交行为，往往不在人们的comfort zone（舒适区域）内，但是却经常有意想不到的收获。如果你和我一样都很内向，那么请告诉自己，每一个在linkedin上精心设计自己页面的人，内心都是渴望被约的：）</p><p></p>", "excerpt": "<b>换张好照片</b>：这是个看脸的世界，在职业社交网站请用专业摄影师拍的照片。<b>上传简历</b>：帮猎头和HR省事，就等于帮你自己增加获得面试的概率。<b>和微信账号绑定</b>：道理同上。<b>写一段summary（个人概况）</b>：两三句话，让猎头10秒内能对你的经验/技能/求职意向有个大概的了解。比如：“我叫小札，脸书创始人，15年编程经验，想找一份NGO工作”<b>加入校友/公司群</b>：不要错过和求职相关的新闻与活动，充分利用校友人脉。<b>设置职位提醒</b>：在Linkedin…", "excerpt_new": "<b>换张好照片</b>：这是个看脸的世界，在职业社交网站请用专业摄影师拍的照片。<b>上传简历</b>：帮猎头和HR省事，就等于帮你自己增加获得面试的概率。<b>和微信账号绑定</b>：道理同上。<b>写一段summary（个人概况）</b>：两三句话，让猎头10秒内能对你的经验/技能/求职意向有个大概的了解。比如：“我叫小札，脸书创始人，15年编程经验，想找一份NGO工作”<b>加入校友/公司群</b>：不要错过和求职相关的新闻与活动，充分利用校友人脉。<b>设置职位提醒</b>：在Linkedin…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1515989442, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1515989442403&page_num=72"}}