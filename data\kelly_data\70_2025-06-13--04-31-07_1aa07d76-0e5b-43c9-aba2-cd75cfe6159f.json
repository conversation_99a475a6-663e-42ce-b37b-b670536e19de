{"data": [{"id": "1517513017809", "type": "feed", "target": {"id": "139955256", "type": "answer", "url": "https://api.zhihu.com/answers/139955256", "voteup_count": 3023, "thanks_count": 457, "question": {"id": "27646061", "title": "为什么海外的华人感觉好爱国，但是让他回国却是不愿意的？", "url": "https://api.zhihu.com/questions/27646061", "type": "question", "question_type": "normal", "created": 1421532365, "answer_count": 1688, "comment_count": 130, "follower_count": 7523, "detail": "每次看新闻，海外的华人爱国爱的都不行了，为什么当初他们还要移民呀。", "excerpt": "每次看新闻，海外的华人爱国爱的都不行了，为什么当初他们还要移民呀。", "bound_topic_ids": [400, 472, 3251, 4109, 20465], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "8aaafc80790654da90ec18d4dfd9c1d9", "name": "海森寳", "headline": "没有知觉", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/hai-sen-bao-44", "url_token": "hai-sen-bao-44", "avatar_url": "https://picx.zhimg.com/e96e03f25798ebfaaf3d759a3c7ef065_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1484273647, "created_time": 1483930070, "author": {"id": "74e369c8c4f2c7afda009525547901dc", "name": "发芽", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/ding-bei-4", "url_token": "ding-bei-4", "avatar_url": "https://pica.zhimg.com/v2-cbe9ffb88fcab680e6113d9930c1f601_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 298, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"_0xLee-x\">我给大家讲和我的专业相关的两个故事吧。</p><p data-pid=\"gMZHkUmw\">第一个故事是关于一个美国华人教授。WG十年结束之后，我们国家开始改革开放，想要派遣一批学生去美国学习新的科学技术。但是当时GRE和TOEFL的标准考试在中国还没有开始，因此美国大学在招收中国学生的时候顾虑重重，简单来说就是不肯收。这位华人教授给当时的副总理方毅写信，信中建议将美国一些好的物理系研究院的大学联系起来，每年在中国举行一次标准考试，根据考试的成绩选拔学生去美国深造。得到肯定回复之后（后来得到了小平同志的大力支持），这位教授向美国53所大学的物理系写信邀请参加这个项目，并且请求各个大学免除申请费，因为当时的中国学生不可能兑换外汇。在困难重重的情况下，他一次次说服自己的美国同事、朋友，这个项目最终成功了，并且延续了十年。就是著名的CUSPEA项目。这个项目为中国培养了大批的物理学科骨干，并且延伸到了化学，生物学科。在1985年这位教授还帮助中国设计了符合中国国情的博士后制度。他积极推进了我国自然科学基金的设立，将同行评议引入科研经费的分配制度。这位教授还推动了中国很多物理科研中心的建设。除此之外还设置了给中国优秀高中生的大学入学奖学金和促进两岸本科生科研交流的”君政奖学金“ 。</p><p data-pid=\"XQLHIjqo\">这位教授是一位诺贝尔奖得主，叫做李政道。如果他的故事大家还是有所耳闻，只是不了解细节的话，下面这个故事也许知道的人就没有那么多了，如果不是从亲历者那里听说，我也完全不会相信。</p><p data-pid=\"nx1PBKO9\">这位华人教授也是一位诺贝尔奖得主。从WG结束后，1979年到1999年的二十年里，他几乎每次都要回一次中国，帮助大陆分子反应动力学的学科的建立和发展。这几乎就是从无到有的过程。从一开始把仪器的设计图纸提供给大陆的实验室，帮助建立仪器，到后面亲自几天不眠不休调试仪器（当时他已获诺贝尔奖）。后来发现光有仪器还不行，他就发掘了大陆的年轻人去美国进修，从头培养，并且将他们送去接受相关的仪器设计训练，通过几十年的时间在中国建起国际先进水平的分子反应动力学的实验室。他的学生又成为了我们的老师，继续培养中国新一代的物理化学的科技人员。</p><p data-pid=\"JhevPgYv\">可是这位教授的故事就淹没在岁月里了。为什么？因为他在1999年之后回到了他出生的地方，他后来的一些言行也备受争议。因为就像其他答主说的那样，爱国是有层次的，是一圈一圈的，他的成长和背景决定了他的立场。这个立场也许是我们的对面。他爱的那个国也许只是最最外层的一圈，但是他的大陆的学科贡献是不可磨灭的，也是不可替代的，即便没有宣传，也会通过口耳相传传递下去。</p><p data-pid=\"u65VR4rO\">我讲这两个故事是为什么呢？我只是想说海外华人的爱国并不是大家想象的那样口头的空话。有的时候他们的付出和贡献不是亲历者，完全没有办法想象。中国各行各业发展到今天，很多情况下，海外华人的贡献是不可取代的，我知道的故事也远远不只是这两个。可是因为种种可以理解的原因，这些贡献多数情况下国家都没有宣传，也不能宣传。很多情况下他们自己也不希望宣传。可是只要在春晚听到一声“海外侨胞”，他们就觉得足够了。也许我们在外面会遇到一些为生计所迫不那么好的华人，可是我们该记住怎样的人，自己要成为怎样的人，这才是最重要的，不是吗？</p><p data-pid=\"Td4LgSEp\">2017.01.10补充</p><p data-pid=\"to8DHzMe\">最近恰好看到一篇文章： 种豆得瓜：李政道和中国互联网的诞生。和大家分享：</p><h1><a href=\"https://link.zhihu.com/?target=http%3A//www.v4.cc/News-3310904.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">种豆得瓜：李政道和中国互联网的诞生-微众圈</a> </h1>", "excerpt": "我给大家讲和我的专业相关的两个故事吧。 第一个故事是关于一个美国华人教授。WG十年结束之后，我们国家开始改革开放，想要派遣一批学生去美国学习新的科学技术。但是当时GRE和TOEFL的标准考试在中国还没有开始，因此美国大学在招收中国学生的时候顾虑重重，简单来说就是不肯收。这位华人教授给当时的副总理方毅写信，信中建议将美国一些好的物理系研究院的大学联系起来，每年在中国举行一次标准考试，根据考试的成绩选拔学生去…", "excerpt_new": "我给大家讲和我的专业相关的两个故事吧。 第一个故事是关于一个美国华人教授。WG十年结束之后，我们国家开始改革开放，想要派遣一批学生去美国学习新的科学技术。但是当时GRE和TOEFL的标准考试在中国还没有开始，因此美国大学在招收中国学生的时候顾虑重重，简单来说就是不肯收。这位华人教授给当时的副总理方毅写信，信中建议将美国一些好的物理系研究院的大学联系起来，每年在中国举行一次标准考试，根据考试的成绩选拔学生去…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1517513017, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1517281170286", "type": "feed", "target": {"id": "33322002", "type": "article", "author": {"id": "8baab2164ff8bca575996e1fcc55e1a9", "name": "优达学城（Udacity）", "headline": "由 Google 无人车之父创立、硅谷前沿在线科技学习平台", "type": "people", "user_type": "organization", "url": "https://www.zhihu.com/people/you-da-xue-cheng", "url_token": "you-<PERSON>-x<PERSON>-cheng", "avatar_url": "https://picx.zhimg.com/v2-1296127db7c71171a197658a57a6cac6_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": true, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "created": 1517278363, "updated": 1574848958, "title": "数据分析入门|12个数据可视化工具，人人都能做出酷炫图表", "excerpt_title": "", "content": "<p data-pid=\"1_tPhbHg\">导语：今天我们带来一篇来自 Adobe 工程师 Rohit Boggarapu 的文章。他在文章中介绍了一些适合网页开发者的数据可视化和绘图工具，让你不必再花大力气与枯燥的数据抗争。部分工具不要求写代码也可以使用！</p><blockquote data-pid=\"EPMOm08A\">文/ Rohit Boggarapu<br/>编译/ 优达菌</blockquote><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"52nbgai4\">优达学城诠释数据的方式和数据本身之间存在着巨大的鸿沟。当我们唯一的选择是盯着表格中一列列不知所云的数字时。这可能是最无聊的一种格式了。</p><p data-pid=\"npMX2ByW\">没有哪个网页开发者会喜欢电子表格。好消息是，现在我们有了许多更加优雅的方式来呈现数据，再也没有必要使用静态的 Excel 图表了。</p><p data-pid=\"22tHXX3v\">在为你的项目选择合适的绘图工具时，要考虑到许多事情。我们将为你分析适合网页开发者的 12 个最好的工具，让你不再花费大把时间跟数据做斗争，而是开始轻松地绘制漂亮的图表。虽然本文推荐的工具是面向网页开发者的，但其中一些并不需要会写代码就能使用。许多工具都有着丰富的交互式例子，即使是新手也能轻松地通过改动代码来创建自定义图表。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"SVgdhJMx\"><b>1. Google Charts</b></p><p data-pid=\"pdWgPCbw\">文档和帮助信息丰富的 Google Charts 对于刚刚入门 JavaScript 绘图的人来说是极佳的选择。它的文档里到处都是带注释的代码和逐步的讲解，可以直接用来把 HTML5 / SVG 图标嵌入到你的网页中。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-992583f34621d1fbda50de3b1e991162_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"264\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pica.zhimg.com/v2-992583f34621d1fbda50de3b1e991162_r.jpg\" data-original-token=\"v2-992583f34621d1fbda50de3b1e991162\"/></figure><p data-pid=\"xuE_znLG\">如果你需要更进阶的自定义功能或是 Google 原始提供的 18 类以外的图表，下面会介绍一些有着更多类别和特性的选择。</p><p data-pid=\"sRy_risl\"><b>适合人群：</b>追求灵活性和良好文档的严肃开发者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"7WJ2VVMM\"><b>2. MetricsGraphics</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-c3e534c319468de0310b30ee1ecfda56_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"279\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pica.zhimg.com/v2-c3e534c319468de0310b30ee1ecfda56_r.jpg\" data-original-token=\"v2-c3e534c319468de0310b30ee1ecfda56\"/></figure><p data-pid=\"_eBn04nv\">MetricsGraphics 是一个在 D3.js 的基础上专为可视化时间序列数据而开发的绘图库。虽然它只支持线图、散点图、柱状图、直方图和数据表格，但它在这几类图表上的表现非常强。</p><p data-pid=\"IOqt1ALb\">跟 Google Charts 一样（MetricsGraphics 是 Mozilla 的产品），丰富的文档和例子使得它很容易上手。比如这个非常有趣的关于 UFO 目击事件的交互式例子。</p><p data-pid=\"xrrMj_PG\">同时它也是一个非常简易和轻量级的选择。</p><p data-pid=\"KIXoU5X4\"><b>适合人群：</b>追求快速美观同时又不需要写一堆杂乱代码的开发者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"i3kqVr14\"><b>3. FusionCharts</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-916cbab6e77e3089495f4c7cddaff237_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"398\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic4.zhimg.com/v2-916cbab6e77e3089495f4c7cddaff237_r.jpg\" data-original-token=\"v2-916cbab6e77e3089495f4c7cddaff237\"/></figure><p data-pid=\"qA8cbtL4\">FusionCharts 支持 vanilla JavaScript、jQuery、Angular 等一系列高人气的库和框架。它内置 90 多种图表和超过 1000 种地图，相比 Google Charts 和 MetricsGraphics 要完整得多。你可以在这里查看它所支持的全部图表类型。</p><p data-pid=\"RPHHpobF\">考虑到应用或是网站的拓展性，如果你选择了一个功能不完整的绘图库，这就有可能在将来发展成一个问题。而像 Microsoft、Google 和 IBM 这样的公司都在使用 FusionCharts，这说明它是一个能满足企业级拓展性需求的工具。</p><p data-pid=\"n2WK7qfq\"><b>适合人群：</b>需要各种不同种类的易自定义图表的开发者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"c0B7JRgu\"><b>4. Epoch</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-8df134b70b9d0805206e5e8fbee4e8dc_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"191\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic1.zhimg.com/v2-8df134b70b9d0805206e5e8fbee4e8dc_r.jpg\" data-original-token=\"v2-8df134b70b9d0805206e5e8fbee4e8dc\"/></figure><p data-pid=\"BR9wvKo1\">Epoch 是一个基于 d3.js 开发的工具，它使得开发者可以方便地在他们的应用或是网站上部署实时图表。它的文档整洁，完全免费并且开源，这使得它对于不想花钱购买重量级解决方案的人来说是一个很好的选择。</p><p data-pid=\"p_-GDPh3\">对普通数据和实时数据，Epoch 都支持 5 种图表类型。这个数量并不能与 FusionCharts 或是 Highcharts 这种特性完整的产品对抗，但它所专长的是以简单和友好的方式呈现实时数据。</p><p data-pid=\"ChJ73D9O\"><b>适合人群：</b>需要简单灵活的实时数据呈现方案的开发者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"I85NkYJ4\"><b>5. ECharts</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-fb0e815e2b6dcbd962713b1931e1aa75_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"288\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic4.zhimg.com/v2-fb0e815e2b6dcbd962713b1931e1aa75_r.jpg\" data-original-token=\"v2-fb0e815e2b6dcbd962713b1931e1aa75\"/></figure><p data-pid=\"yInY8eOU\">百度的 ECharts 是一个很棒的工具，它支持在绘制完数据后再对其进行操作。这个被称为 Drag-Recalculate 的特性使得用户可以在图表之间拖动一部分的数据并得到实时的反馈。同时，ECharts 是专为绘制大量数据设计的。它可以瞬间在二维平面上绘制出 20 万个点，并用专为 ECharts 开发的轻量级 Canvas 库 ZRender 使数据动起来。</p><p data-pid=\"efas0sGv\">你可以在这里对上图进行操作，来体验 ECharts 所提供的特性。</p><p data-pid=\"vCFO4_1V\"><b>适合人群：</b>想尽量避免写代码并有实时数据操作需求的开发者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"_gwiK9e4\"><b>6. D3.js</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-e9747c6a92664a9b62d3350e4242f248_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"318\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pica.zhimg.com/v2-e9747c6a92664a9b62d3350e4242f248_r.jpg\" data-original-token=\"v2-e9747c6a92664a9b62d3350e4242f248\"/></figure><p data-pid=\"03U_9-aA\">虽然并不是对用户最友好的工具，但 d3.js 在 JavaScript 绘图界的重要性是不可小觑的。许多其他的库都是基于它所开发，因为它提供了你所能想到的所有功能。它支持 HTML、SVG 和 CSS，并且有着海量的用户贡献内容来弥补它缺乏自定义内容的劣势。</p><p data-pid=\"kDITnhT1\">由于 D3.js 的学习曲线比较陡峭，你可以学习优达学城（Udacity）上的《Data Visualization and D3.js》，这门课能为你打下坚实的基础。</p><p data-pid=\"XSXTz3bx\"><b>适合人群：</b>不怕写代码的硬核绘图专家。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"dZYEC-u8\"><b>7. Sigma</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-4344357c42eed259c806f90296a367c4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"389\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic3.zhimg.com/v2-4344357c42eed259c806f90296a367c4_r.jpg\" data-original-token=\"v2-4344357c42eed259c806f90296a367c4\"/></figure><p data-pid=\"AdY_f19y\">跟上面已经提到过的工具相比，Sigma 有着自己独特的定位，那就是图模型的绘制。它基于 Canvas 和 WebGL 开发并提供了公开的 API。所以你可以在 GitHub 上找到社区贡献的许多插件。举例来说，你可以用 Sigma.js 画出以上这样的图</p><p data-pid=\"d27fHM7i\">Sigma 同时也是响应式的，并支持触屏。开发者很容易添加新的功能以及精细地控制边和顶点的规格。 <b>适合人群：</b>需要专为绘制图模型设计的强大工具的开发者。<br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"RhVOtmoz\"><b>8. Highcharts</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-da8c6c2ad553d6cf58f6fce6f0f4fbe0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"438\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic3.zhimg.com/v2-da8c6c2ad553d6cf58f6fce6f0f4fbe0_r.jpg\" data-original-token=\"v2-da8c6c2ad553d6cf58f6fce6f0f4fbe0\"/></figure><p data-pid=\"vJ4NuGrG\">人气极高的 Highcharts 可以在不依赖插件的情况下绘制交互式的图表。它高灵活性的绘图 API 也被 Nokia、Twitter、Visa 和 Facebook 这样的公司所青睐。</p><p data-pid=\"ayJIecIw\">Highcharts 对于非商业使用是免费的，而商业许可的价格是一份 590 美元（附带技术支持）。绘制图例子如上图。</p><p data-pid=\"sG2RAr6l\"><b>适合人群：</b>需要在技术支持的帮助下绘制各种复杂的图表的开发者。 </p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ZQ4TYFdL\"><b>9. dc.js</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-75d2531f9af07a67843bde6120a63ae5_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"353\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://picx.zhimg.com/v2-75d2531f9af07a67843bde6120a63ae5_r.jpg\" data-original-token=\"v2-75d2531f9af07a67843bde6120a63ae5\"/></figure><p data-pid=\"RmNAxQql\">dc.js 是一个开源的 JavaScript 绘图库。它非常适合用来创建交互式的仪表盘（Dashboard）。图表之间是有联系的，所以当你与其中一个部分进行交互时，其他部分都会做出实时的反馈。这是一个例子：</p><p data-pid=\"_6_0BhGV\">除了一些在线课程以外，你可以通过各种例子来学习使用这个库。等你照着文档动手一遍以后就有能力创建自己的图表了。</p><p data-pid=\"AOPm4VSY\">虽然 dc.js 并没有像 ECharts 或是 Google Charts 那样丰富的功能，但它在自己的卖点——易于呈现和探索巨量的维度数据集上做的非常好。</p><p data-pid=\"uUC9wbG6\"><b>适合人群：</b>需要为关系型图表创建一个仪表盘的开发者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"dL1Y-0mV\"><b>10. dygraphs</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-e2a459602c02e7589c57a47ec9555d07_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"342\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic4.zhimg.com/v2-e2a459602c02e7589c57a47ec9555d07_r.jpg\" data-original-token=\"v2-e2a459602c02e7589c57a47ec9555d07\"/></figure><p data-pid=\"ZMA2qQcL\">由 Google 开发的 dygraphs 绝对是绘图工具中的明星。到现在 Google Correlate 还在使用它（当然，在设计上经过了一些调整）。它可以被用于绘图密集的项目，因为它能在不影响性能的情况下轻松地绘制几百万个数据点，这在很大程度上弥补了它那过于朴素的审美设计。</p><p data-pid=\"LTen9e-N\">从一开始作为 Google 的一个内部项目到最后公开发布，dygraphs 一直有着活跃的社区支持。同时它也在 GitHub 上开源。</p><p data-pid=\"YBCUpwFN\"><b>适合人群：</b>需要有着活跃支持的专为绘制海量数据集设计的工具的开发者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"udnqR4cD\"><b>11. Vega</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-8aa656d1083daaf5fa156d37480db74c_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"415\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic3.zhimg.com/v2-8aa656d1083daaf5fa156d37480db74c_r.jpg\" data-original-token=\"v2-8aa656d1083daaf5fa156d37480db74c\"/></figure><p data-pid=\"BJ1tKR-0\">Vega 是一个基于 d3.js 的用于创建、分享和保存可视化图标的库。它由许多部件组成，其中一些能够在不需要写代码的前提下达到与 d3 竞争的水平。Vega 能够把 JSON 数据转换成 SVG 或 HTML5 图表。虽然这没什么了不起的，但它把这一步做的很踏实。</p><p data-pid=\"QskK-fI-\">因为使用 Vega 不需要写任何代码（只要会编辑 JSON 文件即可），它是一个很好的 d3 替代品，能在降低使用复杂度的同时保留 d3 的特性。</p><p data-pid=\"eAPim_iW\"><b>适合人群：</b>需要 d3 强大的特性又不希望从头学起的开发者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Mep3_A5E\"><b>12. NVD3</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-f57e4d4617fc4513733cd2be4f928165_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"371\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic2.zhimg.com/v2-f57e4d4617fc4513733cd2be4f928165_r.jpg\" data-original-token=\"v2-f57e4d4617fc4513733cd2be4f928165\"/></figure><p data-pid=\"778Zl0LW\">最后介绍的工具也是基于 d3.js 的。作为绘图界的佼佼者，NVD3 是由一系列部件组成的，允许开发者创建可重用的图标。你可以在它的网站上找到许多 demo 和对应的代码。这也是上手 NVD3 的最佳方式。</p><p data-pid=\"TjugYHnm\">NVD3 的审美风格要比 d3.js 更为精致一点。它支持 11 种图表类型，包括区域图、线图、柱状图、气泡图、饼状图和散点图。同时也支持所有现代浏览器以及 IE 10 以后的版本。</p><p data-pid=\"nvsvXfMT\"><b>适合人群：</b>熟悉 d3 并想要可重用图表的开发者。</p><hr/><p data-pid=\"CB-6iMJy\">本文首发于微信订阅号优达学城Udacity（id：youdaxue），扫码关注订阅号有福利哦！</p><p data-pid=\"xGO2t_Mc\">想学用数据讲故事？用可视化图表证明自己的专业结论和预测？立即加入 Udacity 【数据分析师】纳米学位，13周入门数据分析，学会用 Tableau、Python 和 R 创造最佳的数据解读，戳链接<a href=\"https://link.zhihu.com/?target=http%3A//cn.udacity.com/dand%3Futm_source%3Dzhihu-oa%26utm_medium%3Dsocial\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">数据分析师 | Udacity</a>立即了解详情</p><p></p>", "excerpt": "导语：今天我们带来一篇来自 Adobe 工程师 Rohit Boggarapu 的文章。他在文章中介绍了一些适合网页开发者的数据可视化和绘图工具，让你不必再花大力气与枯燥的数据抗争。部分工具不要求写代码也可以使用！ 文/ Rohit Boggarapu 编译/ 优达菌 优达学城诠释数据的方式和数据本身之间存在着巨大的鸿沟。当我们唯一的选择是盯着表格中一列列不知所云的数字时。这可能是最无聊的一种格式了。 没有哪个网页开发者会喜欢电子表格。好消…", "excerpt_new": "导语：今天我们带来一篇来自 Adobe 工程师 Rohit Boggarapu 的文章。他在文章中介绍了一些适合网页开发者的数据可视化和绘图工具，让你不必再花大力气与枯燥的数据抗争。部分工具不要求写代码也可以使用！ 文/ Rohit Boggarapu 编译/ 优达菌 优达学城诠释数据的方式和数据本身之间存在着巨大的鸿沟。当我们唯一的选择是盯着表格中一列列不知所云的数字时。这可能是最无聊的一种格式了。 没有哪个网页开发者会喜欢电子表格。好消…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/33322002", "comment_permission": "all", "voteup_count": 216, "comment_count": 29, "image_url": "https://pica.zhimg.com/v2-72caa9828137514ca1f9a52cab270e7f_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_COLLECT_ARTICLE", "created_time": 1517281170, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "收藏了文章", "is_sticky": false}, {"id": "1517259635840", "type": "feed", "target": {"id": "20535466", "title": "Airbnb 短租模式，美国可以做的为什么中国不可以呢？", "url": "https://api.zhihu.com/questions/20535466", "type": "question", "question_type": "normal", "created": 1350374634, "answer_count": 36, "comment_count": 3, "follower_count": 893, "detail": "", "excerpt": "", "bound_topic_ids": [3282], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "4094ac644ae8c71c15a125b456a97796", "name": "麦田", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/maitian", "url_token": "maitian", "avatar_url": "https://pica.zhimg.com/6ba4f6b5179cf3088e8e22a9fc1ae2e2_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1517259635, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1517259630116", "type": "feed", "target": {"id": "28143594", "title": "Airbnb 在中国的发展前景如何？该如何定位？", "url": "https://api.zhihu.com/questions/28143594", "type": "question", "question_type": "normal", "created": 1423721037, "answer_count": 45, "comment_count": 2, "follower_count": 1154, "detail": "中国包含內地和港澳台", "excerpt": "中国包含內地和港澳台", "bound_topic_ids": [3282, 40931], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "ad1ccbe6832093b46b7f430ceaf97d54", "name": "<PERSON><PERSON><PERSON>", "headline": "Just another human being", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/catitude", "url_token": "catitude", "avatar_url": "https://picx.zhimg.com/b2c58c248_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1517259630, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1517132451183", "type": "feed", "target": {"id": "288244972", "type": "answer", "url": "https://api.zhihu.com/answers/288244972", "voteup_count": 8072, "thanks_count": 2519, "question": {"id": "50408698", "title": "有哪些你看了以后大呼过瘾的编程书？", "url": "https://api.zhihu.com/questions/50408698", "type": "question", "question_type": "normal", "created": 1473173544, "answer_count": 522, "comment_count": 11, "follower_count": 54865, "detail": "<p>镜像问题:</p><a href=\"https://www.zhihu.com/question/60241622/answer/174329743\" class=\"internal\">有哪些你看了以后大呼过瘾的数据分析书？ - 知乎</a>", "excerpt": "镜像问题:<a href=\"https://www.zhihu.com/question/60241622/answer/174329743\" class=\"internal\">有哪些你看了以后大呼过瘾的数据分析书？ - 知乎</a>", "bound_topic_ids": [53, 2458, 10033, 25257, 35436], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "16af5e08208b678a431d3c845dd1da43", "name": "陈添", "headline": "在无聊世界里找乐子", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/chen-tian-93-11", "url_token": "chen-tian-93-11", "avatar_url": "https://picx.zhimg.com/v2-e6abe10c45083b595e4255fcf9edb48b_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1587623641, "created_time": 1514958400, "author": {"id": "a41cd47de5e857c9e784374f48fa00fb", "name": "Memoria", "headline": "他们怎么成的角儿啊，得挨多少打啊", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/afox", "url_token": "afox", "avatar_url": "https://pica.zhimg.com/6647a1923cd79e3df878492ee53de1a4_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 78, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"xq5VciTi\">没想到竟然上了日报。好方……</p><p data-pid=\"GqojZpvv\">———————————————</p><p data-pid=\"GIx2-BNj\">大学时我对编程语言有过一些困惑：应该学哪个编程语言？到底怎么样才算是掌握了编程语言？5年前我幸运地看到了垠神在新浪博客的一篇文章 - 如何掌握程序语言，里面推荐阅读SICP的前三章。我读过之后，受益良多。</p><p data-pid=\"7dIo71qT\">曾几何时，SICP是每个MIT CS学生大一就要上的基础课的教材，毕竟是世界名校，比我们的课程不知高到哪里去了。然鹅，时过境迁，MIT的教授们已经停止这门基于SICP的课程了，因为一方面他们已经教了太久（十几年了），感觉很烦了，另一方面他们认为软件工程的学生现在把大部分时间花在阅读软件库的说明书上，然后搞清楚怎么调用，SICP跟这套工作流程已经不怎么相关了。</p><p data-pid=\"R-Igo68f\">好了，虽说这门课已经不更新了，SICP的作者承认他们并不知道应该如何安排更合理的课程，所以我们还是要看看这本书。</p><p data-pid=\"_BVVXbeq\">另外，基于这本书，伯克利的一位教授搞了个教学网站 - <a href=\"https://link.zhihu.com/?target=http%3A//composingprograms.com/about.html\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">composingprograms.com/a</span><span class=\"invisible\">bout.html</span><span class=\"ellipsis\"></span></a>，用Python来教SICP，有兴趣可以去看看。这个网站的内容也是基于伯克利的CS 61A，这门课的名字就叫SICP。实际上MIT也已经用Python替换了Scheme，所以我个人更推荐上面这个网站，比起Scheme，Python更加适合初学者。不过SICP的内容不会过时，Scheme只是一个表现形式，扫一遍前三章也绝对不亏。非初学者也会有时看时新的感觉。</p><p data-pid=\"mqQKMgit\">SICP帮助读者了解CS的核心概念，属于从高空俯瞰世界，刚入门的读者不应该错过。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"jjraY2YU\">说完了高空，接下来我想推荐另一本中间高度的书，这本书帮助我从了解如何组织代码的模块，以及实践中业界遇到的问题，以及如何用合适的架构来解决这些问题。</p><p data-pid=\"3sRuHsff\">Clean Architecture: A Craftsman&#39;s Guide to Software Structure and Design</p><p data-pid=\"smH9eedH\">这本书是Uncle Bob最近几年的博文合集，读完以后有种灵台清明的感觉，做code review的时候思路清晰了很多。</p><p data-pid=\"zldVv6tc\">看过差的架构才明白好的有多么珍贵。架构都是一点点烂掉的，所以防微杜渐很重要，而要防微杜渐，就必须要从一开始就知道正确的架构方向。这本书不长，英文版也只需要不到10个小时就能读完，大学里或是工作中的读者应该会喜欢这本书。这本书2017年9月才出版，时效性远远超过经典的Martin Fowler的Patterns of Enterprise Application Architecture，你如果准备读后者，建议你先读这本小书。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"pYjnY6US\">说完了中间高度，还有一本更加细节的书，我实在不能不推荐，因为我看到高票答案里都没提到，也许是因为这本书2017年三月才出版，还没有中文的。</p><p data-pid=\"WDHvV-de\">Martin Kleppmann的DDIA：Designing Data-Intensive Applications。</p><p data-pid=\"VQRExwjx\">这本书的作者阅读量非常广泛，以数据密集型软件的设计为楔子讲了分布式系统的很多东西，从设计思路到一些具体细节，收放自如。从Netflix的技术博客，到开源软件的技术来源，再到OSDI上各个业界大厂发的论文，让我大饱眼福。一共12章节，目前看到了第9章。</p><p data-pid=\"ltHTJw9K\">如果说上一本是讲了高可扩展性、高可维护性的架构，是骨架；那么这本书是讲了分布式、高并发、高性能、高可用的软件，是血肉。细胞的级别讲不出来，绝知此事要躬行。</p><p data-pid=\"a_NttGA_\">微软CTO Kevin Scott对这本书的<a href=\"https://link.zhihu.com/?target=https%3A//dataintensive.net/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">评价</a>是『每一位软件工程师的必读书。』</p><p data-pid=\"zQQN2HIP\">目前这本书在Goodreads上有404个评价，评分高达4.68，要知道SICP才4.45. </p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WNsy1afi\">简单强调一下：国内的朋友不要排斥阅读英文资料，作者都是理工科，写作词汇量基本也就是四级英语，跟咱们高中英语差不多。</p><a data-draft-node=\"block\" data-draft-type=\"mcn-link-card\" data-mcn-id=\"1236663209387888640\"></a><a data-draft-node=\"block\" data-draft-type=\"mcn-link-card\" data-mcn-id=\"1236663789959274496\"></a><p data-pid=\"89CeZErD\">看了这两个回答，赶紧学习吧：</p><p data-pid=\"d70cLSto\"><a href=\"https://www.zhihu.com/question/65863653/answer/264376474\" class=\"internal\">Memoria：17 年美国 CS new grad 就业到底有多差？</a></p><p data-pid=\"hNytX-pl\"><a href=\"https://www.zhihu.com/question/41464964/answer/307400008\" class=\"internal\">David Dong：如果编程语言变成高考科目会怎样？</a></p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-867546b22393f03a32d60277c9a19d36_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"300\" data-rawheight=\"166\" data-original-token=\"v2-867546b22393f03a32d60277c9a19d36\" data-thumbnail=\"https://pic3.zhimg.com/v2-867546b22393f03a32d60277c9a19d36_b.jpg\" class=\"content_image\" width=\"300\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;300&#39; height=&#39;166&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"300\" data-rawheight=\"166\" data-original-token=\"v2-867546b22393f03a32d60277c9a19d36\" data-thumbnail=\"https://pic3.zhimg.com/v2-867546b22393f03a32d60277c9a19d36_b.jpg\" class=\"content_image lazy\" width=\"300\" data-actualsrc=\"https://pic3.zhimg.com/v2-867546b22393f03a32d60277c9a19d36_b.gif\"/></figure><p></p>", "excerpt": "没想到竟然上了日报。好方…… ——————————————— 大学时我对编程语言有过一些困惑：应该学哪个编程语言？到底怎么样才算是掌握了编程语言？5年前我幸运地看到了垠神在新浪博客的一篇文章 - 如何掌握程序语言，里面推荐阅读SICP的前三章。我读过之后，受益良多。 曾几何时，SICP是每个MIT CS学生大一就要上的基础课的教材，毕竟是世界名校，比我们的课程不知高到哪里去了。然鹅，时过境迁，MIT的教授们已经停止这门…", "excerpt_new": "没想到竟然上了日报。好方…… ——————————————— 大学时我对编程语言有过一些困惑：应该学哪个编程语言？到底怎么样才算是掌握了编程语言？5年前我幸运地看到了垠神在新浪博客的一篇文章 - 如何掌握程序语言，里面推荐阅读SICP的前三章。我读过之后，受益良多。 曾几何时，SICP是每个MIT CS学生大一就要上的基础课的教材，毕竟是世界名校，比我们的课程不知高到哪里去了。然鹅，时过境迁，MIT的教授们已经停止这门…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1517132451, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1516860578016", "type": "feed", "target": {"id": "293896242", "type": "answer", "url": "https://api.zhihu.com/answers/293896242", "voteup_count": 22114, "thanks_count": 4344, "question": {"id": "31870607", "title": "如何评价电影《无问西东》?", "url": "https://api.zhihu.com/questions/31870607", "type": "question", "question_type": "normal", "created": 1435887604, "answer_count": 3358, "comment_count": 81, "follower_count": 13502, "detail": "<p><a href=\"https://link.zhihu.com/?target=https%3A//v.qq.com/x/cover/wagzbx91asjomnu.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">腾讯视频正在热播：点击观看《无问西东》</a></p>", "excerpt": "<a href=\"https://link.zhihu.com/?target=https%3A//v.qq.com/x/cover/wagzbx91asjomnu.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">腾讯视频正在热播：点击观看《无问西东》</a>", "bound_topic_ids": [68, 2191, 12418, 47917, 207585], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "474fd4fc46702582b6dc8703d2296e85", "name": "羡慕曲筱绡的关关", "headline": "一个loser而已。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/zhang-yue-jia-70", "url_token": "zhang-yue-jia-70", "avatar_url": "https://picx.zhimg.com/v2-fbc54990add0d1ffb797a9fcacae9061_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1515687007, "created_time": 1515678148, "author": {"id": "363ff0b1536c151cd14ee01734484fe8", "name": "花痴女王", "headline": "资深娱评人，创意生活爱好者，资深猫奴", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/still-40", "url_token": "still-40", "avatar_url": "https://picx.zhimg.com/e46052d73a6f2e08d8993f6afddf0213_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["综艺"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "综艺话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "a4939b69ea2eb04c6dbed2219b64c43c", "avatar_url": "https://pica.zhimg.com/a4939b69ea2eb04c6dbed2219b64c43c_720w.jpg?source=32738c0c", "description": "", "id": "19618569", "name": "综艺", "priority": 0, "token": "19618569", "type": "topic", "url": "https://www.zhihu.com/topic/19618569"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}], "title": "综艺话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": false, "comment_count": 1912, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"5Lf9q1j7\">在《前任3》横扫票房，不停在微博朋友圈刷爆话题的时候，我揣着纸巾，悄咪咪地去了隔壁影厅，看了另外一部电影的点映——</p><p data-pid=\"FhuAJ1a-\"><b>《无问西东》。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-df4ce23f70b97bb3aa27842939491663_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1542\" data-original-token=\"v2-df4ce23f70b97bb3aa27842939491663\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-df4ce23f70b97bb3aa27842939491663_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;1542&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1542\" data-original-token=\"v2-df4ce23f70b97bb3aa27842939491663\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-df4ce23f70b97bb3aa27842939491663_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-df4ce23f70b97bb3aa27842939491663_b.jpg\"/></figure><p data-pid=\"qxIDue3R\">《无问西东》是我很久以前就期待一部电影。</p><p data-pid=\"WIVY4kPr\"><b>大概从六年前，2012年，我就听到过这名字。</b></p><p data-pid=\"MoGdyfag\">当时，我在新闻上看到，章子怡、张震、黄晓明、王力宏、陈楚生……这些大牌一起拍了一部名字非常文艺的青春片。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"iMH7GjKZ\">（后来去查了资料才知道，这是出自清华的校歌“立德立言 无问西东”，本意是“做学问不分中西”，后来延伸到一种“不计较得失 无所畏惧”的人生态度，电影当然是说后者）</p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-8950cfbc846257b77842feebe752d143_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"600\" data-rawheight=\"858\" data-original-token=\"v2-8950cfbc846257b77842feebe752d143\" class=\"origin_image zh-lightbox-thumb\" width=\"600\" data-original=\"https://picx.zhimg.com/v2-8950cfbc846257b77842feebe752d143_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;600&#39; height=&#39;858&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"600\" data-rawheight=\"858\" data-original-token=\"v2-8950cfbc846257b77842feebe752d143\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"600\" data-original=\"https://picx.zhimg.com/v2-8950cfbc846257b77842feebe752d143_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-8950cfbc846257b77842feebe752d143_b.jpg\"/></figure><p data-pid=\"W0IdciDQ\">我，文艺青年嘛，就喜欢这种调调，看到新闻那一刻就记住了这部电影。 </p><p data-pid=\"i7aIy4j3\"><b>结果没想到，一等就是六年。</b></p><p data-pid=\"wIL2Q5eR\">直到今年才终于看到它。</p><figure data-size=\"normal\"><noscript><img src=\"https://pica.zhimg.com/v2-65af151d12b9d0dfb8c006ee93ff03e2_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"426\" data-original-token=\"v2-65af151d12b9d0dfb8c006ee93ff03e2\" class=\"origin_image zh-lightbox-thumb\" width=\"1000\" data-original=\"https://pica.zhimg.com/v2-65af151d12b9d0dfb8c006ee93ff03e2_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1000&#39; height=&#39;426&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"426\" data-original-token=\"v2-65af151d12b9d0dfb8c006ee93ff03e2\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1000\" data-original=\"https://pica.zhimg.com/v2-65af151d12b9d0dfb8c006ee93ff03e2_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-65af151d12b9d0dfb8c006ee93ff03e2_b.jpg\"/></figure><p data-pid=\"SUtVbesA\">等待时间是很长。</p><p data-pid=\"JmDxN38l\">质量稍微差一点的电影，经过这六年的折腾，早就被淘汰了。</p><p data-pid=\"tfxERG5K\">但《无问西东》没有。</p><p data-pid=\"dK0Ut5Ah\">前两天，我看完了点映场之后，还是深深被打动了。（尽管我早就不是当年的文艺腔）</p><p data-pid=\"gWSuWe_H\">我想把它推荐给你们。</p><p data-pid=\"x_TZY-em\"><b>因为，《无问西东》是我看过，最特别的青春片了。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-d041f04e5e8bdaf2db66588405542947_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"446\" data-rawheight=\"188\" data-original-token=\"v2-d041f04e5e8bdaf2db66588405542947\" data-thumbnail=\"https://picx.zhimg.com/v2-d041f04e5e8bdaf2db66588405542947_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"446\" data-original=\"https://picx.zhimg.com/v2-d041f04e5e8bdaf2db66588405542947_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;446&#39; height=&#39;188&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"446\" data-rawheight=\"188\" data-original-token=\"v2-d041f04e5e8bdaf2db66588405542947\" data-thumbnail=\"https://picx.zhimg.com/v2-d041f04e5e8bdaf2db66588405542947_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"446\" data-original=\"https://picx.zhimg.com/v2-d041f04e5e8bdaf2db66588405542947_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-d041f04e5e8bdaf2db66588405542947_b.gif\"/></figure><p data-pid=\"AteJJwJu\">▼</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"EBS8S3md\"><b>说它特别，是因为它时间跨度特别大。</b></p><p data-pid=\"PoOCfBvI\">绝大多数的青春片，为了引起特定年龄段人的共鸣，都是特定某一个时期的青春。</p><p data-pid=\"_z5uJ-Wi\">比如，《你好旧时光》，是2004年的高中。</p><p data-pid=\"laWQyR2y\">《致我们终将逝去的青春》，是1990年代的大学。</p><p data-pid=\"idS7CW6-\">还有冯小刚的《芳华》，是1970-1980年的文工团。</p><p data-pid=\"CZaQjt9w\">这些片子，有很重的时代痕迹，打的是怀旧牌。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-dc043f2da4c6a1e6b48be9790f454060_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"459\" data-original-token=\"v2-dc043f2da4c6a1e6b48be9790f454060\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-dc043f2da4c6a1e6b48be9790f454060_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;459&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"459\" data-original-token=\"v2-dc043f2da4c6a1e6b48be9790f454060\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-dc043f2da4c6a1e6b48be9790f454060_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-dc043f2da4c6a1e6b48be9790f454060_b.jpg\"/></figure><p data-pid=\"CECChzTP\">△其实《无问西东》很多场景也很怀旧很有历史感，但他们的整个故事不是为了怀旧</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wlNCYekU\">但《无问西东》，不同。 </p><p data-pid=\"cvMN2emS\"><b>它的时间线，从1923年民国开始到2010年的现代，跨度将近100年。</b> </p><p data-pid=\"NxyN7Gom\">这100年里，又穿插了四段完全不同主题的故事。</p><p data-pid=\"ckzD8heV\">其中只有张震演的那段都市青年，是离我们比较近的。</p><p data-pid=\"cy3Ji8Kd\">其他，都是1920年代，1930年代，1960年代的事情。</p><p data-pid=\"r4hP8BEL\">别说怀旧了，大家可能连电影里说的时代背景都未必很熟悉。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-a464558c7121bf924152b1e77535bdd7_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"426\" data-original-token=\"v2-a464558c7121bf924152b1e77535bdd7\" class=\"origin_image zh-lightbox-thumb\" width=\"1000\" data-original=\"https://pic2.zhimg.com/v2-a464558c7121bf924152b1e77535bdd7_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1000&#39; height=&#39;426&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"426\" data-original-token=\"v2-a464558c7121bf924152b1e77535bdd7\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1000\" data-original=\"https://pic2.zhimg.com/v2-a464558c7121bf924152b1e77535bdd7_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-a464558c7121bf924152b1e77535bdd7_b.jpg\"/></figure><p data-pid=\"1eM9MjDN\">△离我们最远的，应该是陈楚生演的这段，1923年，发生在北京（那时候还叫北平）清华大学里的故事。如果不看电影，我们根本很难想象那时候的年轻人在做什么。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"2juB8SBO\">那既然是这样，为什么还要这样拍？</p><p data-pid=\"EeseS1Sh\"><b>因为《无问西东》，想说的，不是某个人，或者某个群体的记忆。</b></p><p data-pid=\"Qlarbz4E\">而是整个中国，所有人，包括那些死去的人的青春。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-5409701f2bb2a298132c3d2733895f4f_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"160\" data-original-token=\"v2-5409701f2bb2a298132c3d2733895f4f\" data-thumbnail=\"https://pic4.zhimg.com/v2-5409701f2bb2a298132c3d2733895f4f_b.jpg\" class=\"content_image\" width=\"320\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;320&#39; height=&#39;160&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"160\" data-original-token=\"v2-5409701f2bb2a298132c3d2733895f4f\" data-thumbnail=\"https://pic4.zhimg.com/v2-5409701f2bb2a298132c3d2733895f4f_b.jpg\" class=\"content_image lazy\" width=\"320\" data-actualsrc=\"https://pic4.zhimg.com/v2-5409701f2bb2a298132c3d2733895f4f_b.gif\"/></figure><p data-pid=\"fzhRyYdS\">△连空战都有，虽然这场景跟诺兰的《敦刻尔克》有距离，但不得不说，《无问西东》是一部很宏大的青春片。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"rg9hf1zR\">为了还原这100年的历史，导演和工作人员，在筹备阶段，就看了超过百万字的资料。</p><p data-pid=\"p57gwWZQ\"><b>手工制作生产服饰近万件，还原各种历史建筑、场景与道具。</b></p><p data-pid=\"xbaXh3wu\">看电影的时候，你会感觉到这是一部相当恢宏的片子，绝对不是简单的青春怀旧。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-94f44056f124564fd51681b37e8f1b5d_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"459\" data-original-token=\"v2-94f44056f124564fd51681b37e8f1b5d\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-94f44056f124564fd51681b37e8f1b5d_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;459&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"459\" data-original-token=\"v2-94f44056f124564fd51681b37e8f1b5d\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-94f44056f124564fd51681b37e8f1b5d_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-94f44056f124564fd51681b37e8f1b5d_b.jpg\"/></figure><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-a630286bd5caf8f2f380b6a67b7bd87b_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"459\" data-original-token=\"v2-a630286bd5caf8f2f380b6a67b7bd87b\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-a630286bd5caf8f2f380b6a67b7bd87b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;459&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"459\" data-original-token=\"v2-a630286bd5caf8f2f380b6a67b7bd87b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-a630286bd5caf8f2f380b6a67b7bd87b_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-a630286bd5caf8f2f380b6a67b7bd87b_b.jpg\"/></figure><p data-pid=\"3kEaItVG\">△这部片子是用胶片拍的，也可能是最后一部胶片拍的国产片了。就画面而言，美爆了。我随手截的这些剧照，都觉得好像画一样漂亮啊。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WvyaqzAB\">▼</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Q5nQhnYR\">光看主题，《无问西东》已经比其他青春片格局大很多了。</p><p data-pid=\"pJ0Lhxlc\"><b>人物，《无问西东》也比其他青春片要丰富、立体。</b></p><p data-pid=\"tP0yA7mp\">它关注的，不再是校园或群体生活，也没有什么学渣、学霸、好人、坏人的设置。</p><p data-pid=\"HKAWHrfK\">戏里面的每一个主角，性格都非常突出，他们都代表了一种青春个性。</p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-c591c1497c3fe67007d38b35c509e207_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"640\" data-original-token=\"v2-c591c1497c3fe67007d38b35c509e207\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-c591c1497c3fe67007d38b35c509e207_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;640&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"640\" data-original-token=\"v2-c591c1497c3fe67007d38b35c509e207\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-c591c1497c3fe67007d38b35c509e207_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-c591c1497c3fe67007d38b35c509e207_b.jpg\"/></figure><p data-pid=\"SdJ4bZlL\">像离我们最近的张震。 </p><p data-pid=\"ATFq3UXx\">他演的是在广告公司工作的张果果。（这名字实在是太萌了）</p><p data-pid=\"BIkrvxd_\">表面看他是一个冷静内敛、心思缜密的人。</p><p data-pid=\"sKc5Zhul\">（甚至一开始你可能会觉得他有点冷血，以为他是那种为了钱不顾一切的人）</p><figure data-size=\"normal\"><noscript><img src=\"https://pica.zhimg.com/v2-63e1e439ba17846ba2803a6c495db040_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"463\" data-original-token=\"v2-63e1e439ba17846ba2803a6c495db040\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-63e1e439ba17846ba2803a6c495db040_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;463&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"463\" data-original-token=\"v2-63e1e439ba17846ba2803a6c495db040\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-63e1e439ba17846ba2803a6c495db040_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-63e1e439ba17846ba2803a6c495db040_b.jpg\"/></figure><p data-pid=\"7IqDDMgl\">但实际上，他是一个内心很有温度，做事很有原则的人。 </p><p data-pid=\"fWJ6N7Z_\">虽然他身处的行业是一个竞争非常激烈的地方，到处都是算计、心机，可是张果果并不愿意就这样堕落，他纠结，他反抗。</p><p data-pid=\"mhd83vsZ\">最终他甚至决定放弃利益，去做自己想做的事情。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-9c86cb1a4f9b3070084e09324c632574_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"463\" data-rawheight=\"209\" data-original-token=\"v2-9c86cb1a4f9b3070084e09324c632574\" class=\"origin_image zh-lightbox-thumb\" width=\"463\" data-original=\"https://pic1.zhimg.com/v2-9c86cb1a4f9b3070084e09324c632574_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;463&#39; height=&#39;209&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"463\" data-rawheight=\"209\" data-original-token=\"v2-9c86cb1a4f9b3070084e09324c632574\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"463\" data-original=\"https://pic1.zhimg.com/v2-9c86cb1a4f9b3070084e09324c632574_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-9c86cb1a4f9b3070084e09324c632574_b.jpg\"/></figure><p data-pid=\"ViMgSpub\">张震说，他很喜欢这样一个真诚的角色。</p><p data-pid=\"O3IYtEpU\">这个角色代表的是年轻人的“初心”，他想要表达的是——</p><p data-pid=\"t_d_pZl9\"><b>“你想要随波逐流，还是喜欢忠于自己？”</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-5271367f5fddba9c98532e45696998e2_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"440\" data-rawheight=\"211\" data-original-token=\"v2-5271367f5fddba9c98532e45696998e2\" class=\"origin_image zh-lightbox-thumb\" width=\"440\" data-original=\"https://pic1.zhimg.com/v2-5271367f5fddba9c98532e45696998e2_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;440&#39; height=&#39;211&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"440\" data-rawheight=\"211\" data-original-token=\"v2-5271367f5fddba9c98532e45696998e2\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"440\" data-original=\"https://pic1.zhimg.com/v2-5271367f5fddba9c98532e45696998e2_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-5271367f5fddba9c98532e45696998e2_b.jpg\"/></figure><p data-pid=\"1j9b4iH5\">王力宏。</p><p data-pid=\"jamCMCSR\">一如既往的帅气。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-9f5a779ff66011a94ae88814016809a3_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"606\" data-original-token=\"v2-9f5a779ff66011a94ae88814016809a3\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-9f5a779ff66011a94ae88814016809a3_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;606&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"606\" data-original-token=\"v2-9f5a779ff66011a94ae88814016809a3\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-9f5a779ff66011a94ae88814016809a3_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-9f5a779ff66011a94ae88814016809a3_b.jpg\"/></figure><p data-pid=\"x2QnKS3V\">但在帅气之余，他又多了一些硬朗、气概。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-7faad59ee4fc96cad4ed78523827b939_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"459\" data-rawheight=\"207\" data-original-token=\"v2-7faad59ee4fc96cad4ed78523827b939\" class=\"origin_image zh-lightbox-thumb\" width=\"459\" data-original=\"https://pic4.zhimg.com/v2-7faad59ee4fc96cad4ed78523827b939_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;459&#39; height=&#39;207&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"459\" data-rawheight=\"207\" data-original-token=\"v2-7faad59ee4fc96cad4ed78523827b939\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"459\" data-original=\"https://pic4.zhimg.com/v2-7faad59ee4fc96cad4ed78523827b939_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-7faad59ee4fc96cad4ed78523827b939_b.jpg\"/></figure><p data-pid=\"ZdjxU_9a\">在《无问西东》里，王力宏演的是抗日时期的在西南联大求学的一名清华大学学生沈光耀。 </p><p data-pid=\"FEOozJzf\">他家境好，学习也好，用现在的话说，就是前途无可限量，注定是要成为“霸道总裁”一类的人物。</p><p data-pid=\"N82al-3d\">可是最后，他却选择了最艰难的一条路，弃笔从戎，当空军抗日去了。</p><p data-pid=\"E3kvK-4G\"><b>王力宏的角色，代表的是青春的“盛放”。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-62b9606a9fd9af7acdb232aea46e4931_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"426\" data-original-token=\"v2-62b9606a9fd9af7acdb232aea46e4931\" class=\"origin_image zh-lightbox-thumb\" width=\"1000\" data-original=\"https://pic2.zhimg.com/v2-62b9606a9fd9af7acdb232aea46e4931_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1000&#39; height=&#39;426&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"426\" data-original-token=\"v2-62b9606a9fd9af7acdb232aea46e4931\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1000\" data-original=\"https://pic2.zhimg.com/v2-62b9606a9fd9af7acdb232aea46e4931_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-62b9606a9fd9af7acdb232aea46e4931_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Wu1taPOm\">章子怡一直是我欣赏的演员。</p><p data-pid=\"vwNbuOth\">这次，她也贡献了一个难忘的角色。</p><p data-pid=\"ykZYgyMB\">王敏佳。</p><p data-pid=\"_XnJjoaR\">1962年北京医院里的一个中医实习生。</p><p data-pid=\"pRVzgKaj\">她漂亮，倔强，可爱。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-a0d2b40bdca609f9a8e050c116f2ad69_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1024\" data-rawheight=\"436\" data-original-token=\"v2-a0d2b40bdca609f9a8e050c116f2ad69\" class=\"origin_image zh-lightbox-thumb\" width=\"1024\" data-original=\"https://pic2.zhimg.com/v2-a0d2b40bdca609f9a8e050c116f2ad69_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1024&#39; height=&#39;436&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1024\" data-rawheight=\"436\" data-original-token=\"v2-a0d2b40bdca609f9a8e050c116f2ad69\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1024\" data-original=\"https://pic2.zhimg.com/v2-a0d2b40bdca609f9a8e050c116f2ad69_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-a0d2b40bdca609f9a8e050c116f2ad69_b.jpg\"/></figure><p data-pid=\"m33ooSSD\">可惜，在最美的年华里，她因为一个小小的谎言，被背叛，被诬陷，生活就此走向下坡路。</p><p data-pid=\"QUzkVLrL\">但是，王敏佳并没有就此而放弃。</p><p data-pid=\"dwf0i1BM\"><b>她一直勇敢地追寻自己的内心，爱情，她代表的是“敢勇”。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-1aeeb1de9c2abd0882e8dee7cedc2004_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1024\" data-rawheight=\"474\" data-original-token=\"v2-1aeeb1de9c2abd0882e8dee7cedc2004\" class=\"origin_image zh-lightbox-thumb\" width=\"1024\" data-original=\"https://pic1.zhimg.com/v2-1aeeb1de9c2abd0882e8dee7cedc2004_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1024&#39; height=&#39;474&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1024\" data-rawheight=\"474\" data-original-token=\"v2-1aeeb1de9c2abd0882e8dee7cedc2004\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1024\" data-original=\"https://pic1.zhimg.com/v2-1aeeb1de9c2abd0882e8dee7cedc2004_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-1aeeb1de9c2abd0882e8dee7cedc2004_b.jpg\"/></figure><p data-pid=\"PFjKxNVi\">还有黄晓明的陈鹏。</p><p data-pid=\"Epgg7F9_\">他和章子怡是同一个年代，同一个故事里的主人公。</p><p data-pid=\"SfSzK33N\">在看之前，大家都很担心，黄晓明的“总裁我最帅”演技会拖垮这部戏的内涵。</p><p data-pid=\"uo1e02jY\">不过放心，并没有。</p><p data-pid=\"aCkKrF4w\">小明同学在这戏里演得很好。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-31bffba6bd48d2d91e9747aab4284e10_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"462\" data-original-token=\"v2-31bffba6bd48d2d91e9747aab4284e10\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-31bffba6bd48d2d91e9747aab4284e10_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;462&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"462\" data-original-token=\"v2-31bffba6bd48d2d91e9747aab4284e10\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-31bffba6bd48d2d91e9747aab4284e10_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-31bffba6bd48d2d91e9747aab4284e10_b.jpg\"/></figure><p data-pid=\"BAUwzP-q\">他的笑容是灿烂的。</p><p data-pid=\"5Pqvf1Ev\">他对王敏佳的爱，是明媚的。</p><p data-pid=\"nY3Q15wW\">我最喜欢是他拉着王敏佳的手在清华校园奔跑那一段，啊，那是真正的青春荷尔蒙。</p><p data-pid=\"eMoOgVtP\">像媒体人吕彦妮说的，<b>“黄晓明有了不动声色的力量，一直在跑，跑起来是虎，虽不怎么说话，却满是浪漫”。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-f0a65ed0cb288e47b0aa7b0007bac623_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-f0a65ed0cb288e47b0aa7b0007bac623\" data-thumbnail=\"https://pic4.zhimg.com/v2-f0a65ed0cb288e47b0aa7b0007bac623_b.jpg\" class=\"content_image\" width=\"320\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;320&#39; height=&#39;162&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-f0a65ed0cb288e47b0aa7b0007bac623\" data-thumbnail=\"https://pic4.zhimg.com/v2-f0a65ed0cb288e47b0aa7b0007bac623_b.jpg\" class=\"content_image lazy\" width=\"320\" data-actualsrc=\"https://pic4.zhimg.com/v2-f0a65ed0cb288e47b0aa7b0007bac623_b.gif\"/></figure><p data-pid=\"4LIMn0td\">△黄晓明说：“我们这一对在电影中代表的是所有人的爱，希望每个人都能做到，无所畏惧。”</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"n6sHdrt9\"><b>而这几个主演里面，我最喜欢，也最惊喜的一个人物，竟然是陈楚生演的吴岭澜。</b></p><p data-pid=\"2YZK_I0R\">我从没想过陈楚生的演技会这么好。</p><p data-pid=\"LlMt5uQE\">他演的是1923年在北平清华大学读书的学生。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-0876dc098e643c95a2cb4d4fe15fc474_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"460\" data-original-token=\"v2-0876dc098e643c95a2cb4d4fe15fc474\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-0876dc098e643c95a2cb4d4fe15fc474_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;460&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"460\" data-original-token=\"v2-0876dc098e643c95a2cb4d4fe15fc474\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-0876dc098e643c95a2cb4d4fe15fc474_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-0876dc098e643c95a2cb4d4fe15fc474_b.jpg\"/></figure><p data-pid=\"4j7Hj4O4\">他在那里，见到了很多影响时代的大人物：</p><p data-pid=\"HNq0cUlG\">泰戈尔、徐志摩、梁启超、梅贻琦……</p><p data-pid=\"ZN3ketqc\">在这些人的影响下，吴岭澜开始思考：</p><p data-pid=\"q2QMeYyc\"><b>什么是真实？什么是自由？</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-eea824af91784ca84b0ddd82be2f0594_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"475\" data-rawheight=\"205\" data-original-token=\"v2-eea824af91784ca84b0ddd82be2f0594\" class=\"origin_image zh-lightbox-thumb\" width=\"475\" data-original=\"https://pic1.zhimg.com/v2-eea824af91784ca84b0ddd82be2f0594_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;475&#39; height=&#39;205&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"475\" data-rawheight=\"205\" data-original-token=\"v2-eea824af91784ca84b0ddd82be2f0594\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"475\" data-original=\"https://pic1.zhimg.com/v2-eea824af91784ca84b0ddd82be2f0594_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-eea824af91784ca84b0ddd82be2f0594_b.jpg\"/></figure><figure data-size=\"normal\"><noscript><img src=\"https://pica.zhimg.com/v2-99759c54c928071ea53cff8152f6f3f4_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"476\" data-rawheight=\"207\" data-original-token=\"v2-99759c54c928071ea53cff8152f6f3f4\" class=\"origin_image zh-lightbox-thumb\" width=\"476\" data-original=\"https://pica.zhimg.com/v2-99759c54c928071ea53cff8152f6f3f4_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;476&#39; height=&#39;207&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"476\" data-rawheight=\"207\" data-original-token=\"v2-99759c54c928071ea53cff8152f6f3f4\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"476\" data-original=\"https://pica.zhimg.com/v2-99759c54c928071ea53cff8152f6f3f4_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-99759c54c928071ea53cff8152f6f3f4_b.jpg\"/></figure><p data-pid=\"-AVMn6LK\">和其他主角比起来，吴岭澜的戏份并不多。</p><p data-pid=\"vFHwVclO\">但是，陈楚生简单几段戏，<b>就演出了一个温文尔雅，但内心自有一股力量的知识分子模样。</b></p><p data-pid=\"d-oGCdbQ\">让我不禁对这样笃定的人，心驰神往啊~~~~</p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-99ce0e60e4eedfecd4d00a396efc432f_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"820\" data-rawheight=\"1744\" data-original-token=\"v2-99ce0e60e4eedfecd4d00a396efc432f\" class=\"origin_image zh-lightbox-thumb\" width=\"820\" data-original=\"https://pic4.zhimg.com/v2-99ce0e60e4eedfecd4d00a396efc432f_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;820&#39; height=&#39;1744&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"820\" data-rawheight=\"1744\" data-original-token=\"v2-99ce0e60e4eedfecd4d00a396efc432f\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"820\" data-original=\"https://pic4.zhimg.com/v2-99ce0e60e4eedfecd4d00a396efc432f_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-99ce0e60e4eedfecd4d00a396efc432f_b.jpg\"/></figure><p data-pid=\"IaCN2XdJ\">△看完这部电影入了陈楚生的坑</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"CJJ5kEIK\">除了主角魅力非凡，<b>这部电影的配角，也是牛逼闪闪的。</b></p><p data-pid=\"1nvh_qCN\">看完电影，你会对祖峰演的梅贻琦</p><figure data-size=\"normal\"><noscript><img src=\"https://pica.zhimg.com/v2-f7405dac414fd965832b9666d8df7e4c_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"425\" data-original-token=\"v2-f7405dac414fd965832b9666d8df7e4c\" class=\"origin_image zh-lightbox-thumb\" width=\"1000\" data-original=\"https://pica.zhimg.com/v2-f7405dac414fd965832b9666d8df7e4c_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1000&#39; height=&#39;425&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"425\" data-original-token=\"v2-f7405dac414fd965832b9666d8df7e4c\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1000\" data-original=\"https://pica.zhimg.com/v2-f7405dac414fd965832b9666d8df7e4c_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-f7405dac414fd965832b9666d8df7e4c_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"6FzR1qAy\">米雪演的沈妈妈，印象深刻。</p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-17ebce8b3f5310adeda35237a116e123_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"426\" data-original-token=\"v2-17ebce8b3f5310adeda35237a116e123\" class=\"origin_image zh-lightbox-thumb\" width=\"1000\" data-original=\"https://picx.zhimg.com/v2-17ebce8b3f5310adeda35237a116e123_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1000&#39; height=&#39;426&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"426\" data-original-token=\"v2-17ebce8b3f5310adeda35237a116e123\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1000\" data-original=\"https://picx.zhimg.com/v2-17ebce8b3f5310adeda35237a116e123_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-17ebce8b3f5310adeda35237a116e123_b.jpg\"/></figure><p data-pid=\"M4eYTuaj\">△米雪这部戏真的好美，一点儿都不比章子怡逊色。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"95sVLBBb\">他们就是每段青春里都会出现的人生导师。</p><p data-pid=\"qnxtobtD\">有了他们，这些精神世界强大的人，我们这些后来者的人生，才会变得豁然开朗。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-859de6535517260fc3f2dbdc2d2da0cc_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"451\" data-rawheight=\"189\" data-original-token=\"v2-859de6535517260fc3f2dbdc2d2da0cc\" data-thumbnail=\"https://pic3.zhimg.com/v2-859de6535517260fc3f2dbdc2d2da0cc_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"451\" data-original=\"https://pic3.zhimg.com/v2-859de6535517260fc3f2dbdc2d2da0cc_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;451&#39; height=&#39;189&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"451\" data-rawheight=\"189\" data-original-token=\"v2-859de6535517260fc3f2dbdc2d2da0cc\" data-thumbnail=\"https://pic3.zhimg.com/v2-859de6535517260fc3f2dbdc2d2da0cc_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"451\" data-original=\"https://pic3.zhimg.com/v2-859de6535517260fc3f2dbdc2d2da0cc_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-859de6535517260fc3f2dbdc2d2da0cc_b.gif\"/></figure><p data-pid=\"XpwDl2uG\">△米雪这段叫儿子不要去当兵的戏很棒，“我不希望你还没想好怎么过这一生，就连命都没有了”。你可以看出一个母亲对儿子真正的爱，这一段好催泪。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ImR891Kx\">▼</p><p data-pid=\"XnMyGNDe\"><b>除了上面这些以外，这部戏最打动我的，还是情感。</b></p><p data-pid=\"ZK0l_YUX\">我本来以为像《你好旧时光》这样，没有车祸堕胎打架这类狗血事，老老实实地讲青春故事，描写青春期悸动的已经是很好的片子了。</p><p data-pid=\"0coK7H-8\"><b>但没想到，《无问西东》拍的情感，比这些还要深，还要美。</b></p><p data-pid=\"sH_KIP0b\">它关注的不是小情小爱，不是初恋情怀。 </p><p data-pid=\"yKQpQGI8\"><b>而是我们作为一个年轻人，应该怎么活？我们应该怎样真实地面对自我？</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-b7cc34fe442a92fb351c01da9752f911_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"486\" data-rawheight=\"209\" data-original-token=\"v2-b7cc34fe442a92fb351c01da9752f911\" class=\"origin_image zh-lightbox-thumb\" width=\"486\" data-original=\"https://pic2.zhimg.com/v2-b7cc34fe442a92fb351c01da9752f911_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;486&#39; height=&#39;209&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"486\" data-rawheight=\"209\" data-original-token=\"v2-b7cc34fe442a92fb351c01da9752f911\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"486\" data-original=\"https://pic2.zhimg.com/v2-b7cc34fe442a92fb351c01da9752f911_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-b7cc34fe442a92fb351c01da9752f911_b.jpg\"/></figure><p data-pid=\"Cjsxfshr\">△黄晓明说自己看了三遍哭了三遍。章子怡说看剧本就哭了，张震也说非常喜欢这种简单的情感。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"S0KB09P-\">故事里的五个主角，每个人都有迷茫、痛苦的时候：</p><p data-pid=\"op0als2Q\">吴岭澜和沈光耀被战争打断了学习、研究，被迫从北京迁到了云南的西南联大。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-6911a70761c14b9e4708c4eb6eb1eb90_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-6911a70761c14b9e4708c4eb6eb1eb90\" data-thumbnail=\"https://pic3.zhimg.com/v2-6911a70761c14b9e4708c4eb6eb1eb90_b.jpg\" class=\"content_image\" width=\"320\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;320&#39; height=&#39;162&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-6911a70761c14b9e4708c4eb6eb1eb90\" data-thumbnail=\"https://pic3.zhimg.com/v2-6911a70761c14b9e4708c4eb6eb1eb90_b.jpg\" class=\"content_image lazy\" width=\"320\" data-actualsrc=\"https://pic3.zhimg.com/v2-6911a70761c14b9e4708c4eb6eb1eb90_b.gif\"/></figure><p data-pid=\"qd6rq-oX\">△西南联大这一段拍得特别好，特别美。在战火纷飞的环境下学习，看了以后，你大概才会明白我们现在能在一张安静的书桌上读书是多么幸福的事情。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"3Hl1UDfM\">王敏佳因为被诬陷，失去了自己的美貌和喜欢的人。</p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-d1b405fd6cb76da2212130730a9e6219_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-d1b405fd6cb76da2212130730a9e6219\" data-thumbnail=\"https://picx.zhimg.com/v2-d1b405fd6cb76da2212130730a9e6219_b.jpg\" class=\"content_image\" width=\"320\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;320&#39; height=&#39;162&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-d1b405fd6cb76da2212130730a9e6219\" data-thumbnail=\"https://picx.zhimg.com/v2-d1b405fd6cb76da2212130730a9e6219_b.jpg\" class=\"content_image lazy\" width=\"320\" data-actualsrc=\"https://picx.zhimg.com/v2-d1b405fd6cb76da2212130730a9e6219_b.gif\"/></figure><p data-pid=\"JqK5D33J\">张果果则是被工作中各种利益撕扯着，被上司当作权力斗争的工具，开始怀疑自己的选择。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-7ceea77abe53bd0c1cae1d661e028a65_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"451\" data-rawheight=\"189\" data-original-token=\"v2-7ceea77abe53bd0c1cae1d661e028a65\" data-thumbnail=\"https://pic2.zhimg.com/v2-7ceea77abe53bd0c1cae1d661e028a65_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"451\" data-original=\"https://pic2.zhimg.com/v2-7ceea77abe53bd0c1cae1d661e028a65_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;451&#39; height=&#39;189&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"451\" data-rawheight=\"189\" data-original-token=\"v2-7ceea77abe53bd0c1cae1d661e028a65\" data-thumbnail=\"https://pic2.zhimg.com/v2-7ceea77abe53bd0c1cae1d661e028a65_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"451\" data-original=\"https://pic2.zhimg.com/v2-7ceea77abe53bd0c1cae1d661e028a65_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-7ceea77abe53bd0c1cae1d661e028a65_b.gif\"/></figure><p data-pid=\"lfpyCcOz\">可是，无论他们怎么纠结，怎么彷徨，他们也没有迷失自我。</p><p data-pid=\"bo_zQVkF\">他们的选择最终都是善良的、勇敢的，不会以伤害别人为代价的。</p><p data-pid=\"q07ny0Ej\">关于《无问西东》的主题，章子怡在采访中说过——</p><p data-pid=\"1R3RTXPl\"><b>就是一个字，“善”。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><blockquote data-pid=\"QpmLmeqH\">“每一个故事，其实都是洋溢着美好的青春岁月，他们活着更多时候不只是为了自己，还有别人。<br/><br/>像我演的王敏佳，她那么小的一个角色，在最痛苦的时候，也没有出卖别人，坚持住了自己的善良，更何况是历史上那些有名有姓的人。”</blockquote><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-f7f93544df959aa0f5674d196a20d046_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"477\" data-rawheight=\"209\" data-original-token=\"v2-f7f93544df959aa0f5674d196a20d046\" class=\"origin_image zh-lightbox-thumb\" width=\"477\" data-original=\"https://pic3.zhimg.com/v2-f7f93544df959aa0f5674d196a20d046_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;477&#39; height=&#39;209&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"477\" data-rawheight=\"209\" data-original-token=\"v2-f7f93544df959aa0f5674d196a20d046\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"477\" data-original=\"https://pic3.zhimg.com/v2-f7f93544df959aa0f5674d196a20d046_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-f7f93544df959aa0f5674d196a20d046_b.jpg\"/></figure><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-e8a81b9dd8a3a5ae6f23f5b3017c8043_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"478\" data-rawheight=\"203\" data-original-token=\"v2-e8a81b9dd8a3a5ae6f23f5b3017c8043\" class=\"origin_image zh-lightbox-thumb\" width=\"478\" data-original=\"https://pic4.zhimg.com/v2-e8a81b9dd8a3a5ae6f23f5b3017c8043_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;478&#39; height=&#39;203&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"478\" data-rawheight=\"203\" data-original-token=\"v2-e8a81b9dd8a3a5ae6f23f5b3017c8043\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"478\" data-original=\"https://pic4.zhimg.com/v2-e8a81b9dd8a3a5ae6f23f5b3017c8043_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-e8a81b9dd8a3a5ae6f23f5b3017c8043_b.jpg\"/></figure><p data-pid=\"SIM5v_nQ\">△就算面临要被惩罚的命运，王敏佳也没有出卖朋友。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"fvtrP9rE\">我很喜欢章子怡的这个解释，也很喜欢这个片子关于“善”的串联。</p><p data-pid=\"lAaBAFOZ\">四个故事之间，看似毫无关系，毫无牵连，<b>但实际上所有人的命运都有某种羁绊。</b></p><p data-pid=\"D6-Hb8tc\">是吴岭澜的勇敢，影响了沈光耀。</p><p data-pid=\"OTadOxOh\">沈光耀的善良，又帮助了陈鹏。</p><p data-pid=\"GPF_Ooxd\">陈鹏又用他的真诚，（间接）感动了张果果。</p><p data-pid=\"5XikH7pZ\">（具体关系是怎样，我就不剧透了，你们可以去电影院看）</p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-aaf18aae5af2025cc77760b17ce20935_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-aaf18aae5af2025cc77760b17ce20935\" data-thumbnail=\"https://pic2.zhimg.com/v2-aaf18aae5af2025cc77760b17ce20935_b.jpg\" class=\"content_image\" width=\"320\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;320&#39; height=&#39;162&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-aaf18aae5af2025cc77760b17ce20935\" data-thumbnail=\"https://pic2.zhimg.com/v2-aaf18aae5af2025cc77760b17ce20935_b.jpg\" class=\"content_image lazy\" width=\"320\" data-actualsrc=\"https://pic2.zhimg.com/v2-aaf18aae5af2025cc77760b17ce20935_b.gif\"/></figure><p data-pid=\"bG2ywAEs\">△每个故事都有关联这一点真的很棒</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"CQo8_le7\">一层层看下来，你会发现，没有一个人的故事，是孤立的，没有一个人的成长，是可以独立存在的。</p><p data-pid=\"nvjYpxm8\">这就是导演李芳芳想表达的东西。</p><p class=\"ztext-empty-paragraph\"><br/></p><blockquote data-pid=\"T3OjnnfA\">“打通不同群体之间的桥梁是善意，很多时候就是你做出的选择。<br/>60年代你选择做不做拿起石头（打倒别人）的人，抗战时期要不要做一个为国奋战的飞行员，20年代国门刚刚打开，你能不能拥抱西方的知识，同时坚守传承。”</blockquote><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Hd1XeC5n\">你做的这些事情，看似只是为了你自己。</p><p data-pid=\"LxUGzfWY\">但如果你坚守住了，<b>它就会像蝴蝶效应一样，一点点传递下去，最终慢慢地影响整个社会。</b></p><p data-pid=\"SPPn1NwN\"><b>你的善良，不是一个人的，而是我们每个人，都本该如此。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-ef552653d81335a796d941f92eda826a_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"382\" data-rawheight=\"161\" data-original-token=\"v2-ef552653d81335a796d941f92eda826a\" data-thumbnail=\"https://pic1.zhimg.com/v2-ef552653d81335a796d941f92eda826a_b.jpg\" class=\"content_image\" width=\"382\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;382&#39; height=&#39;161&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"382\" data-rawheight=\"161\" data-original-token=\"v2-ef552653d81335a796d941f92eda826a\" data-thumbnail=\"https://pic1.zhimg.com/v2-ef552653d81335a796d941f92eda826a_b.jpg\" class=\"content_image lazy\" width=\"382\" data-actualsrc=\"https://pic1.zhimg.com/v2-ef552653d81335a796d941f92eda826a_b.gif\"/></figure><p data-pid=\"vemeIpt4\">看，这立意是不是很棒？ </p><p data-pid=\"a6rqtQSz\">但很可惜，现在环境越来越浮躁了，有很多人并不能看明白这一点。</p><p data-pid=\"iFpeQEvL\">我在看电影的时候，旁边就有两个人，一直不停地说：</p><p data-pid=\"glfg0vcY\"><b>“卧槽，这电影好作啊，怎么可能会有这么傻的人。”</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-5e7508c605a4703679b01a4b6db0bb27_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"571\" data-rawheight=\"307\" data-original-token=\"v2-5e7508c605a4703679b01a4b6db0bb27\" class=\"origin_image zh-lightbox-thumb\" width=\"571\" data-original=\"https://pic2.zhimg.com/v2-5e7508c605a4703679b01a4b6db0bb27_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;571&#39; height=&#39;307&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"571\" data-rawheight=\"307\" data-original-token=\"v2-5e7508c605a4703679b01a4b6db0bb27\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"571\" data-original=\"https://pic2.zhimg.com/v2-5e7508c605a4703679b01a4b6db0bb27_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-5e7508c605a4703679b01a4b6db0bb27_b.jpg\"/></figure><p data-pid=\"4wJrVnsc\">△电影里这句台词挺真相的，不是所有人都能接受这么慢这么深的电影</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"mYjBP2lv\">唉，听到这句话的时候，其实我挺难过的。</p><p data-pid=\"hkwPYluy\">我难过的，不是一部这么用心的电影，没有得到承认。</p><p data-pid=\"xDvtTOB1\"><b>而是，原来大部分的人，已经不能接受这么纯粹的感情；</b></p><p data-pid=\"XDrYY8u3\"><b>原来现在很多的人，已经不会相信我们可以这么勇敢去爱了。</b></p><p data-pid=\"wByJO_Er\">这是多么可悲的一件事。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-f2f36d845d0d220ab28cd28ad31b5b5a_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1024\" data-rawheight=\"436\" data-original-token=\"v2-f2f36d845d0d220ab28cd28ad31b5b5a\" class=\"origin_image zh-lightbox-thumb\" width=\"1024\" data-original=\"https://pic1.zhimg.com/v2-f2f36d845d0d220ab28cd28ad31b5b5a_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1024&#39; height=&#39;436&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1024\" data-rawheight=\"436\" data-original-token=\"v2-f2f36d845d0d220ab28cd28ad31b5b5a\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1024\" data-original=\"https://pic1.zhimg.com/v2-f2f36d845d0d220ab28cd28ad31b5b5a_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-f2f36d845d0d220ab28cd28ad31b5b5a_b.jpg\"/></figure><p data-pid=\"bAjHAU4k\">想到这里，我就忍不住写下这篇文章。</p><p data-pid=\"-VvXHgMa\">当是鸡汤也好，自来水安利也罢。</p><p data-pid=\"fcmYiGLt\">我还是很希望大家有时间可以去看看这部特别的片子。</p><p data-pid=\"95Vce1zF\">或许它的情感，在我们现在的人看来，是很傻，很天真。</p><p data-pid=\"ZDg1-WJh\">但是，就像电影里说的，<b>我们现在的生活就是从过去的“善意”积累而来的。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-59d096e5866b1ebf6e2d5215c91fe7c0_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"472\" data-rawheight=\"208\" data-original-token=\"v2-59d096e5866b1ebf6e2d5215c91fe7c0\" class=\"origin_image zh-lightbox-thumb\" width=\"472\" data-original=\"https://pic1.zhimg.com/v2-59d096e5866b1ebf6e2d5215c91fe7c0_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;472&#39; height=&#39;208&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"472\" data-rawheight=\"208\" data-original-token=\"v2-59d096e5866b1ebf6e2d5215c91fe7c0\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"472\" data-original=\"https://pic1.zhimg.com/v2-59d096e5866b1ebf6e2d5215c91fe7c0_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-59d096e5866b1ebf6e2d5215c91fe7c0_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ap1Zedob\">只有回望过去，我们才会发现：</p><blockquote data-pid=\"RRtERX0Q\">原来青春不是只有恋爱、读书和各种乱七八糟的荷尔蒙。<br/>在这些琐碎之上。<br/><b>我们还可以追求浪漫、诗意以及值得付出一切的——</b> <b>信仰。</b></blockquote><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-2adc43e5a0ef00c72d1b6a69e4fcd19f_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-2adc43e5a0ef00c72d1b6a69e4fcd19f\" data-thumbnail=\"https://picx.zhimg.com/v2-2adc43e5a0ef00c72d1b6a69e4fcd19f_b.jpg\" class=\"content_image\" width=\"320\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;320&#39; height=&#39;162&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"162\" data-original-token=\"v2-2adc43e5a0ef00c72d1b6a69e4fcd19f\" data-thumbnail=\"https://picx.zhimg.com/v2-2adc43e5a0ef00c72d1b6a69e4fcd19f_b.jpg\" class=\"content_image lazy\" width=\"320\" data-actualsrc=\"https://picx.zhimg.com/v2-2adc43e5a0ef00c72d1b6a69e4fcd19f_b.gif\"/></figure><p data-pid=\"agZplZ8t\">最后，把电影里的一段台词送给你们。</p><p data-pid=\"7BWU4r7k\">无论做到与否，我还是很喜欢这里面的价值观——</p><p class=\"ztext-empty-paragraph\"><br/></p><blockquote data-pid=\"roucp3Eq\">“世俗是这样强大，强大到你们无法忽视它的存在。<br/>可是如果了解到青春只有这些日子，不知道你们是否还会在意那些世俗让你们在意的事情，比如占有什么才荣耀，拥有什么才被爱？”<br/><br/><b>“爱你所爱，无问西东。”</b> <b>“听从你心，无问西东。”</b></blockquote><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-f47fb0905e73b9633d55dda2ae4e04bd_b.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"160\" data-original-token=\"v2-f47fb0905e73b9633d55dda2ae4e04bd\" data-thumbnail=\"https://pic4.zhimg.com/v2-f47fb0905e73b9633d55dda2ae4e04bd_b.jpg\" class=\"content_image\" width=\"320\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;320&#39; height=&#39;160&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"320\" data-rawheight=\"160\" data-original-token=\"v2-f47fb0905e73b9633d55dda2ae4e04bd\" data-thumbnail=\"https://pic4.zhimg.com/v2-f47fb0905e73b9633d55dda2ae4e04bd_b.jpg\" class=\"content_image lazy\" width=\"320\" data-actualsrc=\"https://pic4.zhimg.com/v2-f47fb0905e73b9633d55dda2ae4e04bd_b.gif\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"EtxFpt_Q\">花痴女王|文</p><p data-pid=\"h7KOyR8M\">喜欢文章的欢迎关注我的微信公众号“花吃了那女孩”（ID：huachinvwang）</p>", "excerpt": "在《前任3》横扫票房，不停在微博朋友圈刷爆话题的时候，我揣着纸巾，悄咪咪地去了隔壁影厅，看了另外一部电影的点映—— <b>《无问西东》。</b>《无问西东》是我很久以前就期待一部电影。 <b>大概从六年前，2012年，我就听到过这名字。</b>当时，我在新闻上看到，章子怡、张震、黄晓明、王力宏、陈楚生……这些大牌一起拍了一部名字非常文艺的青春片。 （后来去查了资料才知道，这是出自清华的校歌“立德立言 无问西东”，本意是“做学问不分…", "excerpt_new": "在《前任3》横扫票房，不停在微博朋友圈刷爆话题的时候，我揣着纸巾，悄咪咪地去了隔壁影厅，看了另外一部电影的点映—— <b>《无问西东》。</b>《无问西东》是我很久以前就期待一部电影。 <b>大概从六年前，2012年，我就听到过这名字。</b>当时，我在新闻上看到，章子怡、张震、黄晓明、王力宏、陈楚生……这些大牌一起拍了一部名字非常文艺的青春片。 （后来去查了资料才知道，这是出自清华的校歌“立德立言 无问西东”，本意是“做学问不分…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {"REACTION_CONTENT_SEGMENT_LIKE": "HIDE"}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "disallowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1516860578, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1516780650340", "type": "feed", "target": {"id": "122181939", "type": "answer", "url": "https://api.zhihu.com/answers/122181939", "voteup_count": 413, "thanks_count": 155, "question": {"id": "24466160", "title": "Medium 上有哪些优秀的 UI/UX 设计师？", "url": "https://api.zhihu.com/questions/24466160", "type": "question", "question_type": "normal", "created": 1405180894, "answer_count": 8, "comment_count": 1, "follower_count": 2339, "detail": "", "excerpt": "", "bound_topic_ids": [386, 1990, 3005, 3835, 63840], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "d96f6e374757be081fc1bbddddc5ff40", "name": "知乎用户9193cY", "headline": "伪文艺青年一枚!", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/skycn", "url_token": "skycn", "avatar_url": "https://pica.zhimg.com/53b03cb38_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1488965632, "created_time": 1473995347, "author": {"id": "76bf91c42fb87ac19655c873c4288fda", "name": "逗砂", "headline": "我们藉由知识接近神灵。公众号：AIGC研修社", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/dousha", "url_token": "dousha", "avatar_url": "https://pica.zhimg.com/v2-db718603dc1ce826a7d122271e2a13fc_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://pica.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": false, "comment_count": 15, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"d78gmQ1k\"><strong>值得关注的顶尖的设计师</strong></p><br/><br/><p data-pid=\"TPxccBom\"><figure><noscript><img src=\"https://picx.zhimg.com/844860f4bd03051fa2c1911f20c2c153_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"844860f4bd03051fa2c1911f20c2c153\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"844860f4bd03051fa2c1911f20c2c153\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://picx.zhimg.com/844860f4bd03051fa2c1911f20c2c153_b.png\"/></figure><strong>Julie Zhuo   </strong></p><p data-pid=\"SPlMjoor\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40joulee\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@joulee</span><span class=\"invisible\"></span></a></p><p data-pid=\"wBQxC93H\">Facebook 产品设计总监，斯坦福大学毕业。食物、游戏和文字爱好者。写过很多广为流传的好文，也算是互联网设计圈里华人职位最高的设计师之一了。</p><br/><br/><p data-pid=\"YR1-uPCv\"><figure><noscript><img src=\"https://pic1.zhimg.com/2ba4be3e219b9a89f6685c911c5b4e82_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"2ba4be3e219b9a89f6685c911c5b4e82\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"2ba4be3e219b9a89f6685c911c5b4e82\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic1.zhimg.com/2ba4be3e219b9a89f6685c911c5b4e82_b.png\"/></figure><strong>Jared M. Spool</strong></p><p data-pid=\"hXvJpKnB\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40jmspool\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@jmspool</span><span class=\"invisible\"></span></a></p><p data-pid=\"5Fs73Z8V\">UIE创始人。CenterCentre联合创始人。一直致力于探索用户体验。CenterCentre貌似是个培养UX设计师的学校，查了下，这货09年的时候还来中国做过演讲。</p><br/><br/><figure><noscript><img src=\"https://pica.zhimg.com/f3022eb6c27b86b9f902400fe0ac8146_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"f3022eb6c27b86b9f902400fe0ac8146\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"f3022eb6c27b86b9f902400fe0ac8146\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pica.zhimg.com/f3022eb6c27b86b9f902400fe0ac8146_b.png\"/></figure><p data-pid=\"-jbAYXuf\"><strong>Tobias van Schneider  </strong></p><p data-pid=\"6_Q14xr8\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40vanschneider\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@vanschneide</span><span class=\"invisible\">r</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"QxSSq4oN\">此人算是界面设计这两年最火的网红之一，如果你关注国外的设计动向，你几乎可以在任何一个平台上看到他的身影。哥们是德国人，现生活在纽约。小时候很苦逼属于外国蓝翔出来自学成才的设计师，之前是Spotify的首席设计师和艺术指导，拿过很多奖，开过自己的工作室。</p><br/><br/><figure><noscript><img src=\"https://picx.zhimg.com/2a83072e8facb1f6b91b3f3fccfa2c83_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"2a83072e8facb1f6b91b3f3fccfa2c83\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"2a83072e8facb1f6b91b3f3fccfa2c83\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://picx.zhimg.com/2a83072e8facb1f6b91b3f3fccfa2c83_b.png\"/></figure><p data-pid=\"et9rORiC\"><strong>Khoi Vinh</strong></p><p data-pid=\"76nfS1IW\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40khoi\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@khoi</span><span class=\"invisible\"></span></a></p><p data-pid=\"YtZOxCFQ\">被评为“在美国五十个最具影响力的设计师”之一。 <a href=\"https://link.zhihu.com/?target=http%3A//Trywildcard.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">Trywildcard.com</span><span class=\"invisible\"></span></a>用户体验副总裁，前纽约时报设计总监。写过《秩序之美—网页中的风格设计》此书已经有中文版。</p><br/><br/><figure><noscript><img src=\"https://pic3.zhimg.com/8f3576ae6f7ba733fbf248a812b357c2_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"8f3576ae6f7ba733fbf248a812b357c2\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"8f3576ae6f7ba733fbf248a812b357c2\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic3.zhimg.com/8f3576ae6f7ba733fbf248a812b357c2_b.png\"/></figure><p data-pid=\"dBG81INV\"><strong>Brian Chesky</strong></p><p data-pid=\"Hfo06F8d\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40bchesky\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@bchesky</span><span class=\"invisible\"></span></a></p><p data-pid=\"8rG_M7I2\">Airbnb联合创始人、首席执行官，也是个设计师。从一个失业的设计师到美国最有影响力的100人之一，进入了福布斯40岁以下的富豪榜。详细八卦可以看张小哈的漫画<a href=\"https://zhuanlan.zhihu.com/p/22373358\" class=\"internal\">Airbnb创始人：从失业设计师到公司估值250亿的逆袭 - 啊哈时刻 - 知乎专栏</a>。</p><br/><br/><p data-pid=\"mj2qaWMX\"><figure><noscript><img src=\"https://pic3.zhimg.com/864449b1316b2540d88b0eadf33052ca_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"864449b1316b2540d88b0eadf33052ca\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"864449b1316b2540d88b0eadf33052ca\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic3.zhimg.com/864449b1316b2540d88b0eadf33052ca_b.png\"/></figure><strong>Chris Messina</strong></p><p data-pid=\"1cPNIVx9\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40chrismessina\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@chrismessin</span><span class=\"invisible\">a</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"6TIItB5_\">优步的用户体验总监，前谷歌用户体验设计师。曾发表长文对 Google+ 进行了严厉批评。全文感人肺腑,发人深省╮(╯▽╰)╭。</p><br/><br/><figure><noscript><img src=\"https://picx.zhimg.com/aab8fdbc6183ff06d653724d7f495f09_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"aab8fdbc6183ff06d653724d7f495f09\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"aab8fdbc6183ff06d653724d7f495f09\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://picx.zhimg.com/aab8fdbc6183ff06d653724d7f495f09_b.png\"/></figure><p data-pid=\"7wCNa2EI\"><strong>Joel Gascoigne</strong></p><p data-pid=\"fh_rguLY\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40joelgascoigne\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@joelgascoig</span><span class=\"invisible\">ne</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"AHN16F12\">社交分享工具Buffer的创始人兼CEO。专注于精益创业和提升用户幸福感。36氪上能找到他很多创业相关的文章。</p><br/><br/><figure><noscript><img src=\"https://pic1.zhimg.com/4b49e293238e5ef724e3a6e70935fca6_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"4b49e293238e5ef724e3a6e70935fca6\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"4b49e293238e5ef724e3a6e70935fca6\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic1.zhimg.com/4b49e293238e5ef724e3a6e70935fca6_b.png\"/></figure><p data-pid=\"BarmGWlE\"><strong>Daniel Burka</strong></p><p data-pid=\"BhY7dhK1\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40dburka\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@dburka</span><span class=\"invisible\"></span></a></p><p data-pid=\"Gqg0Cmme\">GoogleVentures的设计合伙人，前Digg 的首席设计师，一个经验老道的产品设计师和企业家。</p><br/><br/><figure><noscript><img src=\"https://pic1.zhimg.com/34d469f61a94a4d84cab162513a7aa00_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"34d469f61a94a4d84cab162513a7aa00\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"34d469f61a94a4d84cab162513a7aa00\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic1.zhimg.com/34d469f61a94a4d84cab162513a7aa00_b.png\"/></figure><p data-pid=\"f9mtTRTV\"><strong>Andy Budd</strong></p><p data-pid=\"i3hEaWE-\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40andybudd\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@andybudd</span><span class=\"invisible\"></span></a></p><p data-pid=\"AyCm1Str\">用户体验设计师，clearleft合伙人 、@dconstruct和@uxlondon的管理者。</p><br/><br/><p data-pid=\"b8zyP1Sa\"><br/><figure><noscript><img src=\"https://pic3.zhimg.com/3aae1752258a92157d40ba1ec8d6bee8_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"3aae1752258a92157d40ba1ec8d6bee8\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"3aae1752258a92157d40ba1ec8d6bee8\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic3.zhimg.com/3aae1752258a92157d40ba1ec8d6bee8_b.png\"/></figure><strong>Jake Knapp</strong></p><p data-pid=\"RjC9OROR\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40jakek\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@jakek</span><span class=\"invisible\"></span></a></p><p data-pid=\"bf3-rOrj\">GoogleVentures的设计合伙人，“设计冲刺”流程发明者,著有《设计冲刺》一书。（我已经买了 ）</p><br/><br/><p data-pid=\"hzDlnJ3r\"><figure><noscript><img src=\"https://pica.zhimg.com/933545ccf38e2bd6895f23ed926225ca_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"933545ccf38e2bd6895f23ed926225ca\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"933545ccf38e2bd6895f23ed926225ca\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pica.zhimg.com/933545ccf38e2bd6895f23ed926225ca_b.png\"/></figure><strong>Jeffrey Zeldman</strong></p><p data-pid=\"h_wCnyqF\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40zeldman\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@zeldman</span><span class=\"invisible\"></span></a></p><p data-pid=\"WcEpM02t\">万维网标准之王，制定了万维网标准终止了浏览器之战，最早一批Web设计师之一，之前他曾担任艺术总监和广告文案撰稿人。1995年，他开始建立最有影响力的个人站点撰写Web设计方面理论的经验教程，该网站拥有广泛的读者。</p><br/><br/><figure><noscript><img src=\"https://pic3.zhimg.com/2405ec41c6196f2ea4d5b39eaebf0692_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"2405ec41c6196f2ea4d5b39eaebf0692\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"2405ec41c6196f2ea4d5b39eaebf0692\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic3.zhimg.com/2405ec41c6196f2ea4d5b39eaebf0692_b.png\"/></figure><p data-pid=\"tyZWZMrr\"><b>Addy Osmani</b><br/></p><p data-pid=\"pGazFC8O\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40zeldman\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">https://medium.com/@addyosmani</a></p><p data-pid=\"XTmGKW7P\">谷歌的前端工程师，创建了 TodoMVC、jQuery UI Bootstrap、jQuery Plugin Patterns 和 Backbone Paginator，出版了《Learning JavaScript Design Patterns》和《Developing Backbone.js Applications》两本技术书籍，而且还是著名的 jQuery、Modernizr 以及 Backbone.js 框架的贡献者。</p><br/><br/><figure><noscript><img src=\"https://pica.zhimg.com/ee14020b286a84b45902e9b7dc199d42_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"ee14020b286a84b45902e9b7dc199d42\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"ee14020b286a84b45902e9b7dc199d42\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pica.zhimg.com/ee14020b286a84b45902e9b7dc199d42_b.png\"/></figure><p data-pid=\"uLjANMxW\"><b>Samuel Hulick</b></p><p data-pid=\"w1kfOh2J\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40zeldman\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">https://medium.com/@samuelhulick</a></p><p data-pid=\"wMa1PFLv\">UserOnboard的创始人。连续创业者，写过很多用户体验方面的文章。</p><br/><br/><p data-pid=\"qjXkgb1m\"><br/><figure><noscript><img src=\"https://pic2.zhimg.com/41b2581f8d80b8e9cbe8246e9c5dc813_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"41b2581f8d80b8e9cbe8246e9c5dc813\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"41b2581f8d80b8e9cbe8246e9c5dc813\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic2.zhimg.com/41b2581f8d80b8e9cbe8246e9c5dc813_b.png\"/></figure><strong>Nathan Curtis</strong></p><p data-pid=\"mRmz3f8F\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40nathanacurtis\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@nathanacurt</span><span class=\"invisible\">is</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"j4vutIRo\">UX firm的创始人。演说家，作家。</p><br/><br/><br/><figure><noscript><img src=\"https://pica.zhimg.com/629e9074f3c5800e3488f2a42755c362_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"629e9074f3c5800e3488f2a42755c362\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"629e9074f3c5800e3488f2a42755c362\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pica.zhimg.com/629e9074f3c5800e3488f2a42755c362_b.png\"/></figure><p data-pid=\"n8Y3CEAk\"><b>Patrick Neeman</b><br/></p><p data-pid=\"jPit-Tht\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40usabilitycounts\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@usabilityco</span><span class=\"invisible\">unts</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"wqoFagOm\">用户体验设计师。经营着<a href=\"https://link.zhihu.com/?target=http%3A//uxdrinkinggame.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">uxdrinkinggame.com</span><span class=\"invisible\"></span></a> 和 <a href=\"https://link.zhihu.com/?target=http%3A//usabilitycounts.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">usabilitycounts.com</span><span class=\"invisible\"></span></a>。</p><br/><br/><figure><noscript><img src=\"https://pic4.zhimg.com/de7a55e0f9375402e0a045ec6cd19c59_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"de7a55e0f9375402e0a045ec6cd19c59\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"de7a55e0f9375402e0a045ec6cd19c59\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic4.zhimg.com/de7a55e0f9375402e0a045ec6cd19c59_b.png\"/></figure><p data-pid=\"lQp9PCw8\"><strong>Jesse James Garrett</strong></p><p data-pid=\"x3Mj1HMR\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40jjg\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@jjg</span><span class=\"invisible\"></span></a></p><p data-pid=\"ZkHbXFAF\">用户体验设计师。ajax之父。用户体验设计咨询公司Adaptive Path的共同创始人和首席创意官。那本著名的《用户体验要素》的作者。</p><br/><br/><figure><noscript><img src=\"https://pic3.zhimg.com/cf7251ca234a35a76819e0a0b43ab52a_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"cf7251ca234a35a76819e0a0b43ab52a\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"cf7251ca234a35a76819e0a0b43ab52a\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic3.zhimg.com/cf7251ca234a35a76819e0a0b43ab52a_b.png\"/></figure><p data-pid=\"dSHvAeL5\"><strong>Joshua Porter</strong></p><p data-pid=\"0VTRWfU8\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40jjg\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">https://medium.com/@bokardo</a></p><p data-pid=\"X6QhhhFO\">产品设计师，Rocket Insights的联合创始人。</p><br/><br/><p data-pid=\"efT-LYSH\">————————————————————————————————————————</p><br/><br/><p data-pid=\"4YsTkZ-H\"><strong>值得关注的新锐设计师</strong><br/></p><br/><br/><p data-pid=\"N6Vfyv7k\"><br/><figure><noscript><img src=\"https://picx.zhimg.com/17fffa246b07f9f406b2850886d76f85_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"17fffa246b07f9f406b2850886d76f85\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"17fffa246b07f9f406b2850886d76f85\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://picx.zhimg.com/17fffa246b07f9f406b2850886d76f85_b.png\"/></figure><strong>Andrew Coyle</strong></p><p data-pid=\"jo0tcqcH\"><a href=\"https://link.zhihu.com/?target=https%3A//uxdesign.cc/%40CoyleAndrew\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">uxdesign.cc/@CoyleAndre</span><span class=\"invisible\">w</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"BbuZgeTj\">Flexport的产品设计总监 | 作品详见 <a href=\"https://link.zhihu.com/?target=http%3A//andrewcoyle.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Andrew Coyle</a>。</p><br/><br/><p data-pid=\"7Cm9sQnD\"><br/><figure><noscript><img src=\"https://pic1.zhimg.com/ac627a8fdfc0508d9c23f3d4810be176_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"ac627a8fdfc0508d9c23f3d4810be176\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"ac627a8fdfc0508d9c23f3d4810be176\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic1.zhimg.com/ac627a8fdfc0508d9c23f3d4810be176_b.png\"/></figure><strong>Jessie Chen</strong></p><p data-pid=\"OYYixXOm\"><a href=\"https://link.zhihu.com/?target=https%3A//uxdesign.cc/%40lovejessiecat\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">uxdesign.cc/@lovejessie</span><span class=\"invisible\">cat</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"2255az5N\">ZapLabs的UI/UX设计师，曾在旧金山艺术大学学习插画。</p><br/><br/><p data-pid=\"WZYag60J\"><br/><figure><noscript><img src=\"https://picx.zhimg.com/7af31013deb2f0f4bcf6002bb14475bb_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"7af31013deb2f0f4bcf6002bb14475bb\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"7af31013deb2f0f4bcf6002bb14475bb\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://picx.zhimg.com/7af31013deb2f0f4bcf6002bb14475bb_b.png\"/></figure><strong>Sacha Greif</strong></p><p data-pid=\"BaGezPPW\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40sachagreif\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@sachagreif</span><span class=\"invisible\"></span></a></p><p data-pid=\"AjAC9rIj\">来自巴黎的设计师，现在住在大阪。Sidebar和Folyo的创始人, 联合编写了Discover Meteor。</p><br/><br/><p data-pid=\"N77I0daY\"><br/><figure><noscript><img src=\"https://pic4.zhimg.com/cca5b3f243a7c6b77432f8ebc9da732b_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"cca5b3f243a7c6b77432f8ebc9da732b\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"cca5b3f243a7c6b77432f8ebc9da732b\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic4.zhimg.com/cca5b3f243a7c6b77432f8ebc9da732b_b.png\"/></figure><strong>Chantal Jandard</strong></p><p data-pid=\"2t-Ru0f4\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40chantastique\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@chantastiqu</span><span class=\"invisible\">e</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"EYa0U07p\">具有心理学和前端开发双重背景的设计师，像素是比利剑更强大的武器。</p><br/><br/><p data-pid=\"FsDnz0sx\"><br/><figure><noscript><img src=\"https://pic3.zhimg.com/3d2b68ab5f4e095fe49d6efcf033f464_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"3d2b68ab5f4e095fe49d6efcf033f464\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"3d2b68ab5f4e095fe49d6efcf033f464\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic3.zhimg.com/3d2b68ab5f4e095fe49d6efcf033f464_b.png\"/></figure><strong>Michael Abehsera</strong></p><p data-pid=\"GWAv_ZV9\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40michaelabehsera\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@michaelabeh</span><span class=\"invisible\">sera</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"_b9t1ScV\"><a href=\"https://link.zhihu.com/?target=http%3A//michaelabehsera.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">michaelabehsera.com</span><span class=\"invisible\"></span></a>的UI设计师和前端开发工程师。</p><br/><br/><p data-pid=\"YbF_eSR1\"><br/><figure><noscript><img src=\"https://pica.zhimg.com/416266ddffa36ae501f7a565ad432d46_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"416266ddffa36ae501f7a565ad432d46\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"416266ddffa36ae501f7a565ad432d46\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pica.zhimg.com/416266ddffa36ae501f7a565ad432d46_b.png\"/></figure><strong>Gabriel Tomescu</strong></p><p data-pid=\"oqvkEQ_j\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40gabrieltomescu\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@gabrieltome</span><span class=\"invisible\">scu</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"PmC2lJvl\">产品设计师。</p><br/><br/><figure><noscript><img src=\"https://pic4.zhimg.com/053cedb9cd03da11475a96bb91d6d677_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"053cedb9cd03da11475a96bb91d6d677\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"053cedb9cd03da11475a96bb91d6d677\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic4.zhimg.com/053cedb9cd03da11475a96bb91d6d677_b.png\"/></figure><p data-pid=\"dwFvZ366\"><strong>Jon H. Pittman</strong></p><p data-pid=\"Ma8UiUM8\"><a href=\"https://link.zhihu.com/?target=https%3A//uxdesign.cc/%40jonhpittman\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">uxdesign.cc/@jonhpittma</span><span class=\"invisible\">n</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"mujqydVU\">在Autodesk工作的设计师，对设计、技术与商业的交叉领域感兴趣。</p><br/><br/><figure><noscript><img src=\"https://pic3.zhimg.com/2ddf7f5cab71ae593f585501d713ee82_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"2ddf7f5cab71ae593f585501d713ee82\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"2ddf7f5cab71ae593f585501d713ee82\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic3.zhimg.com/2ddf7f5cab71ae593f585501d713ee82_b.png\"/></figure><p data-pid=\"dynXSSWG\"><strong>Adrian Zumbrunnen</strong></p><p data-pid=\"wt1sdr3w\"><a href=\"https://link.zhihu.com/?target=https%3A//uxdesign.cc/%40azumbrunnen\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">uxdesign.cc/@azumbrunne</span><span class=\"invisible\">n</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"KtSMDkxj\">google的设计师，之前在iA工作，会就设计写一些文章和发布演讲。</p><br/><br/><figure><noscript><img src=\"https://pic2.zhimg.com/1deb4ab7a8db0ab3186770885f8f29bf_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"1deb4ab7a8db0ab3186770885f8f29bf\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"1deb4ab7a8db0ab3186770885f8f29bf\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic2.zhimg.com/1deb4ab7a8db0ab3186770885f8f29bf_b.png\"/></figure><p data-pid=\"4zFpHl3L\"><strong>Joanna Ngai</strong></p><p data-pid=\"9OdLnUIA\"><a href=\"https://link.zhihu.com/?target=https%3A//uxdesign.cc/%40ngai.yt\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">uxdesign.cc/@ngai.yt</span><span class=\"invisible\"></span></a></p><p data-pid=\"gHgCzwE5\">微软的UI设计师，插画师，喜欢喝绿茶。作品链接请戳<a href=\"https://link.zhihu.com/?target=http%3A//dribbble.com/joannan\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Joanna Ngai - Dribbble</a> | <a href=\"https://link.zhihu.com/?target=https%3A//tofusan.threadless.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">tofusan&#39;s Artist Shop</a></p><br/><br/><figure><noscript><img src=\"https://pic1.zhimg.com/7850bd063220f0bf4136340a7bc62d48_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"7850bd063220f0bf4136340a7bc62d48\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"7850bd063220f0bf4136340a7bc62d48\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic1.zhimg.com/7850bd063220f0bf4136340a7bc62d48_b.png\"/></figure><p data-pid=\"GCzufZDj\"><strong>César Bejarano</strong></p><p data-pid=\"ZUOMS0QN\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40caradecesar\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@caradecesar</span><span class=\"invisible\"></span></a></p><p data-pid=\"MxQHLa3o\">产品设计师和建筑师，个人blog戳<a href=\"https://link.zhihu.com/?target=http%3A//againstdesign.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Against Design</a>。</p><br/><br/><figure><noscript><img src=\"https://pic1.zhimg.com/d581b012b9ec7e59c9d2edaca4e66552_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"d581b012b9ec7e59c9d2edaca4e66552\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"d581b012b9ec7e59c9d2edaca4e66552\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic1.zhimg.com/d581b012b9ec7e59c9d2edaca4e66552_b.png\"/></figure><p data-pid=\"SY0TFn0A\"><strong>Caio Braga</strong></p><p data-pid=\"C7dTcnql\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40caioab\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@caioab</span><span class=\"invisible\"></span></a></p><p data-pid=\"sPQ6Qqd-\">UX设计师，<a href=\"https://link.zhihu.com/?target=https%3A//uxdesign.cc\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">uxdesign.cc</span><span class=\"invisible\"></span></a>的编辑。</p><br/><br/><figure><noscript><img src=\"https://pic2.zhimg.com/86f23cb11afad95a283b1d6d350f5abb_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"86f23cb11afad95a283b1d6d350f5abb\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"86f23cb11afad95a283b1d6d350f5abb\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic2.zhimg.com/86f23cb11afad95a283b1d6d350f5abb_b.png\"/></figure><p data-pid=\"pyT2m_Ch\"><strong>Kristof Orts</strong></p><p data-pid=\"gdkVNHUg\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40kristoforts\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@kristoforts</span><span class=\"invisible\"></span></a></p><p data-pid=\"GSXq4GG9\">数字产品设计师，会写一些关于产品、设计、灵感和技术方面的文章。</p><br/><br/><figure><noscript><img src=\"https://pic2.zhimg.com/176f7ab01e3378321dfacbd5cbf69577_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"176f7ab01e3378321dfacbd5cbf69577\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"176f7ab01e3378321dfacbd5cbf69577\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic2.zhimg.com/176f7ab01e3378321dfacbd5cbf69577_b.png\"/></figure><p data-pid=\"lH7-HK-9\"><strong>John Saito</strong></p><p data-pid=\"F20ccFFh\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40jsaito\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@jsaito</span><span class=\"invisible\"></span></a></p><p data-pid=\"USuWAZvA\">Dropbox设计师。之前在YouTube和Google工作。</p><br/><br/><figure><noscript><img src=\"https://pic1.zhimg.com/83ef711f18dcbba46bba5a4a7e459ba4_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"83ef711f18dcbba46bba5a4a7e459ba4\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"83ef711f18dcbba46bba5a4a7e459ba4\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic1.zhimg.com/83ef711f18dcbba46bba5a4a7e459ba4_b.png\"/></figure><p data-pid=\"ml2Sx8T0\"><strong>Joe Toscano</strong></p><p data-pid=\"Rp40zWPh\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40realjoet\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@realjoet</span><span class=\"invisible\"></span></a></p><p data-pid=\"XIZg6SpP\">google的用户体验设计师，<a href=\"https://link.zhihu.com/?target=http%3A//uxdesign.cc\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">uxdesign.cc</span><span class=\"invisible\"></span></a>和The Mission的特邀博主，运动员，试图用微笑、设计和代码改变世界。</p><br/><br/><figure><noscript><img src=\"https://pic3.zhimg.com/cae39c94f1828ad85aeaf0d645267cd0_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"cae39c94f1828ad85aeaf0d645267cd0\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"cae39c94f1828ad85aeaf0d645267cd0\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic3.zhimg.com/cae39c94f1828ad85aeaf0d645267cd0_b.png\"/></figure><p data-pid=\"4XA5aybb\"><strong>Laura Klein</strong></p><p data-pid=\"NRqyA4mP\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40lauraklein\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@lauraklein</span><span class=\"invisible\"></span></a></p><p data-pid=\"jeb6bFH9\">《UX for Lean Startups》一书的作者。会写关于UX、调研、产品管理和精益创业相关的内容的文章。</p><br/><br/><figure><noscript><img src=\"https://pic4.zhimg.com/8cf3749467cc9caaf7ef833713a47c07_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"8cf3749467cc9caaf7ef833713a47c07\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"8cf3749467cc9caaf7ef833713a47c07\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic4.zhimg.com/8cf3749467cc9caaf7ef833713a47c07_b.png\"/></figure><p data-pid=\"drAd-tqe\"><strong>Amber Cartwright</strong></p><p data-pid=\"U680wFEg\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40amberwrencart\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@amberwrenca</span><span class=\"invisible\">rt</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"f9OAwW2N\">Airbnb的设计经理。生活探索者。知识的探索者。</p><br/><br/><figure><noscript><img src=\"https://pic1.zhimg.com/1c339362b1a469e62c806a4c8c9f1794_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"1c339362b1a469e62c806a4c8c9f1794\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"1c339362b1a469e62c806a4c8c9f1794\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://pic1.zhimg.com/1c339362b1a469e62c806a4c8c9f1794_b.png\"/></figure><p data-pid=\"HXKwgcwd\"><strong>Sagi Shrieber</strong></p><p data-pid=\"_MSgaJyN\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40sagishrieber\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@sagishriebe</span><span class=\"invisible\">r</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"K1OP1uvw\">pixelperfectmag的创始人，谷歌校园创业的UX导师。</p><br/><br/><figure><noscript><img src=\"https://picx.zhimg.com/92a063542f262e4b8f1315ddc02d0af3_b.png\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"92a063542f262e4b8f1315ddc02d0af3\" class=\"content_image\" width=\"100\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;100&#39; height=&#39;100&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"100\" data-rawheight=\"100\" data-original-token=\"92a063542f262e4b8f1315ddc02d0af3\" class=\"content_image lazy\" width=\"100\" data-actualsrc=\"https://picx.zhimg.com/92a063542f262e4b8f1315ddc02d0af3_b.png\"/></figure><p data-pid=\"bPJY4v7-\"><strong>Mikael Cho</strong></p><p data-pid=\"WvyST_vv\"><a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40mikaelcho/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@mikaelcho/</span><span class=\"invisible\"></span></a></p><p data-pid=\"_2HCoYzH\">Crew的创始人。</p>", "excerpt": "<strong>值得关注的顶尖的设计师</strong> <strong><PERSON> </strong> <a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40joulee\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@joulee</span><span class=\"invisible\"></span></a>Facebook 产品设计总监，斯坦福大学毕业。食物、游戏和文字爱好者。写过很多广为流传的好文，也算是互联网设计圈里华人职位最高的设计师之一了。 <strong>Jared M. Spool</strong> <a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40jmspool\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@jmspool</span><span class=\"invisible\"></span></a>UIE创始人。CenterCentre联合创始人。一直致力于探索用户体验。CenterCentre貌似是个培养UX设计师的学校，查了下，这货09年的时候还来中国做过演讲。 <strong>Tobias van Schneider </strong>…", "excerpt_new": "<strong>值得关注的顶尖的设计师</strong> <strong><PERSON> </strong> <a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40joulee\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@joulee</span><span class=\"invisible\"></span></a>Facebook 产品设计总监，斯坦福大学毕业。食物、游戏和文字爱好者。写过很多广为流传的好文，也算是互联网设计圈里华人职位最高的设计师之一了。 <strong>Jared M. Spool</strong> <a href=\"https://link.zhihu.com/?target=https%3A//medium.com/%40jmspool\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">medium.com/@jmspool</span><span class=\"invisible\"></span></a>UIE创始人。CenterCentre联合创始人。一直致力于探索用户体验。CenterCentre貌似是个培养UX设计师的学校，查了下，这货09年的时候还来中国做过演讲。 <strong>Tobias van Schneider </strong>…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "need_payment", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1516780650, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1516780650340&page_num=70"}}