{"data": [{"id": "1516655467579", "type": "feed", "target": {"id": "265917569", "title": "目前对神经网络有哪些理论研究？", "url": "https://api.zhihu.com/questions/265917569", "type": "question", "question_type": "normal", "created": 1516495836, "answer_count": 22, "comment_count": 0, "follower_count": 2514, "detail": "<p>比如说  <PERSON>, <PERSON>, et al. &#34;Mathematics of Deep Learning.&#34; <i>arXiv preprint arXiv:1712.04741</i> (2017).</p>", "excerpt": "比如说 <PERSON>, <PERSON>, et al. &#34;Mathematics of Deep Learning.&#34; <i>arXiv preprint arXi…</i>", "bound_topic_ids": [3084, 19008, 89794], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "49d238c14c4ede4de75f21a21776a69d", "name": "邪恶总督", "headline": "迎接领导检查", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/jiapengzhang", "url_token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar_url": "https://picx.zhimg.com/v2-38398ab79f25cbaec547509291d35e29_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1516655467, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1516514047052", "type": "feed", "target": {"id": "25327755", "type": "article", "author": {"id": "06a67981ced7a2e9f07005185605685c", "name": "机器之心", "headline": "人工智能信息服务平台", "type": "people", "user_type": "organization", "url": "https://www.zhihu.com/people/ji-qi-zhi-xin-65", "url_token": "ji-qi-zhi-xin-65", "avatar_url": "https://picx.zhimg.com/v2-dd115d399e55c37e3312c8ee4713890e_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": true, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["数学", "人工智能"], "topics": []}, {"type": "identity", "description": "已认证机构号", "topic_names": [], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "数学等 2 个话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "v2-351d57389cf50b002a20606caac645cf", "avatar_url": "https://picx.zhimg.com/v2-351d57389cf50b002a20606caac645cf_720w.jpg?source=32738c0c", "description": "", "id": "19554091", "name": "数学", "priority": 0, "token": "19554091", "type": "topic", "url": "https://www.zhihu.com/topic/19554091"}, {"avatar_path": "v2-c41d10d22173d515740c43c70f885705", "avatar_url": "https://picx.zhimg.com/v2-c41d10d22173d515740c43c70f885705_720w.jpg?source=32738c0c", "description": "", "id": "19551275", "name": "人工智能", "priority": 0, "token": "19551275", "type": "topic", "url": "https://www.zhihu.com/topic/19551275"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}, {"badge_status": "passed", "description": "已认证机构号", "detail_type": "identity", "icon": "", "night_icon": "", "sources": [], "title": "认证", "type": "identity", "url": "https://www.zhihu.com/term/institution-settle"}], "title": "数学等 2 个话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "created": 1487571245, "updated": 1487571245, "title": "机器学习算法集锦：从贝叶斯到深度学习及各自优缺点", "excerpt_title": "", "content": "<blockquote data-pid=\"l48uheNu\"><p data-pid=\"VIugkv9B\"><em>在我们日常生活中所用到的推荐系统、智能图片美化应用和聊天机器人等应用中，各种各样的机器学习和数据处理算法正尽职尽责地发挥着自己的功效。本文筛选并简单介绍了一些最常见算法类别，还为每一个类别列出了一些实际的算法并简单介绍了它们的优缺点。</em></p></blockquote><figure><img src=\"https://pic1.zhimg.com/v2-7c6c25406abcba766ed618c226a49f92_1440w.png\" data-rawwidth=\"1151\" data-rawheight=\"703\" class=\"origin_image zh-lightbox-thumb\" width=\"1151\" data-original=\"https://pic1.zhimg.com/v2-7c6c25406abcba766ed618c226a49f92_r.jpg\" data-original-token=\"v2-7c6c25406abcba766ed618c226a49f92\"/></figure><br/><p data-pid=\"CHAJWfW_\"><a href=\"https://link.zhihu.com/?target=https%3A//static.coggle.it/diagram/WHeBqDIrJRk-kDDY\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">static.coggle.it/diagra</span><span class=\"invisible\">m/WHeBqDIrJRk-kDDY</span><span class=\"ellipsis\"></span></a></p><br/><br/><p data-pid=\"lFFaBRqW\">目录</p><br/><ul><li data-pid=\"auIF9xxO\"><p data-pid=\"BOobPNxA\">正则化算法（Regularization Algorithms）</p></li><li data-pid=\"rjguTZvf\"><p data-pid=\"4bNEITRs\">集成算法（Ensemble Algorithms）</p></li><li data-pid=\"i8EGgqcD\"><p data-pid=\"_3AkOhJe\">决策树算法（Decision Tree Algorithm）</p></li><li data-pid=\"m4jTxI1p\"><p data-pid=\"7GZjs9_B\">回归（Regression）</p></li><li data-pid=\"yN4k5VMz\"><p data-pid=\"oVSlkfKz\">人工神经网络（Artificial Neural Network）</p></li><li data-pid=\"lNkES6Gg\"><p data-pid=\"8J1afUfC\">深度学习（Deep Learning）</p></li><li data-pid=\"g6KSPZ1s\"><p data-pid=\"8LvPiG-9\">支持向量机（Support Vector Machine）</p></li><li data-pid=\"gGirTxOX\"><p data-pid=\"uFZTEaRW\">降维算法（Dimensionality Reduction Algorithms）</p></li><li data-pid=\"YWl8Aqy6\"><p data-pid=\"ogH2WFNu\">聚类算法（Clustering Algorithms）</p></li><li data-pid=\"LCzLj-hS\"><p data-pid=\"WABGOHKg\">基于实例的算法（Instance-based Algorithms）</p></li><li data-pid=\"2ZyLf_9g\"><p data-pid=\"muILNVCd\">贝叶斯算法（Bayesian Algorithms）</p></li><li data-pid=\"XjxM84rf\"><p data-pid=\"3G7SvPDL\">关联规则学习算法（Association Rule Learning Algorithms）</p></li><li data-pid=\"gq4I9V5B\"><p data-pid=\"JZOK4lkc\">图模型（Graphical Models）</p></li></ul><br/><p data-pid=\"3x3WdCuv\"><strong>正则化算法（Regularization Algorithms）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-5262baa79289a5586495b332acb8b3b2_1440w.png\" data-rawwidth=\"199\" data-rawheight=\"227\" class=\"content_image\" width=\"199\" data-original-token=\"v2-5262baa79289a5586495b332acb8b3b2\"/></figure><br/><p data-pid=\"FmBfYlh1\">它是另一种方法（通常是回归方法）的拓展，这种方法会基于模型复杂性对其进行惩罚，它喜欢相对简单能够更好的泛化的模型。</p><br/><p data-pid=\"C_UodCGe\"><strong>例子：</strong></p><br/><ul><li data-pid=\"QhW03I8_\"><p data-pid=\"KKoZMEDE\">岭回归（Ridge Regression）</p></li><li data-pid=\"g3SMQ57R\"><p data-pid=\"zIj8T-mJ\">最小绝对收缩与选择算子（LASSO）</p></li><li data-pid=\"b1S-JWF2\"><p data-pid=\"Nbm3i_nL\">GLASSO</p></li><li data-pid=\"HYKRTITk\"><p data-pid=\"CxWp3AlJ\">弹性网络（Elastic Net）</p></li><li data-pid=\"joSi_Tys\"><p data-pid=\"iiss_6-L\">最小角回归（Least-Angle Regression）</p></li></ul><br/><p data-pid=\"iO4tpdHC\"><strong>优点：</strong></p><br/><ul><li data-pid=\"CAo6l67A\"><p data-pid=\"MURdgDz8\">其惩罚会减少过拟合</p></li><li data-pid=\"BnUB3Bxv\"><p data-pid=\"U_WWogWa\">总会有解决方法</p></li></ul><br/><p data-pid=\"4Is2fpx9\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"vNkGT6z1\"><p data-pid=\"XYjJUbsC\">惩罚会造成欠拟合</p></li><li data-pid=\"mOZJumr1\"><p data-pid=\"0T39PT6Q\">很难校准</p></li></ul><br/><p data-pid=\"jI9S8rwS\"><strong>集成算法（Ensemble algorithms）</strong></p><br/><figure><img src=\"https://pica.zhimg.com/v2-2a081ed7fa4f74fa305aa37cac983164_1440w.png\" data-rawwidth=\"204\" data-rawheight=\"230\" class=\"content_image\" width=\"204\" data-original-token=\"v2-2a081ed7fa4f74fa305aa37cac983164\"/></figure><br/><p data-pid=\"h7oZf_1x\">集成方法是由多个较弱的模型集成模型组，其中的模型可以单独进行训练，并且它们的预测能以某种方式结合起来去做出一个总体预测。</p><br/><p data-pid=\"vITe0uGk\">该算法主要的问题是要找出哪些较弱的模型可以结合起来，以及结合的方法。这是一个非常强大的技术集，因此广受欢迎。</p><br/><ul><li data-pid=\"T6LRcvW1\"><p data-pid=\"IcjqMf4C\">Boosting</p></li><li data-pid=\"_7sFGVAw\"><p data-pid=\"W_7AIH6g\">Bootstrapped Aggregation（Bagging）</p></li><li data-pid=\"aHLekMoN\"><p data-pid=\"VFRRrdbd\">AdaBoost</p></li><li data-pid=\"DNnBYT32\"><p data-pid=\"Kio0IP4b\">层叠泛化（Stacked Generalization）（blending）</p></li><li data-pid=\"kJoRqb7f\"><p data-pid=\"_4NM_-Uq\">梯度推进机（Gradient Boosting Machines，GBM）</p></li><li data-pid=\"dHp9dVik\"><p data-pid=\"vt1zkJDn\">梯度提升回归树（Gradient Boosted Regression Trees，GBRT）</p></li><li data-pid=\"3QECKTy9\"><p data-pid=\"i89MK_Rs\">随机森林（Random Forest）</p></li></ul><br/><p data-pid=\"T2ESlDJ2\"><strong>优点：</strong></p><br/><ul><li data-pid=\"9VxqiLM1\"><p data-pid=\"h_DP3_0-\">当先最先进的预测几乎都使用了算法集成。它比使用单个模型预测出来的结果要精确的多</p></li></ul><br/><p data-pid=\"5Xy92aR_\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"GSOL_08Z\"><p data-pid=\"WrvyHPZR\">需要大量的维护工作</p></li></ul><br/><p data-pid=\"Ljqacz9Q\"><strong>决策树算法（Decision Tree Algorithm）</strong></p><br/><figure><img src=\"https://pic4.zhimg.com/v2-1c3e835f10e86c70373eff2b91975d23_1440w.png\" data-rawwidth=\"201\" data-rawheight=\"226\" class=\"content_image\" width=\"201\" data-original-token=\"v2-1c3e835f10e86c70373eff2b91975d23\"/></figure><br/><p data-pid=\"r0HJmJYm\">决策树学习使用一个决策树作为一个预测模型，它将对一个 item（表征在分支上）观察所得映射成关于该 item 的目标值的结论（表征在叶子中）。</p><br/><p data-pid=\"YBWPPLPF\">树模型中的目标是可变的，可以采一组有限值，被称为分类树；在这些树结构中，叶子表示类标签，分支表示表征这些类标签的连接的特征。</p><br/><p data-pid=\"nxyQQLNg\"><strong>例子：</strong></p><br/><ul><li data-pid=\"xLg-5Hca\"><p data-pid=\"C-4upj9k\">分类和回归树（Classification and Regression Tree，CART）</p></li><li data-pid=\"1Ue0eNyX\"><p data-pid=\"Rv1BjXx_\">Iterative Dichotomiser 3（ID3）</p></li><li data-pid=\"lVaXR8kh\"><p data-pid=\"VWzFuPvq\">C4.5 和 C5.0（一种强大方法的两个不同版本）</p></li></ul><br/><p data-pid=\"_OUPAj54\"><strong>优点：</strong></p><br/><ul><li data-pid=\"DfnW9iZH\"><p data-pid=\"PIyVfftk\">容易解释</p></li><li data-pid=\"hsQLFun3\"><p data-pid=\"ICT8SYZL\">非参数型</p></li></ul><br/><p data-pid=\"3YoUfDqG\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"GfA2mau0\"><p data-pid=\"lIMCUSK8\">趋向过拟合</p></li><li data-pid=\"30nlwpnc\"><p data-pid=\"XJ_lJroB\">可能或陷于局部最小值中</p></li><li data-pid=\"02AX0yMd\"><p data-pid=\"Gis2NzlS\">没有在线学习</p></li></ul><br/><p data-pid=\"4B3IdE9z\"><strong>回归（Regression）算法</strong></p><br/><figure><img src=\"https://picx.zhimg.com/v2-161db14670eb6525a534b4fe74961475_1440w.png\" data-rawwidth=\"207\" data-rawheight=\"226\" class=\"content_image\" width=\"207\" data-original-token=\"v2-161db14670eb6525a534b4fe74961475\"/></figure><br/><p data-pid=\"wd6z1NcW\">回归是用于估计两种变量之间关系的统计过程。当用于分析因变量和一个 多个自变量之间的关系时，该算法能提供很多建模和分析多个变量的技巧。具体一点说，回归分析可以帮助我们理解当任意一个自变量变化，另一个自变量不变时，因变量变化的典型值。最常见的是，回归分析能在给定自变量的条件下估计出因变量的条件期望。</p><br/><p data-pid=\"_u7W9Xwh\">回归算法是统计学中的主要算法，它已被纳入统计机器学习。</p><br/><p data-pid=\"sRZ-WqTV\"><strong>例子：</strong></p><br/><ul><li data-pid=\"lqACA8-J\"><p data-pid=\"MPKypIoZ\">普通最小二乘回归（Ordinary Least Squares Regression，OLSR）</p></li><li data-pid=\"C7iH1pXo\"><p data-pid=\"OjCHqt4Z\">线性回归（Linear Regression）</p></li><li data-pid=\"1d1MvITx\"><p data-pid=\"OJ8zAkkz\">逻辑回归（Logistic Regression）</p></li><li data-pid=\"F03F4VqG\"><p data-pid=\"tB14Xurm\">逐步回归（Stepwise Regression）</p></li><li data-pid=\"eciTEZzr\"><p data-pid=\"_qJIbbRu\">多元自适应回归样条（Multivariate Adaptive Regression Splines，MARS）</p></li><li data-pid=\"dxFJvjtO\"><p data-pid=\"0RDYvQBv\">本地散点平滑估计（Locally Estimated Scatterplot Smoothing，LOESS）</p></li></ul><br/><p data-pid=\"eefKJ2Iy\"><strong>优点：</strong></p><br/><ul><li data-pid=\"8ZFF6_Ur\"><p data-pid=\"6yjdfnqk\">直接、快速</p></li><li data-pid=\"Wn85jNfA\"><p data-pid=\"aTlZYDIX\">知名度高</p></li></ul><br/><p data-pid=\"zqoP9y4F\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"GYqnvUXh\"><p data-pid=\"pZ7khgkK\">要求严格的假设</p></li><li data-pid=\"48NJcUc4\"><p data-pid=\"pYm50dDn\">需要处理异常值</p></li></ul><br/><p data-pid=\"NHHvo_Ur\"><strong>人工神经网络</strong></p><br/><figure><img src=\"https://pic4.zhimg.com/v2-91537c1910440135f8a4ab4ff00b16db_1440w.png\" data-rawwidth=\"192\" data-rawheight=\"226\" class=\"content_image\" width=\"192\" data-original-token=\"v2-91537c1910440135f8a4ab4ff00b16db\"/></figure><br/><p data-pid=\"xyfvu5zS\">人工神经网络是受生物神经网络启发而构建的算法模型。</p><br/><p data-pid=\"5YYFBcDz\">它是一种模式匹配，常被用于回归和分类问题，但拥有庞大的子域，由数百种算法和各类问题的变体组成。</p><br/><p data-pid=\"1GJBIkXD\"><strong>例子：</strong></p><br/><ul><li data-pid=\"LBoBGlha\"><p data-pid=\"Ue78lubP\">感知器</p></li><li data-pid=\"A4FC4w4k\"><p data-pid=\"vBqCeyZy\">反向传播</p></li><li data-pid=\"tFseBhHI\"><p data-pid=\"TUJoFHMa\">Hopfield 网络</p></li><li data-pid=\"miovCqdL\"><p data-pid=\"SlCEDrDy\">径向基函数网络（Radial Basis Function Network，RBFN）</p></li></ul><br/><p data-pid=\"6eOEkzO9\"><strong>优点：</strong></p><br/><ul><li data-pid=\"HSEYBKm_\"><p data-pid=\"mDqqJ_E_\">在语音、语义、视觉、各类游戏（如围棋）的任务中表现极好。</p></li><li data-pid=\"wbbA_wV0\"><p data-pid=\"n1wxJNEF\">算法可以快速调整，适应新的问题。</p></li></ul><br/><p data-pid=\"-w3vTWs3\"><strong>缺点：</strong></p><br/><p data-pid=\"ZPqEX7At\">需要大量数据进行训练</p><p data-pid=\"ymZWuV8_\">训练要求很高的硬件配置</p><p data-pid=\"v-szY7tf\">模型处于「黑箱状态」，难以理解内部机制</p><p data-pid=\"Sh9afExM\">元参数（Metaparameter）与网络拓扑选择困难。</p><br/><p data-pid=\"1Lm7iedv\"><strong>深度学习（Deep Learning）</strong></p><br/><figure><img src=\"https://pica.zhimg.com/v2-2158dacd56df1df4e3ecca4bac8e8b10_1440w.png\" data-rawwidth=\"200\" data-rawheight=\"228\" class=\"content_image\" width=\"200\" data-original-token=\"v2-2158dacd56df1df4e3ecca4bac8e8b10\"/></figure><br/><p data-pid=\"VlIWV2SL\">深度学习是人工神经网络的最新分支，它受益于当代硬件的快速发展。</p><br/><p data-pid=\"CbDsnhiC\">众多研究者目前的方向主要集中于构建更大、更复杂的神经网络，目前有许多方法正在聚焦半监督学习问题，其中用于训练的大数据集只包含很少的标记。</p><br/><p data-pid=\"vi5a1eZb\"><strong>例子：</strong></p><br/><ul><li data-pid=\"AbusKlRi\"><p data-pid=\"3J628Pcb\">深玻耳兹曼机（Deep Boltzmann Machine，DBM）</p></li><li data-pid=\"x7HcxtqF\"><p data-pid=\"6rP68cQQ\">Deep Belief Networks（DBN）</p></li><li data-pid=\"SuzIuTi7\"><p data-pid=\"jq1C2tt-\">卷积神经网络（CNN）</p></li><li data-pid=\"V06i712y\"><p data-pid=\"iJylhm0f\">Stacked Auto-Encoders</p></li></ul><br/><p data-pid=\"djjsx7hj\"><strong>优点/缺点</strong>：见神经网络</p><br/><p data-pid=\"uAaj20Y8\"><strong>支持向量机（Support Vector Machines）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-c9abeb277deab695af7cb4ddbb11ca4e_1440w.png\" data-rawwidth=\"235\" data-rawheight=\"110\" class=\"content_image\" width=\"235\" data-original-token=\"v2-c9abeb277deab695af7cb4ddbb11ca4e\"/></figure><br/><p data-pid=\"rwVvlEkk\">给定一组训练事例，其中每个事例都属于两个类别中的一个，支持向量机（SVM）训练算法可以在被输入新的事例后将其分类到两个类别中的一个，使自身成为非概率二进制线性分类器。</p><br/><p data-pid=\"cZ6Jj5z_\">SVM 模型将训练事例表示为空间中的点，它们被映射到一幅图中，由一条明确的、尽可能宽的间隔分开以区分两个类别。</p><br/><p data-pid=\"kUwtUlgN\">随后，新的示例会被映射到同一空间中，并基于它们落在间隔的哪一侧来预测它属于的类别。</p><br/><p data-pid=\"uieZOA01\"><strong>优点：</strong></p><br/><p data-pid=\"8sXWE5i0\">在非线性可分问题上表现优秀</p><br/><p data-pid=\"rwmK5txZ\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"DrX4e7OZ\"><p data-pid=\"gX2egneX\">非常难以训练</p></li><li data-pid=\"m8f4AJMB\"><p data-pid=\"Z3LgxQlL\">很难解释</p></li></ul><br/><p data-pid=\"nQmJ8TPB\"><strong>降维算法（Dimensionality Reduction Algorithms）</strong></p><br/><figure><img src=\"https://pic2.zhimg.com/v2-29114e6b93d4ac1862e1c3bd18bff0d9_1440w.png\" data-rawwidth=\"196\" data-rawheight=\"227\" class=\"content_image\" width=\"196\" data-original-token=\"v2-29114e6b93d4ac1862e1c3bd18bff0d9\"/></figure><br/><p data-pid=\"ynWpMuTW\">和集簇方法类似，降维追求并利用数据的内在结构，目的在于使用较少的信息总结或描述数据。</p><br/><p data-pid=\"6yACVGt8\">这一算法可用于可视化高维数据或简化接下来可用于监督学习中的数据。许多这样的方法可针对分类和回归的使用进行调整。</p><br/><p data-pid=\"OPh8pXSa\"><strong>例子：</strong></p><br/><ul><li data-pid=\"JPvpEsVU\"><p data-pid=\"j33LuAV_\">主成分分析（Principal Component Analysis (PCA)）</p></li><li data-pid=\"KyzjSLEB\"><p data-pid=\"_jpVjSkJ\">主成分回归（Principal Component Regression (PCR)）</p></li><li data-pid=\"RPaEBDAH\"><p data-pid=\"gj0xZc1h\">偏最小二乘回归（Partial Least Squares Regression (PLSR)）</p></li><li data-pid=\"f5vR-56C\"><p data-pid=\"FcypWKgU\">Sammon 映射（Sammon Mapping）</p></li><li data-pid=\"Xmy4WP3i\"><p data-pid=\"0qBTTLhI\">多维尺度变换（Multidimensional Scaling (MDS)）</p></li><li data-pid=\"__WZ2NEu\"><p data-pid=\"g1o_ttNw\">投影寻踪（Projection Pursuit）</p></li><li data-pid=\"qBZD6X5x\"><p data-pid=\"e2xBzU6b\">线性判别分析（Linear Discriminant Analysis (LDA)）</p></li><li data-pid=\"B3tfjVbg\"><p data-pid=\"6eXf_Mqm\">混合判别分析（Mixture Discriminant Analysis (MDA)）</p></li><li data-pid=\"OwFJldHP\"><p data-pid=\"bdN_xB5T\">二次判别分析（Quadratic Discriminant Analysis (QDA)）</p></li><li data-pid=\"pENBE5zz\"><p data-pid=\"h1q7Rb3A\">灵活判别分析（Flexible Discriminant Analysis (FDA)）</p></li></ul><br/><p data-pid=\"uTFUQuEy\"><strong>优点：</strong></p><br/><ul><li data-pid=\"hEk5lHjO\"><p data-pid=\"bqFEOkLZ\">可处理大规模数据集</p></li><li data-pid=\"6ShJYHqB\"><p data-pid=\"2Zt6VG6c\">无需在数据上进行假设</p></li></ul><br/><p data-pid=\"y2YA1Qy2\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"VkwIrMMY\"><p data-pid=\"loms3nbQ\">难以搞定非线性数据</p></li><li data-pid=\"Of9VJzaq\"><p data-pid=\"nHE7v2Jx\">难以理解结果的意义</p></li></ul><br/><p data-pid=\"JOQtUOyL\"><strong>聚类算法（Clustering Algorithms）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-3be3129d8ce4217ec08567607e0ccdac_1440w.png\" data-rawwidth=\"211\" data-rawheight=\"225\" class=\"content_image\" width=\"211\" data-original-token=\"v2-3be3129d8ce4217ec08567607e0ccdac\"/></figure><br/><p data-pid=\"BeVUvbF8\">聚类算法是指对一组目标进行分类，属于同一组（亦即一个类，cluster）的目标被划分在一组中，与其他组目标相比，同一组目标更加彼此相似（在某种意义上）。</p><br/><p data-pid=\"E-MyEfkB\"><strong>例子：</strong></p><br/><ul><li data-pid=\"VA27cjJf\"><p data-pid=\"l06B8wRV\">K-均值（k-Means）</p></li><li data-pid=\"XLdq2bN3\"><p data-pid=\"asEVdEBL\">k-Medians 算法</p></li><li data-pid=\"F0jyZw-p\"><p data-pid=\"KdMpoA0D\">Expectation Maximi 封层 ation (EM)</p></li><li data-pid=\"LSTcYrJY\"><p data-pid=\"kSCtGBwj\">最大期望算法（EM）</p></li><li data-pid=\"oyPT6DfZ\"><p data-pid=\"OzW-lCvT\">分层集群（Hierarchical Clstering）</p></li></ul><br/><p data-pid=\"MFiruGDZ\"><strong>优点：</strong></p><br/><ul><li data-pid=\"TpgHuv88\"><p data-pid=\"1OmJWO_Z\">让数据变得有意义</p></li></ul><br/><p data-pid=\"qeqy1Y9l\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"rgmaVWXq\"><p data-pid=\"Jbj6FtXT\">结果难以解读，针对不寻常的数据组，结果可能无用。</p></li></ul><br/><p data-pid=\"hcbsBm6I\"><strong>基于实例的算法（Instance-based Algorithms）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-9fe2126ad4beaafa3f739b9f5b9b4b8a_1440w.png\" data-rawwidth=\"199\" data-rawheight=\"225\" class=\"content_image\" width=\"199\" data-original-token=\"v2-9fe2126ad4beaafa3f739b9f5b9b4b8a\"/></figure><br/><p data-pid=\"hUR7X4CA\">基于实例的算法（有时也称为基于记忆的学习）是这样学 习算法，不是明确归纳，而是将新的问题例子与训练过程中见过的例子进行对比，这些见过的例子就在存储器中。</p><br/><p data-pid=\"jlkI_dcw\">之所以叫基于实例的算法是因为它直接从训练实例中建构出假设。这意味这，假设的复杂度能随着数据的增长而变化：最糟的情况是，假设是一个训练项目列表，分类一个单独新实例计算复杂度为 O（n）</p><br/><p data-pid=\"BI6LLyYW\"><strong>例子：</strong></p><br/><ul><li data-pid=\"3gBdhnbA\"><p data-pid=\"SHYleRrG\">K 最近邻（k-Nearest Neighbor (kNN)）</p></li><li data-pid=\"MqH9KjOM\"><p data-pid=\"3doOhWS5\">学习向量量化（Learning Vector Quantization (LVQ)）</p></li><li data-pid=\"iFz9ZSgI\"><p data-pid=\"fEumsrJB\">自组织映射（Self-Organizing Map (SOM)）</p></li><li data-pid=\"zkTDB9qs\"><p data-pid=\"6Dp1xmbj\">局部加权学习（Locally Weighted Learning (LWL)）</p></li></ul><br/><p data-pid=\"Xp8ENmR3\"><strong>优点：</strong></p><br/><ul><li data-pid=\"_6MTG8KD\"><p data-pid=\"dq2H73JW\">算法简单、结果易于解读</p></li></ul><br/><p data-pid=\"z7DZ4z2n\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"CoIsBwdm\"><p data-pid=\"VBDVrUW-\">内存使用非常高</p></li><li data-pid=\"kzcdfp9p\"><p data-pid=\"HnIu5hwZ\">计算成本高</p></li><li data-pid=\"z_T7x_m2\"><p data-pid=\"DSwZiSRr\">不可能用于高维特征空间</p></li></ul><br/><p data-pid=\"WEY9EH0J\"><strong>贝叶斯算法（Bayesian Algorithms）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-5869f7fa022e5d055f6fd435de62e25c_1440w.png\" data-rawwidth=\"205\" data-rawheight=\"224\" class=\"content_image\" width=\"205\" data-original-token=\"v2-5869f7fa022e5d055f6fd435de62e25c\"/></figure><br/><p data-pid=\"82Z_wBjK\">贝叶斯方法是指明确应用了贝叶斯定理来解决如分类和回归等问题的方法。</p><br/><p data-pid=\"xvB7s-Bp\"><strong>例子：</strong></p><br/><ul><li data-pid=\"BhiUpYN0\"><p data-pid=\"2axnFVry\">朴素贝叶斯（Naive Bayes）</p></li><li data-pid=\"bqqTbUDL\"><p data-pid=\"4NUnRsjP\">高斯朴素贝叶斯（Gaussian Naive Bayes）</p></li><li data-pid=\"n582IIZD\"><p data-pid=\"TvFGOAmj\">多项式朴素贝叶斯（Multinomial Naive Bayes）</p></li><li data-pid=\"-9Tle8Wn\"><p data-pid=\"7JCqwwqn\">平均一致依赖估计器（Averaged One-Dependence Estimators (AODE)）</p></li><li data-pid=\"7qbVGaC7\"><p data-pid=\"xNJ4ANb6\">贝叶斯信念网络（Bayesian Belief Network (BBN)）</p></li><li data-pid=\"54n4Sq7b\"><p data-pid=\"eGLEofWc\">贝叶斯网络（Bayesian Network (BN)）</p></li></ul><br/><p data-pid=\"rJlKFREA\"><strong>优点：</strong></p><br/><p data-pid=\"HVel2Bv5\">快速、易于训练、给出了它们所需的资源能带来良好的表现</p><br/><p data-pid=\"DSnmiPFr\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"WTLMlLfA\"><p data-pid=\"pKeiNXeN\">如果输入变量是相关的，则会出现问题</p></li></ul><br/><p data-pid=\"EEBGOJ7u\"><strong>关联规则学习算法（Association Rule Learning Algorithms）</strong></p><br/><figure><img src=\"https://picx.zhimg.com/v2-978b11e3871b00ed9dc3baf0f974ab73_1440w.png\" data-rawwidth=\"194\" data-rawheight=\"225\" class=\"content_image\" width=\"194\" data-original-token=\"v2-978b11e3871b00ed9dc3baf0f974ab73\"/></figure><br/><p data-pid=\"nao4i4YN\">关联规则学习方法能够提取出对数据中的变量之间的关系的最佳解释。比如说一家超市的销售数据中存在规则 {洋葱，土豆}=&gt; {汉堡}，那说明当一位客户同时购买了洋葱和土豆的时候，他很有可能还会购买汉堡肉。</p><br/><p data-pid=\"UUbvBSJ9\"><strong>例子：</strong></p><br/><ul><li data-pid=\"p-h_vKw0\"><p data-pid=\"HKDikHfc\">Apriori 算法（Apriori algorithm）</p></li><li data-pid=\"uavBMxWM\"><p data-pid=\"iJQI8V1S\">Eclat 算法（Eclat algorithm）</p></li><li data-pid=\"K7QGTgLP\"><p data-pid=\"gDFZuyw7\">FP-growth</p></li></ul><br/><p data-pid=\"B6cmmh7A\"><strong>图模型（Graphical Models）</strong></p><br/><figure><img src=\"https://pica.zhimg.com/v2-83300d03abcfb6a2a676ea6e1c9f0d7e_1440w.png\" data-rawwidth=\"194\" data-rawheight=\"226\" class=\"content_image\" width=\"194\" data-original-token=\"v2-83300d03abcfb6a2a676ea6e1c9f0d7e\"/></figure><br/><p data-pid=\"g_oH7FHF\">图模型或概率图模型（PGM/probabilistic graphical model）是一种概率模型，一个图（graph）可以通过其表示随机变量之间的条件依赖结构（conditional dependence structure）。</p><br/><p data-pid=\"KclLZ4Du\"><strong>例子：</strong></p><br/><ul><li data-pid=\"wV1mB7-A\"><p data-pid=\"eTvDuYPz\">贝叶斯网络（Bayesian network）</p></li><li data-pid=\"Bkin4x0X\"><p data-pid=\"k1qdydNw\">马尔可夫随机域（Markov random field）</p></li><li data-pid=\"dViL2Iwn\"><p data-pid=\"hi2lUOHn\">链图（Chain Graphs）</p></li><li data-pid=\"aoTB6CFc\"><p data-pid=\"BWfvfWwy\">祖先图（Ancestral graph）</p></li></ul><br/><p data-pid=\"H5G8uPIU\"><strong>优点：</strong></p><br/><ul><li data-pid=\"bSU9Za9L\"><p data-pid=\"LCFCwLwH\">模型清晰，能被直观地理解</p></li></ul><br/><p data-pid=\"LpHkdZaU\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"HYhCDMiD\"><p data-pid=\"scllDO38\">确定其依赖的拓扑很困难，有时候也很模糊</p></li></ul><p data-pid=\"_XsoOIhi\">选自<a href=\"https://link.zhihu.com/?target=https%3A//static.coggle.it/diagram/WHeBqDIrJRk-kDDY\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Coggle</a><b>机器之心编译</b></p>", "excerpt": "<em>在我们日常生活中所用到的推荐系统、智能图片美化应用和聊天机器人等应用中，各种各样的机器学习和数据处理算法正尽职尽责地发挥着自己的功效。本文筛选并简单介绍了一些最常见算法类别，还为每一个类别列出了一些实际的算法并简单介绍了它们的优缺点。</em> <a href=\"https://link.zhihu.com/?target=https%3A//static.coggle.it/diagram/WHeBqDIrJRk-kDDY\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">static.coggle.it/diagra</span><span class=\"invisible\">m/WHeBqDIrJRk-kDDY</span><span class=\"ellipsis\"></span></a> 目录 正则化算法（Regularization Algorithms） 集成算法（Ensemble Algorithms） 决策树算法（Decision Tree Algorithm…", "excerpt_new": "<em>在我们日常生活中所用到的推荐系统、智能图片美化应用和聊天机器人等应用中，各种各样的机器学习和数据处理算法正尽职尽责地发挥着自己的功效。本文筛选并简单介绍了一些最常见算法类别，还为每一个类别列出了一些实际的算法并简单介绍了它们的优缺点。</em> <a href=\"https://link.zhihu.com/?target=https%3A//static.coggle.it/diagram/WHeBqDIrJRk-kDDY\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">static.coggle.it/diagra</span><span class=\"invisible\">m/WHeBqDIrJRk-kDDY</span><span class=\"ellipsis\"></span></a> 目录 正则化算法（Regularization Algorithms） 集成算法（Ensemble Algorithms） 决策树算法（Decision Tree Algorithm…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/25327755", "comment_permission": "all", "voteup_count": 2484, "comment_count": 48, "image_url": "https://picx.zhimg.com/v2-a4c0982fcb3d7013d9ae1a0a275edc5b_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_COLLECT_ARTICLE", "created_time": 1516514047, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "收藏了文章", "is_sticky": false}, {"id": "1516514044134", "type": "feed", "target": {"id": "25327755", "type": "article", "author": {"id": "06a67981ced7a2e9f07005185605685c", "name": "机器之心", "headline": "人工智能信息服务平台", "type": "people", "user_type": "organization", "url": "https://www.zhihu.com/people/ji-qi-zhi-xin-65", "url_token": "ji-qi-zhi-xin-65", "avatar_url": "https://picx.zhimg.com/v2-dd115d399e55c37e3312c8ee4713890e_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": true, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["数学", "人工智能"], "topics": []}, {"type": "identity", "description": "已认证机构号", "topic_names": [], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "数学等 2 个话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "v2-351d57389cf50b002a20606caac645cf", "avatar_url": "https://picx.zhimg.com/v2-351d57389cf50b002a20606caac645cf_720w.jpg?source=32738c0c", "description": "", "id": "19554091", "name": "数学", "priority": 0, "token": "19554091", "type": "topic", "url": "https://www.zhihu.com/topic/19554091"}, {"avatar_path": "v2-c41d10d22173d515740c43c70f885705", "avatar_url": "https://picx.zhimg.com/v2-c41d10d22173d515740c43c70f885705_720w.jpg?source=32738c0c", "description": "", "id": "19551275", "name": "人工智能", "priority": 0, "token": "19551275", "type": "topic", "url": "https://www.zhihu.com/topic/19551275"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}, {"badge_status": "passed", "description": "已认证机构号", "detail_type": "identity", "icon": "", "night_icon": "", "sources": [], "title": "认证", "type": "identity", "url": "https://www.zhihu.com/term/institution-settle"}], "title": "数学等 2 个话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "created": 1487571245, "updated": 1487571245, "title": "机器学习算法集锦：从贝叶斯到深度学习及各自优缺点", "excerpt_title": "", "content": "<blockquote data-pid=\"l48uheNu\"><p data-pid=\"VIugkv9B\"><em>在我们日常生活中所用到的推荐系统、智能图片美化应用和聊天机器人等应用中，各种各样的机器学习和数据处理算法正尽职尽责地发挥着自己的功效。本文筛选并简单介绍了一些最常见算法类别，还为每一个类别列出了一些实际的算法并简单介绍了它们的优缺点。</em></p></blockquote><figure><img src=\"https://pic1.zhimg.com/v2-7c6c25406abcba766ed618c226a49f92_1440w.png\" data-rawwidth=\"1151\" data-rawheight=\"703\" class=\"origin_image zh-lightbox-thumb\" width=\"1151\" data-original=\"https://pic1.zhimg.com/v2-7c6c25406abcba766ed618c226a49f92_r.jpg\" data-original-token=\"v2-7c6c25406abcba766ed618c226a49f92\"/></figure><br/><p data-pid=\"CHAJWfW_\"><a href=\"https://link.zhihu.com/?target=https%3A//static.coggle.it/diagram/WHeBqDIrJRk-kDDY\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">static.coggle.it/diagra</span><span class=\"invisible\">m/WHeBqDIrJRk-kDDY</span><span class=\"ellipsis\"></span></a></p><br/><br/><p data-pid=\"lFFaBRqW\">目录</p><br/><ul><li data-pid=\"auIF9xxO\"><p data-pid=\"BOobPNxA\">正则化算法（Regularization Algorithms）</p></li><li data-pid=\"rjguTZvf\"><p data-pid=\"4bNEITRs\">集成算法（Ensemble Algorithms）</p></li><li data-pid=\"i8EGgqcD\"><p data-pid=\"_3AkOhJe\">决策树算法（Decision Tree Algorithm）</p></li><li data-pid=\"m4jTxI1p\"><p data-pid=\"7GZjs9_B\">回归（Regression）</p></li><li data-pid=\"yN4k5VMz\"><p data-pid=\"oVSlkfKz\">人工神经网络（Artificial Neural Network）</p></li><li data-pid=\"lNkES6Gg\"><p data-pid=\"8J1afUfC\">深度学习（Deep Learning）</p></li><li data-pid=\"g6KSPZ1s\"><p data-pid=\"8LvPiG-9\">支持向量机（Support Vector Machine）</p></li><li data-pid=\"gGirTxOX\"><p data-pid=\"uFZTEaRW\">降维算法（Dimensionality Reduction Algorithms）</p></li><li data-pid=\"YWl8Aqy6\"><p data-pid=\"ogH2WFNu\">聚类算法（Clustering Algorithms）</p></li><li data-pid=\"LCzLj-hS\"><p data-pid=\"WABGOHKg\">基于实例的算法（Instance-based Algorithms）</p></li><li data-pid=\"2ZyLf_9g\"><p data-pid=\"muILNVCd\">贝叶斯算法（Bayesian Algorithms）</p></li><li data-pid=\"XjxM84rf\"><p data-pid=\"3G7SvPDL\">关联规则学习算法（Association Rule Learning Algorithms）</p></li><li data-pid=\"gq4I9V5B\"><p data-pid=\"JZOK4lkc\">图模型（Graphical Models）</p></li></ul><br/><p data-pid=\"3x3WdCuv\"><strong>正则化算法（Regularization Algorithms）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-5262baa79289a5586495b332acb8b3b2_1440w.png\" data-rawwidth=\"199\" data-rawheight=\"227\" class=\"content_image\" width=\"199\" data-original-token=\"v2-5262baa79289a5586495b332acb8b3b2\"/></figure><br/><p data-pid=\"FmBfYlh1\">它是另一种方法（通常是回归方法）的拓展，这种方法会基于模型复杂性对其进行惩罚，它喜欢相对简单能够更好的泛化的模型。</p><br/><p data-pid=\"C_UodCGe\"><strong>例子：</strong></p><br/><ul><li data-pid=\"QhW03I8_\"><p data-pid=\"KKoZMEDE\">岭回归（Ridge Regression）</p></li><li data-pid=\"g3SMQ57R\"><p data-pid=\"zIj8T-mJ\">最小绝对收缩与选择算子（LASSO）</p></li><li data-pid=\"b1S-JWF2\"><p data-pid=\"Nbm3i_nL\">GLASSO</p></li><li data-pid=\"HYKRTITk\"><p data-pid=\"CxWp3AlJ\">弹性网络（Elastic Net）</p></li><li data-pid=\"joSi_Tys\"><p data-pid=\"iiss_6-L\">最小角回归（Least-Angle Regression）</p></li></ul><br/><p data-pid=\"iO4tpdHC\"><strong>优点：</strong></p><br/><ul><li data-pid=\"CAo6l67A\"><p data-pid=\"MURdgDz8\">其惩罚会减少过拟合</p></li><li data-pid=\"BnUB3Bxv\"><p data-pid=\"U_WWogWa\">总会有解决方法</p></li></ul><br/><p data-pid=\"4Is2fpx9\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"vNkGT6z1\"><p data-pid=\"XYjJUbsC\">惩罚会造成欠拟合</p></li><li data-pid=\"mOZJumr1\"><p data-pid=\"0T39PT6Q\">很难校准</p></li></ul><br/><p data-pid=\"jI9S8rwS\"><strong>集成算法（Ensemble algorithms）</strong></p><br/><figure><img src=\"https://pica.zhimg.com/v2-2a081ed7fa4f74fa305aa37cac983164_1440w.png\" data-rawwidth=\"204\" data-rawheight=\"230\" class=\"content_image\" width=\"204\" data-original-token=\"v2-2a081ed7fa4f74fa305aa37cac983164\"/></figure><br/><p data-pid=\"h7oZf_1x\">集成方法是由多个较弱的模型集成模型组，其中的模型可以单独进行训练，并且它们的预测能以某种方式结合起来去做出一个总体预测。</p><br/><p data-pid=\"vITe0uGk\">该算法主要的问题是要找出哪些较弱的模型可以结合起来，以及结合的方法。这是一个非常强大的技术集，因此广受欢迎。</p><br/><ul><li data-pid=\"T6LRcvW1\"><p data-pid=\"IcjqMf4C\">Boosting</p></li><li data-pid=\"_7sFGVAw\"><p data-pid=\"W_7AIH6g\">Bootstrapped Aggregation（Bagging）</p></li><li data-pid=\"aHLekMoN\"><p data-pid=\"VFRRrdbd\">AdaBoost</p></li><li data-pid=\"DNnBYT32\"><p data-pid=\"Kio0IP4b\">层叠泛化（Stacked Generalization）（blending）</p></li><li data-pid=\"kJoRqb7f\"><p data-pid=\"_4NM_-Uq\">梯度推进机（Gradient Boosting Machines，GBM）</p></li><li data-pid=\"dHp9dVik\"><p data-pid=\"vt1zkJDn\">梯度提升回归树（Gradient Boosted Regression Trees，GBRT）</p></li><li data-pid=\"3QECKTy9\"><p data-pid=\"i89MK_Rs\">随机森林（Random Forest）</p></li></ul><br/><p data-pid=\"T2ESlDJ2\"><strong>优点：</strong></p><br/><ul><li data-pid=\"9VxqiLM1\"><p data-pid=\"h_DP3_0-\">当先最先进的预测几乎都使用了算法集成。它比使用单个模型预测出来的结果要精确的多</p></li></ul><br/><p data-pid=\"5Xy92aR_\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"GSOL_08Z\"><p data-pid=\"WrvyHPZR\">需要大量的维护工作</p></li></ul><br/><p data-pid=\"Ljqacz9Q\"><strong>决策树算法（Decision Tree Algorithm）</strong></p><br/><figure><img src=\"https://pic4.zhimg.com/v2-1c3e835f10e86c70373eff2b91975d23_1440w.png\" data-rawwidth=\"201\" data-rawheight=\"226\" class=\"content_image\" width=\"201\" data-original-token=\"v2-1c3e835f10e86c70373eff2b91975d23\"/></figure><br/><p data-pid=\"r0HJmJYm\">决策树学习使用一个决策树作为一个预测模型，它将对一个 item（表征在分支上）观察所得映射成关于该 item 的目标值的结论（表征在叶子中）。</p><br/><p data-pid=\"YBWPPLPF\">树模型中的目标是可变的，可以采一组有限值，被称为分类树；在这些树结构中，叶子表示类标签，分支表示表征这些类标签的连接的特征。</p><br/><p data-pid=\"nxyQQLNg\"><strong>例子：</strong></p><br/><ul><li data-pid=\"xLg-5Hca\"><p data-pid=\"C-4upj9k\">分类和回归树（Classification and Regression Tree，CART）</p></li><li data-pid=\"1Ue0eNyX\"><p data-pid=\"Rv1BjXx_\">Iterative Dichotomiser 3（ID3）</p></li><li data-pid=\"lVaXR8kh\"><p data-pid=\"VWzFuPvq\">C4.5 和 C5.0（一种强大方法的两个不同版本）</p></li></ul><br/><p data-pid=\"_OUPAj54\"><strong>优点：</strong></p><br/><ul><li data-pid=\"DfnW9iZH\"><p data-pid=\"PIyVfftk\">容易解释</p></li><li data-pid=\"hsQLFun3\"><p data-pid=\"ICT8SYZL\">非参数型</p></li></ul><br/><p data-pid=\"3YoUfDqG\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"GfA2mau0\"><p data-pid=\"lIMCUSK8\">趋向过拟合</p></li><li data-pid=\"30nlwpnc\"><p data-pid=\"XJ_lJroB\">可能或陷于局部最小值中</p></li><li data-pid=\"02AX0yMd\"><p data-pid=\"Gis2NzlS\">没有在线学习</p></li></ul><br/><p data-pid=\"4B3IdE9z\"><strong>回归（Regression）算法</strong></p><br/><figure><img src=\"https://picx.zhimg.com/v2-161db14670eb6525a534b4fe74961475_1440w.png\" data-rawwidth=\"207\" data-rawheight=\"226\" class=\"content_image\" width=\"207\" data-original-token=\"v2-161db14670eb6525a534b4fe74961475\"/></figure><br/><p data-pid=\"wd6z1NcW\">回归是用于估计两种变量之间关系的统计过程。当用于分析因变量和一个 多个自变量之间的关系时，该算法能提供很多建模和分析多个变量的技巧。具体一点说，回归分析可以帮助我们理解当任意一个自变量变化，另一个自变量不变时，因变量变化的典型值。最常见的是，回归分析能在给定自变量的条件下估计出因变量的条件期望。</p><br/><p data-pid=\"_u7W9Xwh\">回归算法是统计学中的主要算法，它已被纳入统计机器学习。</p><br/><p data-pid=\"sRZ-WqTV\"><strong>例子：</strong></p><br/><ul><li data-pid=\"lqACA8-J\"><p data-pid=\"MPKypIoZ\">普通最小二乘回归（Ordinary Least Squares Regression，OLSR）</p></li><li data-pid=\"C7iH1pXo\"><p data-pid=\"OjCHqt4Z\">线性回归（Linear Regression）</p></li><li data-pid=\"1d1MvITx\"><p data-pid=\"OJ8zAkkz\">逻辑回归（Logistic Regression）</p></li><li data-pid=\"F03F4VqG\"><p data-pid=\"tB14Xurm\">逐步回归（Stepwise Regression）</p></li><li data-pid=\"eciTEZzr\"><p data-pid=\"_qJIbbRu\">多元自适应回归样条（Multivariate Adaptive Regression Splines，MARS）</p></li><li data-pid=\"dxFJvjtO\"><p data-pid=\"0RDYvQBv\">本地散点平滑估计（Locally Estimated Scatterplot Smoothing，LOESS）</p></li></ul><br/><p data-pid=\"eefKJ2Iy\"><strong>优点：</strong></p><br/><ul><li data-pid=\"8ZFF6_Ur\"><p data-pid=\"6yjdfnqk\">直接、快速</p></li><li data-pid=\"Wn85jNfA\"><p data-pid=\"aTlZYDIX\">知名度高</p></li></ul><br/><p data-pid=\"zqoP9y4F\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"GYqnvUXh\"><p data-pid=\"pZ7khgkK\">要求严格的假设</p></li><li data-pid=\"48NJcUc4\"><p data-pid=\"pYm50dDn\">需要处理异常值</p></li></ul><br/><p data-pid=\"NHHvo_Ur\"><strong>人工神经网络</strong></p><br/><figure><img src=\"https://pic4.zhimg.com/v2-91537c1910440135f8a4ab4ff00b16db_1440w.png\" data-rawwidth=\"192\" data-rawheight=\"226\" class=\"content_image\" width=\"192\" data-original-token=\"v2-91537c1910440135f8a4ab4ff00b16db\"/></figure><br/><p data-pid=\"xyfvu5zS\">人工神经网络是受生物神经网络启发而构建的算法模型。</p><br/><p data-pid=\"5YYFBcDz\">它是一种模式匹配，常被用于回归和分类问题，但拥有庞大的子域，由数百种算法和各类问题的变体组成。</p><br/><p data-pid=\"1GJBIkXD\"><strong>例子：</strong></p><br/><ul><li data-pid=\"LBoBGlha\"><p data-pid=\"Ue78lubP\">感知器</p></li><li data-pid=\"A4FC4w4k\"><p data-pid=\"vBqCeyZy\">反向传播</p></li><li data-pid=\"tFseBhHI\"><p data-pid=\"TUJoFHMa\">Hopfield 网络</p></li><li data-pid=\"miovCqdL\"><p data-pid=\"SlCEDrDy\">径向基函数网络（Radial Basis Function Network，RBFN）</p></li></ul><br/><p data-pid=\"6eOEkzO9\"><strong>优点：</strong></p><br/><ul><li data-pid=\"HSEYBKm_\"><p data-pid=\"mDqqJ_E_\">在语音、语义、视觉、各类游戏（如围棋）的任务中表现极好。</p></li><li data-pid=\"wbbA_wV0\"><p data-pid=\"n1wxJNEF\">算法可以快速调整，适应新的问题。</p></li></ul><br/><p data-pid=\"-w3vTWs3\"><strong>缺点：</strong></p><br/><p data-pid=\"ZPqEX7At\">需要大量数据进行训练</p><p data-pid=\"ymZWuV8_\">训练要求很高的硬件配置</p><p data-pid=\"v-szY7tf\">模型处于「黑箱状态」，难以理解内部机制</p><p data-pid=\"Sh9afExM\">元参数（Metaparameter）与网络拓扑选择困难。</p><br/><p data-pid=\"1Lm7iedv\"><strong>深度学习（Deep Learning）</strong></p><br/><figure><img src=\"https://pica.zhimg.com/v2-2158dacd56df1df4e3ecca4bac8e8b10_1440w.png\" data-rawwidth=\"200\" data-rawheight=\"228\" class=\"content_image\" width=\"200\" data-original-token=\"v2-2158dacd56df1df4e3ecca4bac8e8b10\"/></figure><br/><p data-pid=\"VlIWV2SL\">深度学习是人工神经网络的最新分支，它受益于当代硬件的快速发展。</p><br/><p data-pid=\"CbDsnhiC\">众多研究者目前的方向主要集中于构建更大、更复杂的神经网络，目前有许多方法正在聚焦半监督学习问题，其中用于训练的大数据集只包含很少的标记。</p><br/><p data-pid=\"vi5a1eZb\"><strong>例子：</strong></p><br/><ul><li data-pid=\"AbusKlRi\"><p data-pid=\"3J628Pcb\">深玻耳兹曼机（Deep Boltzmann Machine，DBM）</p></li><li data-pid=\"x7HcxtqF\"><p data-pid=\"6rP68cQQ\">Deep Belief Networks（DBN）</p></li><li data-pid=\"SuzIuTi7\"><p data-pid=\"jq1C2tt-\">卷积神经网络（CNN）</p></li><li data-pid=\"V06i712y\"><p data-pid=\"iJylhm0f\">Stacked Auto-Encoders</p></li></ul><br/><p data-pid=\"djjsx7hj\"><strong>优点/缺点</strong>：见神经网络</p><br/><p data-pid=\"uAaj20Y8\"><strong>支持向量机（Support Vector Machines）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-c9abeb277deab695af7cb4ddbb11ca4e_1440w.png\" data-rawwidth=\"235\" data-rawheight=\"110\" class=\"content_image\" width=\"235\" data-original-token=\"v2-c9abeb277deab695af7cb4ddbb11ca4e\"/></figure><br/><p data-pid=\"rwVvlEkk\">给定一组训练事例，其中每个事例都属于两个类别中的一个，支持向量机（SVM）训练算法可以在被输入新的事例后将其分类到两个类别中的一个，使自身成为非概率二进制线性分类器。</p><br/><p data-pid=\"cZ6Jj5z_\">SVM 模型将训练事例表示为空间中的点，它们被映射到一幅图中，由一条明确的、尽可能宽的间隔分开以区分两个类别。</p><br/><p data-pid=\"kUwtUlgN\">随后，新的示例会被映射到同一空间中，并基于它们落在间隔的哪一侧来预测它属于的类别。</p><br/><p data-pid=\"uieZOA01\"><strong>优点：</strong></p><br/><p data-pid=\"8sXWE5i0\">在非线性可分问题上表现优秀</p><br/><p data-pid=\"rwmK5txZ\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"DrX4e7OZ\"><p data-pid=\"gX2egneX\">非常难以训练</p></li><li data-pid=\"m8f4AJMB\"><p data-pid=\"Z3LgxQlL\">很难解释</p></li></ul><br/><p data-pid=\"nQmJ8TPB\"><strong>降维算法（Dimensionality Reduction Algorithms）</strong></p><br/><figure><img src=\"https://pic2.zhimg.com/v2-29114e6b93d4ac1862e1c3bd18bff0d9_1440w.png\" data-rawwidth=\"196\" data-rawheight=\"227\" class=\"content_image\" width=\"196\" data-original-token=\"v2-29114e6b93d4ac1862e1c3bd18bff0d9\"/></figure><br/><p data-pid=\"ynWpMuTW\">和集簇方法类似，降维追求并利用数据的内在结构，目的在于使用较少的信息总结或描述数据。</p><br/><p data-pid=\"6yACVGt8\">这一算法可用于可视化高维数据或简化接下来可用于监督学习中的数据。许多这样的方法可针对分类和回归的使用进行调整。</p><br/><p data-pid=\"OPh8pXSa\"><strong>例子：</strong></p><br/><ul><li data-pid=\"JPvpEsVU\"><p data-pid=\"j33LuAV_\">主成分分析（Principal Component Analysis (PCA)）</p></li><li data-pid=\"KyzjSLEB\"><p data-pid=\"_jpVjSkJ\">主成分回归（Principal Component Regression (PCR)）</p></li><li data-pid=\"RPaEBDAH\"><p data-pid=\"gj0xZc1h\">偏最小二乘回归（Partial Least Squares Regression (PLSR)）</p></li><li data-pid=\"f5vR-56C\"><p data-pid=\"FcypWKgU\">Sammon 映射（Sammon Mapping）</p></li><li data-pid=\"Xmy4WP3i\"><p data-pid=\"0qBTTLhI\">多维尺度变换（Multidimensional Scaling (MDS)）</p></li><li data-pid=\"__WZ2NEu\"><p data-pid=\"g1o_ttNw\">投影寻踪（Projection Pursuit）</p></li><li data-pid=\"qBZD6X5x\"><p data-pid=\"e2xBzU6b\">线性判别分析（Linear Discriminant Analysis (LDA)）</p></li><li data-pid=\"B3tfjVbg\"><p data-pid=\"6eXf_Mqm\">混合判别分析（Mixture Discriminant Analysis (MDA)）</p></li><li data-pid=\"OwFJldHP\"><p data-pid=\"bdN_xB5T\">二次判别分析（Quadratic Discriminant Analysis (QDA)）</p></li><li data-pid=\"pENBE5zz\"><p data-pid=\"h1q7Rb3A\">灵活判别分析（Flexible Discriminant Analysis (FDA)）</p></li></ul><br/><p data-pid=\"uTFUQuEy\"><strong>优点：</strong></p><br/><ul><li data-pid=\"hEk5lHjO\"><p data-pid=\"bqFEOkLZ\">可处理大规模数据集</p></li><li data-pid=\"6ShJYHqB\"><p data-pid=\"2Zt6VG6c\">无需在数据上进行假设</p></li></ul><br/><p data-pid=\"y2YA1Qy2\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"VkwIrMMY\"><p data-pid=\"loms3nbQ\">难以搞定非线性数据</p></li><li data-pid=\"Of9VJzaq\"><p data-pid=\"nHE7v2Jx\">难以理解结果的意义</p></li></ul><br/><p data-pid=\"JOQtUOyL\"><strong>聚类算法（Clustering Algorithms）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-3be3129d8ce4217ec08567607e0ccdac_1440w.png\" data-rawwidth=\"211\" data-rawheight=\"225\" class=\"content_image\" width=\"211\" data-original-token=\"v2-3be3129d8ce4217ec08567607e0ccdac\"/></figure><br/><p data-pid=\"BeVUvbF8\">聚类算法是指对一组目标进行分类，属于同一组（亦即一个类，cluster）的目标被划分在一组中，与其他组目标相比，同一组目标更加彼此相似（在某种意义上）。</p><br/><p data-pid=\"E-MyEfkB\"><strong>例子：</strong></p><br/><ul><li data-pid=\"VA27cjJf\"><p data-pid=\"l06B8wRV\">K-均值（k-Means）</p></li><li data-pid=\"XLdq2bN3\"><p data-pid=\"asEVdEBL\">k-Medians 算法</p></li><li data-pid=\"F0jyZw-p\"><p data-pid=\"KdMpoA0D\">Expectation Maximi 封层 ation (EM)</p></li><li data-pid=\"LSTcYrJY\"><p data-pid=\"kSCtGBwj\">最大期望算法（EM）</p></li><li data-pid=\"oyPT6DfZ\"><p data-pid=\"OzW-lCvT\">分层集群（Hierarchical Clstering）</p></li></ul><br/><p data-pid=\"MFiruGDZ\"><strong>优点：</strong></p><br/><ul><li data-pid=\"TpgHuv88\"><p data-pid=\"1OmJWO_Z\">让数据变得有意义</p></li></ul><br/><p data-pid=\"qeqy1Y9l\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"rgmaVWXq\"><p data-pid=\"Jbj6FtXT\">结果难以解读，针对不寻常的数据组，结果可能无用。</p></li></ul><br/><p data-pid=\"hcbsBm6I\"><strong>基于实例的算法（Instance-based Algorithms）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-9fe2126ad4beaafa3f739b9f5b9b4b8a_1440w.png\" data-rawwidth=\"199\" data-rawheight=\"225\" class=\"content_image\" width=\"199\" data-original-token=\"v2-9fe2126ad4beaafa3f739b9f5b9b4b8a\"/></figure><br/><p data-pid=\"hUR7X4CA\">基于实例的算法（有时也称为基于记忆的学习）是这样学 习算法，不是明确归纳，而是将新的问题例子与训练过程中见过的例子进行对比，这些见过的例子就在存储器中。</p><br/><p data-pid=\"jlkI_dcw\">之所以叫基于实例的算法是因为它直接从训练实例中建构出假设。这意味这，假设的复杂度能随着数据的增长而变化：最糟的情况是，假设是一个训练项目列表，分类一个单独新实例计算复杂度为 O（n）</p><br/><p data-pid=\"BI6LLyYW\"><strong>例子：</strong></p><br/><ul><li data-pid=\"3gBdhnbA\"><p data-pid=\"SHYleRrG\">K 最近邻（k-Nearest Neighbor (kNN)）</p></li><li data-pid=\"MqH9KjOM\"><p data-pid=\"3doOhWS5\">学习向量量化（Learning Vector Quantization (LVQ)）</p></li><li data-pid=\"iFz9ZSgI\"><p data-pid=\"fEumsrJB\">自组织映射（Self-Organizing Map (SOM)）</p></li><li data-pid=\"zkTDB9qs\"><p data-pid=\"6Dp1xmbj\">局部加权学习（Locally Weighted Learning (LWL)）</p></li></ul><br/><p data-pid=\"Xp8ENmR3\"><strong>优点：</strong></p><br/><ul><li data-pid=\"_6MTG8KD\"><p data-pid=\"dq2H73JW\">算法简单、结果易于解读</p></li></ul><br/><p data-pid=\"z7DZ4z2n\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"CoIsBwdm\"><p data-pid=\"VBDVrUW-\">内存使用非常高</p></li><li data-pid=\"kzcdfp9p\"><p data-pid=\"HnIu5hwZ\">计算成本高</p></li><li data-pid=\"z_T7x_m2\"><p data-pid=\"DSwZiSRr\">不可能用于高维特征空间</p></li></ul><br/><p data-pid=\"WEY9EH0J\"><strong>贝叶斯算法（Bayesian Algorithms）</strong></p><br/><figure><img src=\"https://pic3.zhimg.com/v2-5869f7fa022e5d055f6fd435de62e25c_1440w.png\" data-rawwidth=\"205\" data-rawheight=\"224\" class=\"content_image\" width=\"205\" data-original-token=\"v2-5869f7fa022e5d055f6fd435de62e25c\"/></figure><br/><p data-pid=\"82Z_wBjK\">贝叶斯方法是指明确应用了贝叶斯定理来解决如分类和回归等问题的方法。</p><br/><p data-pid=\"xvB7s-Bp\"><strong>例子：</strong></p><br/><ul><li data-pid=\"BhiUpYN0\"><p data-pid=\"2axnFVry\">朴素贝叶斯（Naive Bayes）</p></li><li data-pid=\"bqqTbUDL\"><p data-pid=\"4NUnRsjP\">高斯朴素贝叶斯（Gaussian Naive Bayes）</p></li><li data-pid=\"n582IIZD\"><p data-pid=\"TvFGOAmj\">多项式朴素贝叶斯（Multinomial Naive Bayes）</p></li><li data-pid=\"-9Tle8Wn\"><p data-pid=\"7JCqwwqn\">平均一致依赖估计器（Averaged One-Dependence Estimators (AODE)）</p></li><li data-pid=\"7qbVGaC7\"><p data-pid=\"xNJ4ANb6\">贝叶斯信念网络（Bayesian Belief Network (BBN)）</p></li><li data-pid=\"54n4Sq7b\"><p data-pid=\"eGLEofWc\">贝叶斯网络（Bayesian Network (BN)）</p></li></ul><br/><p data-pid=\"rJlKFREA\"><strong>优点：</strong></p><br/><p data-pid=\"HVel2Bv5\">快速、易于训练、给出了它们所需的资源能带来良好的表现</p><br/><p data-pid=\"DSnmiPFr\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"WTLMlLfA\"><p data-pid=\"pKeiNXeN\">如果输入变量是相关的，则会出现问题</p></li></ul><br/><p data-pid=\"EEBGOJ7u\"><strong>关联规则学习算法（Association Rule Learning Algorithms）</strong></p><br/><figure><img src=\"https://picx.zhimg.com/v2-978b11e3871b00ed9dc3baf0f974ab73_1440w.png\" data-rawwidth=\"194\" data-rawheight=\"225\" class=\"content_image\" width=\"194\" data-original-token=\"v2-978b11e3871b00ed9dc3baf0f974ab73\"/></figure><br/><p data-pid=\"nao4i4YN\">关联规则学习方法能够提取出对数据中的变量之间的关系的最佳解释。比如说一家超市的销售数据中存在规则 {洋葱，土豆}=&gt; {汉堡}，那说明当一位客户同时购买了洋葱和土豆的时候，他很有可能还会购买汉堡肉。</p><br/><p data-pid=\"UUbvBSJ9\"><strong>例子：</strong></p><br/><ul><li data-pid=\"p-h_vKw0\"><p data-pid=\"HKDikHfc\">Apriori 算法（Apriori algorithm）</p></li><li data-pid=\"uavBMxWM\"><p data-pid=\"iJQI8V1S\">Eclat 算法（Eclat algorithm）</p></li><li data-pid=\"K7QGTgLP\"><p data-pid=\"gDFZuyw7\">FP-growth</p></li></ul><br/><p data-pid=\"B6cmmh7A\"><strong>图模型（Graphical Models）</strong></p><br/><figure><img src=\"https://pica.zhimg.com/v2-83300d03abcfb6a2a676ea6e1c9f0d7e_1440w.png\" data-rawwidth=\"194\" data-rawheight=\"226\" class=\"content_image\" width=\"194\" data-original-token=\"v2-83300d03abcfb6a2a676ea6e1c9f0d7e\"/></figure><br/><p data-pid=\"g_oH7FHF\">图模型或概率图模型（PGM/probabilistic graphical model）是一种概率模型，一个图（graph）可以通过其表示随机变量之间的条件依赖结构（conditional dependence structure）。</p><br/><p data-pid=\"KclLZ4Du\"><strong>例子：</strong></p><br/><ul><li data-pid=\"wV1mB7-A\"><p data-pid=\"eTvDuYPz\">贝叶斯网络（Bayesian network）</p></li><li data-pid=\"Bkin4x0X\"><p data-pid=\"k1qdydNw\">马尔可夫随机域（Markov random field）</p></li><li data-pid=\"dViL2Iwn\"><p data-pid=\"hi2lUOHn\">链图（Chain Graphs）</p></li><li data-pid=\"aoTB6CFc\"><p data-pid=\"BWfvfWwy\">祖先图（Ancestral graph）</p></li></ul><br/><p data-pid=\"H5G8uPIU\"><strong>优点：</strong></p><br/><ul><li data-pid=\"bSU9Za9L\"><p data-pid=\"LCFCwLwH\">模型清晰，能被直观地理解</p></li></ul><br/><p data-pid=\"LpHkdZaU\"><strong>缺点：</strong></p><br/><ul><li data-pid=\"HYhCDMiD\"><p data-pid=\"scllDO38\">确定其依赖的拓扑很困难，有时候也很模糊</p></li></ul><p data-pid=\"_XsoOIhi\">选自<a href=\"https://link.zhihu.com/?target=https%3A//static.coggle.it/diagram/WHeBqDIrJRk-kDDY\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Coggle</a><b>机器之心编译</b></p>", "excerpt": "<em>在我们日常生活中所用到的推荐系统、智能图片美化应用和聊天机器人等应用中，各种各样的机器学习和数据处理算法正尽职尽责地发挥着自己的功效。本文筛选并简单介绍了一些最常见算法类别，还为每一个类别列出了一些实际的算法并简单介绍了它们的优缺点。</em> <a href=\"https://link.zhihu.com/?target=https%3A//static.coggle.it/diagram/WHeBqDIrJRk-kDDY\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">static.coggle.it/diagra</span><span class=\"invisible\">m/WHeBqDIrJRk-kDDY</span><span class=\"ellipsis\"></span></a> 目录 正则化算法（Regularization Algorithms） 集成算法（Ensemble Algorithms） 决策树算法（Decision Tree Algorithm…", "excerpt_new": "<em>在我们日常生活中所用到的推荐系统、智能图片美化应用和聊天机器人等应用中，各种各样的机器学习和数据处理算法正尽职尽责地发挥着自己的功效。本文筛选并简单介绍了一些最常见算法类别，还为每一个类别列出了一些实际的算法并简单介绍了它们的优缺点。</em> <a href=\"https://link.zhihu.com/?target=https%3A//static.coggle.it/diagram/WHeBqDIrJRk-kDDY\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">static.coggle.it/diagra</span><span class=\"invisible\">m/WHeBqDIrJRk-kDDY</span><span class=\"ellipsis\"></span></a> 目录 正则化算法（Regularization Algorithms） 集成算法（Ensemble Algorithms） 决策树算法（Decision Tree Algorithm…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/25327755", "comment_permission": "all", "voteup_count": 2484, "comment_count": 48, "image_url": "https://picx.zhimg.com/v2-a4c0982fcb3d7013d9ae1a0a275edc5b_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1516514044, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1516513946981", "type": "feed", "target": {"id": "57662817", "title": "你身边的精英都有什么样的特质？", "url": "https://api.zhihu.com/questions/57662817", "type": "question", "question_type": "normal", "created": 1490572360, "answer_count": 632, "comment_count": 33, "follower_count": 41389, "detail": "如题", "excerpt": "如题", "bound_topic_ids": [405, 2566, 27237, 91364], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "26af8c8f0d0412cae2413448fd11bbbd", "name": "<PERSON>", "headline": "一蓑烟雨任平生", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/yun-duo-li-de-re-dai-yu", "url_token": "yun-duo-li-de-re-dai-yu", "avatar_url": "https://picx.zhimg.com/v2-ed125c3fff90a1ab388be0a6c7a07c1d_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1516513946, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1516513796837", "type": "feed", "target": {"id": "151687889", "type": "answer", "url": "https://api.zhihu.com/answers/151687889", "voteup_count": 7, "thanks_count": 3, "question": {"id": "56904981", "title": "如何正确地撸《算法导论》？", "url": "https://api.zhihu.com/questions/56904981", "type": "question", "question_type": "normal", "created": 1489135088, "answer_count": 52, "comment_count": 2, "follower_count": 2256, "detail": "大四，通信工程，做Linux c软件开发，尚未去公司，（水平只有写写链表，进程线程控制的地步。）想在毕业前多学点东西，每天花费2小时看《算法导论》目前看了三天，到第四章最大子数组这里。我有点担忧，因为能大致看懂数学推导和渐进记号,但是自我推导和证明很吃力。想只看伪代码和设计思路，不知道只看伪代码和设计思路会不会影响后面的内容看不看的懂？求撸完的大佬们分享一下学习方法呗！（为什么我感觉网易公开课上的课没有看书好？不看书完全听不懂在讲啥而且板书惨不忍睹...)", "excerpt": "大四，通信工程，做Linux c软件开发，尚未去公司，（水平只有写写链表，进程线程控…", "bound_topic_ids": [1093, 10220, 25257], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "edd59e50f7c6422fae0fd3be6f4bb98e", "name": "扶醉入香闺", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/fu-zui-ru-xiang-gui", "url_token": "fu-zui-ru-xiang-gui", "avatar_url": "https://pic1.zhimg.com/8e376e4d5dbec43ff06f7f6565fe609a_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1489547721, "created_time": 1489547594, "author": {"id": "a13060756ec6c8f3dbc6319e482631c0", "name": "章鱼小丸子", "headline": "每一个不曾起舞的日子，都是对生命的辜负。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/jj-xx-91-53", "url_token": "jj-xx-91-53", "avatar_url": "https://picx.zhimg.com/v2-454e33db75435589f23b22c5efda9672_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"hEaPy8uA\"><b>基于一个有价值的应用场景深入的去想解决方法。</b>比如如何用算法设计公交车线路，如何设计知乎回答排名算法，如何预测房价……</p><p data-pid=\"tp1BiPYw\">以算法书为参考书来寻求解决方案，理解算法本质原理。不要硬啃，记住答案没用。</p><p data-pid=\"X3dmdsdb\">如何我告诉你，这个问题解决方案值一百万，你什么算法都学得会。</p>", "excerpt": "<b>基于一个有价值的应用场景深入的去想解决方法。</b>比如如何用算法设计公交车线路，如何设计知乎回答排名算法，如何预测房价……以算法书为参考书来寻求解决方案，理解算法本质原理。不要硬啃，记住答案没用。 如何我告诉你，这个问题解决方案值一百万，你什么算法都学得会。 ", "excerpt_new": "<b>基于一个有价值的应用场景深入的去想解决方法。</b>比如如何用算法设计公交车线路，如何设计知乎回答排名算法，如何预测房价……以算法书为参考书来寻求解决方案，理解算法本质原理。不要硬啃，记住答案没用。 如何我告诉你，这个问题解决方案值一百万，你什么算法都学得会。 ", "preview_type": "expand", "preview_text": "<p data-pid=\"hEaPy8uA\"><b>基于一个有价值的应用场景深入的去想解决方法。</b>比如如何用算法设计公交车线路，如何设计知乎回答排名算法，如何预测房价……</p><p data-pid=\"tp1BiPYw\">以算法书为参考书来寻求解决方案，理解算法本质原理。不要硬啃，记住答案没用。</p><p data-pid=\"X3dmdsdb\">如何我告诉你，这个问题解决方案值一百万，你什么算法都学得会。</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1516513796, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1516513699283", "type": "feed", "target": {"id": "151692629", "type": "answer", "url": "https://api.zhihu.com/answers/151692629", "voteup_count": 4, "thanks_count": 2, "question": {"id": "26726794", "title": "各种机器学习算法的应用场景分别是什么（比如朴素贝叶斯、决策树、K 近邻、SVM、逻辑回归最大熵模型）？", "url": "https://api.zhihu.com/questions/26726794", "type": "question", "question_type": "normal", "created": 1416623643, "answer_count": 112, "comment_count": 0, "follower_count": 14909, "detail": "<p>k近邻、贝叶斯、决策树、svm、逻辑斯蒂回归和最大熵模型、隐马尔科夫、条件随机场、adaboost、em 这些在一般工作中分别用到的频率多大？一般用途是什么？需要注意什么？</p>", "excerpt": "k近邻、贝叶斯、决策树、svm、逻辑斯蒂回归和最大熵模型、隐马尔科夫、条件随机场、…", "bound_topic_ids": [350, 1093, 1103, 2143, 3084], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1489549000, "created_time": 1489549000, "author": {"id": "a13060756ec6c8f3dbc6319e482631c0", "name": "章鱼小丸子", "headline": "每一个不曾起舞的日子，都是对生命的辜负。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/jj-xx-91-53", "url_token": "jj-xx-91-53", "avatar_url": "https://pica.zhimg.com/v2-454e33db75435589f23b22c5efda9672_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"WvUCPsJ3\">算法是解决方法的数学抽象，一个算法诞生于某个应用场景下，但也可以用在其他应用场景。按场景来分不太合理。</p><br/><p data-pid=\"Ae5_rL3e\">比如pagerank是用来做网页排序的，有人把它用在文本处理上，发现效果奇好，于是发明了textrank。再比如word2vec是自然语言处理的方法，但有人用它来处理交互数据给微博用户做推荐。</p>", "excerpt": "算法是解决方法的数学抽象，一个算法诞生于某个应用场景下，但也可以用在其他应用场景。按场景来分不太合理。 比如pagerank是用来做网页排序的，有人把它用在文本处理上，发现效果奇好，于是发明了textrank。再比如word2vec是自然语言处理的方法，但有人用它来处理交互数据给微博用户做推荐。 ", "excerpt_new": "算法是解决方法的数学抽象，一个算法诞生于某个应用场景下，但也可以用在其他应用场景。按场景来分不太合理。 比如pagerank是用来做网页排序的，有人把它用在文本处理上，发现效果奇好，于是发明了textrank。再比如word2vec是自然语言处理的方法，但有人用它来处理交互数据给微博用户做推荐。 ", "preview_type": "expand", "preview_text": "<p data-pid=\"WvUCPsJ3\">算法是解决方法的数学抽象，一个算法诞生于某个应用场景下，但也可以用在其他应用场景。按场景来分不太合理。</p><br/><p data-pid=\"Ae5_rL3e\">比如pagerank是用来做网页排序的，有人把它用在文本处理上，发现效果奇好，于是发明了textrank。再比如word2vec是自然语言处理的方法，但有人用它来处理交互数据给微博用户做推荐。</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1516513699, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1516513561899", "type": "feed", "target": {"id": "151282052", "type": "answer", "url": "https://api.zhihu.com/answers/151282052", "voteup_count": 7664, "thanks_count": 2204, "question": {"id": "26726794", "title": "各种机器学习算法的应用场景分别是什么（比如朴素贝叶斯、决策树、K 近邻、SVM、逻辑回归最大熵模型）？", "url": "https://api.zhihu.com/questions/26726794", "type": "question", "question_type": "normal", "created": 1416623643, "answer_count": 112, "comment_count": 0, "follower_count": 14909, "detail": "<p>k近邻、贝叶斯、决策树、svm、逻辑斯蒂回归和最大熵模型、隐马尔科夫、条件随机场、adaboost、em 这些在一般工作中分别用到的频率多大？一般用途是什么？需要注意什么？</p>", "excerpt": "k近邻、贝叶斯、决策树、svm、逻辑斯蒂回归和最大熵模型、隐马尔科夫、条件随机场、…", "bound_topic_ids": [350, 1093, 1103, 2143, 3084], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1495049639, "created_time": 1489363425, "author": {"id": "d57c943a51bd73606ff2ff1bda5c79dc", "name": "xyzh", "headline": "CS PhD，主做用户行为建模", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xyzhang0914", "url_token": "xyzhang0914", "avatar_url": "https://picx.zhimg.com/v2-b9eec508f10dc3c6629b0deb522e1b12_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 93, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"VY4EqQTY\">关于这个问题我今天正好看到了这个文章。讲的正是各个算法的优劣分析，很中肯。</p><a href=\"https://zhuanlan.zhihu.com/p/25327755\" class=\"internal\"><span class=\"invisible\">https://</span><span class=\"visible\">zhuanlan.zhihu.com/p/25</span><span class=\"invisible\">327755</span><span class=\"ellipsis\"></span></a><p data-pid=\"655KAf5_\">正好14年的时候有人做过一个实验[1]，比较在不同数据集上（121个），不同的分类器（179个）的实际效果。</p><p data-pid=\"58Te1oPk\">论文题为：Do we Need Hundreds of Classifiers to Solve Real World Classification Problems?</p><p data-pid=\"VWkr2c8H\">实验时间有点早，我尝试着结合我自己的理解、一些最近的实验，来谈一谈吧。主要针对分类器(Classifier)。</p><br/><p data-pid=\"fYVEBE0h\">写给懒得看的人：</p><blockquote data-pid=\"l_8e6rrF\"><b>没有最好的分类器，只有最合适的分类器。</b></blockquote><p data-pid=\"Qoi3oSI2\">随机森林平均来说最强，但也只在9.9%的数据集上拿到了第一，优点是鲜有短板。</p><p data-pid=\"K82JF-vp\">SVM的平均水平紧随其后，在10.7%的数据集上拿到第一。</p><p data-pid=\"IRVvQcLW\">神经网络（13.2%）和boosting（~9%）表现不错。</p><p data-pid=\"vkv8Clzu\"><b>数据维度越高</b>，随机森林就比AdaBoost强越多，但是整体不及SVM[2]。</p><p data-pid=\"e-0FWPj3\"><b>数据量越大</b>，神经网络就越强。</p><br/><h2>近邻 (Nearest Neighbor)</h2><figure><noscript><img src=\"https://pic4.zhimg.com/v2-db981be0101f97bd2e29cc0d9494e1cb_b.jpg\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-db981be0101f97bd2e29cc0d9494e1cb\" class=\"content_image\" width=\"300\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;300&#39; height=&#39;305&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-db981be0101f97bd2e29cc0d9494e1cb\" class=\"content_image lazy\" width=\"300\" data-actualsrc=\"https://pic4.zhimg.com/v2-db981be0101f97bd2e29cc0d9494e1cb_b.jpg\"/></figure><p data-pid=\"q4voOUw9\">典型的例子是KNN，它的思路就是——对于待判断的点，找到离它最近的几个数据点，根据它们的类型决定待判断点的类型。</p><p data-pid=\"CxoYMG2n\">它的特点是完全跟着数据走，没有数学模型可言。</p><br/><p data-pid=\"egcu5sCG\">适用情景：</p><p data-pid=\"2KMzXCO7\">需要一个特别容易解释的模型的时候。</p><p data-pid=\"9P56lQ1R\">比如需要向用户解释原因的推荐算法。</p><br/><h2>贝叶斯 (Bayesian)</h2><figure><noscript><img src=\"https://picx.zhimg.com/v2-6a364799487ac3d08de175ee52bba54b_b.jpg\" data-rawwidth=\"800\" data-rawheight=\"238\" data-original-token=\"v2-6a364799487ac3d08de175ee52bba54b\" class=\"origin_image zh-lightbox-thumb\" width=\"800\" data-original=\"https://picx.zhimg.com/v2-6a364799487ac3d08de175ee52bba54b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;800&#39; height=&#39;238&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"800\" data-rawheight=\"238\" data-original-token=\"v2-6a364799487ac3d08de175ee52bba54b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"800\" data-original=\"https://picx.zhimg.com/v2-6a364799487ac3d08de175ee52bba54b_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-6a364799487ac3d08de175ee52bba54b_b.jpg\"/></figure><br/><p data-pid=\"Huo4fvPp\">典型的例子是Naive Bayes，核心思路是根据条件概率计算待判断点的类型。</p><p data-pid=\"UdUOYt8k\">是相对容易理解的一个模型，至今依然被垃圾邮件过滤器使用。</p><br/><p data-pid=\"a_h-0fTy\">适用情景：</p><p data-pid=\"aAIk-V6p\">需要一个比较容易解释，而且不同维度之间相关性较小的模型的时候。</p><p data-pid=\"jKX-F1P6\">可以高效处理高维数据，虽然结果可能不尽如人意。</p><br/><h2>决策树 (Decision tree)</h2><figure><noscript><img src=\"https://pica.zhimg.com/v2-4191c581aa44793282f1801caf4b378e_b.jpg\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-4191c581aa44793282f1801caf4b378e\" class=\"content_image\" width=\"300\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;300&#39; height=&#39;305&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-4191c581aa44793282f1801caf4b378e\" class=\"content_image lazy\" width=\"300\" data-actualsrc=\"https://pica.zhimg.com/v2-4191c581aa44793282f1801caf4b378e_b.jpg\"/></figure><br/><p data-pid=\"jWLtR84O\">决策树的特点是它总是在沿着特征做切分。随着层层递进，这个划分会越来越细。</p><p data-pid=\"Z8dDpUvk\">虽然生成的树不容易给用户看，但是数据分析的时候，通过观察树的上层结构，能够对分类器的核心思路有一个直观的感受。</p><p data-pid=\"kVztTIBc\">举个简单的例子，当我们预测一个孩子的身高的时候，决策树的第一层可能是这个孩子的性别。男生走左边的树进行进一步预测，女生则走右边的树。这就说明性别对身高有很强的影响。</p><br/><p data-pid=\"OFMt7PS7\">适用情景：</p><p data-pid=\"gzLKFezQ\">因为它能够生成清晰的基于特征(feature)选择不同预测结果的树状结构，数据分析师希望更好的理解手上的数据的时候往往可以使用决策树。</p><p data-pid=\"zdE_ATer\">同时它也是相对容易被攻击的分类器[3]。这里的攻击是指人为的改变一些特征，使得分类器判断错误。常见于垃圾邮件躲避检测中。因为决策树最终在底层判断是基于单个条件的，攻击者往往只需要改变很少的特征就可以逃过监测。</p><p data-pid=\"LWN36sqH\">受限于它的简单性，决策树更大的用处是作为一些更有用的算法的基石。</p><br/><h2>随机森林 (Random forest)</h2><figure><noscript><img src=\"https://pic2.zhimg.com/v2-5b55bf6ba5b214d4bf73867166cfe5ff_b.jpg\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-5b55bf6ba5b214d4bf73867166cfe5ff\" class=\"content_image\" width=\"300\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;300&#39; height=&#39;305&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-5b55bf6ba5b214d4bf73867166cfe5ff\" class=\"content_image lazy\" width=\"300\" data-actualsrc=\"https://pic2.zhimg.com/v2-5b55bf6ba5b214d4bf73867166cfe5ff_b.jpg\"/></figure><br/><p data-pid=\"Fx5oV6U_\">提到决策树就不得不提随机森林。顾名思义，森林就是很多树。</p><p data-pid=\"oDmIKmeK\">严格来说，随机森林其实算是一种集成算法。它首先随机选取不同的特征(feature)和训练样本(training sample)，生成大量的决策树，然后综合这些决策树的结果来进行最终的分类。</p><p data-pid=\"yLNoSvLe\">随机森林在现实分析中被大量使用，它相对于决策树，在准确性上有了很大的提升，同时一定程度上改善了决策树容易被攻击的特点。</p><br/><p data-pid=\"wQn0Qlyt\">适用情景：</p><p data-pid=\"Oe3fTSsI\">数据维度相对低（几十维），同时对准确性有较高要求时。</p><p data-pid=\"teBIX9q8\">因为不需要很多参数调整就可以达到不错的效果，基本上不知道用什么方法的时候都可以先试一下随机森林。</p><br/><h2>SVM (Support vector machine)</h2><figure><noscript><img src=\"https://pic2.zhimg.com/v2-31a190418b15d074a36eb42b5555b189_b.jpg\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-31a190418b15d074a36eb42b5555b189\" class=\"content_image\" width=\"300\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;300&#39; height=&#39;305&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-31a190418b15d074a36eb42b5555b189\" class=\"content_image lazy\" width=\"300\" data-actualsrc=\"https://pic2.zhimg.com/v2-31a190418b15d074a36eb42b5555b189_b.jpg\"/></figure><br/><p data-pid=\"dWyUeE-u\">SVM的核心思想就是找到不同类别之间的分界面，使得两类样本尽量落在面的两边，而且离分界面尽量远。</p><p data-pid=\"XDvGfQNc\">最早的SVM是平面的，局限很大。但是利用核函数(kernel function)，我们可以把平面投射(mapping)成曲面，进而大大提高SVM的适用范围。</p><figure><noscript><img src=\"https://pica.zhimg.com/v2-6f329fd5233c34fbf40a325f1b396ac0_b.jpg\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-6f329fd5233c34fbf40a325f1b396ac0\" class=\"content_image\" width=\"300\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;300&#39; height=&#39;305&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-6f329fd5233c34fbf40a325f1b396ac0\" class=\"content_image lazy\" width=\"300\" data-actualsrc=\"https://pica.zhimg.com/v2-6f329fd5233c34fbf40a325f1b396ac0_b.jpg\"/></figure><p data-pid=\"yXFYJJA2\">提高之后的SVM同样被大量使用，在实际分类中展现了很优秀的正确率。</p><br/><p data-pid=\"w_2_FLQd\">适用情景：</p><p data-pid=\"8x6l6vIL\">SVM在很多数据集上都有优秀的表现。</p><p data-pid=\"JyuatNKj\">相对来说，SVM尽量保持与样本间距离的性质导致它抗攻击的能力更强。</p><p data-pid=\"syqu53jC\">和随机森林一样，这也是一个拿到数据就可以先尝试一下的算法。</p><br/><h2>逻辑斯蒂回归 (Logistic regression)</h2><figure><noscript><img src=\"https://pic4.zhimg.com/v2-0bb8543ebe94192b6160046e74a964b3_b.jpg\" data-rawwidth=\"300\" data-rawheight=\"405\" data-original-token=\"v2-0bb8543ebe94192b6160046e74a964b3\" class=\"content_image\" width=\"300\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;300&#39; height=&#39;405&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"300\" data-rawheight=\"405\" data-original-token=\"v2-0bb8543ebe94192b6160046e74a964b3\" class=\"content_image lazy\" width=\"300\" data-actualsrc=\"https://pic4.zhimg.com/v2-0bb8543ebe94192b6160046e74a964b3_b.jpg\"/></figure><p data-pid=\"KS8CFpWi\">逻辑斯蒂回归这个名字太诡异了，我就叫它LR吧，反正讨论的是分类器，也没有别的方法叫LR。顾名思义，它其实是回归类方法的一个变体。</p><p data-pid=\"QGCDJzdQ\">回归方法的核心就是为函数找到最合适的参数，使得函数的值和样本的值最接近。例如线性回归(Linear regression)就是对于函数f(x)=ax+b，找到最合适的a,b。</p><p data-pid=\"W13Zfy6g\">LR拟合的就不是线性函数了，它拟合的是一个概率学中的函数，f(x)的值这时候就反映了样本属于这个类的概率。</p><br/><p data-pid=\"urAG0bxp\">适用情景：</p><p data-pid=\"wTqXawyA\">LR同样是很多分类算法的基础组件，它的好处是输出值自然地落在0到1之间，并且有概率意义。</p><p data-pid=\"XULqc9J_\">因为它本质上是一个线性的分类器，所以处理不好特征之间相关的情况。</p><p data-pid=\"EXGy79my\">虽然效果一般，却胜在模型清晰，背后的概率学经得住推敲。它拟合出来的参数就代表了每一个特征(feature)对结果的影响。也是一个理解数据的好工具。</p><br/><h2>判别分析 (Discriminant analysis)</h2><figure><noscript><img src=\"https://pic4.zhimg.com/v2-768d9045306ce042edc4926f6fcf3b79_b.jpg\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-768d9045306ce042edc4926f6fcf3b79\" class=\"content_image\" width=\"300\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;300&#39; height=&#39;305&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"300\" data-rawheight=\"305\" data-original-token=\"v2-768d9045306ce042edc4926f6fcf3b79\" class=\"content_image lazy\" width=\"300\" data-actualsrc=\"https://pic4.zhimg.com/v2-768d9045306ce042edc4926f6fcf3b79_b.jpg\"/></figure><p data-pid=\"1ZP5oNfn\">判别分析主要是统计那边在用，所以我也不是很熟悉，临时找统计系的闺蜜补了补课。这里就现学现卖了。</p><p data-pid=\"T3PBX7Ie\">判别分析的典型例子是线性判别分析(Linear discriminant analysis)，简称LDA。</p><p data-pid=\"Kf4Sf1GH\">（这里注意不要和隐含狄利克雷分布(Latent Dirichlet allocation)弄混，虽然都叫LDA但说的不是一件事。）</p><p data-pid=\"eynozbAj\">LDA的核心思想是把高维的样本投射(project)到低维上，如果要分成两类，就投射到一维。要分三类就投射到二维平面上。这样的投射当然有很多种不同的方式，LDA投射的标准就是让同类的样本尽量靠近，而不同类的尽量分开。对于未来要预测的样本，用同样的方式投射之后就可以轻易地分辨类别了。</p><br/><p data-pid=\"eu6t4f4a\">使用情景：</p><p data-pid=\"zVZeI2OI\">判别分析适用于高维数据需要降维的情况，自带降维功能使得我们能方便地观察样本分布。它的正确性有数学公式可以证明，所以同样是很经得住推敲的方式。</p><p data-pid=\"pFM7zSvl\">但是它的分类准确率往往不是很高，所以不是统计系的人就把它作为降维工具用吧。</p><p data-pid=\"Ktgxp2vu\">同时注意它是假定样本成正态分布的，所以那种同心圆形的数据就不要尝试了。</p><br/><h2>神经网络 (Neural network)</h2><p data-pid=\"gzX8K_Yk\">神经网络现在是火得不行啊。它的核心思路是利用训练样本(training sample)来逐渐地完善参数。还是举个例子预测身高的例子，如果输入的特征中有一个是性别（1:男；0:女），而输出的特征是身高（1:高；0:矮）。那么当训练样本是一个个子高的男生的时候，在神经网络中，从“男”到“高”的路线就会被强化。同理，如果来了一个个子高的女生，那从“女”到“高”的路线就会被强化。</p><p data-pid=\"n0hq8UFs\">最终神经网络的哪些路线比较强，就由我们的样本所决定。</p><p data-pid=\"JDBxtI3a\">神经网络的优势在于，它可以有很多很多层。如果输入输出是直接连接的，那它和LR就没有什么区别。但是通过大量中间层的引入，它就能够捕捉很多输入特征之间的关系。卷积神经网络有很经典的不同层的可视化展示(visulization)，我这里就不赘述了。</p><p data-pid=\"tl8KF66f\">神经网络的提出其实很早了，但是它的准确率依赖于庞大的训练集，原本受限于计算机的速度，分类效果一直不如随机森林和SVM这种经典算法。</p><br/><p data-pid=\"naXgPZ2n\">使用情景：</p><p data-pid=\"DzXAQ7LS\">数据量庞大，参数之间存在内在联系的时候。</p><p data-pid=\"Z0lOmd27\">当然现在神经网络不只是一个分类器，它还可以用来生成数据，用来做降维，这些就不在这里讨论了。</p><br/><h2>Rule-based methods</h2><p data-pid=\"9ti2cSF5\">这个我是真不熟，都不知道中文翻译是什么。</p><p data-pid=\"ST0yGer_\">它里面典型的算法是C5.0 Rules，一个基于决策树的变体。因为决策树毕竟是树状结构，理解上还是有一定难度。所以它把决策树的结果提取出来，形成一个一个两三个条件组成的小规则。</p><br/><p data-pid=\"NVqCQ7us\">使用情景：</p><p data-pid=\"ncwKGKzd\">它的准确度比决策树稍低，很少见人用。大概需要提供明确小规则来解释决定的时候才会用吧。</p><br/><h2>提升算法（Boosting）</h2><p data-pid=\"92VLwdh2\">接下来讲的一系列模型，都属于集成学习算法(Ensemble Learning)，基于一个核心理念：三个臭皮匠，顶个诸葛亮。</p><p data-pid=\"6RJk5BRB\">翻译过来就是：当我们把多个较弱的分类器结合起来的时候，它的结果会比一个强的分类器更</p><p data-pid=\"vy-J-Mtc\">典型的例子是AdaBoost。</p><p data-pid=\"vyZTFShb\">AdaBoost的实现是一个渐进的过程，从一个最基础的分类器开始，每次寻找一个最能解决当前错误样本的分类器。用加权取和(weighted sum)的方式把这个新分类器结合进已有的分类器中。</p><p data-pid=\"VfT0nUdv\">它的好处是自带了特征选择（feature selection），只使用在训练集中发现有效的特征(feature)。这样就降低了分类时需要计算的特征数量，也在一定程度上解决了高维数据难以理解的问题。</p><p data-pid=\"3pnkw_SI\">最经典的AdaBoost实现中，它的每一个弱分类器其实就是一个决策树。这就是之前为什么说决策树是各种算法的基石。</p><br/><p data-pid=\"73PvLhja\">使用情景：</p><p data-pid=\"1C4MYivK\">好的Boosting算法，它的准确性不逊于随机森林。虽然在[1]的实验中只有一个挤进前十，但是实际使用中它还是很强的。因为自带特征选择（feature selection）所以对新手很友好，是一个“不知道用什么就试一下它吧”的算法。</p><br/><h2>装袋算法（Bagging）</h2><p data-pid=\"BQb4FMiY\">同样是弱分类器组合的思路，相对于Boosting，其实Bagging更好理解。它首先随机地抽取训练集（training set），以之为基础训练多个弱分类器。然后通过取平均，或者投票(voting)的方式决定最终的分类结果。</p><p data-pid=\"TUGBuK7C\">因为它随机选取训练集的特点，Bagging可以一定程度上避免过渡拟合(overfit)。</p><p data-pid=\"_e6abE6g\">在[1]中，最强的Bagging算法是基于SVM的。如果用定义不那么严格的话，随机森林也算是Bagging的一种。</p><br/><p data-pid=\"GKbXR4Qp\">使用情景：</p><p data-pid=\"JE9qtvbr\">相较于经典的必使算法，Bagging使用的人更少一些。一部分的原因是Bagging的效果和参数的选择关系比较大，用默认参数往往没有很好的效果。</p><p data-pid=\"33IcWKO2\">虽然调对参数结果会比决策树和LR好，但是模型也变得复杂了，没事有特别的原因就别用它了。</p><br/><h2>Stacking</h2><p data-pid=\"UXJB0Eh0\">这个我是真不知道中文怎么说了。它所做的是在多个分类器的结果上，再套一个新的分类器。</p><p data-pid=\"8HMMRdHc\">这个新的分类器就基于弱分类器的分析结果，加上训练标签(training label)进行训练。一般这最后一层用的是LR。</p><p data-pid=\"1ytD-uNw\">Stacking在[1]里面的表现不好，可能是因为增加的一层分类器引入了更多的参数，也可能是因为有过渡拟合(overfit)的现象。</p><br/><p data-pid=\"XnUBv5CU\">使用情景：</p><p data-pid=\"h7mfWexN\">没事就别用了。</p><p data-pid=\"R66oQUdt\">（修订：<a class=\"member_mention\" href=\"https://www.zhihu.com/people/7ca4a0bcf00428d17e73e7ad0bd51e4d\" data-hash=\"7ca4a0bcf00428d17e73e7ad0bd51e4d\" data-hovercard=\"p$b$7ca4a0bcf00428d17e73e7ad0bd51e4d\">@庄岩</a> 提醒说stacking在数据挖掘竞赛的网站kaggle上很火，相信参数调得好的话还是对结果能有帮助的。</p><p data-pid=\"cTMVDLy0\"><a href=\"https://link.zhihu.com/?target=http%3A//blog.kaggle.com/2016/12/27/a-kagglers-guide-to-model-stacking-in-practice/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">blog.kaggle.com/2016/12</span><span class=\"invisible\">/27/a-kagglers-guide-to-model-stacking-in-practice/</span><span class=\"ellipsis\"></span></a> </p><p data-pid=\"VK7hA_1i\">这篇文章很好地介绍了stacking的好处。在kaggle这种一点点提升就意味着名次不同的场合下，stacking还是很有效的，但是对于一般商用，它所带来的提升就很难值回额外的复杂度了。）</p><br/><br/><h2>多专家模型（Mixture of Experts）</h2><p data-pid=\"SD2H-A9J\">最近这个模型还挺流行的，主要是用来合并神经网络的分类结果。我也不是很熟，对神经网络感兴趣，而且训练集异质性（heterogeneity）比较强的话可以研究一下这个。</p><br/><p data-pid=\"EsQyTm_Q\"><b>讲到这里分类器其实基本说完了。讲一下问题里面其他一些名词吧。</b></p><br/><h2>最大熵模型 (Maximum entropy model) </h2><p data-pid=\"Q7UswWRV\">最大熵模型本身不是分类器，它一般是用来判断模型预测结果的好坏的。</p><p data-pid=\"XNo58ZpV\">对于它来说，分类器预测是相当于是：针对样本，给每个类一个出现概率。比如说样本的特征是：性别男。我的分类器可能就给出了下面这样一个概率：高（60%），矮（40%）。</p><p data-pid=\"55bRBx-Z\">而如果这个样本真的是高的，那我们就得了一个分数60%。最大熵模型的目标就是让这些分数的乘积尽量大。</p><p data-pid=\"Q8gbdzZX\">LR其实就是使用最大熵模型作为优化目标的一个算法[4]。</p><br/><h2>EM</h2><p data-pid=\"qkVENg1V\">就像最大熵模型一样，EM不是分类器，而是一个思路。很多算法都是基于这个思路实现的。</p><p data-pid=\"_u8zZaRo\">@刘奕驰  已经讲得很清楚了，我就不多说了。</p><br/><br/><h2>隐马尔科夫 (Hidden Markov model)</h2><p data-pid=\"_sQBRJ8N\">这是一个基于序列的预测方法，核心思想就是通过上一个（或几个）状态预测下一个状态。</p><p data-pid=\"J1Tpl88M\">之所以叫“隐”马尔科夫是因为它的设定是状态本身我们是看不到的，我们只能根据状态生成的结果序列来学习可能的状态。</p><br/><p data-pid=\"nV_1H3kF\">适用场景：</p><p data-pid=\"g4_Vh9gY\">可以用于序列的预测，可以用来生成序列。</p><br/><h2>条件随机场 (Conditional random field)</h2><p data-pid=\"n2ZeI4Ql\">典型的例子是linear-chain CRF。</p><p data-pid=\"WyC9hV_3\">具体的使用 @Aron 有讲，我就不献丑了，因为我从来没用过这个。</p><br/><p data-pid=\"WUkR5cJl\">就是这些啦。</p><br/><p data-pid=\"osn3UT7k\">相关的文章：</p><p data-pid=\"aJcWyNRg\">[1]: Do we need hundreds of classifiers to solve real world classification problems.</p><p data-pid=\"3bbkZ1nB\">Fernández-Delgado, Manuel, et al. J. Mach. Learn. Res 15.1 (2014)</p><p data-pid=\"MYaVBQWW\">[2]: An empirical evaluation of supervised learning in high dimensions.</p><p data-pid=\"zr9EK4lm\">Rich Caruana, Nikos Karampatziakis, and Ainur Yessenalina. ICML &#39;08</p><p data-pid=\"fx7NQWZD\">[3]: Man vs. Machine: Practical Adversarial Detection of Malicious Crowdsourcing Workers</p><p data-pid=\"dNnKgcr0\">Wang, G., Wang, T., Zheng, H., &amp; Zhao, B. Y. Usenix Security&#39;14</p><p data-pid=\"nFrCVMbU\">[4]: <a href=\"https://link.zhihu.com/?target=http%3A//www.win-vector.com/dfiles/LogisticRegressionMaxEnt.pdf\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">win-vector.com/dfiles/L</span><span class=\"invisible\">ogisticRegressionMaxEnt.pdf</span><span class=\"ellipsis\"></span></a></p>", "excerpt": "关于这个问题我今天正好看到了这个文章。讲的正是各个算法的优劣分析，很中肯。 <a href=\"https://zhuanlan.zhihu.com/p/25327755\" class=\"internal\"><span class=\"invisible\">https://</span><span class=\"visible\">zhuanlan.zhihu.com/p/25</span><span class=\"invisible\">327755</span><span class=\"ellipsis\"></span></a>正好14年的时候有人做过一个实验[1]，比较在不同数据集上（121个），不同的分类器（179个）的实际效果。 论文题为：Do we Need Hundreds of Classifiers to Solve Real World Classification Problems? 实验时间有点早，我尝试着结合我自己的理解、一些最近的实验，来谈一谈吧。主要针对分类器(Classifier)。 写给懒得看的人： <b>…</b>", "excerpt_new": "关于这个问题我今天正好看到了这个文章。讲的正是各个算法的优劣分析，很中肯。 <a href=\"https://zhuanlan.zhihu.com/p/25327755\" class=\"internal\"><span class=\"invisible\">https://</span><span class=\"visible\">zhuanlan.zhihu.com/p/25</span><span class=\"invisible\">327755</span><span class=\"ellipsis\"></span></a>正好14年的时候有人做过一个实验[1]，比较在不同数据集上（121个），不同的分类器（179个）的实际效果。 论文题为：Do we Need Hundreds of Classifiers to Solve Real World Classification Problems? 实验时间有点早，我尝试着结合我自己的理解、一些最近的实验，来谈一谈吧。主要针对分类器(Classifier)。 写给懒得看的人： <b>…</b>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {"REACTION_CONTENT_SEGMENT_LIKE": "HIDE"}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1516513561, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1516513561899&page_num=71"}}