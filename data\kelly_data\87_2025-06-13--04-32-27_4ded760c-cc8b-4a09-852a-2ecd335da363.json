{"data": [{"id": "1453840176082", "type": "feed", "target": {"id": "73319548", "type": "answer", "url": "https://api.zhihu.com/answers/73319548", "voteup_count": 11478, "thanks_count": 1149, "question": {"id": "37721533", "title": "作为一个作死小能手是种怎样的体验？", "url": "https://api.zhihu.com/questions/37721533", "type": "question", "question_type": "normal", "created": 1448074626, "answer_count": 1576, "comment_count": 17, "follower_count": 29551, "detail": "<p>相关问题：<a href=\"https://www.zhihu.com/question/50363937\" class=\"internal\">有一个爱作死的小伙伴是怎样的体验？ - 生活</a></p>", "excerpt": "相关问题：<a href=\"https://www.zhihu.com/question/50363937\" class=\"internal\">有一个爱作死的小伙伴是怎样的体验？ - 生活</a>", "bound_topic_ids": [3352, 6446, 117317, 155709, 1833036], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "d46d541b732f75376e379faa199f031d", "name": "嘻嘻哥哥", "headline": "知乎有毒啊", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/youngcy-li", "url_token": "young<PERSON>-li", "avatar_url": "https://picx.zhimg.com/df9785453e9d199f387f3d78f26d7b19_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1454731428, "created_time": 1448119236, "author": {"id": "1c0c2a921e0f55921c7b15d27fb37aa6", "name": "Stereohead", "headline": "苟……", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/zhao-ri-tian-44-54", "url_token": "zhao-ri-tian-44-54", "avatar_url": "https://picx.zhimg.com/v2-e12385c16bd43304ee5cfcb4570237ec_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 615, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"gcGOaM4l\">图片来源于网络,出处不详.</p><figure><noscript><img data-rawwidth=\"600\" data-rawheight=\"3092\" src=\"https://pic4.zhimg.com/67ee9887ce12f789ceead371954f81ed_b.jpg\" data-original-token=\"67ee9887ce12f789ceead371954f81ed\" class=\"origin_image zh-lightbox-thumb\" width=\"600\" data-original=\"https://pic4.zhimg.com/67ee9887ce12f789ceead371954f81ed_r.jpg\"/></noscript><img data-rawwidth=\"600\" data-rawheight=\"3092\" src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;600&#39; height=&#39;3092&#39;&gt;&lt;/svg&gt;\" data-original-token=\"67ee9887ce12f789ceead371954f81ed\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"600\" data-original=\"https://pic4.zhimg.com/67ee9887ce12f789ceead371954f81ed_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/67ee9887ce12f789ceead371954f81ed_b.jpg\"/></figure>", "excerpt": "图片来源于网络,出处不详. ", "excerpt_new": "图片来源于网络,出处不详. ", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1453840176, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1453266192920", "type": "feed", "target": {"id": "22393192", "type": "answer", "url": "https://api.zhihu.com/answers/22393192", "voteup_count": 369, "thanks_count": 77, "question": {"id": "21677041", "title": "HBase 和 Hive 的差别是什么，各自适用在什么场景中？", "url": "https://api.zhihu.com/questions/21677041", "type": "question", "question_type": "normal", "created": 1379679215, "answer_count": 53, "comment_count": 1, "follower_count": 1393, "detail": "<p>比起单机的 MySQL、Oracle 有什么优势，有没有什么劣势？</p>", "excerpt": "比起单机的 MySQL、Oracle 有什么优势，有没有什么劣势？", "bound_topic_ids": [4373, 16911, 35123, 63708, 400984], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1392183277, "created_time": 1392178346, "author": {"id": "8aaf3d670545b4997273a988622f21e7", "name": "taiwo", "headline": "在实践中完善方法论...", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/shao-bing", "url_token": "shao-bing", "avatar_url": "https://picx.zhimg.com/517dfeab9_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 5, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"MwRZXHQt\">网络上有篇deck，题目为 NoSQL and Big Data Processing - Hbase, Hive and Pig (</p><a href=\"https://link.zhihu.com/?target=http%3A//www.cs.kent.edu/~jin/Cloud12Spring/HbaseHivePig.pptx\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">cs.kent.edu/~jin/Cloud1</span><span class=\"invisible\">2Spring/HbaseHivePig.pptx</span><span class=\"ellipsis\"></span></a><p data-pid=\"AVxljVSz\">)，从 关系型数据库开始，到 NoSQL, 到 CAP 原理，再到 HBase 和 Hive，基本描述了整个数据存储的演进路线以及原因。</p><p data-pid=\"1V4hr3oR\">以下是我个人对这篇deck的整理，和deck的结构基本相同。虽然不能直接回答题主的问题，但相信读完这个deck之后，这个问题一定可以迎刃而解。</p><p data-pid=\"mfduJ56m\"><b>1. RDBMS</b></p><p data-pid=\"Kud3jpA-\">让数据集保持在一台单一的机器上是RDBMS提供ACID特性和丰富查询模型的最好方式。但数据集变大时，垂直扩展（scaling up）带来诸多限制。企业慢慢发现，通过增加多节点的服务器进行横向扩展（scaling out）是一种更经济和更可行的方式。DBA们对RDBMS采用的横向扩展的方法主要有主从复制（Master-slave）、分片（Sharding）。</p><p data-pid=\"KqjFug3q\">横向扩展RDBMS – Master/Slave </p><ul><li data-pid=\"IqRk2iXf\">利用数据库的复制或镜像功能，同时在多台数据库上保存相同的数据，并且将读操作和写操作分开，写操作集中在一台主数据库上，读操作集中在多台从数据库上。复制过程的速度与系统中的从节点数量成反比。<br/></li><li data-pid=\"MfCQF79M\">关键的读取可能不正确，因为写可能没有来得及被向下传播；<br/></li><li data-pid=\"NI67PcwU\">大数据集可能会造成问题，因为主节点需要将数据复制到从节点；<br/></li></ul><p data-pid=\"NIq4E3ws\">横向扩展RDBMS - Sharding</p><ul><li data-pid=\"Tz4k3Ske\">通常来说，在满足ACID特性的数据库中进行扩展是非常难的。基于这个原因，对数据进行扩展，这个数据库本身就必须拥有简单的模型，将数据分割为N片，然后在单独的片中执行查询。数据分割的单元被称为“shard”。将N片数据分配个M个DBMS进行操作。DBMS并不会去管理数据片，这需由服务开发者自行完成。<br/></li><li data-pid=\"4qz4Jwe1\">不同的分片方法有：<br/></li></ul><p data-pid=\"Exd2JRxU\">      - 垂直分区（Vertical Partitioning）：将不需要进行联合查询的数据表分散到不同的数据库服务器上。</p><p data-pid=\"jHH9yC2l\">      - 水平分区(sharding/分表) 将同一个表的记录拆分到不同的表甚至是服务器上，这往往需要一个稳定的算法来保证读取时能正确从不同的服务器上取得数据。比如，将ZIP codes小于50000的客户存储在CustomersEast，将ZIP codes大于或等于50000的客户存储在CustomersWest。CustomersEast和CustomersWest就成为两个分区表。方法有 Range-Based Partitioning, Key or Hash-Based partitioning等。</p><ul><li data-pid=\"6LrJhcVF\">优点与不足<br/></li></ul><p data-pid=\"YdlVBz39\">      - 对读取和写入都有很好的扩展</p><p data-pid=\"pn6VgU8N\">      - 不透明，应用需要做到可识别分区 </p><p data-pid=\"AruKl5M0\">      - 不再有跨分区的关系/joins </p><p data-pid=\"Rd-ZFjcF\">      - 跨片参照完整性损失</p><p data-pid=\"G84i8fD_\">其他RDBMS扩展方法</p><ul><li data-pid=\"UreZ4QmB\">Multi-Master replication：所有成员都响应客户端数据查询。多主复制系统负责将任意成员做出的数据更新传播给组内其他成员，并解决不同成员间并发修改可能带来的冲突。<br/></li><li data-pid=\"dV9b7P9L\">INSERT only, not UPDATES/DELETES：数据进行版本化处理。<br/></li><li data-pid=\"oFw3Voho\">No JOINs, thereby reducing query time：Join的开销很大,而且频繁访问会使开销随着时间逐渐增加。非规范化（Denormalization）可以降低数据仓库的复杂性，以提高效率和改善性能。<br/></li><li data-pid=\"2MI6sVD2\">In-memory databases：磁盘数据库解决的是大容量存储和数据分析问题，内存数据库解决的是实时处理和高并发问题。主流常规的RDBMS更多的是磁盘密集型，而不是内存密集型。<br/></li></ul><p data-pid=\"uHpUfkjM\"><b>2. NoSQL</b></p><p data-pid=\"d4eVzNI3\">NoSQL现在被理解为 Not Only SQL 的缩写，是对非关系型的数据库管理系统的统称（正因为此，人们通常理解 NoSQL 是 anti-RDBMS）。</p><p data-pid=\"6ZviSuqy\">NoSQL 与 RDBMS 存在许多不同点，</p><p data-pid=\"oNfCShvo\">- 最重要的是NoSQL不使用SQL作为查询语言。</p><p data-pid=\"S2I9hpzI\">- NoSQL 不需要固定的表模式(table schema)，也经常会避免使用SQL的JOIN操作，一般有可水平扩展的特征。</p><p data-pid=\"IoGZ7Wqe\">- NoSQL产品会放宽一个或多个 ACID 属性（CAP定理）</p><p data-pid=\"W9NoPOXs\">CAP 理论</p><p data-pid=\"1mnpcN6D\">CAP理论是数据系统设计的基本理论，目前几乎所有的数据系统的设计都遵循了这个理论。CAP理论指出，分布式系统只能满足以下三项中的两项而不可能满足全部三项，</p><ul><li data-pid=\"vh0U28A_\">一致性（Consistency)（所有节点在同一时间具有相同的数据）<br/></li><li data-pid=\"QsOy0i93\">可用性（Availability）（保证每个请求不管成功或者失败都有响应）<br/></li><li data-pid=\"qriJazeH\">分区容忍性（Partition tolerance）（系统中任意信息的丢失或失败不会影响系统的继续运作）<br/></li></ul><p data-pid=\"i_1jw4X2\">一致性有两种类型：</p><p data-pid=\"2kOj-RVY\">  - strong consistency – ACID(Atomicity Consistency Isolation Durability)：对于关系型数据库，要求更新过的数据能被后续所有的访问都看到，这是强一致性。</p><p data-pid=\"5Ag3CpKY\">  - weak consistency – BASE(Basically Available Soft-state Eventual consistency )</p><p data-pid=\"ofnuGi4g\">-- Basically Available - system seems to work all the time  (基本可用)</p><p data-pid=\"liuo65S0\">-- Soft State - it doesn&#39;t have to be consistent all the time （不要求所有时间都一致）</p><p data-pid=\"mxzAe9NF\">-- Eventually Consistent - becomes consistent at some later time （最终一致性）</p><p data-pid=\"YoL_nIZm\">对于分布式数据系统(scale out)，分区容忍性是基本要求，否则就失去了价值。因此只能在一致性和可用性上做取舍，如何处理这种取舍正是目前NoSQL数据库的核心焦点。几乎所有的情况都是牺牲一致性而换取高可用性。当然，牺牲一致性，只是不再要求关系数据库中的强一致性，而是只要系统能达到最终一致性即可。考虑到客户体验，这个最终一致的时间窗口，要尽可能的对用户透明，也就是需要保障“用户感知到的一致性”。通常是通过数据的多份异步复制来实现系统的高可用和数据的最终一致性的。</p><p data-pid=\"7lWTpOQr\"><b>3. HBase</b></p><p data-pid=\"kvniTp1c\">HBase is an open-source, distributed, column-oriented database built on top of HDFS (or KFS) based on BigTable! </p><p data-pid=\"aMLPRaT9\">按照CAP理论，HBase属于C+P类型的系统。HBase是强一致性的（仅支持单行事务）。每一行由单个区域服务器（region server）host，行锁（row locks）和多版本并发控制(multiversion concurrency control)的组合被用来保证行的一致性。</p><p data-pid=\"acqPxg6D\">There are three types of lookups:</p><ul><li data-pid=\"9ZKsQCdr\">Fast lookup using row key and optional timestamp.<br/></li><li data-pid=\"us_zFwEI\">Range scan from region start to end.<br/></li><li data-pid=\"oMip-Kkk\">Full table scan (全表扫描)<br/></li></ul><p data-pid=\"WUMB4KIf\">Access or manipulate</p><p data-pid=\"xiT6xKle\">  - Programmatic access via Java, REST, or Thrift APIs.</p><p data-pid=\"FTuRvZQq\">  - Scripting via JRuby.</p><p data-pid=\"RmBB40gx\">HBase benefits than RDBMS</p><p data-pid=\"uyQNfcx8\">  - No real indexes</p><p data-pid=\"K_JakLyu\">  - Automatic partitioning</p><p data-pid=\"Mpf2gbdx\">  - Scale linearly and automatically with new nodes</p><p data-pid=\"1RiVs8V9\">  - Commodity hardware</p><p data-pid=\"xLSewDre\">  - Fault tolerance</p><p data-pid=\"-pmgmLKz\">  - Batch processing</p><p data-pid=\"9ZMtr_AI\"><b>4. Hive</b></p><p data-pid=\"josptA_S\">  - Provide higher-level language (HQL, like SQL) to facilitate large-data processing</p><p data-pid=\"zolC7LYW\">  - Higher-level language “compiles down” to Hadoop Map/Reduce jobs</p><p data-pid=\"ioBRHRQb\"><b>5. Hive + HBase</b></p><p data-pid=\"cwLM-8ow\">Reasons to use Hive on HBase:</p><ul><li data-pid=\"Ko7aQBx2\">A lot of data sitting in HBase due to its usage in a real-time environment, but never used for analysis<br/></li><li data-pid=\"PieQYmiE\">Give access to data in HBase usually only queried through MapReduce to people that don’t code (business analysts)<br/></li><li data-pid=\"zsXOIouH\">When needing a more flexible storage solution, so that rows can be updated live by either a Hive job or an application and can be seen immediately to the other<br/></li></ul><p data-pid=\"mkrN0sSq\"><b>References</b></p><p data-pid=\"0R63uLch\">1. Hbase, Hive and Pig: </p><a href=\"https://link.zhihu.com/?target=http%3A//www.cs.kent.edu/~jin/Cloud12Spring/HbaseHivePig.pptx\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">cs.kent.edu/~jin/Cloud1</span><span class=\"invisible\">2Spring/HbaseHivePig.pptx</span><span class=\"ellipsis\"></span></a><p data-pid=\"BliO55Tl\">2. </p><a href=\"https://link.zhihu.com/?target=http%3A//zh.wikipedia.org/wiki/Slashdot%25E6%2595%2588%25E5%25BA%2594\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Slashdot效应</a><br/><p data-pid=\"xywoHYPt\">3. </p><a href=\"https://link.zhihu.com/?target=http%3A//www.csdn.net/article/2013-08-08/2816503-rdbms-vs-nosql\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">RDBMS vs. NoSQL：反派为什么会得以存活并发展壮大-CSDN.NET</a><br/><p data-pid=\"utemoC5N\">4. </p><a href=\"https://link.zhihu.com/?target=http%3A//en.wikipedia.org/wiki/Partition_%28database%29\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Partition (database)</a><br/><p data-pid=\"9kOzkgJ0\">5. </p><a href=\"https://link.zhihu.com/?target=http%3A//en.wikipedia.org/wiki/Multi-master_replication\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Multi-master replication</a><br/><p data-pid=\"Yg2_yGJR\">6. </p><a href=\"https://link.zhihu.com/?target=http%3A//soft.chinabyte.com/database/93/12835593.shtml\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">内存数据库的基本原理与应用</a><br/><p data-pid=\"BuV3gSDf\">7. </p><a href=\"https://link.zhihu.com/?target=http%3A//zh.wikipedia.org/zh-cn/NoSQL\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">NoSQL</a><br/><p data-pid=\"RXjI9P0-\">8. </p><a href=\"https://link.zhihu.com/?target=http%3A//zh.wikipedia.org/wiki/CAP%25E5%25AE%259A%25E7%2590%2586\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">CAP定理</a><br/><p data-pid=\"aN2eLSvL\">9. </p><a href=\"https://link.zhihu.com/?target=http%3A//www.programmer.com.cn/9260/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">如何“打败”CAP定理</a><br/><p data-pid=\"Bt_y7hcE\">10. </p><a href=\"https://link.zhihu.com/?target=http%3A//en.wikipedia.org/wiki/Eventual_consistency\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Eventual consistency</a><br/><p data-pid=\"2Ufg1DyW\">11. </p><a href=\"https://link.zhihu.com/?target=https%3A//hbase.apache.org/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">HBase - \n    Apache HBase Home</a><br/><p data-pid=\"BM5VN0qb\">12. </p><a href=\"https://link.zhihu.com/?target=http%3A//www.alidata.org/archives/1509\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">hbase介绍 - 阿里数据平台 alidata.org</a><br/><p data-pid=\"a2aQKCCO\">13. </p><a href=\"https://link.zhihu.com/?target=http%3A//database.51cto.com/art/201008/218065.htm\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">细数Google HBase与BigTable区别在哪里？</a>", "excerpt": "网络上有篇deck，题目为 NoSQL and Big Data Processing - Hbase, Hive and Pig ( <a href=\"https://link.zhihu.com/?target=http%3A//www.cs.kent.edu/~jin/Cloud12Spring/HbaseHivePig.pptx\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">cs.kent.edu/~jin/Cloud1</span><span class=\"invisible\">2Spring/HbaseHivePig.pptx</span><span class=\"ellipsis\"></span></a>)，从 关系型数据库开始，到 NoSQL, 到 CAP 原理，再到 HBase 和 Hive，基本描述了整个数据存储的演进路线以及原因。 以下是我个人对这篇deck的整理，和deck的结构基本相同。虽然不能直接回答题主的问题，但相信读完这个deck之后，这个问题一定可以迎刃而解。 <b>1. RDBMS</b>让数据集保持在一台单一的机器上是RDBMS提供ACID特性和…", "excerpt_new": "网络上有篇deck，题目为 NoSQL and Big Data Processing - Hbase, Hive and Pig ( <a href=\"https://link.zhihu.com/?target=http%3A//www.cs.kent.edu/~jin/Cloud12Spring/HbaseHivePig.pptx\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">cs.kent.edu/~jin/Cloud1</span><span class=\"invisible\">2Spring/HbaseHivePig.pptx</span><span class=\"ellipsis\"></span></a>)，从 关系型数据库开始，到 NoSQL, 到 CAP 原理，再到 HBase 和 Hive，基本描述了整个数据存储的演进路线以及原因。 以下是我个人对这篇deck的整理，和deck的结构基本相同。虽然不能直接回答题主的问题，但相信读完这个deck之后，这个问题一定可以迎刃而解。 <b>1. RDBMS</b>让数据集保持在一台单一的机器上是RDBMS提供ACID特性和…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1453266192, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1452333102857", "type": "feed", "target": {"id": "38316747", "type": "answer", "url": "https://api.zhihu.com/answers/38316747", "voteup_count": 8751, "thanks_count": 2347, "question": {"id": "21423568", "title": "如何布置独居小房间能惬意地生活？", "url": "https://api.zhihu.com/questions/21423568", "type": "question", "question_type": "normal", "created": 1375512027, "answer_count": 620, "comment_count": 70, "follower_count": 134800, "detail": "还有几个月毕业，马上迎来独居的生活，话说期待一个人住的日子很久了呃... 现在暂时想到的是尽量合理利用空间，买个带书桌的高低床，书架、小桌板、衣柜...外加小冰箱、小洗衣机、微波炉、电磁炉...其实现在手边还有酸奶机、煮蛋器... 因为人在香港，红酒很便宜，经常幻想周末的晚上边看电影边喝红酒，或者早上的时候坐在窗台上看书... 再说下去就跑偏了...捂脸", "excerpt": "还有几个月毕业，马上迎来独居的生活，话说期待一个人住的日子很久了呃... 现在暂时…", "bound_topic_ids": [307, 1280, 21287, 57924], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "ac6d9606fdc0e164364ea21e8f8d8591", "name": "<PERSON>", "headline": "一人，一心，一座城", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/debbie-yue", "url_token": "de<PERSON><PERSON>-yue", "avatar_url": "https://pica.zhimg.com/fdac98ab2_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "updated_time": 1449626475, "created_time": 1422451102, "author": {"id": "8a0e64e6dc40922ca44975c71d13323a", "name": "海伦娜儿", "headline": "一身明亮。     b站/微博：海伦娜儿", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/hai-lun-na-er", "url_token": "hai-lun-na-er", "avatar_url": "https://picx.zhimg.com/v2-961cd0293dc105759c4780237c36abf4_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "widget": {"url": "https://pic1.zhimg.com/v2-ffa2a1dfa6cfa55c3aabed57b0adc8b2_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-ffa2a1dfa6cfa55c3aabed57b0adc8b2_r.jpg?source=5a24d060", "id": 40001}, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 776, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"H0JJuYco\">早先住过的小公寓，利用废旧物来改造家居，有兴趣的知友可以参考看看～</p><p data-pid=\"HKaQo4EU\">客厅：</p><figure><noscript><img src=\"https://pic2.zhimg.com/3511a1b92c9a45c656865537899e0713_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"640\" data-original-token=\"3511a1b92c9a45c656865537899e0713\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://pic2.zhimg.com/3511a1b92c9a45c656865537899e0713_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;640&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"640\" data-original-token=\"3511a1b92c9a45c656865537899e0713\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://pic2.zhimg.com/3511a1b92c9a45c656865537899e0713_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/3511a1b92c9a45c656865537899e0713_b.jpg\"/></figure><p data-pid=\"BA3fy-Uv\">一、客厅吊灯</p><p data-pid=\"hYrEsKFo\">       材料（工具）：捡来的船木、钢丝、LED射灯、电线、电胶带、模具、打磨机、电锯、钻孔机</p><figure><noscript><img src=\"https://pic2.zhimg.com/4e67c776792e597eae8a324ad85c30cd_b.jpg\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"4e67c776792e597eae8a324ad85c30cd\" class=\"origin_image zh-lightbox-thumb\" width=\"1136\" data-original=\"https://pic2.zhimg.com/4e67c776792e597eae8a324ad85c30cd_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1136&#39; height=&#39;852&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"4e67c776792e597eae8a324ad85c30cd\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1136\" data-original=\"https://pic2.zhimg.com/4e67c776792e597eae8a324ad85c30cd_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/4e67c776792e597eae8a324ad85c30cd_b.jpg\"/></figure><p data-pid=\"gYrEtHcq\">二、客厅小书柜（老式菜厨改造）</p><figure><noscript><img src=\"https://picx.zhimg.com/5bb3cb1d0a638af74cde9a5ed27ad663_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"5bb3cb1d0a638af74cde9a5ed27ad663\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://picx.zhimg.com/5bb3cb1d0a638af74cde9a5ed27ad663_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"5bb3cb1d0a638af74cde9a5ed27ad663\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://picx.zhimg.com/5bb3cb1d0a638af74cde9a5ed27ad663_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/5bb3cb1d0a638af74cde9a5ed27ad663_b.jpg\"/></figure><p data-pid=\"6kHXcQpE\">三、客厅壁灯（闪光灯改造）</p><figure><noscript><img src=\"https://pic3.zhimg.com/682a951a752b5accde3705f1bbe8c3f6_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"682a951a752b5accde3705f1bbe8c3f6\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://pic3.zhimg.com/682a951a752b5accde3705f1bbe8c3f6_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"682a951a752b5accde3705f1bbe8c3f6\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://pic3.zhimg.com/682a951a752b5accde3705f1bbe8c3f6_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/682a951a752b5accde3705f1bbe8c3f6_b.jpg\"/></figure><p data-pid=\"GlbaD3dh\"> 四、过道收纳架</p><p data-pid=\"8tE5vqW_\">      材料（工具）：厚木头、钉子、打磨机、电锯、钻孔机</p><figure><noscript><img src=\"https://picx.zhimg.com/15971466882298a5ff8db04b6dbec03f_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"15971466882298a5ff8db04b6dbec03f\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://picx.zhimg.com/15971466882298a5ff8db04b6dbec03f_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"15971466882298a5ff8db04b6dbec03f\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://picx.zhimg.com/15971466882298a5ff8db04b6dbec03f_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/15971466882298a5ff8db04b6dbec03f_b.jpg\"/></figure><p data-pid=\"M1dEH8wV\">五、过道吊灯</p><p data-pid=\"oYZ3By_O\">       材料（工具）：暖水壶、电线、挂钩、灯头灯泡、电胶带、打磨机、切割机、钻孔机</p><figure><noscript><img src=\"https://picx.zhimg.com/05323b6125b1507f0a3bde71d6430209_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"05323b6125b1507f0a3bde71d6430209\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://picx.zhimg.com/05323b6125b1507f0a3bde71d6430209_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"05323b6125b1507f0a3bde71d6430209\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://picx.zhimg.com/05323b6125b1507f0a3bde71d6430209_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/05323b6125b1507f0a3bde71d6430209_b.jpg\"/></figure><figure><noscript><img src=\"https://pic4.zhimg.com/db90693359838d2f534caeb7e2a9b271_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"db90693359838d2f534caeb7e2a9b271\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://pic4.zhimg.com/db90693359838d2f534caeb7e2a9b271_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"db90693359838d2f534caeb7e2a9b271\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://pic4.zhimg.com/db90693359838d2f534caeb7e2a9b271_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/db90693359838d2f534caeb7e2a9b271_b.jpg\"/></figure><br/><p data-pid=\"5gSj639F\">其余改造物：电视柜、壁柜、茶几</p><br/><p data-pid=\"hFjs4ZzE\">客厅另一侧：</p><figure><noscript><img src=\"https://picx.zhimg.com/9eab2987ae4473da13afc86a2414c313_b.jpg\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"9eab2987ae4473da13afc86a2414c313\" class=\"origin_image zh-lightbox-thumb\" width=\"1136\" data-original=\"https://picx.zhimg.com/9eab2987ae4473da13afc86a2414c313_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1136&#39; height=&#39;852&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"9eab2987ae4473da13afc86a2414c313\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1136\" data-original=\"https://picx.zhimg.com/9eab2987ae4473da13afc86a2414c313_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/9eab2987ae4473da13afc86a2414c313_b.jpg\"/></figure><p data-pid=\"6wydVPVX\"> 六、工作台</p><p data-pid=\"dnKZag7w\">       材料：缝纫机底座、厚木板、螺丝、切割机、打磨机、钻孔机、电锯</p><figure><noscript><img src=\"https://pica.zhimg.com/89c35b9fbbd190becea0606b6beafad4_b.jpg\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"89c35b9fbbd190becea0606b6beafad4\" class=\"origin_image zh-lightbox-thumb\" width=\"1136\" data-original=\"https://pica.zhimg.com/89c35b9fbbd190becea0606b6beafad4_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1136&#39; height=&#39;852&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"89c35b9fbbd190becea0606b6beafad4\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1136\" data-original=\"https://pica.zhimg.com/89c35b9fbbd190becea0606b6beafad4_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/89c35b9fbbd190becea0606b6beafad4_b.jpg\"/></figure><p data-pid=\"KIW-yzVR\">七、烛台</p><p data-pid=\"oEqrHtal\">      材料（工具）：漂浮木、小蜡烛、电钻、打磨机、电锯</p><figure><noscript><img src=\"https://pic2.zhimg.com/66813811cff12995ce80ce24bdf80437_b.jpg\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"66813811cff12995ce80ce24bdf80437\" class=\"origin_image zh-lightbox-thumb\" width=\"1136\" data-original=\"https://pic2.zhimg.com/66813811cff12995ce80ce24bdf80437_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1136&#39; height=&#39;852&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"66813811cff12995ce80ce24bdf80437\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1136\" data-original=\"https://pic2.zhimg.com/66813811cff12995ce80ce24bdf80437_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/66813811cff12995ce80ce24bdf80437_b.jpg\"/></figure><p data-pid=\"KLL_FLwB\">其余改造物：陈列架，挂衣架</p><br/><p data-pid=\"Gme3_hux\">浴室一侧：</p><figure><noscript><img src=\"https://picx.zhimg.com/7ddc322cba67d077a41b9256d952a04d_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"7ddc322cba67d077a41b9256d952a04d\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://picx.zhimg.com/7ddc322cba67d077a41b9256d952a04d_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"7ddc322cba67d077a41b9256d952a04d\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://picx.zhimg.com/7ddc322cba67d077a41b9256d952a04d_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/7ddc322cba67d077a41b9256d952a04d_b.jpg\"/></figure><p data-pid=\"GQnkKh2A\">八、壁灯</p><p data-pid=\"HYZ4RYS7\">      材料（工具）：树根、五金店角铁、LED射灯、电线、电胶带、螺丝、模具、打磨机、电锯、                                 钻孔机</p><figure><noscript><img src=\"https://pic2.zhimg.com/264d2100ddaf165b4b41818fd71c58e1_b.jpg\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"264d2100ddaf165b4b41818fd71c58e1\" class=\"origin_image zh-lightbox-thumb\" width=\"1136\" data-original=\"https://pic2.zhimg.com/264d2100ddaf165b4b41818fd71c58e1_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1136&#39; height=&#39;852&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1136\" data-rawheight=\"852\" data-original-token=\"264d2100ddaf165b4b41818fd71c58e1\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1136\" data-original=\"https://pic2.zhimg.com/264d2100ddaf165b4b41818fd71c58e1_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/264d2100ddaf165b4b41818fd71c58e1_b.jpg\"/></figure><br/><p data-pid=\"fB5E7vFI\">卧室局部：</p><p data-pid=\"zhw6p2tg\">九：床头柜</p><p data-pid=\"vsFNr56E\">      材料（工具）：厚木头、 槽钢、刨刀、轮子、钢筋、电焊机、切割机、打磨机、电锯、玻璃胶</p><figure><noscript><img src=\"https://pic4.zhimg.com/64938167e90e7d46f67804e948b6056d_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"64938167e90e7d46f67804e948b6056d\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://pic4.zhimg.com/64938167e90e7d46f67804e948b6056d_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"64938167e90e7d46f67804e948b6056d\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://pic4.zhimg.com/64938167e90e7d46f67804e948b6056d_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/64938167e90e7d46f67804e948b6056d_b.jpg\"/></figure><br/><p data-pid=\"VN639iYr\">十、吊灯（鸟笼改造）</p><figure><noscript><img src=\"https://picx.zhimg.com/093ac4c761bfcb3af5761d01e51df5fb_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"093ac4c761bfcb3af5761d01e51df5fb\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://picx.zhimg.com/093ac4c761bfcb3af5761d01e51df5fb_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"093ac4c761bfcb3af5761d01e51df5fb\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://picx.zhimg.com/093ac4c761bfcb3af5761d01e51df5fb_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/093ac4c761bfcb3af5761d01e51df5fb_b.jpg\"/></figure><p data-pid=\"fXevVb2e\">其余改造物：床、墙板、化妆柜</p><br/><p data-pid=\"CKcKL4Us\">小阳台：</p><figure><noscript><img src=\"https://pic4.zhimg.com/1b3c68b828c998c99a2ade1dd067973d_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"1b3c68b828c998c99a2ade1dd067973d\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://pic4.zhimg.com/1b3c68b828c998c99a2ade1dd067973d_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"1b3c68b828c998c99a2ade1dd067973d\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://pic4.zhimg.com/1b3c68b828c998c99a2ade1dd067973d_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/1b3c68b828c998c99a2ade1dd067973d_b.jpg\"/></figure><p data-pid=\"0plb5bX1\">改造物：瓷砖改木地板、桌子、吊灯</p><br/><p data-pid=\"RbEKZ-zj\">厨房：</p><figure><noscript><img src=\"https://pic3.zhimg.com/9f0692537300d582e1f3c2ed7af598e8_b.jpg\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"9f0692537300d582e1f3c2ed7af598e8\" class=\"origin_image zh-lightbox-thumb\" width=\"852\" data-original=\"https://pic3.zhimg.com/9f0692537300d582e1f3c2ed7af598e8_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;852&#39; height=&#39;1136&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"852\" data-rawheight=\"1136\" data-original-token=\"9f0692537300d582e1f3c2ed7af598e8\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"852\" data-original=\"https://pic3.zhimg.com/9f0692537300d582e1f3c2ed7af598e8_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/9f0692537300d582e1f3c2ed7af598e8_b.jpg\"/></figure><p data-pid=\"VAgW8gCi\">改造物：杯架、灶台</p><br/><br/><br/><br/><p data-pid=\"hU7AZFnm\">感谢支持～！</p>", "excerpt": "早先住过的小公寓，利用废旧物来改造家居，有兴趣的知友可以参考看看～ 客厅： 一、客厅吊灯 材料（工具）：捡来的船木、钢丝、LED射灯、电线、电胶带、模具、打磨机、电锯、钻孔机 二、客厅小书柜（老式菜厨改造） 三、客厅壁灯（闪光灯改造） 四、过道收纳架 材料（工具）：厚木头、钉子、打磨机、电锯、钻孔机 五、过道吊灯 材料（工具）：暖水壶、电线、挂钩、灯头灯泡、电胶带、打磨机、切割机、钻孔机 其余改造物：电视柜…", "excerpt_new": "早先住过的小公寓，利用废旧物来改造家居，有兴趣的知友可以参考看看～ 客厅： 一、客厅吊灯 材料（工具）：捡来的船木、钢丝、LED射灯、电线、电胶带、模具、打磨机、电锯、钻孔机 二、客厅小书柜（老式菜厨改造） 三、客厅壁灯（闪光灯改造） 四、过道收纳架 材料（工具）：厚木头、钉子、打磨机、电锯、钻孔机 五、过道吊灯 材料（工具）：暖水壶、电线、挂钩、灯头灯泡、电胶带、打磨机、切割机、钻孔机 其余改造物：电视柜…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1452333102, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1452333009735", "type": "feed", "target": {"id": "21423568", "title": "如何布置独居小房间能惬意地生活？", "url": "https://api.zhihu.com/questions/21423568", "type": "question", "question_type": "normal", "created": 1375512027, "answer_count": 620, "comment_count": 70, "follower_count": 134800, "detail": "还有几个月毕业，马上迎来独居的生活，话说期待一个人住的日子很久了呃... 现在暂时想到的是尽量合理利用空间，买个带书桌的高低床，书架、小桌板、衣柜...外加小冰箱、小洗衣机、微波炉、电磁炉...其实现在手边还有酸奶机、煮蛋器... 因为人在香港，红酒很便宜，经常幻想周末的晚上边看电影边喝红酒，或者早上的时候坐在窗台上看书... 再说下去就跑偏了...捂脸", "excerpt": "还有几个月毕业，马上迎来独居的生活，话说期待一个人住的日子很久了呃... 现在暂时…", "bound_topic_ids": [307, 1280, 21287, 57924], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "ac6d9606fdc0e164364ea21e8f8d8591", "name": "<PERSON>", "headline": "一人，一心，一座城", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/debbie-yue", "url_token": "de<PERSON><PERSON>-yue", "avatar_url": "https://picx.zhimg.com/fdac98ab2_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1452333009, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1452328672279", "type": "feed", "target": {"id": "80727936", "type": "answer", "url": "https://api.zhihu.com/answers/80727936", "voteup_count": 1, "thanks_count": 0, "question": {"id": "35564122", "title": "年少没多少感觉，长大才恍然大悟、赞叹不已的好书或好文，有哪些？", "url": "https://api.zhihu.com/questions/35564122", "type": "question", "question_type": "normal", "created": 1442104043, "answer_count": 5036, "comment_count": 112, "follower_count": 76443, "detail": "", "excerpt": "", "bound_topic_ids": [53, 113, 2058], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "108ccc0c04eeb2b80dc2034725ca4c99", "name": "布酱", "headline": "坐标东京｜游走世界｜以梦为马｜孜孜不息", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/bu-jiang-7", "url_token": "bu-jiang-7", "avatar_url": "https://pica.zhimg.com/3dcb492bc8d520ee69388e4eb240bce1_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "updated_time": 1452328672, "created_time": 1452328672, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"ba8vR_2s\">“古人之观于天地、山川、草木、虫鱼、鸟兽，往往有得，以其求思之深而无不在也。夫夷以近，则游者众；险以远，则至者少。而<b>世之奇伟、瑰怪，非常之观，常在于险远，而人之所罕至焉，故非有志者不能至也</b>。有志矣，不随以止也，然力不足者，亦不能至也。有志与力，而又不随以怠，至于幽暗昏惑而无物以相之，亦不能至也。然力足以至焉，于人为可讥，而在己为有悔；尽吾志也而不能至者，可以无悔矣，其孰能讥之乎？此余之所得也！”</p><p data-pid=\"BuZLiyqE\">——王安石《游褒禅山记》</p><p data-pid=\"g0yUrveu\">走遍天下看风景如此，学习和工作做事亦如此。容易去到的地方就游人多，简单的事情就谁都会做，而往往艰险的地方才有奇伟瑰怪之美景，吃了苦学了东西才能做到的事情才有更高的价值。一切事情要做成，首先要精神上“有志”，身体上“力足”，还要有合适的外部条件。尽力而为而不能至，对于自己也“可以无悔矣”。</p><p data-pid=\"XGbKhCqZ\">“尽人事，听天命”一直是我的行事态度，最近重读到这篇高中课文，觉得写的真好，当时光顾着背诵全文了。</p>", "excerpt": "“古人之观于天地、山川、草木、虫鱼、鸟兽，往往有得，以其求思之深而无不在也。夫夷以近，则游者众；险以远，则至者少。而 <b>世之奇伟、瑰怪，非常之观，常在于险远，而人之所罕至焉，故非有志者不能至也</b>。有志矣，不随以止也，然力不足者，亦不能至也。有志与力，而又不随以怠，至于幽暗昏惑而无物以相之，亦不能至也。然力足以至焉，于人为可讥，而在己为有悔；尽吾志也而不能至者，可以无悔矣，其孰能讥之乎？此余之所得也！…", "excerpt_new": "“古人之观于天地、山川、草木、虫鱼、鸟兽，往往有得，以其求思之深而无不在也。夫夷以近，则游者众；险以远，则至者少。而 <b>世之奇伟、瑰怪，非常之观，常在于险远，而人之所罕至焉，故非有志者不能至也</b>。有志矣，不随以止也，然力不足者，亦不能至也。有志与力，而又不随以怠，至于幽暗昏惑而无物以相之，亦不能至也。然力足以至焉，于人为可讥，而在己为有悔；尽吾志也而不能至者，可以无悔矣，其孰能讥之乎？此余之所得也！…", "preview_type": "expand", "preview_text": "<p data-pid=\"ba8vR_2s\">“古人之观于天地、山川、草木、虫鱼、鸟兽，往往有得，以其求思之深而无不在也。夫夷以近，则游者众；险以远，则至者少。而<b>世之奇伟、瑰怪，非常之观，常在于险远，而人之所罕至焉，故非有志者不能至也</b>。有志矣，不随以止也，然力不足者，亦不能至也。有志与力，而又不随以怠，至于幽暗昏惑而无物以相之，亦不能至也。然力足以至焉，于人为可讥，而在己为有悔；尽吾志也而不能至者，可以无悔矣，其孰能讥之乎？此余之所得也！”</p><p data-pid=\"BuZLiyqE\">——王安石《游褒禅山记》</p><p data-pid=\"g0yUrveu\">走遍天下看风景如此，学习和工作做事亦如此。容易去到的地方就游人多，简单的事情就谁都会做，而往往艰险的地方才有奇伟瑰怪之美景，吃了苦学了东西才能做到的事情才有更高的价值。一切事情要做成，首先要精神上“有志”，身体上“力足”，还要有合适的外部条件。尽力而为而不能至，对于自己也“可以无悔矣”。</p><p data-pid=\"XGbKhCqZ\">“尽人事，听天命”一直是我的行事态度，最近重读到这篇高中课文，觉得写的真好，当时光顾着背诵全文了。</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1452328672, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}, {"id": "1452328020619", "type": "feed", "target": {"id": "64344360", "type": "answer", "url": "https://api.zhihu.com/answers/64344360", "voteup_count": 282, "thanks_count": 30, "question": {"id": "35564122", "title": "年少没多少感觉，长大才恍然大悟、赞叹不已的好书或好文，有哪些？", "url": "https://api.zhihu.com/questions/35564122", "type": "question", "question_type": "normal", "created": 1442104043, "answer_count": 5036, "comment_count": 112, "follower_count": 76443, "detail": "", "excerpt": "", "bound_topic_ids": [53, 113, 2058], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "108ccc0c04eeb2b80dc2034725ca4c99", "name": "布酱", "headline": "坐标东京｜游走世界｜以梦为马｜孜孜不息", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/bu-jiang-7", "url_token": "bu-jiang-7", "avatar_url": "https://pic1.zhimg.com/3dcb492bc8d520ee69388e4eb240bce1_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "updated_time": 1442668317, "created_time": 1442668317, "author": {"id": "560b3e4c6351e832b4330c959b80254b", "name": "<PERSON><PERSON><PERSON>born", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/wilsonslideborn", "url_token": "wils<PERSON><PERSON><PERSON>", "avatar_url": "https://pic1.zhimg.com/fbca7384f03b0e331e3722b2433ba7fe_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 10, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"WHSP-I8L\">露从今夜白，月是故乡明</p>", "excerpt": "露从今夜白，月是故乡明 ", "excerpt_new": "<p data-pid=\"WHSP-I8L\">露从今夜白，月是故乡明</p>", "preview_type": "no_expand", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1452328020, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1452328013351", "type": "feed", "target": {"id": "64202282", "type": "answer", "url": "https://api.zhihu.com/answers/64202282", "voteup_count": 1379, "thanks_count": 233, "question": {"id": "35564122", "title": "年少没多少感觉，长大才恍然大悟、赞叹不已的好书或好文，有哪些？", "url": "https://api.zhihu.com/questions/35564122", "type": "question", "question_type": "normal", "created": 1442104043, "answer_count": 5036, "comment_count": 112, "follower_count": 76443, "detail": "", "excerpt": "", "bound_topic_ids": [53, 113, 2058], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "108ccc0c04eeb2b80dc2034725ca4c99", "name": "布酱", "headline": "坐标东京｜游走世界｜以梦为马｜孜孜不息", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/bu-jiang-7", "url_token": "bu-jiang-7", "avatar_url": "https://pic1.zhimg.com/3dcb492bc8d520ee69388e4eb240bce1_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "updated_time": 1443146942, "created_time": 1442575245, "author": {"id": "9a347589f87f27a971092788ae1bea25", "name": "一个猴儿", "headline": "早卧早起，与鸡俱兴", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/asape", "url_token": "asape", "avatar_url": "https://picx.zhimg.com/09e4ba7a04ec2591ecddcf76c4298ab9_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "", "night_mode_url": ""}, "widget": {"url": "https://pic1.zhimg.com/v2-5f2558d9a0ab384096c4ab33a96f722f_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-5f2558d9a0ab384096c4ab33a96f722f_r.jpg?source=5a24d060", "id": 40136}, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 89, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"VrBiYFZG\">苏子曰：“客亦知夫水与月乎？逝者如斯，而未尝往也；盈虚者如彼，而卒莫消长也。盖将自其变者而观之，则天地曾不能以一瞬；自其不变者而观之，则物与我皆无尽也，而又何羡乎！<b>且夫天地之间，物各有主，苟非吾之所有，虽一毫而莫取。</b>惟江上之清风，与山间之明月，耳得之而为声，目遇之而成色，取之无禁，用之不竭。是造物者之无尽藏也，而吾与子之所共适。”</p><p data-pid=\"PZsXpdO-\">时间那么长，世界那么大，没有什么是过不去的。</p>", "excerpt": "苏子曰：“客亦知夫水与月乎？逝者如斯，而未尝往也；盈虚者如彼，而卒莫消长也。盖将自其变者而观之，则天地曾不能以一瞬；自其不变者而观之，则物与我皆无尽也，而又何羡乎！ <b>且夫天地之间，物各有主，苟非吾之所有，虽一毫而莫取。</b>惟江上之清风，与山间之明月，耳得之而为声，目遇之而成色，取之无禁，用之不竭。是造物者之无尽藏也，而吾与子之所共适。”时间那么长，世界那么大，没有什么是过不去的。 ", "excerpt_new": "苏子曰：“客亦知夫水与月乎？逝者如斯，而未尝往也；盈虚者如彼，而卒莫消长也。盖将自其变者而观之，则天地曾不能以一瞬；自其不变者而观之，则物与我皆无尽也，而又何羡乎！ <b>且夫天地之间，物各有主，苟非吾之所有，虽一毫而莫取。</b>惟江上之清风，与山间之明月，耳得之而为声，目遇之而成色，取之无禁，用之不竭。是造物者之无尽藏也，而吾与子之所共适。”时间那么长，世界那么大，没有什么是过不去的。 ", "preview_type": "expand", "preview_text": "<p data-pid=\"VrBiYFZG\">苏子曰：“客亦知夫水与月乎？逝者如斯，而未尝往也；盈虚者如彼，而卒莫消长也。盖将自其变者而观之，则天地曾不能以一瞬；自其不变者而观之，则物与我皆无尽也，而又何羡乎！<b>且夫天地之间，物各有主，苟非吾之所有，虽一毫而莫取。</b>惟江上之清风，与山间之明月，耳得之而为声，目遇之而成色，取之无禁，用之不竭。是造物者之无尽藏也，而吾与子之所共适。”</p><p data-pid=\"PZsXpdO-\">时间那么长，世界那么大，没有什么是过不去的。</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1452328013, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1452328013351&page_num=87"}}