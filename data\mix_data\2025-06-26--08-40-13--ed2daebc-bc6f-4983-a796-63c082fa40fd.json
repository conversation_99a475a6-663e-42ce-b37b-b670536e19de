{"data": [{"id": "42_1750898459.650", "type": "feed", "offset": 42, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898459, "updated_time": 1750898459, "target": {"id": "3069942647", "type": "article", "url": "https://api.zhihu.com/articles/3069942647", "author": {"id": "f36e4e44343072c5126767d2999c14b4", "url": "https://api.zhihu.com/people/f36e4e44343072c5126767d2999c14b4", "user_type": "people", "url_token": "11347-69", "name": "知乎用户11347", "headline": "", "avatar_url": "https://pica.zhimg.com/50/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 376, "is_following": false, "is_followed": false}, "title": "马斯克的钢铁内心，强大到令人窒息", "comment_permission": "all", "created": 1729824554, "updated": 1729824554, "voteup_count": 3179, "voting": 0, "comment_count": 98, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "这两天，网上看到马斯克的一组图，都是他的语录，非常震撼，我读了两遍。 结合这几年工作、生活的经验，觉得他说的太对了，分享给大家：                   马斯克的人生，充满了失败、波折、逆境，也有荣誉和成功、不管多么富有，他依旧还在挑战自己，也在不断改变这个世界。 这些信念，是支撑他走到今天，分享给大家： 1.做热爱的事才会事半功倍当初没人愿意加入Space X当首席工程师，最后马斯克通过自学胜任该职位。 采访中被问到，一窍不通的…", "excerpt_new": "这两天，网上看到马斯克的一组图，都是他的语录，非常震撼，我读了两遍。 结合这几年工作、生活的经验，觉得他说的太对了，分享给大家：                   马斯克的人生，充满了失败、波折、逆境，也有荣誉和成功、不管多么富有，他依旧还在挑战自己，也在不断改变这个世界。 这些信念，是支撑他走到今天，分享给大家： 1.做热爱的事才会事半功倍当初没人愿意加入Space X当首席工程师，最后马斯克通过自学胜任该职位。 采访中被问到，一窍不通的…", "preview_type": "default", "preview_text": "", "content": "<p data-pid=\"q7BInbS_\">这两天，网上看到马斯克的一组图，都是他的语录，非常震撼，我读了两遍。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"-ttdRp3x\">结合这几年工作、生活的经验，觉得他说的太对了，分享给大家：</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-d0dd832b08ca09af6941f2282d3513b0_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1440\" data-size=\"normal\" data-original-token=\"v2-898f52206e305da7f8a025ffe20a6785\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-d0dd832b08ca09af6941f2282d3513b0_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-bc0765a8568fb21d6b115b661de266d5_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1790\" data-size=\"normal\" data-original-token=\"v2-32a1cbeeb96b658b100aa964d5a3db4f\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-bc0765a8568fb21d6b115b661de266d5_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-a9517959c735ae316a6366f041b48b6a_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1610\" data-size=\"normal\" data-original-token=\"v2-a77f9c0da99c1134ffade64b2388e9eb\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-a9517959c735ae316a6366f041b48b6a_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-9134d05a035524896c93536a5a3f5585_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1782\" data-size=\"normal\" data-original-token=\"v2-551864814549b6cb311013b100ff0213\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-9134d05a035524896c93536a5a3f5585_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-05a15e7dc18a1be12bb84bbb5a00af75_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1723\" data-size=\"normal\" data-original-token=\"v2-8715377c2dc0e7d459727c4d2a4136d2\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-05a15e7dc18a1be12bb84bbb5a00af75_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-b84646efb80569063b2e38263d1e062d_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1736\" data-size=\"normal\" data-original-token=\"v2-d25caff675ceb9a213c6c4e0750c91f8\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-b84646efb80569063b2e38263d1e062d_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-99e9bbebedccbd3911d03b9366c110f1_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1432\" data-size=\"normal\" data-original-token=\"v2-078445f4d10972dd59eddf3f7e7315f8\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-99e9bbebedccbd3911d03b9366c110f1_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-a004909f9fb84a59a24dea8671f7747c_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"2185\" data-size=\"normal\" data-original-token=\"v2-bee20b72d6b5af9eb8ccbd9f14bb6f6b\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-a004909f9fb84a59a24dea8671f7747c_r.jpg\"/></figure><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-b8633ab3621228cd6b384597bf223284_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1686\" data-size=\"normal\" data-original-token=\"v2-22121e180c816d58fa9a2bc23da9a46a\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-b8633ab3621228cd6b384597bf223284_r.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WXDsaHE8\">马斯克的人生，充满了失败、波折、逆境，也有荣誉和成功、不管多么富有，他依旧还在挑战自己，也在不断改变这个世界。</p><p data-pid=\"buSEXNxt\">这些信念，是支撑他走到今天，分享给大家：</p><p data-pid=\"_WKZQu3B\"><b>1.做热爱的事才会事半功倍</b></p><p data-pid=\"74F1XYw4\">当初没人愿意加入Space X当首席工程师，最后马斯克通过自学胜任该职位。</p><p data-pid=\"ihp34fMA\">采访中被问到，一窍不通的他是如何做到的，他说：</p><p data-pid=\"59ObuU7T\"><b>“首先这是我的兴趣，我们必须要做一些喜欢的事情，它能激发你对未来感到兴奋，你会时刻想着这件事，如果你不喜欢它，我认为你真的无法将它做好。”</b></p><p data-pid=\"M_rEM150\"><b>2.先解决有无，再解决好坏</b></p><p data-pid=\"zrYxGVpc\">马斯克说：</p><p data-pid=\"nov9Sgdu\"><b>“当你想要做一件事时，先去做，做成一堆烂泥，再慢慢改，无论在哪个领域都是先开始再改进的，一个粗糙的开始，就是最好的开始。”</b></p><p data-pid=\"r6oSJtDs\">所以他创建了9家公司，几乎每一个，都在改变人类的历史。</p><p data-pid=\"B0OcJoS5\"><b>3.失败不是人生污点</b></p><p data-pid=\"Lg0r3I1x\">Space X即将原地破产，特斯拉也面临倒闭时，马斯克将所有钱投入两家公司，甚至要借钱交房租，最终置于死地而后生。</p><p data-pid=\"7cFFMLEf\">他说：</p><p data-pid=\"WCHDkAgh\">“有些事会失败，有些事会成功，如果努力了但没有成功，汲取教训，接着再来，这没什么大不了的，因为<b>失败永远不会成为人生污点</b>。”</p><p data-pid=\"LTOs8mGK\">记者问如果发射再失败怎么办，他笑着说：</p><p data-pid=\"0AmEQ5Pq\"><b>“我绝对还是会成功的，如果失败就当放烟花吧。”</b></p><p data-pid=\"uYEJKLfo\">真是应了书里那句：</p><p data-pid=\"kBLKm4iT\"><b>“接受无常和最坏的结果，那些变化、抓不住的变化、不能预期的变化才是人世间最普遍的现象。”</b></p><p data-pid=\"FEFBCby3\"><b>4.世界没有真相，只有视角</b></p><p data-pid=\"GK6wm75F\">马斯克说：</p><p data-pid=\"9txFPnRY\">“我总会思考一些事，不管那件事是真是假，人们思考的过程太拘于惯例或对过去经验的类比<b>，我认为以第一性原理思考，就是把解决事情回归到事物的本质上去，并由此开始推理</b>。”</p><p data-pid=\"ZCTOI4Fe\">成为一个顶尖的成事者，你一定要有跳出框架、独立思考的能力。</p><p data-pid=\"S0EdTWLb\"><b>5.积极寻求负面反馈</b></p><p data-pid=\"4M0t5Xd1\">马斯克通过海量阅读、与人交流以及积极寻求和听取负面反馈来获取信息。</p><p data-pid=\"OPwnMUQK\">他说：“人们通常倾向于避免负面反馈，因为它是痛苦的，但有时候你必须脸皮厚一点，能让别人给你提出真实的批评与反馈，好及时纠错。”</p><p data-pid=\"j5REvZru\">宁愿错误的乐观，也不正确的悲观，生命的意义就是整个旅途本身。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"5BTECGS4\">​点赞加关注。</p><p></p>", "is_labeled": false, "visited_count": 158410, "thumbnails": ["https://pic1.zhimg.com/50/v2-48de4cd7c786fd841cb1fad5bb2a4645_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-295eb29ee5b4d5167dbe48091826fb7b_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-830227b8b103dd840cf4e7cedf1235bb_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-56038b1126361e18c8aade5338ea9f89_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-935cfda8a5b3bc71a9b4ebffc07e67ed_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-ee327adc12173aa662313fb22e0617ce_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-ce98e74bdf2f73d805d3620c9a7ba263_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-922e28252a35c8864a1c07ff24e3ddcd_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-0a53406e8b0ef19887fe092a369591c5_720w.jpg?source=b6762063"], "favorite_count": 8118, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": 3069942647}", "attached_info": "CsgJCOXSz7OH4ZH1mwEQBxoJMjQ5NjM2NDM0IKqO7LgGKOsYMGJAKkpBCixUU19TT1VSQ0VfVFdPVE9XRVJfU0hPUlRJTlRFUkVTVF9SRUNBTExfVEVYVBIBMBgAIAA6CnsicmF3IjoiIn1iIDU5MDE5OTM4OTdiMmI2NzE1MzRiNDFkZmUxNzAzOTQ3cgozMDY5OTQyNjQ3qgEJcmVjb21tZW5kwgEgZjM2ZTRlNDQzNDMwNzJjNTEyNjc2N2QyOTk5YzE0YjTyAQoIDBIGTm9ybWFs8gEoCAoSJDIzMWQ1ODc0LTBmMWYtNDMzZC04NmNiLTdlYTI1MDM3ZmI5N/IBBQgLEgE4ggIAiALQg7nN+jKSAiBmMzZlNGU0NDM0MzA3MmM1MTI2NzY3ZDI5OTljMTRiNJoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhZBY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIbSW50ZXJhY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIYUGVyaW9kSW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxlygIUQ29udGVudEFnZVdlaWdodFJ1bGXaAixUU19TT1VSQ0VfVFdPVE9XRVJfU0hPUlRJTlRFUkVTVF9SRUNBTExfVEVYVOgCAvoCC05PUk1BTF9GTE9XigMgZmRhNzBjZmIzOTZiNGQyNjg1MjhhNzNlZTJmMzdmMDeaAw0KAnYyEAAaBW90aGVyqAPK1QnYAwDqAxpmZWVkX2F0dG1fdHdvdG93ZXJfdjJfdGV4dPoDxgMSDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFOi0IAhC4CBigCyIjdjItODk4ZjUyMjA2ZTMwNWRhN2Y4YTAyNWZmZTIwYTY3ODU6LQgCELgIGP4NIiN2Mi0zMmExY2JlZWI5NmI2NThiMTAwYWE5NjRkNWEzZGI0ZjotCAIQuAgYygwiI3YyLWE3N2Y5YzBkYTk5YzExMzRmZmFkZTY0YjIzODhlOWViOi0IAhC4CBj2DSIjdjItNTUxODY0ODE0NTQ5YjZjYjMxMTAxM2IxMDBmZjAyMTM6LQgCELgIGLsNIiN2Mi04NzE1Mzc3YzJkYzBlN2Q0NTk3MjdjNGQyYTQxMzZkMjotCAIQuAgYyA0iI3YyLWQyNWNhZmY2NzVjZWI5YTIxM2M2YzRlMDc1MGM5MWY4Oi0IAhC4CBiYCyIjdjItMDc4NDQ1ZjRkMTA5NzJkZDU5ZWRkZjNmN2U3MzE1Zjg6LQgCELgIGIkRIiN2Mi1iZWUyMGI3MmQ2YjVhZjllYjhjY2JkOWYxNGJiNmY2YjotCAIQuAgYlg0iI3YyLTIyMTIxZTE4MGM4MTZkNThmYTlhMmJjMjNkYTlhNDZhgAQAiAQAkgQGTm9ybWFsmgQBMqAEAKgEALAEALoEBm1hbnVhbMIEAzE2MMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAYDClyT+BBQAAAAAAAAAAiQWsYdagiXbSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUIkAYAoAYuqAYBkgIlCgkyNDk2MzY0MzQSCjMwNjk5NDI2NDcYByIKSU1BR0VfVEVYVA==", "action_card": false}, {"id": "43_1750898459.10", "type": "feed", "offset": 43, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898459, "updated_time": 1750898459, "target": {"id": "*********", "type": "article", "url": "https://api.zhihu.com/articles/*********", "author": {"id": "17cb6c18005433a446fefd09e8c61a78", "url": "https://api.zhihu.com/people/17cb6c18005433a446fefd09e8c61a78", "user_type": "people", "url_token": "su-<PERSON><PERSON>-yong-52-22", "name": "苏治龙", "headline": "公众号：苏治龙。全网同名：苏治龙", "avatar_url": "https://picx.zhimg.com/50/v2-c79b15bfa563d535eeae076ac363003f_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 3253, "is_following": false, "is_followed": false}, "title": "怎么选择赚一百万的商业模式？", "image_url": "https://picx.zhimg.com/v2-565390e505a1f16180dda0ea72de904c.jpg?source=7e7ef6e2&needBackground=1", "comment_permission": "all", "created": 1718188622, "updated": 1718188622, "voteup_count": 261, "voting": 0, "comment_count": 15, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "赚一百万大概有四种模式： 第一种，在1000个客户身上各赚一千块，这样能实现赚一百万的目标； 第二种，在100个人身上各赚一万块，这样就能实现赚一百万的目标； 第三种，在10个人身上各赚十万块，这样能实现赚一百万的目标； 第四种，在1个人身上赚一百万，这样也能实现赚一百万的目标； 那么，四种模式中最容易做到的是哪种？最难做到的是哪种？最不应该去做的是哪种？最适合创始人去做的又是哪种？ 在进行难易程度排行之前，我…", "excerpt_new": "赚一百万大概有四种模式： 第一种，在1000个客户身上各赚一千块，这样能实现赚一百万的目标； 第二种，在100个人身上各赚一万块，这样就能实现赚一百万的目标； 第三种，在10个人身上各赚十万块，这样能实现赚一百万的目标； 第四种，在1个人身上赚一百万，这样也能实现赚一百万的目标； 那么，四种模式中最容易做到的是哪种？最难做到的是哪种？最不应该去做的是哪种？最适合创始人去做的又是哪种？ 在进行难易程度排行之前，我…", "preview_type": "default", "preview_text": "", "column": {"id": "c_1440732936232816640", "type": "column", "url": "https://api.zhihu.com/columns/c_1440732936232816640", "author": {"id": "", "url": "", "user_type": "people", "url_token": "", "name": "匿名用户", "headline": "", "avatar_url": "https://picx.zhimg.com/v2-d41c2ceaed8f51999522f903672a521f_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "苏治龙文章集合", "imageUrl": "https://pic1.zhimg.com/v2-d46b7ce005c7b4c96c1f6acf8fddb5a1_720w.jpg?source=d16d100b", "comment_permission": "public", "intro": "创业经验、职场技巧、老大哲学、帝王之学", "updated": 1738748877, "is_following": false}, "content": "<p data-pid=\"FdCx48qp\">赚一百万大概有四种模式：</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"nbSDHbzs\">第一种，在1000个客户身上各赚一千块，这样能实现赚一百万的目标；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"QYZ1G7c-\">第二种，在100个人身上各赚一万块，这样就能实现赚一百万的目标；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WPMPeusA\">第三种，在10个人身上各赚十万块，这样能实现赚一百万的目标；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"GX6PeVSf\">第四种，在1个人身上赚一百万，这样也能实现赚一百万的目标；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"-YfnHyCR\">那么，四种模式中最容易做到的是哪种？最难做到的是哪种？最不应该去做的是哪种？最适合创始人去做的又是哪种？</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"qjXwb1j8\">在进行难易程度排行之前，我们先来讲一下成交的三个要素：①足够的客流量；②客户的刚需；③客户的支付能力。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"VD_0Wwsd\">举个例子，为了实现赚一百万的目标，我们现在走的是第一种形式，即在1000个客户身上各赚一千块，条件是什么？</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"FkgE8FSc\">①你得有大于1000的铁杆粉丝；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"CZTogPmk\">②这1000人对你的产品有明确的需求；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"LccOe5Vt\">③这1000人有足够的支付能力；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"AhtWH0iO\">成交之后，你需要做到什么？</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Yd7UZUYA\">要让这1000客户都满意你的服务或产品。因为一旦他们对你的产品和服务不满意，下次就不买了，甚至还会在市场上传播你的坏名声。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"k6wccWfv\">好的，接下来我们来思考两个问题：</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"pLsSE5CD\">①口碑和什么相关？</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"QBAGLGFy\">交付质量，即后端服务的质量。收钱是很重要的环节，交付是更重要的环节。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WOmtmyoT\">②那么，交付质量又与什么息息相关？</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"46-ZZl3z\">交付人的水平、心思和时间。也就是说，交付人越有水平、越花心思、越有时间去服务客户，那么交付质量就会更加高，口碑就会更加好，然后他的产品和服务就会卖的更厉害，势能就会更加高，势能高了以后就能卖价格更高的产品，他赚钱就更多了。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"uvmHtNyg\">举个例子，我们在服务1000个客户的时候，能够做到“水平+心思+时间”的各项满足，这样我们的口碑就会更好，就会有更多人来购买我们的东西，然后我们就能推出更高端的产品服务，就能赚更多的钱。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"C_1ChZKU\">这里有个问题：世上存在完美服务1000个客户的案例吗？在1000个客户面前，有人能做到“水平+心思+时间”的各项满足吗？</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TjdWoZmH\">没有的，哪怕一家走规模路线的巨头公司，也无法保证能让买单的1000个客户都满意。人多了，始终会有人不满意，因为你无法面面俱到。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ga_Ecjqd\">明白了这个点，我们就可以得到答案了：</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"dvdkQTs4\">①从口碑方面来看：服务1000个人不如服务100个人，服务100个人不如服务10个人，服务十个人不如服务1个人，即服务1个人＞服务10个人＞服务100个人＞服务1000个人；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"1BhUkz7C\">②从难易程度来看：服务1000个人比服务100人难，服务100人比服务10个人难，服务10个人比服务1个人难；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"zT5AR2WE\">但是，在文章一开头我们就看到了：同样是赚到100万，服务的人数越少，单价就越高。譬如，我们要服务一个人，就必须收他100万，这样才能实现我们的目标。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"bklWZZ8x\">从这个角度来说，服务1000个人是更容易的，因为它只需要你卖出去1000块单价的商品。但从服务人数来说，服务1000个人是极难的。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"7uxAdBdw\">在这里，“服务人数”和“单价”形成了两大矛盾，我们必须要解决这个矛盾才能赚到一百万。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"hkwE8cS9\">怎么解决这个矛盾然后实现赚到一百万的目标呢？放心，在我苏治龙的文章里，不仅会给诸位分享事物的本质逻辑，还会给大家可行的解决方案。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"hRxNigSC\">解决方案只有4个字：折中主义。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"zwP3nhBg\">首先服务1000人太难了，所以我们排除在一千人身上各赚一千块的方案；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"XUOjHfnW\">然后服务一个人很容易，但100万的单价对于普通创始人来说太高了，所以我们也把在一个人身上赚一百万的方案排除掉，日后势能起来了再考虑；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"414SjIWn\">剩下的只有两个方案：1.在100个人身上各赚一万块；2.在10个人身上各赚十万块；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"4PCB7C38\">怎么做选择呢？</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"vfAt4OW6\">根据你的事业阶段来做选择：①如果是刚起步，就选择第一种，努力积累势能，争取服务好更多的人，磨砺自己；②如果是有一定影响力了，就选择第二种，这样能让你更加省力，能让你赚钱更多，而且能让你的口碑越来越好。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"RrdoHZ8V\">苏治龙说：单价问题本质上是溢价问题，解决了溢价问题，单价就不成问题了。而解决溢价问题的最佳手段，就是打造高端的个人品牌。譬如，某个景点本来是票价很低的，平时也没有什么游客，在某些名人住过之后，这个景点的价值就变高了，慕名而来的游客就会变多，哪怕价格比以往更高了，由于名人效应解决了溢价问题，所以单价问题也得到了解决。而这里所讲的名人效应，就是典型的个人品牌所带来的效应。</p><p></p>", "is_labeled": false, "visited_count": 16262, "thumbnails": ["https://picx.zhimg.com/v2-565390e505a1f16180dda0ea72de904c.jpg?source=7e7ef6e2&needBackground=1", "https://pic1.zhimg.com/50/v2-0991e8c8861120750c2d9f186b061bee_720w.jpg?source=b6762063"], "favorite_count": 911, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": *********}", "attached_info": "CucHCOXSz7OH4ZH1mwEQBxoJMjQ0MzUzNDc5IM70pbMGKIUCMA9AK0pBCixUU19TT1VSQ0VfVFdPVE9XRVJfU0hPUlRJTlRFUkVTVF9SRUNBTExfVEVYVBIBMBgAIAA6CnsicmF3IjoiIn1aCDEyODM3MzMyYiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3IJNzAzMDAzMTY0ggFfaHR0cHM6Ly9waWN4LnpoaW1nLmNvbS92Mi01NjUzOTBlNTA1YTFmMTYxODBkZGEwZWE3MmRlOTA0Yy5qcGc/c291cmNlPTdlN2VmNmUyJm5lZWRCYWNrZ3JvdW5kPTGKARVjXzE0NDA3MzI5MzYyMzI4MTY2NDCqAQlyZWNvbW1lbmTCASAxN2NiNmMxODAwNTQzM2E0NDZmZWZkMDllOGM2MWE3OPIBCggMEgZOb3JtYWzyASgIChIkZjFjNDc5MWUtZjE4ZS00ZmFjLWFiNGMtNDhiZjg5YzQwN2E38gEFCAsSATiCAgCIAtCDuc36MpICIDE3Y2I2YzE4MDA1NDMzYTQ0NmZlZmQwOWU4YzYxYTc4mgIAygIWU2hvckludGVyZXN0V2VpZ2h0UnVsZcoCFkFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhtJbnRlcmFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhhQZXJpb2RJbnRlcmVzdFdlaWdodFJ1bGXKAhVVc2VyTGNuRXhpdFdlaWdodFJ1bGXKAhRDb250ZW50QWdlV2VpZ2h0UnVsZcoCF1Rlc3RlZEFuZFdvcmtXZWlnaHRSdWxl2gIsVFNfU09VUkNFX1RXT1RPV0VSX1NIT1JUSU5URVJFU1RfUkVDQUxMX1RFWFToAgP6AgtOT1JNQUxfRkxPV4oDIGZkYTcwY2ZiMzk2YjRkMjY4NTI4YTczZWUyZjM3ZjA3mgMNCgJ2MhAAGgVvdGhlcqgDhn/YAwDqAxpmZWVkX2F0dG1fdHdvdG93ZXJfdjJfdGV4dPoDThIMVU5LTk9XTl9NT0RFIAAqDU5PX0lNQUdFX01PREU6LQgDEIAKGIkHIiN2Mi01NjUzOTBlNTA1YTFmMTYxODBkZGEwZWE3MmRlOTA0Y4AEAIgEAJIEBk5vcm1hbJoEATOgBACoBACwBAC6BAJhacIEAzQwMMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAQAGevj+BBQAAAAAAAAAAiQWsYdagiXbSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUIkAYAoAYvqAYDkgIkCgkyNDQzNTM0NzkSCTcwMzAwMzE2NBgHIgpJTUFHRV9URVhU", "action_card": false}, {"id": "44_1750898459.826", "type": "feed", "offset": 44, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898459, "updated_time": 1750898459, "target": {"id": "2963956828", "type": "answer", "url": "https://api.zhihu.com/answers/2963956828", "author": {"id": "00a69a0323d435435c7bbd0f1bd535e5", "url": "https://api.zhihu.com/people/00a69a0323d435435c7bbd0f1bd535e5", "user_type": "people", "url_token": "canglimo", "name": "墨苍离", "headline": "文明其精神，野蛮其意志。世故其言行，耿直其理想。", "avatar_url": "https://picx.zhimg.com/50/v2-47ef6c90e5b3cb6ecbb93c6ff6a9b6f8_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 657330, "is_following": false, "is_followed": false}, "created_time": 1680350172, "updated_time": 1680350172, "voteup_count": 6382, "thanks_count": 3875, "comment_count": 184, "is_copyable": false, "question": {"id": "22921426", "type": "question", "url": "https://api.zhihu.com/questions/22921426", "author": {"id": "", "url": "", "user_type": "people", "url_token": "", "name": "匿名用户", "headline": "", "avatar_url": "https://picx.zhimg.com/v2-d41c2ceaed8f51999522f903672a521f_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "要怎样努力，才能成为很厉害的人？", "created": 1393861183, "answer_count": 0, "follower_count": 0, "comment_count": 173, "bound_topic_ids": [404, 1746, 1761, 5894, 90294], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "照着做，能坚持五年就行。 1，精通阅读和自学。 多数人的阅读都是在自洽或猎奇，这会导致强化个人的一些观察者偏见，而不能获得对陌生领域的体会和了解。阅读必须学会代入作品中的情景和多个人物，去揣摩没有写出来的他们的思考，甚至必须代入作者，去思考他的创作结构和价值观。同时，这也跟自学完全是兼容的。这将有利于你无障碍的获取夸领域和阶层的知识。 2，不迷信条条框框。 思想钢印和约定俗成、大家都这样，等等这些东西…", "excerpt_new": "照着做，能坚持五年就行。 1，精通阅读和自学。 多数人的阅读都是在自洽或猎奇，这会导致强化个人的一些观察者偏见，而不能获得对陌生领域的体会和了解。阅读必须学会代入作品中的情景和多个人物，去揣摩没有写出来的他们的思考，甚至必须代入作者，去思考他的创作结构和价值观。同时，这也跟自学完全是兼容的。这将有利于你无障碍的获取夸领域和阶层的知识。 2，不迷信条条框框。 思想钢印和约定俗成、大家都这样，等等这些东西…", "preview_type": "default", "preview_text": "", "reshipment_settings": "disallowed", "content": "<p data-pid=\"VlbODA0W\">照着做，能坚持五年就行。</p><p data-pid=\"oYiGgLQE\">1，精通阅读和自学。</p><p data-pid=\"xyX5cH2j\">多数人的阅读都是在自洽或猎奇，这会导致强化个人的一些观察者偏见，而不能获得对陌生领域的体会和了解。阅读必须学会代入作品中的情景和多个人物，去揣摩没有写出来的他们的思考，甚至必须代入作者，去思考他的创作结构和价值观。同时，这也跟自学完全是兼容的。这将有利于你无障碍的获取夸领域和阶层的知识。</p><p data-pid=\"xidR_uu7\">2，不迷信条条框框。</p><p data-pid=\"eLukW4lb\">思想钢印和约定俗成、大家都这样，等等这些东西往往会限制人的思维，局限于过去（或在熟悉环境下）适用的状况。一旦换了环境，或者环境发生了变化，就会出现决策盲区。而这些盲区往往是机会点，或别人可以借此剥削你的。</p><p data-pid=\"nl5XPK25\">3，每个月都要接触新事物。</p><p data-pid=\"pBRc1G3y\">舒适、安全感、自得感，通常是对当前环境适应带来的错觉。无论多么舒适和适应当前环境，在剧烈变化中，都是有可能被拉下的，所以逼着自己每个月接触新事物，简单点，可以研究个陌生行业或看几本书，复杂点，可以去找份陌生的实践活动。这有利于自己的眼界和格局。</p><p data-pid=\"t-kIh8al\">4，养成总结习惯。</p><p data-pid=\"d6Osl6mK\">每天用5分钟总结，获得了什么新的知识和思路，知道了什么多样化的观点，学会了什么小技能，获得了什么感悟。这将有利于获得充实感、更多自律性以及养成微成长的成就感。</p><p data-pid=\"z-Y2m9yS\">5，整理社交关系。</p><p data-pid=\"vS1ub3zE\">将社交关系分类管理，分为几个组，有的组，需要客套、节日拜访等，有的组则需要日常联系，或仅仅需要被动等待联系；重要社交关系的总数要可控，有增有减。这将控制好羁绊的数量和边界，减少负担，并采用更少的精力管理好更大的人脉网络。</p><p data-pid=\"A1i54yR1\">6，间歇性切断社交。</p><p data-pid=\"Mh-_sfwy\">间歇性的逼自己切断所有非亲人社交（包括网络的社交），周期可以是每周一天，或者每月连续两天。这会让人离开繁杂的人群，对社交情绪收益进行戒断，从而减少社交依赖，也减少频繁交互带来的“快思考”。</p><p data-pid=\"JIHPeW7b\">7，学好数学基本功。</p><p data-pid=\"ds58YrIT\">在线性代数以前的，包括高数等，通读+理解，这不需要做范例的熟练度，也不需要解题思路，只需要弄懂其原理，融汇贯通其结构。当一个人具备了扎实的数学思路的时候，他的博弈基础就胜过了大多数人。</p><p data-pid=\"Aqn-d73O\">以上。</p><p data-pid=\"aZYdy53U\">供参考。</p><p></p><p></p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 338249, "favorite_count": 23588, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 2963956828}", "attached_info": "CrUGCOXSz7OH4ZH1mwEQBBoJNTY4OTg1NTY2INy3oKEGKO4xMLgBQCxKQQosVFNfU09VUkNFX1RXT1RPV0VSX1NIT1JUSU5URVJFU1RfUkVDQUxMX1RFWFQSATAYACAAOgp7InJhdyI6IiJ9WgcxMzQ5MzgzYiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3IKMjk2Mzk1NjgyOIoBCDIyOTIxNDI2qgEJcmVjb21tZW5kwgEgMDBhNjlhMDMyM2Q0MzU0MzVjN2JiZDBmMWJkNTM1ZTXyAQoIDBIGTm9ybWFs8gEoCAoSJGY2MTY2NDA4LTczMmYtNDg2OC1iZjU3LTgzMmNjMmZlNjA1MfIBBQgLEgE4ggIAiALQg7nN+jKSAiAwMGE2OWEwMzIzZDQzNTQzNWM3YmJkMGYxYmQ1MzVlNZoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhZBY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIbSW50ZXJhY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIYUGVyaW9kSW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxlygIUQ29udGVudEFnZVdlaWdodFJ1bGXaAixUU19TT1VSQ0VfVFdPVE9XRVJfU0hPUlRJTlRFUkVTVF9SRUNBTExfVEVYVOgCAvoCC05PUk1BTF9GTE9XigMgZmRhNzBjZmIzOTZiNGQyNjg1MjhhNzNlZTJmMzdmMDeaAw0KAnYyEAAaBW90aGVyqAPJ0hTYAwDqAxpmZWVkX2F0dG1fdHdvdG93ZXJfdjJfdGV4dPoDHxIMVU5LTk9XTl9NT0RFIAAqDU5PX0lNQUdFX01PREWABACIBACSBAZOb3JtYWyaBAEyoAQAqAQAsAQAugQGbWFudWFswgQDMTYwyAQA0gQP5o6o6I2Q5bey5pu05paw2AQA8AQA+QQAAABg7A7FP4EFAAAAAAAAAACJBaxh1qCJdtI/kgUAmgUDZGZ0ogUDZGZ0sgUBMbkFAAAAAAAAAADQBQDgBQDoBQDwBQiQBgCgBjCoBgCSAiUKCTU2ODk4NTU2NhIKMjk2Mzk1NjgyOBgEIgpJTUFHRV9URVhU", "action_card": false}, {"id": "45_1750898459.448", "type": "feed", "offset": 45, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898459, "updated_time": 1750898459, "target": {"id": "*********", "type": "article", "url": "https://api.zhihu.com/articles/*********", "author": {"id": "0dece830e94a42dd3ed9ebff7acc9fe0", "url": "https://api.zhihu.com/people/0dece830e94a42dd3ed9ebff7acc9fe0", "user_type": "people", "url_token": "yuan-shan-cai", "name": "密排六方橘子", "headline": "混元太极摸鱼门掌门人", "avatar_url": "https://pica.zhimg.com/50/v2-c0b94d9e470c92e33dff0672bda2d24d_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 11270, "is_following": false, "is_followed": false}, "title": "LLM入门指南", "comment_permission": "all", "created": 1701758705, "updated": 1704698507, "voteup_count": 2875, "voting": 0, "comment_count": 41, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "前情提要：本人是学CV的，然后公司梭哈LLM，直接被拉去充了壮丁…… 本文并不是从0开始学NLP，所以有些基础知识可能需要自己去了解一下，比如transformer结构之类的。 那么本文主要关注的是昆仑天工和阿里通义千问（2023.11），至于为什么是这俩……天工是因为他们的开源号称是最有诚意的，阿里的千问则是因为他们的效果目前看起来是最好的。 文章内容似乎有点多，可以参照目录自行查阅需要的内容。 从Scaling Law说起众所周知，…", "excerpt_new": "前情提要：本人是学CV的，然后公司梭哈LLM，直接被拉去充了壮丁…… 本文并不是从0开始学NLP，所以有些基础知识可能需要自己去了解一下，比如transformer结构之类的。 那么本文主要关注的是昆仑天工和阿里通义千问（2023.11），至于为什么是这俩……天工是因为他们的开源号称是最有诚意的，阿里的千问则是因为他们的效果目前看起来是最好的。 文章内容似乎有点多，可以参照目录自行查阅需要的内容。 从Scaling Law说起众所周知，…", "preview_type": "default", "preview_text": "", "content": "<p data-pid=\"GpX42TeV\">前情提要：本人是学CV的，然后公司梭哈LLM，直接被拉去充了壮丁……</p><p data-pid=\"3vf36P3F\">本文并不是从0开始学NLP，所以有些基础知识可能需要自己去了解一下，比如transformer结构之类的。</p><p data-pid=\"lfYNDLqN\">那么本文主要关注的是昆仑天工和阿里通义千问（2023.11），至于为什么是这俩……天工是因为他们的开源号称是最有诚意的，阿里的千问则是因为他们的效果目前看起来是最好的。</p><p data-pid=\"QHr8n-cV\">文章内容似乎有点多，可以参照目录自行查阅需要的内容。</p><p class=\"ztext-empty-paragraph\"><br/></p><h2>从Scaling Law说起</h2><p data-pid=\"q0lxq3pC\">众所周知，根据OpenAI的scaling圣经（<a href=\"https://link.zhihu.com/?target=https%3A//arxiv.org/pdf/2001.08361.pdf\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">arxiv.org/pdf/2001.0836</span><span class=\"invisible\">1.pdf</span><span class=\"ellipsis\"></span></a>）有一些简单的结论，比如：</p><p data-pid=\"Qd4L_NgD\">C ≈ 6NBS，或者写成C ≈ 6ND</p><p data-pid=\"gdaaHFqI\">其中C代表计算量（单位PF-days），N代表模型参数量（需要去掉PE和词表），D代表数据集大小（单位token），你自然可以把D换成BS（即batchsize*step），因为大模型pretrain一般也不会有第二个epoch，所以这两个东西基本上是等价的。</p><p data-pid=\"6KzFUVOn\">于是我们只要预设一个计算量（你能负担得起的时间和显卡成本），以及你需要的模型规模，就可以大致推算出你的数据集得要多大的；反过来也是可以的。</p><p data-pid=\"hoNtPCII\">根据原文，天工13B用512张A800训了39天，我们假定它打满了算力峰值（实际根据文章中是56.5%），可以直接计算出总算力是 <img src=\"https://www.zhihu.com/equation?tex=512%5Ctimes312%5Ctimes39%5Ctimes86400%5Capprox5.38%5Ctimes10%5E%7B11%7D\" alt=\"512\\times312\\times39\\times86400\\approx5.38\\times10^{11}\" eeimg=\"1\"/> TFlops，也就是大概5e23的计算量，其中312是A800的TF16算力（尽管天工用的是BF16，不过根据论文里的一些佐证，这个数应该是对的）。</p><p data-pid=\"H9gJF4st\">考虑到算力的损失，天工总共花了3e23左右的算力，去做一个13B（1.3e10）模型，需要的数据量差不多就是4e12，也就是4TB token左右的数据，而摘要的第一句就说了天工使用了超过3.2T的token，说明天工整体依然是按照scaling law来做的，并没有在这种经验规律上搞什么花活儿。</p><p data-pid=\"RlLoe7q3\">以上就是scaling law的第一个小功能：<b>在一定的算力/模型规模/数据规模的预算下，如何去确定你的最优训练策略</b>，或者说是某种经验主义的“最优配比”。</p><p data-pid=\"xy1-Glxj\">Scaling law的另一个结论如下：</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-53e742750300c48665c3fdf54fea2934_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1104\" data-rawheight=\"328\" data-original-token=\"v2-53e742750300c48665c3fdf54fea2934\" class=\"origin_image zh-lightbox-thumb\" width=\"1104\" data-original=\"https://pica.zhimg.com/v2-53e742750300c48665c3fdf54fea2934_r.jpg\"/></figure><p data-pid=\"CwCPt5UM\">这三个式子反映的是CND在两者固定的情况下，单独调整某一个变量对模型loss带来的影响。除此之外还有一些公式，因为理解起来有点麻烦就不放在这了。</p><p data-pid=\"WR-JjC4J\">PS：我们知道了C ≈ 6ND，可以来看一下这三个式子是什么情况。1 PF-day = 8.64e19Flops，于是我们全用这边的基数做计算，那么6ND大概是2.85e28，C大概是2.68e28，能对上，没毛病。</p><p data-pid=\"XcxFvGKK\">然后你发现，根据C ≈ 6ND计算最优配比之后，如果实际情况稍有区别（比如我的算力有限导致模型未能完全收敛），这时候根据（1.3）就可以立即知道在你的算力条件下，loss能达到多少；当然更极端一点说，如果所有的模型都忠实地按照scaling law来走（不仅是幂律，包括参数的数值），模型在出生之前就已经决定结果了（比如无限算力+无限数据，根据1.1式，模型Loss只跟网络规模有关），你所能做的无非是让数据集更加贴近测试指标……</p><p data-pid=\"jSQ57iNX\">于是这就是scaling law的第二个功能了：<b>你可以在跑实验之前使用scaling law直接预测其他模型的表现</b>。因为大模型训练成本实在是太高了，很难支持“跑多次实验“这样的鬼故事。</p><p data-pid=\"dOWsMKBH\">当然实际情况下，这个幂次的具体数值不一定能直接套用到你的模型结构上，因为模型之间亦有差距。因此可以参考<a href=\"https://zhuanlan.zhihu.com/p/631357320\" class=\"internal\" target=\"_blank\">cingti：介绍一些Scaling Laws</a>里详细描述的内容（本质是上面的1.1式），也就是我们可以先跑几个小模型，然后画多条损失-计算量曲线（L-C，一个模型画一条），如下图所示：</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-3315c7f873056134a08a585b2cd307a9_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"478\" data-original-token=\"v2-3315c7f873056134a08a585b2cd307a9\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://picx.zhimg.com/v2-3315c7f873056134a08a585b2cd307a9_r.jpg\"/></figure><p data-pid=\"D7DjTKN_\">从图上可以看出，收敛的拐点在对数图上连起来差不多是一条直线，这样实际上你可以知道多个小模型收敛时CND各是多少，这样就能估计大模型的表现。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"BIChOFuU\">题外话：</p><p data-pid=\"JDeujIaZ\">在这两年经过大家的反复验证之后，大家逐渐发现scaling law甚至像物理定律一样准确。这时候你突然意识到一个有些严重的问题，就是如果这玩意真这么准的话，那么……</p><p data-pid=\"s1U-pnmY\"><b>大模型在出生之前，它的性能就已经决定了。</b></p><p data-pid=\"gmI1vWpV\">以前主流的AI research的范式都是改网络结构，或者改一些细节（比如RoPE，激活函数等等），你能见到不少闪光的思路。而著名的国产大模型在llama2出来之后很多都沿用了llama（或者说transformer decoder），能改的也就是网络深度宽度这种不涉及结构变化的东西……但是这其实也是scaling law的预言，因为它专门在文中提了一句说<b>模型收敛时的损失跟网络规模的关系很大，而跟网络结构的关系很小</b>……</p><p data-pid=\"07fnIyCJ\">再直白一点说，如果scaling law存在，那么最近所有国产大模型的提升都来源于数据的增加（数量或者质量），<b>能提多少完全取决于你用的训练集跟CEVAL和MMLU这种数据集有多少的domain gap，</b>至于模型本身的能力完全是换汤不换药式的提升，scaling law就是站在那里的一堵高墙，除非你能越过它，否则就是在折腾CND，仅此而已。</p><p data-pid=\"eCkd3zvT\">如果说以前刷榜好歹是提出了一些新方法（哪怕它并不实用），但是现在甚至退化了，只是折腾折腾数据去刷SOTA……意义在哪里呢？甚至不如搞VLM和RLHF更有创意，而实际上GPT4V也在走VLM的道路。</p><p data-pid=\"WWeVIxE1\">当然我并不是要说最近的大模型一无是处，毕竟我只是评价冰箱制不制冷。实际做的时候肯定有诸多细节只有踩一遍坑才知道，哪怕理论创新有限，能从头到尾实现一遍也是不可多得的经验，至少比我这种只会口嗨的强多了。</p><p class=\"ztext-empty-paragraph\"><br/></p><h2>数据集</h2><h3>训练数据集（天工）</h3><p data-pid=\"-_0bThnp\">天工开源了一个150B的数据集（SkyPile），当然他们自家的数据集是6T的，这只是其中非常小的一部分。这150B的数据显然是不够训一个完整的LLM的，所以我们只是介绍一下清洗数据的过程。</p><p data-pid=\"M9ne92dP\">在这里我要点名表扬一下天工的GRE选手，请品鉴：</p><p data-pid=\"G3eIeMWE\">In the pursuit of cultivating a profoundly adept LLM, the model’s exposure must encompass a diverse array of content spanning an extensive spectrum of domains. Prior endeavors within the field have entailed the task of assigning categorical labels to each individual document or webpage, thereby manually dictating the composition of the training corpus. However, we posit that the corpus employed for LLM training has burgeoned to such an extent that the knowledge it encapsulates can not be compartmentalized discretely. Consequently, eschewing a label-centric approach, our methodology centers on benchmarking the semantic affinities existing between textual segments, thereby identifying and omitting those text blocks characterized by an exceedingly high recurrence rate.</p><p data-pid=\"V0BHig9w\">我觉得你们要是之后打算投稿也就算了……要是只是作为造福社区的技术报告，我建议还是稍微user friendly一些，毕竟隔壁qwen，llama乃至scaling law也没见谁把论文写成这样吧。</p><p data-pid=\"m5gmfW8r\">当然我也没什么资格说，毕竟开源代码是我当伸手党……</p><p data-pid=\"s6dAq8SZ\">总之数据清洗大致可以分为以下四个部分：</p><ol><li data-pid=\"DxvyCR9H\">结构化过滤：因为训练数据主要来源于网页，所以需要去掉一些没用的东西（比如导航条，网页上的联系方式等等）</li><li data-pid=\"eQol92TP\">分布过滤：没太看懂什么意思。大致是说其他工作一般都会给文本分一个标签，但是这里认为文本涉及的内容可能很丰富（包含不止一个标签），所以不是按标签，而是测试“文本之间的亲和力”从而去掉一些过于重复的语段，但是这个semantic affinitiy到底是什么东西好像也没说，全文就提过一次affinity，说好最有诚意的开源呢（恼）。千问在去重这方面说得更清楚一些，大致是用MinHash和LSH来做模糊匹配来筛掉重复内容，之后还有一些精确匹配进一步去重。</li><li data-pid=\"lY7hqZhq\">去重：总而言之是分布过滤的一部分。</li><li data-pid=\"Kh0Suo39\">质量过滤：用了CCNet模型来判断 1）质量低的文本 2）中英文以外的语言。除此之外还训练了一个二分类器（是否适合放进维基百科），通过这个分类器筛选出一部分高质量文本专门放到high quality groups里。</li></ol><p data-pid=\"DJ91Y5eW\">其他还有一些细节但是还挺重要的东西（比如说构建了一个完全平行的中英文语料库放进整个数据集里，还有就是保留了一部分带格式的数据，比如xml和json等等）。</p><p data-pid=\"391TZdYA\">最后的数据构成（整个6T数据集，不是150B的）如下图所示：</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-6468a8e2f7af78a51b392f4bf6944dde_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"577\" data-rawheight=\"522\" data-original-token=\"v2-6468a8e2f7af78a51b392f4bf6944dde\" class=\"origin_image zh-lightbox-thumb\" width=\"577\" data-original=\"https://pic1.zhimg.com/v2-6468a8e2f7af78a51b392f4bf6944dde_r.jpg\"/></figure><p data-pid=\"K7FTD7wO\">注意对于一些高质量的语料（比维基百科）可以在数据集里多重复几遍，但是根据某些论文里的经验，最好不要超过5遍。</p><h3>评价数据集</h3><p data-pid=\"MPOcXcrz\">评价数据集也是数据集，就放在这里一块说了。</p><p data-pid=\"DN3yNEFC\">常见的数据集如下：</p><p data-pid=\"JgPLErPu\"><b>MMLU：</b>多任务语言理解（包括STEM和社科之类的内容），形式为选择题。</p><p data-pid=\"wDEI9unb\"><b>C-Eval：</b>中文知识问答，选择题。</p><p data-pid=\"JjbSN87w\"><b>GSM8K：</b>数学题，一般回答形式是problem-solution-final answer，最后根据答案判定是否做对。</p><p data-pid=\"7dbNnd-L\"><b>MATH：</b>数学题，但是格式是latex。</p><p data-pid=\"GLBk2R5u\"><b>HumanEval：</b>代码生成，包含功能描述和输入输出样例。这里的评价指标包含pass@1和pass@x，pass@1表示根据greedy生成的通过率，pass@x代表多写几次去测，当然pass@x的效果还是要高很多的……</p><p data-pid=\"lsQhNlTB\"><b>MBPP：</b>跟HumaEval差不多，也是代码生成。</p><p data-pid=\"PcBFma5M\"><b>BBH：</b>跟MMLU差不多，纯英文，选择题。</p><p data-pid=\"lPibpQMh\"><b>CMMLU：</b>看名字就知道是中文的一个评价基准，跟C-Eval差不多。</p><p data-pid=\"0a935KPL\">以下为私货。</p><p data-pid=\"cfiDlgrw\">我去看了一眼这几个数据集，<b>中文数据集都有一个毛病，就是偏向于“知识问答”</b>，问题很直球，回答也很直球。</p><p data-pid=\"osvo8ong\">MMLU和BBH有一些问题像英语听力阅读理解，相比而言更难一些。还有一些问题搞得更加花里胡哨，比如说“which of the following is a humorous edit of this artist of movie name”，要求模型有更深的语言理解能力，甚至还有一些脑洞大开的测试，有兴趣可以自己去hugging face的BBH看一下。</p><p data-pid=\"Rbh5kC9n\">CMMLU只有在涉及modern Chinese的时候才会偶尔出现这种需要动下脑子的题目，可惜的是整个数据集加起来也没多少条，C-Eval里也是寥寥无几。</p><p data-pid=\"0s3Tw2zj\">这就带来一个问题，就是在一些情况下<b>对语言的理解能力需求远远高于对领域知识的需求</b>（比如长文本summary或者写小说），因此我的建议是<b>不要迷信评价指标</b>，尤其是C-Eval这种纯知识问答类的评价指标。</p><p data-pid=\"aosjIM0r\">这还只是评价数据集本身的问题，更不要说在训练时有可能出现的数据泄露。因为现在的pretrain数据集很多都是来源于网页，但是有一些测试集里面的题目你在网上能搜到原题。</p><p class=\"ztext-empty-paragraph\"><br/></p><h2>关于位置编码</h2><p data-pid=\"bjkbZQkF\">这部分我的建议是看苏剑林（苏神）的两个博客（<a href=\"https://link.zhihu.com/?target=https%3A//kexue.fm/archives/8231\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Transformer升级之路：1、Sinusoidal位置编码追根溯源 - 科学空间|Scientific Spaces</a>和<a href=\"https://link.zhihu.com/?target=https%3A//kexue.fm/archives/8265\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Transformer升级之路：2、博采众长的旋转式位置编码 - 科学空间|Scientific Spaces</a>)。</p><p data-pid=\"B2J3Aoqr\">不过大家来都来了，我就用更加容易理解的说法来介绍一下位置编码。</p><p data-pid=\"AOZIKwB8\">位置编码的主要目的是给transformer（attention）掺入位置信息，因为<b>attention本身并不包含位置信息</b>（你任意调换一个句子中两个字的位置，对attention结果是没有影响的，比如说“我吃饭”和“饭吃我”这两个东西显然不应该是同一个概率），因此一个好办法是<b>在embedding/encoding的时候在每个位置的输入上都掺一个东西</b>，让这种对称性被破坏掉，这样attention的时候就会带着这个位置信息，网络就能学得更好。</p><p data-pid=\"gHCcx1AM\">然后问题就来了，位置信息应该怎么掺。一种思路是把<b>绝对位置</b>直接塞进去，另一种显然就是使用<b>相对位置</b>。但是无论是绝对还是相对位置，最好<b>需要保持一定的外推能力</b>，否则只能处理特定长度以内的文本，因为超过预设长度的embedding/encoding网络在训练的时候是没见过的，测试阶段如果超了长度就变成了一个OOD样本，你不知道他会输出什么东西。</p><p data-pid=\"Ma6dB8Me\">至于相对位置和绝对位置到底哪个更好，尽管没有明确定论，但是在LLM大行其道的年代，谁能更方便地外推谁就是大哥。</p><h3>Sinusoidal位置编码</h3><p data-pid=\"0Zp9DkyQ\">经典的绝对位置编码，来源于Transformer原始论文，这里简单做一个解释。</p><p data-pid=\"iipY6nRu\">我们把attention看作函数 <img src=\"https://www.zhihu.com/equation?tex=f\" alt=\"f\" eeimg=\"1\"/> ，于是对于位置 <img src=\"https://www.zhihu.com/equation?tex=m\" alt=\"m\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=n\" alt=\"n\" eeimg=\"1\"/> ，我们有这两个位置的输入 <img src=\"https://www.zhihu.com/equation?tex=x_%7Bm%7D\" alt=\"x_{m}\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=x_%7Bn%7D\" alt=\"x_{n}\" eeimg=\"1\"/> 以及他们的位置编码 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D\" alt=\"p_{m}\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bn%7D\" alt=\"p_{n}\" eeimg=\"1\"/> 。<b>位置编码可以视作一个小量</b>，对于相加式的的位置编码（比如说<img src=\"https://www.zhihu.com/equation?tex=x_%7Bm%7D\" alt=\"x_{m}\" eeimg=\"1\"/>变成<img src=\"https://www.zhihu.com/equation?tex=x_%7Bm%7D%2Bp_%7Bm%7D\" alt=\"x_{m}+p_{m}\" eeimg=\"1\"/> ）可以直接对 <img src=\"https://www.zhihu.com/equation?tex=f\" alt=\"f\" eeimg=\"1\"/> 做泰勒展开。这里我连展开公式都不用贴，稍微想一下你就知道<b>一阶项只会跟单一位置有关，某个二阶项才会涉及到两个位置的交互</b>，也就是跟<b>相对位置</b>有关。</p><p data-pid=\"Xv2xU7LQ\">而我们其实是<b>希望这个二阶项能够表达相对位置的信息，或者说能找到一个函数g，这个g(m-n)=这个二阶项，</b>这样m-n的信息就能够体现在attention里了。</p><p data-pid=\"83rdzhxa\">这个二阶项大概长这样： <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D%5E%7BT%7DHp_%7Bn%7D\" alt=\"p_{m}^{T}Hp_{n}\" eeimg=\"1\"/> ，其中<img src=\"https://www.zhihu.com/equation?tex=H+%3D+%5Cfrac%7B%5Cpartial%5E%7B2%7Df%7D%7B%5Cpartial+x_%7Bm%7D%5Cpartial+x_%7Bn%7D%7D\" alt=\"H = \\frac{\\partial^{2}f}{\\partial x_{m}\\partial x_{n}}\" eeimg=\"1\"/> 。直接解释H似乎有点困难，但是你可以大致想一下对于一个attention map而言，这一项实际意味着<b>任意两个位置的<img src=\"https://www.zhihu.com/equation?tex=x_%7Bm%7D\" alt=\"x_{m}\" eeimg=\"1\"/>和 <img src=\"https://www.zhihu.com/equation?tex=x_%7Bn%7D\" alt=\"x_{n}\" eeimg=\"1\"/>的变动对attention的影响（或者说贡献）是否相同</b>，以及他俩之间<b>是否有相关性</b>。</p><p data-pid=\"3-YLZ2J7\">理想情况下就是<b>任意两个位置的输入对于attention的贡献相同且互相解耦，此时H直接就是单位阵</b>，我们接下来也会基于这一假设去进一步介绍后面的内容；<b>退一步说</b>，如果他们对attention的贡献不同，但是<b>仍然是互相独立的情况下，H会变成一个对角阵</b>，此时仍然能够保留一些不错的性质；那么<b>最坏的情况下H是一个普通矩阵</b>，这时候我们下面提到的一切性质都不成立。幸运的是，苏神自己check了一下这个矩阵的权重，发现至少在embedding层，把H当成一个对角阵还是合理的，有兴趣可以自己去看一下他的博客。</p><p data-pid=\"uTtXx-m5\">好，那我们现在假定H就是单位阵，此时 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D%5E%7BT%7DHp_%7Bn%7D\" alt=\"p_{m}^{T}Hp_{n}\" eeimg=\"1\"/> 直接退化成 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D%5E%7BT%7Dp_%7Bn%7D\" alt=\"p_{m}^{T}p_{n}\" eeimg=\"1\"/> ，<b>注意这里的p其实都是向量</b>，所以它其实就是两个向量的内积。根据某些我已经忘得差不多了的知识，对于一个二维向量，也可以用复平面上的向量来表示；因此我们干脆再简化一点，就假定p也是二维的，于是此时 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D%5E%7BT%7Dp_%7Bn%7D%3DRe%5Bp_%7Bm%7Dp_%7Bn%7D%5E%7B%2A%7D%5D\" alt=\"p_{m}^{T}p_{n}=Re[p_{m}p_{n}^{*}]\" eeimg=\"1\"/> ，其中 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bn%7D%5E%7B%2A%7D\" alt=\"p_{n}^{*}\" eeimg=\"1\"/> 代表 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bn%7D\" alt=\"p_{n}\" eeimg=\"1\"/> 的共轭，Re代表实部。</p><p data-pid=\"26WfckW0\">于是现在的目标变成了：找到一个复数 <img src=\"https://www.zhihu.com/equation?tex=g\" alt=\"g\" eeimg=\"1\"/> ，使得 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7Dp_%7Bn%7D%5E%7B%2A%7D%3Dg%28m-n%29\" alt=\"p_{m}p_{n}^{*}=g(m-n)\" eeimg=\"1\"/> 。</p><p data-pid=\"b88KfRUA\">那么接下来的事情就非常简单了，我们不妨把它写成复数的指数形式，即：</p><p data-pid=\"XPahVnRq\"><img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D%3Dr_%7Bm%7De%5E%7Bi%5Cphi_%7Bm%7D%7D\" alt=\"p_{m}=r_{m}e^{i\\phi_{m}}\" eeimg=\"1\"/> , <img src=\"https://www.zhihu.com/equation?tex=p_%7Bn%7D%3Dr_%7Bn%7De%5E%7B-i%5Cphi_%7Bn%7D%7D\" alt=\"p_{n}=r_{n}e^{-i\\phi_{n}}\" eeimg=\"1\"/> , <img src=\"https://www.zhihu.com/equation?tex=g%28m-n%29%3DR_%7Bm-n%7De%5E%7Bi%5CPhi_%7Bm-n%7D%7D\" alt=\"g(m-n)=R_{m-n}e^{i\\Phi_{m-n}}\" eeimg=\"1\"/> </p><p data-pid=\"H27F68Fn\">于是很显然，要让他们相等就必然有（模长相等，辐角相等）：</p><p data-pid=\"iTq7N7ZS\"><img src=\"https://www.zhihu.com/equation?tex=r_%7Bm%7Dr_%7Bn%7D%3DR_%7Bm-n%7D\" alt=\"r_{m}r_{n}=R_{m-n}\" eeimg=\"1\"/> , <img src=\"https://www.zhihu.com/equation?tex=%5Cphi_%7Bm%7D-%5Cphi_%7Bn%7D%3D%5CPhi_%7Bm-n%7D\" alt=\"\\phi_{m}-\\phi_{n}=\\Phi_{m-n}\" eeimg=\"1\"/> </p><p data-pid=\"LMN7h_jO\">对第一个式子令m=n立刻得到 <img src=\"https://www.zhihu.com/equation?tex=r_%7Bm%7D%5E%7B2%7D%3DR_%7B0%7D\" alt=\"r_{m}^{2}=R_{0}\" eeimg=\"1\"/> ，也就是说 <img src=\"https://www.zhihu.com/equation?tex=r_%7Bm%7D\" alt=\"r_{m}\" eeimg=\"1\"/> 是个常数，方便起见直接令他等于1就行。</p><p data-pid=\"0HQDXnBm\">对第二个式子我们一眼就能看出令 <img src=\"https://www.zhihu.com/equation?tex=%5CPhi_%7Bm%7D%3D%5Cphi_%7Bm%7D%3Dm%5Ctheta\" alt=\"\\Phi_{m}=\\phi_{m}=m\\theta\" eeimg=\"1\"/> 就是一个解，也就是说反正是等差数列就完事。</p><p data-pid=\"NaXh0pWL\">也就是说我们已经找到了这个式子的一个解了，有没有其他解不重要，反正这个能用。</p><p data-pid=\"7JfC7Ah_\">回到我们之前说的，我们想要求解这个式子，<b>本质是想找到一个函数g作为位置编码，使得g(m-n)恰好等于f泰勒展开的二阶项，如果找到了，那么f里就会自然而然地带上相对位置信息。</b></p><p data-pid=\"tpMqDKIH\">而现在这个g恰好能用，也就是说对于一个二维向量 <img src=\"https://www.zhihu.com/equation?tex=x_%7Bm%7D\" alt=\"x_{m}\" eeimg=\"1\"/> ，你只需要给他加上一个形如 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D%3De%5E%7Bim%5Ctheta%7D\" alt=\"p_{m}=e^{im\\theta}\" eeimg=\"1\"/> 的向量作为位置编码即可。进一步地，只要这个向量是偶数维，你就每两维（第2i和2i+1维）给他安排一个 <img src=\"https://www.zhihu.com/equation?tex=sin%28m%5Ctheta_%7Bi%7D%29\" alt=\"sin(m\\theta_{i})\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=cos%28m%5Ctheta_%7Bi%7D%29\" alt=\"cos(m\\theta_{i})\" eeimg=\"1\"/> 即可。</p><p data-pid=\"4roKDkKi\">我们回头来看transformer论文里写的PE：</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-182c63730d3c6af66b13ad6a08bf1fbd_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"566\" data-rawheight=\"126\" data-original-token=\"v2-182c63730d3c6af66b13ad6a08bf1fbd\" class=\"origin_image zh-lightbox-thumb\" width=\"566\" data-original=\"https://pic2.zhimg.com/v2-182c63730d3c6af66b13ad6a08bf1fbd_r.jpg\"/></figure><p data-pid=\"Hd3RWGNi\">很显然，我们的结果已经跟他非常像了。这两个式子里的pos对应于我们的m，而唯一的区别就是<b>我们的 <img src=\"https://www.zhihu.com/equation?tex=%5Ctheta_%7Bi%7D\" alt=\"\\theta_{i}\" eeimg=\"1\"/> 实际上是随机的，你想让他是多少就可以是多少，但是transformer里给它特意加了一个数值</b> <img src=\"https://www.zhihu.com/equation?tex=10000%5E%7B2i%2Fd%7D\" alt=\"10000^{2i/d}\" eeimg=\"1\"/> 。</p><p data-pid=\"pyymqZiZ\">这个值的意义在何处呢？</p><p data-pid=\"y05uzpbx\">在于他有一个非常好的性质，就是<b>随着m-n（也就是相对距离）的增大， <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D%5E%7BT%7Dp_%7Bn%7D\" alt=\"p_{m}^{T}p_{n}\" eeimg=\"1\"/> 的结果会逐渐减小</b>，反映在transformer里就是两个输入距离越远，相关性越小，这其实是非常符合直觉的。至于为什么会出现这个现象，其实你硬算一下就知道了，在d足够大的时候可以把求和近似转化为震荡积分（或者可以直接画一下图），篇幅所限，这里就不折腾了。至于到底是1w还是1k，似乎并没有太大所谓。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-18e67ef2ca5bcd6814eb6ee0597610ea_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1992\" data-rawheight=\"854\" data-original-token=\"v2-18e67ef2ca5bcd6814eb6ee0597610ea\" class=\"origin_image zh-lightbox-thumb\" width=\"1992\" data-original=\"https://pica.zhimg.com/v2-18e67ef2ca5bcd6814eb6ee0597610ea_r.jpg\"/><figcaption>来源：https://kexue.fm/archives/8231</figcaption></figure><p data-pid=\"LovNWtt1\">于是我们来对这部分做个总结：</p><p data-pid=\"EA2HEwjt\"><b>由于attention机制本身并不带有位置信息，所以我们希望在embedding的时候添加位置编码，使得两个输入的相对位置信息能够体现在attention里。落实下来就是通过构造正余弦的位置编码，使得相对位置信息能够体现在attention的二阶展开项中，并且加上一些特殊的设计使得该项能够随着距离而衰减。</b></p><h3><b>RoPE位置编码</b></h3><p data-pid=\"JxbHkhck\">上面我们已经说了，如果<b>使用“相加式”的位置编码</b>，通过泰勒展开加上一些假设（位置编码是相对小量，二阶交叉项中H是单位阵）那么可以得到PE的一个解是 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D%3De%5E%7Bim%5Ctheta%7D\" alt=\"p_{m}=e^{im\\theta}\" eeimg=\"1\"/> 。由于我们是用泰勒展开做的，所以没有对代表attention的函数 <img src=\"https://www.zhihu.com/equation?tex=f\" alt=\"f\" eeimg=\"1\"/> 做任何限制，也就是说无论是什么 <img src=\"https://www.zhihu.com/equation?tex=f\" alt=\"f\" eeimg=\"1\"/>（哪怕它不是QKV的形式），这个编码都是可用的。</p><p data-pid=\"7Dec5vo9\">但是这似乎不是最优的假设，因为我们已经明确知道 <img src=\"https://www.zhihu.com/equation?tex=f\" alt=\"f\" eeimg=\"1\"/> 的形式，而且也不复杂，不用起来有些说不过去；另一方面“相加式”和泰勒展开的假设又有些太强了。</p><p data-pid=\"Gw--Zvt3\">那么如果我们<b>只考虑transformer QK情况下的</b> <img src=\"https://www.zhihu.com/equation?tex=f\" alt=\"f\" eeimg=\"1\"/> ，换句话说 <img src=\"https://www.zhihu.com/equation?tex=f\" alt=\"f\" eeimg=\"1\"/> 就是向量内积的形式；<b>而对输入 <img src=\"https://www.zhihu.com/equation?tex=x_%7Bm%7D\" alt=\"x_{m}\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D\" alt=\"p_{m}\" eeimg=\"1\"/> 的关系不做要求</b>（不预设它是相加还是相乘等形式，而是等之后求解），情况是不是会有所不同呢？</p><p data-pid=\"ZHczA3tw\">这里我们干脆把加了位置编码的向量记为 <img src=\"https://www.zhihu.com/equation?tex=h%28%5Ctextbf%7Bq%7D%2Cm%29\" alt=\"h(\\textbf{q},m)\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=h%28%5Ctextbf%7Bk%7D%2Cn%29\" alt=\"h(\\textbf{k},n)\" eeimg=\"1\"/> ，其中<b>加粗的代表输入向量，而没加粗的m和n代表位置</b>。（注意，如果你沿用之前的 <img src=\"https://www.zhihu.com/equation?tex=x_%7Bm%7D\" alt=\"x_{m}\" eeimg=\"1\"/> 作为 <img src=\"https://www.zhihu.com/equation?tex=%5Ctextbf%7Bq%7D\" alt=\"\\textbf{q}\" eeimg=\"1\"/> 的记号可能会有一些小问题。因为 <img src=\"https://www.zhihu.com/equation?tex=x_%7Bm%7D\" alt=\"x_{m}\" eeimg=\"1\"/> 实际上是任意的，跟m没有直接联系，但是这个记号跟m没有完全解耦，导致有些东西推不出来）</p><p data-pid=\"__uKWyoD\">我们不妨回头看看前面的目标： <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7Dp_%7Bn%7D%5E%7B%2A%7D%3Dg%28m-n%29\" alt=\"p_{m}p_{n}^{*}=g(m-n)\" eeimg=\"1\"/> </p><p data-pid=\"9BEDuQi_\">巧的是，现在等式左边仍然是向量内积的形式（QK），只不过此时的内积不只是跟位置编码 <img src=\"https://www.zhihu.com/equation?tex=p_%7Bm%7D\" alt=\"p_{m}\" eeimg=\"1\"/> 有关，而是跟输入的向量也有关。因此我们需要换成上面的写法，即左边写成 <img src=\"https://www.zhihu.com/equation?tex=h%28%5Ctextbf%7Bq%7D%2Cm%29h%5E%7B%2A%7D%28%5Ctextbf%7Bk%7D%2Cn%29\" alt=\"h(\\textbf{q},m)h^{*}(\\textbf{k},n)\" eeimg=\"1\"/> ；至于等式右边大概率也要跟qk有关系了，所以我们这里稍微改改就变成了新的目标：</p><p data-pid=\"qHu859hU\"><img src=\"https://www.zhihu.com/equation?tex=h%28%5Ctextbf%7Bq%7D%2Cm%29h%5E%7B%2A%7D%28%5Ctextbf%7Bk%7D%2Cn%29%3Dg%28%5Ctextbf%7Bq%7D%2C+%5Ctextbf%7Bk%7D%2C+m-n%29\" alt=\"h(\\textbf{q},m)h^{*}(\\textbf{k},n)=g(\\textbf{q}, \\textbf{k}, m-n)\" eeimg=\"1\"/> </p><p data-pid=\"FazOxExU\">我们继续接着前面的推理逻辑依葫芦画瓢，同样设为二维向量，同样写成复数的指数形式，同样令模长和辐角都相等，于是就有：</p><p data-pid=\"uvzBjA01\"><img src=\"https://www.zhihu.com/equation?tex=R_%7Bh%7D%28%5Ctextbf%7Bq%7D%2Cm%29R_%7Bh%7D%28%5Ctextbf%7Bk%7D%2Cn%29%3DR_%7Bg%7D%28%5Ctextbf%7Bq%7D%2C%5Ctextbf%7Bk%7D%2C+m-n%29\" alt=\"R_{h}(\\textbf{q},m)R_{h}(\\textbf{k},n)=R_{g}(\\textbf{q},\\textbf{k}, m-n)\" eeimg=\"1\"/> </p><p data-pid=\"I_QNaZ94\"><img src=\"https://www.zhihu.com/equation?tex=%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%2Cm%29-%5CTheta_%7Bh%7D%28%5Ctextbf%7Bk%7D%2Cn%29%3D%5CTheta_%7Bg%7D%28%5Ctextbf%7Bq%7D%2C%5Ctextbf%7Bk%7D%2C+m-n%29\" alt=\"\\Theta_{h}(\\textbf{q},m)-\\Theta_{h}(\\textbf{k},n)=\\Theta_{g}(\\textbf{q},\\textbf{k}, m-n)\" eeimg=\"1\"/> </p><p data-pid=\"HaucI5h1\">先看第一个式子。我们令m=n立即有 <img src=\"https://www.zhihu.com/equation?tex=R_%7Bh%7D%28%5Ctextbf%7Bq%7D%2Cm%29R_%7Bh%7D%28%5Ctextbf%7Bk%7D%2Cm%29%3DR_%7Bg%7D%28%5Ctextbf%7Bq%7D%2C%5Ctextbf%7Bk%7D%2C+0%29\" alt=\"R_{h}(\\textbf{q},m)R_{h}(\\textbf{k},m)=R_{g}(\\textbf{q},\\textbf{k}, 0)\" eeimg=\"1\"/> ，也就是说 <img src=\"https://www.zhihu.com/equation?tex=R_%7Bh%7D%28%5Ctextbf%7Bq%7D%2Cm%29\" alt=\"R_{h}(\\textbf{q},m)\" eeimg=\"1\"/> 和m无关。简单起见，我们直接令 <img src=\"https://www.zhihu.com/equation?tex=R_%7Bh%7D%28%5Ctextbf%7Bq%7D%2Cm%29%3D%7C%7C+%5Ctextbf%7Bq%7D+%7C%7C\" alt=\"R_{h}(\\textbf{q},m)=|| \\textbf{q} ||\" eeimg=\"1\"/> 就结束战斗了。</p><p data-pid=\"GZ0Q1gWK\">对第二个式子，同样令m=n，有 <img src=\"https://www.zhihu.com/equation?tex=%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%2Cm%29-%5CTheta_%7Bh%7D%28%5Ctextbf%7Bk%7D%2Cm%29%3D%5CTheta_%7Bg%7D%28%5Ctextbf%7Bq%7D%2C%5Ctextbf%7Bk%7D%2C+0%29%3D%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%2C0%29-%5CTheta_%7Bh%7D%28%5Ctextbf%7Bk%7D%2C0%29\" alt=\"\\Theta_{h}(\\textbf{q},m)-\\Theta_{h}(\\textbf{k},m)=\\Theta_{g}(\\textbf{q},\\textbf{k}, 0)=\\Theta_{h}(\\textbf{q},0)-\\Theta_{h}(\\textbf{k},0)\" eeimg=\"1\"/> 。</p><p data-pid=\"rEbHOK9R\">于是我们可以知道 <img src=\"https://www.zhihu.com/equation?tex=%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%2Cm%29\" alt=\"\\Theta_{h}(\\textbf{q},m)\" eeimg=\"1\"/> 和qk似乎也没什么关系，因为简单移一下项就可以得到。</p><p data-pid=\"qvW4Jz9A\"><img src=\"https://www.zhihu.com/equation?tex=%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%2Cm%29-%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%2C0%29%3D%5CTheta_%7Bh%7D%28%5Ctextbf%7Bk%7D%2Cm%29-%5CTheta_%7Bh%7D%28%5Ctextbf%7Bk%7D%2C0%29\" alt=\"\\Theta_{h}(\\textbf{q},m)-\\Theta_{h}(\\textbf{q},0)=\\Theta_{h}(\\textbf{k},m)-\\Theta_{h}(\\textbf{k},0)\" eeimg=\"1\"/> </p><p data-pid=\"TXxcFQ_s\">可以看出它只跟m有关，通过简单的求解（比如令n=m-1）就可以知道，它又是一个等差数列，我们可以把它写成</p><p data-pid=\"YVQRS8U_\"><img src=\"https://www.zhihu.com/equation?tex=%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%2Cm%29%3D%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%29%2Bm%5Ctheta\" alt=\"\\Theta_{h}(\\textbf{q},m)=\\Theta_{h}(\\textbf{q})+m\\theta\" eeimg=\"1\"/> </p><p data-pid=\"sakTyuZA\">于是把他俩都带入回原来的复数形式，我们有：</p><p data-pid=\"9IxhoTbM\"><img src=\"https://www.zhihu.com/equation?tex=h%28%5Ctextbf%7Bq%7D%2Cm%29%3D%7C%7C%5Ctextbf%7Bq%7D%7C%7Ce%5E%7Bi%28%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%29%2Bm%5Ctheta%29%7D\" alt=\"h(\\textbf{q},m)=||\\textbf{q}||e^{i(\\Theta_{h}(\\textbf{q})+m\\theta)}\" eeimg=\"1\"/> </p><p data-pid=\"HczfAW23\">注意到上面的 <img src=\"https://www.zhihu.com/equation?tex=%5CTheta_%7Bh%7D%28%5Ctextbf%7Bq%7D%29\" alt=\"\\Theta_{h}(\\textbf{q})\" eeimg=\"1\"/> 其实就是向量q的辐角，可以直接拿下来变成：</p><p data-pid=\"QRnzDQRH\"><img src=\"https://www.zhihu.com/equation?tex=h%28%5Ctextbf%7Bq%7D%2Cm%29%3D%5Ctextbf%7Bq%7De%5E%7Bim%5Ctheta%7D\" alt=\"h(\\textbf{q},m)=\\textbf{q}e^{im\\theta}\" eeimg=\"1\"/> </p><p data-pid=\"9kr03aJZ\"><b>这个东西等价于把原始向量q旋转一个 <img src=\"https://www.zhihu.com/equation?tex=m%5Ctheta\" alt=\"m\\theta\" eeimg=\"1\"/> 角，这也是旋转位置编码（RoPE）名字的由来</b>。</p><p data-pid=\"olha-SyW\">之后的事情就不用多说了，直接参照sinusoidal的后续流程，每两维添加一次位置编码即可，只不过sinusoidal是直接加在输入向量上，而RoPE是转了一下。 <img src=\"https://www.zhihu.com/equation?tex=%5Ctheta_%7Bi%7D\" alt=\"\\theta_{i}\" eeimg=\"1\"/> 的取值也可以取 <img src=\"https://www.zhihu.com/equation?tex=10000%5E%7B2i%2Fd%7D\" alt=\"10000^{2i/d}\" eeimg=\"1\"/> ，同样可以实现很好的远程衰减特性。</p><p data-pid=\"osqIIml2\">我们来对RoPE做个总结。</p><p data-pid=\"nRgp0x_Q\">不同于Sinusoidal的推导方式，<b>我们专门针对attention的QK内积形式（而不是任意函数），并且放宽了对位置编码和输入向量的关系约束（不要求是相加），希望直接求解一个位置编码方式能够反映相对位置。最后推导的结果显示旋转位置编码可以满足这一条件。</b>所以从某种角度上说，RoPE比Sinusoidal好的原因主要是它更适配attention的计算方式，并且“强假设”少了很多。</p><h3>NTK(Neural Tangent Kernel)</h3><p data-pid=\"mh-XlYEx\">这个名字有点抽象，不过也没必要去抠那么细节，篇幅所限，这里就简单说一下。</p><p data-pid=\"oz8qKEv8\">尽管RoPE相对而言有不少优势，不过它依然没解决“测试时文本长度大于训练时文本长度”的情况，比方说你训练时最大长度是1k，测试时是2k长度的文本。</p><p data-pid=\"cdW4pMTk\">很显然如果直接把现有编码方法外推到2k长度会出现模型没见过的输入，这时候结果不见得好；另一个想法是内插，就是把2k的位置编码对应到1k，相当于整体放缩一下，但是这样也有个问题，就是如果你放缩太多了会导致编码过分拥挤，相邻两个位置的差异会变得很小。</p><p data-pid=\"eVF8E_Gl\">于是你只需要稍微动一下脑筋就可以想到……<b>对于长距离的两个字，他们之间的注意力可能没那么重要，毕竟都到1k这个距离了，大部分时候也很难有什么关联；对于很近的两个字，我们最好能忠实地使用训练时的位置编码，这样能够让最重要的attention不受影响。</b></p><p data-pid=\"1NhOR3mJ\">那么一个直觉的想法就出来了：<b>“非线性缩放”，换句话说叫高频外推，低频内插。</b></p><p data-pid=\"x-aRY8Bl\">至于为什么叫高频和低频，可以回到RoPE的编码方式：</p><p data-pid=\"vkBDVOSr\"><img src=\"https://www.zhihu.com/equation?tex=h%28%5Ctextbf%7Bq%7D%2Cm%29%3D%5Ctextbf%7Bq%7De%5E%7Bim%5Ctheta%7D\" alt=\"h(\\textbf{q},m)=\\textbf{q}e^{im\\theta}\" eeimg=\"1\"/> ，其中 <img src=\"https://www.zhihu.com/equation?tex=%5Ctheta%3D10000%5E%7B-2i%2Fd%7D\" alt=\"\\theta=10000^{-2i/d}\" eeimg=\"1\"/> 。可以看到频率主要由 <img src=\"https://www.zhihu.com/equation?tex=i\" alt=\"i\" eeimg=\"1\"/> 决定，由于前面有个负号，所以越大的 <img src=\"https://www.zhihu.com/equation?tex=i\" alt=\"i\" eeimg=\"1\"/> 代表频率越低（注意区分这里的 <img src=\"https://www.zhihu.com/equation?tex=i\" alt=\"i\" eeimg=\"1\"/> 和代表复数的 <img src=\"https://www.zhihu.com/equation?tex=i\" alt=\"i\" eeimg=\"1\"/> ），也就是说前面的维度“转”得快一些，后面的“转”得慢。从直觉上说（其实从前面的推导过程也很容易看出来），<b>转得快的高频分量显然会对近距离更敏感</b>，因为只要位置m稍有变化， <img src=\"https://www.zhihu.com/equation?tex=m%5Ctheta\" alt=\"m\\theta\" eeimg=\"1\"/> 角度就会转很多；低频分量则是对远距离较为敏感。</p><p data-pid=\"zqmGwdy8\">所以一种可以采用的形式是，<b>让转的最快的 <img src=\"https://www.zhihu.com/equation?tex=%5Ctheta\" alt=\"\\theta\" eeimg=\"1\"/> 转速保持不变，对于转的最慢的 <img src=\"https://www.zhihu.com/equation?tex=%5Ctheta\" alt=\"\\theta\" eeimg=\"1\"/> 转速变为原来的1/8，至于中间的维度就按照比例（ <img src=\"https://www.zhihu.com/equation?tex=8%5E%7B-%5Cfrac%7B2i%7D%7Bd-2%7D%7D\" alt=\"8^{-\\frac{2i}{d-2}}\" eeimg=\"1\"/> ）来就行</b>。</p><p data-pid=\"PdMZGaQf\">或者我们用一个形象一点的总结，你对于近处的物体可能会对一两米的距离锱铢必较，但是对于远处的物体，它到底距离你50米还是100米并其实不那么重要，大多数时候你只要知道有这么个东西存在就差不多了。</p><p data-pid=\"fcSZqEuz\"><b>通过NTK，我们可以在测试时接受超过训练时的文本长度</b>。尽管性能相比直接在长文本上训练有差距，但是也比不做NTK要强不少。</p><p class=\"ztext-empty-paragraph\"><br/></p><h2>Act / Norm / Bias</h2><h3>SwiGLU</h3><p data-pid=\"ScQM8n6p\">需要重点提示CV选手们，这里用的激活跟CV里的激活方法稍微有点区别。</p><p data-pid=\"iuLVXQ-Z\">常见的激活函数（ReLU，SiLU，Swish）之类的其实拿着一个输入就行了，但是在现在主流的大模型里稍复杂一些，因为多了一个叫GLU的东西（gated linear unit），简单起见我们直接看代码。</p><div class=\"highlight\"><pre><code class=\"language-python3\"><span class=\"k\">class</span> <span class=\"nc\">FeedForward</span><span class=\"p\">(</span><span class=\"n\">nn</span><span class=\"o\">.</span><span class=\"n\">Module</span><span class=\"p\">):</span>\n    <span class=\"k\">def</span> <span class=\"nf\">__init__</span><span class=\"p\">(</span><span class=\"bp\">self</span><span class=\"p\">,</span> <span class=\"n\">dim</span><span class=\"p\">:</span> <span class=\"nb\">int</span><span class=\"p\">,</span> <span class=\"n\">hidden_dim</span><span class=\"p\">:</span> <span class=\"nb\">int</span><span class=\"p\">,</span> <span class=\"n\">multiple_of</span><span class=\"p\">:</span> <span class=\"nb\">int</span><span class=\"p\">,</span> <span class=\"n\">dropout</span><span class=\"p\">:</span> <span class=\"nb\">float</span><span class=\"p\">):</span>\n        <span class=\"nb\">super</span><span class=\"p\">()</span><span class=\"o\">.</span><span class=\"fm\">__init__</span><span class=\"p\">()</span>\n        <span class=\"n\">hidden_dim</span> <span class=\"o\">=</span> <span class=\"n\">multiple_of</span> <span class=\"o\">*</span> <span class=\"p\">((</span><span class=\"mi\">2</span> <span class=\"o\">*</span> <span class=\"n\">hidden_dim</span> <span class=\"o\">//</span> <span class=\"mi\">3</span> <span class=\"o\">+</span> <span class=\"n\">multiple_of</span> <span class=\"o\">-</span> <span class=\"mi\">1</span><span class=\"p\">)</span> <span class=\"o\">//</span> <span class=\"n\">multiple_of</span><span class=\"p\">)</span>\n        <span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">w1</span> <span class=\"o\">=</span> <span class=\"n\">nn</span><span class=\"o\">.</span><span class=\"n\">Linear</span><span class=\"p\">(</span><span class=\"n\">dim</span><span class=\"p\">,</span> <span class=\"n\">hidden_dim</span><span class=\"p\">)</span>\n        <span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">w2</span> <span class=\"o\">=</span> <span class=\"n\">nn</span><span class=\"o\">.</span><span class=\"n\">Linear</span><span class=\"p\">(</span><span class=\"n\">hidden_dim</span><span class=\"p\">,</span> <span class=\"n\">dim</span><span class=\"p\">)</span>\n        <span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">w3</span> <span class=\"o\">=</span> <span class=\"n\">nn</span><span class=\"o\">.</span><span class=\"n\">Linear</span><span class=\"p\">(</span><span class=\"n\">dim</span><span class=\"p\">,</span> <span class=\"n\">hidden_dim</span><span class=\"p\">)</span>\n        <span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">dropout</span> <span class=\"o\">=</span> <span class=\"n\">nn</span><span class=\"o\">.</span><span class=\"n\">Dropout</span><span class=\"p\">(</span><span class=\"n\">dropout</span><span class=\"p\">)</span>\n\n    <span class=\"k\">def</span> <span class=\"nf\">forward</span><span class=\"p\">(</span><span class=\"bp\">self</span><span class=\"p\">,</span> <span class=\"n\">x</span><span class=\"p\">:</span> <span class=\"n\">torch</span><span class=\"o\">.</span><span class=\"n\">Tensor</span><span class=\"p\">)</span> <span class=\"o\">-&gt;</span> <span class=\"n\">torch</span><span class=\"o\">.</span><span class=\"n\">Tensor</span><span class=\"p\">:</span>\n        <span class=\"k\">return</span> <span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">dropout</span><span class=\"p\">(</span><span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">w2</span><span class=\"p\">(</span><span class=\"n\">F</span><span class=\"o\">.</span><span class=\"n\">silu</span><span class=\"p\">(</span><span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">w1</span><span class=\"p\">(</span><span class=\"n\">x</span><span class=\"p\">))</span> <span class=\"o\">*</span> <span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">w3</span><span class=\"p\">(</span><span class=\"n\">x</span><span class=\"p\">)))</span></code></pre></div><p data-pid=\"kQdrlY26\">对正常的CV选手来说这样才是正常的形式：</p><div class=\"highlight\"><pre><code class=\"language-text\">self.w2(F.silu(self.w1(x))</code></pre></div><p data-pid=\"fA4QfEjD\">也就是说不知道从哪里多出来一个w3。</p><p data-pid=\"ZZPomMPe\">GLU认为silu（或者sigmoid）作为激活函数本质上是“决定保留多少信息”，因此干脆只是把激活函数当作一个门控机制，再额外添加一支作为主要的信息通路，<b>相当于变成了（信息支+门控支）两支通路</b>。因为多加了一个w3导致网络参数量会增加，所以需要对维度数做一些削减以保持参数量与原来一致。</p><h3>RMS Norm</h3><p data-pid=\"w-0aYv4r\">众所周知norm就是归一化均值和方差。BN/LN/GN没有本质上的区别，只是拿来norm的维度不一样而已。</p><p data-pid=\"B6llAj28\">RMS（Root Mean Square）norm认为归一化均值没什么用，主要是方差比较重要，因此干脆把“减均值”这个操作给去了，直接计算RMS然后做norm就行了。</p><h3>Bias</h3><p data-pid=\"69XAoZOZ\">专门提一句，就是千问说他们在大部分层把bias删了，但是在<b>QKV层里加bias反而对模型外推能力是有帮助的</b>，大致是因为bias这项加入之后会有一项随着相对位置增加而递减，从而变相增强了局部注意力。</p><p data-pid=\"Q7fIEFhR\">这里就不细说了，有兴趣的可以看这篇：<a href=\"https://link.zhihu.com/?target=https%3A//spaces.ac.cn/archives/9577\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Bias项的神奇作用：RoPE + Bias = 更好的长度外推性</a>。</p><p class=\"ztext-empty-paragraph\"><br/></p><h2>Training</h2><p data-pid=\"tw61a3IR\">先说天工的训练。</p><p data-pid=\"O2lMcy07\">天工的表示一开始只打算用2T token去训，但是训完2T之后发现还有提升空间，于是又补了1.2T的token继续训。可能是一开始没做好训练规划？或者是一边训一边做的数据集，总之能出现这种失误还挺神奇的……</p><p data-pid=\"i4tdoAmq\">然后文章里表示后面加的这些数据跟2T数据的分布有一些小区别，所以很小心谨慎地tune了几个学习率，最后总之是定下来了，然后把后加的1T多的数据又喂进去了。</p><p data-pid=\"mf6Kfcq4\">接下来是二阶段的pretrain。二阶段的pretrain主要是为了做STEM题（就是理工科那些）。训练方法就是把专门用于STEM的Skypile-STEM和整个数据集Skypile-Main两个掺在一起训（比例为2:8，总共130B左右token），并且实际操作的时候还有一些特别的trick：<b>在训练的过程中逐渐添加STEM数据集所占的比例，</b>比如说一开始只占10%，到训练结束的时候可能占40%。</p><p data-pid=\"Z7-OH-3m\">然后再来简单提一下千问。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-a37ec4e337529a0cc42204f78a0f2d9d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"525\" data-rawheight=\"91\" data-original-token=\"v2-a37ec4e337529a0cc42204f78a0f2d9d\" class=\"origin_image zh-lightbox-thumb\" width=\"525\" data-original=\"https://pic2.zhimg.com/v2-a37ec4e337529a0cc42204f78a0f2d9d_r.jpg\"/></figure><p data-pid=\"jw8hkeR6\">整体的数据集规模差不多，参数设置基本都和LLAMA2保持一致，而天工会略有区别：</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-1ca508f17324bd1aefd8cb45b1959448_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"851\" data-rawheight=\"564\" data-original-token=\"v2-1ca508f17324bd1aefd8cb45b1959448\" class=\"origin_image zh-lightbox-thumb\" width=\"851\" data-original=\"https://pic1.zhimg.com/v2-1ca508f17324bd1aefd8cb45b1959448_r.jpg\"/></figure><p data-pid=\"wy0wwEHI\">千问的训练就正常多了，他也没特地强调训练时有什么花活儿。需要强调的是，训练时采用的都是BF16（和FP32能够表示同样的range，但是精度缩减了）。</p><p class=\"ztext-empty-paragraph\"><br/></p><h2>Flash Attention</h2><p data-pid=\"NGifb95n\">主要功能是给attention加速。</p><p data-pid=\"t0lHHV70\">由于具体内容实在过于复杂，完全搞懂需要大量时间，因此我对这部分做了大量删减，只留下一些定性的结论给大家。我的建议是有兴趣的（尤其是做cuda加速的）仔细阅读这篇：<a href=\"https://www.zhihu.com/question/611236756/answer/3310819022\" class=\"internal\" target=\"_blank\">FlashAttention 的速度优化原理是怎样的？</a>，里面讲得非常清楚，我就不画蛇添足了。</p><h3>速度瓶颈</h3><p data-pid=\"ArtRnYvR\">传统attention流程如下：</p><p data-pid=\"qm0HD4DT\"><b>从显存中取QK计算-&gt;将结果S写回显存-&gt;从显存读S计算softmax-&gt;将结果P写回显存-&gt;从显存读取P和V进行计算-&gt;将结果O写回显存。</b></p><p data-pid=\"1bQXWsba\">由此可见整个attention过程中多次进行显存读写，而众所周知大模型的速度瓶颈其实很多时候并不在于计算速度，而在于显存读写，因此如何去减少显存读写次数是至关重要的。另一个众所周知的事情是，<b>离CPU/GPU越近，缓存速度就越快，在GPU上的速度是L1&gt;L2&gt;显存，但是容量上L1&lt;L2&lt;显存</b>。</p><p data-pid=\"4N4p30V8\">因此，为了减少显存的读写，一个好的办法是<b>进行分块计算。</b>如果我的数据量足够小，我就能<b>全塞到L1缓存上</b>（比如说A100的L1只有192KB）进行计算了，这样<b>由于L1的读写速度远高于显存，就实现了加速</b>。</p><p data-pid=\"AMqZV3-I\"><i>PS：此处可能涉及一些硬件相关的知识，建议自行查阅。比如GPU的基本架构以及SRAM/DRAM，还有roofline图等等。由于本人之前已经略有了解，在此就不做说明了。</i></p><p data-pid=\"QUEtfSmi\"><i>PPS：虽然我上面说的是显存（FlashAttention里用的也是HBM），但是也有部分文章里计算时采用的是L2缓存带宽而不是显存带宽。这个……我只能说还是得上手实操，这里我们就先当它是显存。</i></p><h3>分块attention</h3><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-0f1fa95d351496dd7ed8e5625da64f22_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"863\" data-rawheight=\"421\" data-original-token=\"v2-0f1fa95d351496dd7ed8e5625da64f22\" class=\"origin_image zh-lightbox-thumb\" width=\"863\" data-original=\"https://pic3.zhimg.com/v2-0f1fa95d351496dd7ed8e5625da64f22_r.jpg\"/></figure><p data-pid=\"F2UdrI5x\">以上是分块attention示意图，我们以N为句子长度，d为特征维数。</p><p data-pid=\"MbHJbcBF\">正常的attention是直接计算 <img src=\"https://www.zhihu.com/equation?tex=QK%5E%7BT%7D\" alt=\"QK^{T}\" eeimg=\"1\"/> ，但是这里改成分块进行，比如图上是把Q分了3块，Q分了两块。</p><p data-pid=\"mKAhItH0\">我们注意<b>分块和传统attention实际上并不一致</b>。</p><p data-pid=\"nBhl6J22\">首先是这个O的内容和正常的attention有区别。正常情况下在P×V这一步应该是 <img src=\"https://www.zhihu.com/equation?tex=%28B_%7Br%7D%2CN%29%5Ctimes%28N%2Cd%29\" alt=\"(B_{r},N)\\times(N,d)\" eeimg=\"1\"/> 得到 <img src=\"https://www.zhihu.com/equation?tex=%28B_%7Br%7D%2Cd%29\" alt=\"(B_{r},d)\" eeimg=\"1\"/> 的一个矩阵。<b>现在虽然形状还是 <img src=\"https://www.zhihu.com/equation?tex=%28B_%7Br%7D%2Cd%29\" alt=\"(B_{r},d)\" eeimg=\"1\"/> ，但是由于KV都是分了块的，所以在算结果的时候就有一部分没参与运算，只计算了前</b> <img src=\"https://www.zhihu.com/equation?tex=B_%7Bc%7D\" alt=\"B_{c}\" eeimg=\"1\"/> <b>个数的结果，相当于结果是错的</b>。</p><p data-pid=\"duwejpnd\">但是问题也不止出在这，因为你往前推一步就会发现，前面的softmax也不对。<b>正常的softmax是一整行做softmax，但是这边只有一部分。</b>但是相比后面的output错的没那么离谱，因为这儿只不过切成了两块罢了，做softmax之前是每个数还是正确的。</p><h3>前向计算</h3><p data-pid=\"qRCQAYxH\">我们直接来看算法流程图。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-70eee1fbe8e1e9dc3544a5405b94e1c7_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1899\" data-rawheight=\"1090\" data-original-token=\"v2-70eee1fbe8e1e9dc3544a5405b94e1c7\" class=\"origin_image zh-lightbox-thumb\" width=\"1899\" data-original=\"https://picx.zhimg.com/v2-70eee1fbe8e1e9dc3544a5405b94e1c7_r.jpg\"/></figure><p data-pid=\"0Khsq0g-\">关注5-15行这个循环即可。这个循环的意思大致是，on chip地遍历每一个分块（只读写L1而不是显存，因此速度很快），然后你需要存下来的东西是<b>当前状态下的O矩阵，以及当前状态下的 <img src=\"https://www.zhihu.com/equation?tex=l\" alt=\"l\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=m\" alt=\"m\" eeimg=\"1\"/></b> （即每行的和以及每行的最大值），你就可以计算出跟传统attention一模一样的结果。</p><p data-pid=\"5fM0H1Qj\">至于为什么这两种计算方法是一样的，由于太复杂了这里就不做赘述了，建议去看本章开头推荐的文章。不过如果你喜欢一些定性的描述，大概可以这么理解：我们前面说了，最大的问题是P×V=O这一步算出来的O只有一部分（本来应该拿N列进行计算，现在只拿了 <img src=\"https://www.zhihu.com/equation?tex=B_%7Bc%7D\" alt=\"B_{c}\" eeimg=\"1\"/> 列），<b>由于我们的分块其实是线性可加的，因此你可以迭代计算O的数值然后原地加到上一轮的数值上即可，只不过每次更新的时候需要考虑softmax带来的变化，最终这样更新出来的O和标准attention的O数学上严格等价</b>。</p><p data-pid=\"_TIECtn_\">总之，相比原始attention对HBM的大规模读写（QKSPV），Flash Attention有很大区别，因为它把内层循环跑完只需要在HBM上写<b>一个完整的O矩阵，加上多次l和m（纯数字）。</b>相当于不用写SP这些中间矩阵，但是需要循环地写很多次O。</p><p data-pid=\"dgSRM5Gr\">考虑复杂度，l和m的复杂度只有O(N)，而Q/K/O的复杂度是O(Nd)，S和P的复杂度是O(N^2)，因此传统attention的复杂度大概是 <img src=\"https://www.zhihu.com/equation?tex=O%28Nd+%2B+N%5E2%29\" alt=\"O(Nd + N^2)\" eeimg=\"1\"/> ，FlashAttention的复杂度大概是 <img src=\"https://www.zhihu.com/equation?tex=O%28T_%7Bc%7DNd%29\" alt=\"O(T_{c}Nd)\" eeimg=\"1\"/> ，其中 <img src=\"https://www.zhihu.com/equation?tex=T_%7Bc%7D\" alt=\"T_{c}\" eeimg=\"1\"/> 是上面算法流程里的外层循环次数。在正常情况下 <img src=\"https://www.zhihu.com/equation?tex=T_%7Bc%7Dd\" alt=\"T_{c}d\" eeimg=\"1\"/> 并不会很大，相比O(N^2)还是省了不少复杂度的。</p><h3>反向传播</h3><p data-pid=\"DGRXL-BU\">因为分块attention没有存储S和P两个矩阵，所以反向传播的时候需要先on chip地把SP算出来（当然不是整个算出来，而是只算那一小块），之后的反向传播过程和正常的attention一致，只不过是分块的。</p><p data-pid=\"ni160WMf\">至于计算量其实稍微脑测一下也大致有数，尽管迭代了多次，但是总计算量的量级应该不会有太大区别。我之前觉得Flash Attention的计算量好像变大了，因为直觉上多次更新softmax会带来计算量的增加，但是跟O的计算比起来似乎不是大头。</p><p data-pid=\"c2ZLQdy-\"><b>我们总结一下Flash Attention。</b></p><p data-pid=\"k-1zt5DP\"><b>正常情况下的attention涉及到显存的多次读写导致速度较慢。Flash Attention通过将矩阵分块，可以把一些中间矩阵（如S和P）的计算全都放在L1缓存上，从而避免多次显存读写实现了加速。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><h2><b>人类对齐</b></h2><p data-pid=\"QT1Mnz1f\">天工在report里似乎没怎么提这个事，千问说的比较多。人类对齐的主要作用是让模型“说话”的风格符合人类习惯，一般经过人类对齐的模型主要是用来当聊天机器人的，所以你看各个团队发布的模型一般都会有base模型和chat模型。</p><p data-pid=\"6aVPotJ_\">众所周知，对齐最常用的也就两种：Supervised Finetune（SFT）和Reinforcement Learning with Human Feedback（RLHF）。</p><h3>一些牢骚</h3><p data-pid=\"9ITwiN2o\">我们先说一些天工在论文里发的牢骚，虽然跟human alignment无关，但是因为涉及到SFT所以还是放在这里。</p><p data-pid=\"T0Gk9UF4\">天工提到了一些很有意思的事情：大家做LLM的传统思路是无监督pretrain+有监督finetune，但是实际上你可以用一些投机取巧的办法，尤其是在GPT-4这种东西出来之后<b>获得有监督和高质量数据变得非常容易</b>。</p><p data-pid=\"Wxn0GJjr\">6月份有个很有名的论文叫textbooks are all you need，用了7B token做pretrain加200M做finetune就训了一个非常好的coding模型出来，而且这个模型还很小（1.3B）。但是如果你从头看到这里，想到scaling law就知道事出反常必有妖。实际上在这篇论文里是先使用GPT-4筛选出一些高质量的code来训一个“判断代码质量高低”的分类器，然后那些pretrain和finetune用的“教科书级的代码”都是用GPT-3.5生成的。</p><p data-pid=\"c62CsChY\">也就是说，无论是pretrain还是finetune的数据都集中在coding这个领域，并且数据质量极高，这使得模型在coding方面有极度出众的能力，但是这其实稍稍违背了做大模型的初衷。</p><p data-pid=\"DYE9GiyA\">如果你只是想在某个特定任务上刷榜，你甚至可以在pretrain的时候就选in-domain的数据，比如说做coding就直接全用代码数据就完了，没必要像通用LLM一样用一大堆无关数据来做pretrain再用领域数据做finetune。或者反过来说，<b>尽管大量无监督数据带来了LLM的通用能力，但是如果你只需要在某些特定task上的优秀表现，全程使用高质量的in-domain数据+小模型可能反而比大模型还强</b>。</p><p data-pid=\"YJLYWX-H\">但是你仔细一想，这不是又退化成了传统deep learning了吗……</p><h3>SFT</h3><p data-pid=\"20hzG7oc\">没什么特别好说的，千问在论文里着重强调了<b>内容安全性</b>，也就是对暴力/偏见/色情之类的话题做了很多标注数据，尽量减少模型在聊天的时候口出狂言。</p><p data-pid=\"N9wh2hkq\">此外就是SFT时的格式参照了OpenAI的ChatML，也就是指定角色（system/user/assistant等）的json格式，这样使得模型可以区分消息来源，从而让模型能够分析更复杂的会话数据。</p><h3>RLHF</h3><p data-pid=\"yn3fX379\">对强化学习以及PPO不了解的同学建议阅读我很久以前写的文章：<a href=\"https://zhuanlan.zhihu.com/p/341561826\" class=\"internal\" target=\"_blank\">密排六方橘子：强化学习：PPO(Proximal Policy Optimization)在谈恋爱中的应用</a>，私以为写得还是很清楚的，但是你如果懒得看，我们这里也可以用几句话简单描述一下强化学习（这里指PPO）的基本流程：</p><p data-pid=\"OlQwxrfh\">强化学习的基本思想是“<b>模型在和环境的互动中学到知识</b>”，因此你的模型主要功能是“做出动作”，或者叫它<b>actor</b>。在我们的场景下，LLM就是这个actor，LLM的输出的内容就是“动作“，然后我们对输出的评价其实就是“环境”，因为我们需要告诉actor它的动作是好是坏。<b>此时LLM的参数更新方法和传统的梯度更新一样</b>，也就是你把reference当作gt，然后根据每个act的输出概率做梯度下降即可。</p><p data-pid=\"BY9JB0Jz\">上述过程在传统RL中叫做Policy Gradient（PG，策略梯度），在此基础上做一些优化之后就变成了PPO。但是这种基于梯度下降的方法也有缺陷，比如说由于有时决策序列很长，因为PG是基于采样的（每次采样的是一个决策序列，搜索空间是指数增长的），所以这种情况下<b>训练稳定性非常差</b>。<b>因此我们可以再添加一个模型来“估计当前状态是好是坏”</b>（有兴趣的可以查一下“优势函数”），这两个模型一起训练，这样actor的训练就会稳定很多，<b>我们管它叫actor-critic方法</b>。</p><p data-pid=\"6QM6KUNj\"><i>PS：我觉得应该还挺好理解的？看一遍可能有点懵，多看两遍应该就很清晰了。</i></p><p data-pid=\"ByVZT8i2\">在RLHF中有四个模型，<b>分别是actor，reference，reward和critic。</b>这四个模型的作用大致如下：</p><p data-pid=\"MD6hOI_R\">actor：负责输出结果</p><p data-pid=\"aawkg-ef\">critic：辅助actor估计当前状态</p><p data-pid=\"1lu8szHa\">reference：base模型，负责约束actor的输出不要跟base模型差太远</p><p data-pid=\"uxYqElJA\">reward：用于自动评价输出的结果好坏</p><h3>Reward / Critic model</h3><p data-pid=\"9dH1ol2p\">为训练Reward model，我们需要先训一个<b>偏好模型</b>（preference model，注意区分一下reference……），这个模型的作用顾名思义，就是判断回答的好坏。具体来说就是对同一个prompt有两个回答（一个pair），然后模型负责判断哪一个比较好。</p><p data-pid=\"BmXCrhN6\">实际上偏好模型也不止这一种，比如instruct GPT是拿了9个回复一起排序；llama2虽然也只用了2个回复，但是在标注的时候还添加了标注人员对自己标注的置信度，以及回复的安全性。总之模型设计上还算比较自由，具体怎么操作可以自己开脑洞。</p><p data-pid=\"ITrmhZSF\">偏好模型的训练也分两步，pretrain和finetune，pretrain这一步可以简称PMP。这两个步骤只是数据稍微有点区别，finetune阶段的数据质量要高一些。不过文章里并没有介绍pretrain用的数据集有多大……</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-b18560fe3406b3499e908f8250ef7a7a_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"601\" data-rawheight=\"86\" data-original-token=\"v2-b18560fe3406b3499e908f8250ef7a7a\" class=\"origin_image zh-lightbox-thumb\" width=\"601\" data-original=\"https://pica.zhimg.com/v2-b18560fe3406b3499e908f8250ef7a7a_r.jpg\"/></figure><p data-pid=\"pWXp5WLH\">实际上finetune之后的模型（RM）和只用pretrain的（PMP）主要是在千问自己的数据集上提升比较大（应该是针对性地构造了一些数据），但是在其他数据集上提升并不明显。</p><p data-pid=\"yHaZtSFR\">至于<b>preference model本身结构，就直接沿用了千问base模型，然后用上面说的数据进行PMP之后再finetune，finetune之后的模型就是reward model了，注意这个权重也被拿来初始化critic model。</b></p><h3>Actor / Reference model</h3><p data-pid=\"2zzk8OKe\">这两个模型均使用base模型初始化，区别在于reference是不会变的，actor是不断更新的。</p><p data-pid=\"SFhlflIi\">训练流程如下图所示：</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-180f4629d2ca870bed2f8efc4af5c890_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1973\" data-rawheight=\"1682\" data-qrcode-action=\"none\" data-original-token=\"v2-180f4629d2ca870bed2f8efc4af5c890\" class=\"origin_image zh-lightbox-thumb\" width=\"1973\" data-original=\"https://pica.zhimg.com/v2-180f4629d2ca870bed2f8efc4af5c890_r.jpg\"/></figure><p data-pid=\"LKYJ86Bn\">图中可以看到三个模块，其中initial language model就是reference model，中间的tuned language model就是我们之前说的actor-critic，最后右边红色框里的reward model。</p><p data-pid=\"f4f5ztsH\">训练流程也一目了然，reward model负责给模型的输出打分，reference model负责和模型输出计算KL散度（不能偏得太夸张），之后这两个合起来作为reward来给actor-critic更新梯度。</p><h2>RWKV</h2><p data-pid=\"Cx3mesJr\">线性attention的变种，提这篇文章是为了给LLM学习者们提供一些额外的知识，毕竟基于线性attention的工作相比传统attention少很多，总之就算看到了也不必大惊小怪。</p><h3>AFT</h3><p data-pid=\"By73MRKV\">讲RWKV之前我们需要先说AFT。</p><p data-pid=\"5qv9DFUo\">众所周知，传统Attention定义了QK的相似度： <img src=\"https://www.zhihu.com/equation?tex=sim%28Q_%7Bi%7D%2CK_%7Bj%7D%29%3Dexp%28%5Cfrac%7BQ_%7Bi%7DK_%7Bj%7D%5E%7BT%7D%7D%7B%5Csqrt%7BD%7D%7D%29\" alt=\"sim(Q_{i},K_{j})=exp(\\frac{Q_{i}K_{j}^{T}}{\\sqrt{D}})\" eeimg=\"1\"/> ，可以把V写成这样：</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-56e09890deb4536412bc5461f1cdab23_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"303\" data-rawheight=\"90\" data-original-token=\"v2-56e09890deb4536412bc5461f1cdab23\" class=\"content_image\" width=\"303\"/></figure><p data-pid=\"14GraEeQ\">这就带来了一个问题，计算的复杂度是随着序列长度平方增长 <img src=\"https://www.zhihu.com/equation?tex=O%28N%5E%7B2%7D%29\" alt=\"O(N^{2})\" eeimg=\"1\"/> 的。所以一个直觉的想法是把QK相乘这个东西换掉，比如说它能不能是一次的，而不是二次的。</p><p data-pid=\"ZSMI4GAn\">题外话：</p><p data-pid=\"j0Rna3ct\"><i>如果你是学CV的，看到这个东西立刻会有至少两个deja vu的想法：</i></p><p data-pid=\"cdUNQC5W\"><i>一个是比较古老的<b>Deformable DETR</b>，这个东西里面的attention不是正常的QK相乘，而是直接用query挂了个linear和softmax预测出来的。</i></p><p data-pid=\"x1ugKSES\"><i>另一个是非常古老的<b>squeeze-and-excitation</b>，也是某种attention，但是它也跟QK无关。</i></p><p data-pid=\"6Xlr2vk8\"><i>换句话说，attention绝对不止“QK相似度”这一种，只要你胆子够大，linear+softmax就是attention，乘回原始value上就完事了，这种attention不仅是CNN时代最常见的操作，而且复杂度还是一次的……</i></p><p data-pid=\"UK1tNhhA\">回到正题，我们考虑一次复杂度（线性attention）的QKV应该如何交互。</p><p data-pid=\"hToNganX\">这里不妨先瞅一眼AFT的decoder形式（因为是decoder所以只能看到左边的）：</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-4eb52aa69158cb78fe62b1c6eedb7040_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"386\" data-rawheight=\"80\" data-original-token=\"v2-4eb52aa69158cb78fe62b1c6eedb7040\" class=\"content_image\" width=\"386\"/></figure><p data-pid=\"IyA-mRnR\">其中 <img src=\"https://www.zhihu.com/equation?tex=%5Csigma%28Q_%7Bi%7D%29\" alt=\"\\sigma(Q_{i})\" eeimg=\"1\"/> 是取sigmoid，右边分数里面的 <img src=\"https://www.zhihu.com/equation?tex=w_%7Bi%2Cj%7D\" alt=\"w_{i,j}\" eeimg=\"1\"/> 是可学习项。</p><p data-pid=\"m1EGcFgK\">也就是说，左边这个 <img src=\"https://www.zhihu.com/equation?tex=%5Csigma%28Q_%7Bi%7D%29\" alt=\"\\sigma(Q_{i})\" eeimg=\"1\"/> 相当于把Q当成了一个门控函数，而右边的 <img src=\"https://www.zhihu.com/equation?tex=K_%7Bj%7D\" alt=\"K_{j}\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=w_%7Bi%2Cj%7D\" alt=\"w_{i,j}\" eeimg=\"1\"/> 刚好是wx+b形式的一个值，它是一个自己冒出来的attention值（只通过K而不需要QK交互，和Deformable DETR非常像），突出一个简单粗暴。</p><p data-pid=\"ivqHiazn\">可以想见这种形式的attention大概率还是能work的（我们前面已经铺垫很多次了），只不过没有传统attention复杂而已。</p><p data-pid=\"skLJo-Ch\">我们仔细琢磨一下上面那个公式的形式。</p><p data-pid=\"sHrEVko2\">在正常的attention里 <img src=\"https://www.zhihu.com/equation?tex=Q_%7Bi%7D\" alt=\"Q_{i}\" eeimg=\"1\"/> 是放在求和项里面的，并且拿不出来（因为跟 <img src=\"https://www.zhihu.com/equation?tex=Q_%7Bi%7D\" alt=\"Q_{i}\" eeimg=\"1\"/> 确实有关系），每来一个 <img src=\"https://www.zhihu.com/equation?tex=Q_%7Bi%7D\" alt=\"Q_{i}\" eeimg=\"1\"/> 就要重新计算一遍所有QK attention的内容。</p><p data-pid=\"TQ2ifhOv\">但是AFT里求和项里不包含 <img src=\"https://www.zhihu.com/equation?tex=Q_%7Bi%7D\" alt=\"Q_{i}\" eeimg=\"1\"/> ，这时候我们就可以把<b>i-1位置的K和V之类的东西都存起来</b>，在i位置可以直接复用这一结果，相当于迭代更新右边的求和项，这个形式跟RNN就非常像了，因此<b>可以实现和RNN类似的sequential decoding</b>。</p><p data-pid=\"DgpVTDKA\">但是另一方面AFT也有个问题，就是它仍然是 <img src=\"https://www.zhihu.com/equation?tex=O%28N%5E%7B2%7D%29\" alt=\"O(N^{2})\" eeimg=\"1\"/> 的，因为求和项内部还是有个跟 <img src=\"https://www.zhihu.com/equation?tex=i\" alt=\"i\" eeimg=\"1\"/> 相关的 <img src=\"https://www.zhihu.com/equation?tex=w_%7Bi%2Cj%7D\" alt=\"w_{i,j}\" eeimg=\"1\"/> ，导致虽然你能sequential decoding，但是<b>每一步迭代新增的计算量是</b> <img src=\"https://www.zhihu.com/equation?tex=O%28N%29\" alt=\"O(N)\" eeimg=\"1\"/> ，最终复杂度还是 <img src=\"https://www.zhihu.com/equation?tex=O%28N%5E%7B2%7D%29\" alt=\"O(N^{2})\" eeimg=\"1\"/> 。</p><h3>RWKV的线性attention</h3><p data-pid=\"lmpF0n0Q\">我们前面说了由于 <img src=\"https://www.zhihu.com/equation?tex=w_%7Bi%2Cj%7D\" alt=\"w_{i,j}\" eeimg=\"1\"/> 的存在，AFT的计算量仍然没能缩到O(N)，于是有了如下改进方案：</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-a07731c3b7300d219d29fe415aa0a2b9_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"550\" data-rawheight=\"599\" data-original-token=\"v2-a07731c3b7300d219d29fe415aa0a2b9\" class=\"origin_image zh-lightbox-thumb\" width=\"550\" data-original=\"https://picx.zhimg.com/v2-a07731c3b7300d219d29fe415aa0a2b9_r.jpg\"/></figure><p data-pid=\"h0HPZwRX\">左边的是细节图，右边的是整体框架。</p><p data-pid=\"hglFggV8\"><b>我们先来看下半的time mixing部分</b>。RKV就是transformer里的QKV，黄色框里的 <img src=\"https://www.zhihu.com/equation?tex=%5Cmu\" alt=\"\\mu\" eeimg=\"1\"/> 是一个混合用的参数，具体作用就是把上一时刻的输出和这一时刻做一个混合：</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-8ff1c1e94b8c38e52a422e5318b3ef6c_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"459\" data-rawheight=\"122\" data-original-token=\"v2-8ff1c1e94b8c38e52a422e5318b3ef6c\" class=\"origin_image zh-lightbox-thumb\" width=\"459\" data-original=\"https://pic3.zhimg.com/v2-8ff1c1e94b8c38e52a422e5318b3ef6c_r.jpg\"/></figure><p data-pid=\"N8FhiLK9\">有LSTM那个味儿了。</p><p data-pid=\"GZRZtTqg\">R一支自然没什么好说的，跟AFT一样，而WKV模块长这样：</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-f91a149ff6ddfb04c6eea1bb5449d9c3_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"553\" data-rawheight=\"82\" data-original-token=\"v2-f91a149ff6ddfb04c6eea1bb5449d9c3\" class=\"origin_image zh-lightbox-thumb\" width=\"553\" data-original=\"https://pic2.zhimg.com/v2-f91a149ff6ddfb04c6eea1bb5449d9c3_r.jpg\"/></figure><p data-pid=\"koVx-Rjm\">可以看到分子分母都是两项，前一项是求和，与前t-1项相关，后一项就是当前t时刻的值。</p><p data-pid=\"eUJoElAn\">前一项求和明显是设计过的，很容易发现 <img src=\"https://www.zhihu.com/equation?tex=w\" alt=\"w\" eeimg=\"1\"/> 前面的系数 <img src=\"https://www.zhihu.com/equation?tex=-%28t-1-i%29\" alt=\"-(t-1-i)\" eeimg=\"1\"/> 是随着i的增加而增加的并且一直小于0，也就是说离当前t的相对位置越远，对当前的影响就越小（文中叫decay），这也是符合直觉的设计。</p><p data-pid=\"h6sHRfgF\">后一项里有一个从没见过的东西是 <img src=\"https://www.zhihu.com/equation?tex=u\" alt=\"u\" eeimg=\"1\"/> ，作者表示 <img src=\"https://www.zhihu.com/equation?tex=w\" alt=\"w\" eeimg=\"1\"/> 在一些情况下可能出现退化（比如说是0），所以这里干脆用一个新的独立参数来防止这个问题。</p><p data-pid=\"4diYGr6_\">对照一下前面的AFT，可以发现RWKV这里就是改了一下 <img src=\"https://www.zhihu.com/equation?tex=w\" alt=\"w\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=u\" alt=\"u\" eeimg=\"1\"/> ，这样保留了sequential decoding的优势，同时也把计算复杂度降下来了，可以参考作者在原文里的公式：</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-597c68f3b49abb217925bdb768776bd2_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"374\" data-rawheight=\"209\" data-original-token=\"v2-597c68f3b49abb217925bdb768776bd2\" class=\"content_image\" width=\"374\"/></figure><p data-pid=\"SxjoM-zj\">显然当前时刻t的结果可以由<b>上一时刻的结果+一些和序列长度无关的O(1)计算</b>得出，因此整体复杂度是O(N)，非常环保。</p><p data-pid=\"dVLxhi0z\">然后再来看<b>channel mixing模块</b>。</p><p data-pid=\"TZuCNhr6\">这里的R&#39;K&#39;V&#39;都是带撇的，显然意思是值跟前面的RKV不一样，重新算了一下。同样对于左边R一支没什么好说的，右边一支的计算方法显得很莫名其妙：</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-141c85bc035a9ce92b6f6402a92f2106_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"377\" data-rawheight=\"48\" data-original-token=\"v2-141c85bc035a9ce92b6f6402a92f2106\" class=\"content_image\" width=\"377\"/></figure><p data-pid=\"gnNCiuLO\">相当于把 <img src=\"https://www.zhihu.com/equation?tex=k_%7Bt%7D%27\" alt=\"k_{t}&#39;\" eeimg=\"1\"/> 先做一个squared ReLU，然后再把 <img src=\"https://www.zhihu.com/equation?tex=W_%7Bv%7D%27\" alt=\"W_{v}&#39;\" eeimg=\"1\"/> 乘上去。</p><p data-pid=\"rK97fYgt\">首先是这个squared ReLU就显得很怪异，它未免比ReLU高明到哪里去，可能实验结果好一点所以用了。</p><p data-pid=\"rwgp3aJJ\">再者就是把 <img src=\"https://www.zhihu.com/equation?tex=W_%7Bv%7D%27\" alt=\"W_{v}&#39;\" eeimg=\"1\"/> 乘到 <img src=\"https://www.zhihu.com/equation?tex=k_%7Bt%7D%27\" alt=\"k_{t}&#39;\" eeimg=\"1\"/> 上，也就是这里把k当成和v一个东西，我觉得有些抽象……看作者的意思是这个思路也取材于两三年前的MLP-Mixer（我当时看过这篇文章，不过忘了咋回事了）有空的读者大概可以去瞅一眼。</p><p data-pid=\"vNbZsLfS\">总之这里我看不出有什么理由一定要是这种形式，不过炼丹本来就是先有结果然后往上凑理由，可能作者比较耿直，懒得写理由了……</p><h3>评价</h3><p data-pid=\"KoJ1Npwi\">对RWKV的评价，我觉得参考这个问题里各路大哥的评价即可。</p><p data-pid=\"W8QklFti\"><a href=\"https://www.zhihu.com/question/602564718\" class=\"internal\" target=\"_blank\">如何评价最新的RWKV论文 (arXiv 2305.13048)？</a></p><p data-pid=\"HqkRqaZk\">以及RWKV虽然长得像RNN，但是可以进行并行训练。这里就不细说了，同样可以参照上面问题的回答。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"v_h4TUe6\">以上便是本文全部内容，日后可能会有不定时勘误和内容补充，不过基本算是完工了……要去码下一篇文了。</p>", "is_labeled": false, "visited_count": 89916, "thumbnails": ["https://picx.zhimg.com/50/v2-330bd218414359621af1fedcc4e0dd75_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-d2f039544f50a4717f31590068250111_720w.jpg?source=b6762063"], "favorite_count": 6666, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": *********}", "attached_info": "CqkLCOXSz7OH4ZH1mwEQBxoJMjM2ODQwMDQzIPGNu6sGKLsWMClALUowCgZJdGVtQ0YSIGRvY190eXBlOiBBcnRpY2xlCmlkOiAyNDczMzQ1MDkKGAAgADoAYiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3IJNjY5MTkzNTg1qgEJcmVjb21tZW5kwgEgMGRlY2U4MzBlOTRhNDJkZDNlZDllYmZmN2FjYzlmZTDyAQoIDBIGTm9ybWFs8gEoCAoSJDI5OWQyNmUyLWQ5MTktNGIzNy1iZGU2LTc3M2NlODI3NmE4YfIBBQgLEgE4ggIAiALQg7nN+jKSAiAwZGVjZTgzMGU5NGE0MmRkM2VkOWViZmY3YWNjOWZlMJoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhVVc2VyTGNuRXhpdFdlaWdodFJ1bGXKAhRDb250ZW50QWdlV2VpZ2h0UnVsZdoCBkl0ZW1DRugCA/oCC05PUk1BTF9GTE9XigMgZmRhNzBjZmIzOTZiNGQyNjg1MjhhNzNlZTJmMzdmMDeaAw0KAnYyEAAaBW90aGVyqAO8vgXYAwDqAxV0ZXh0QWxsU2l0ZU12SXRlbUNGVjL6A7YGEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERTotCAIQ0AgYyAIiI3YyLTUzZTc0Mjc1MDMwMGM0ODY2NWMzZmRmNTRmZWEyOTM0Oi0IAhCABRjeAyIjdjItMzMxNWM3Zjg3MzA1NjEzNGEwOGE1ODViMmNkMzA3YTk6LQgCEMEEGIoEIiN2Mi02NDY4YThlMmY3YWY3OGE1MWIzOTJmNGJmNjk0NGRkZTosCAIQtgQYfiIjdjItMTgyYzYzNzMwZDNjNmFmNjZiMTNhZDZhMDhiZjFmYmQ6LQgDEMgPGNYGIiN2Mi0xOGU2N2VmMmNhNWJjZDY4MTRlYjZlZTA1OTc2MTBlYTosCAIQjQQYWyIjdjItYTM3ZWM0ZTMzNzUyOWEwY2M0MjIwNGY3OGEwZjJkOWQ6LQgCENMGGLQEIiN2Mi0xY2E1MDhmMTczMjRiZDFhZWZkOGNiNDViMTk1OTQ0ODotCAMQ3wYYpQMiI3YyLTBmMWZhOTVkMzUxNDk2ZGQ3ZWQ4ZTU2MjVkYTY0ZjIyOi0IAhDrDhjCCCIjdjItNzBlZWUxZmJlOGUxZTlkYzM1NDRhNTQwNWI5NGUxYzc6LAgCENkEGFYiI3YyLWIxODU2MGZlMzQwNmIzNDk5ZTkwOGY4MjUwZWY3YTdhOiwIAhCvAhhaIiN2Mi01NmUwOTg5MGRlYjQ1MzY0MTJiYzU0NjFmMWNkYWIyMzosCAIQggMYUCIjdjItNGViNTJhYTY5MTU4Y2I3OGZlNjJiMWM2ZWVkYjcwNDA6LQgEEKYEGNcEIiN2Mi1hMDc3MzFjM2I3MzAwZDIxOWQyOWZlNDE1YWEwYTJiOTosCAIQywMYeiIjdjItOGZmMWMxZTk0YjhjMzhlNTJhNDIyZTUzMThiM2VmNmM6LAgCEKkEGFIiI3YyLWY5MWExNDlmZjZkZGZiMDRjNmVlYTFiYjU0NDlkOWMzOi0IAhD2AhjRASIjdjItNTk3YzY4ZjNiNDlhYmIyMTc5MjViZGI3Njg3NzZiZDI6LAgDEPkCGDAiI3YyLTE0MWM4NWJjMDM1YTljZTkyYjZmNjQwMmE5MmYyMTA2gAQAiAQAkgQGTm9ybWFsmgQBM6AEAKgEALAEALoEBm1hbnVhbMIEAzE3MMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAA4AU7rj+BBQAAAAAAAAAAiQWsYdagiXbSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUIkAYAoAYxqAYAkgIkCgkyMzY4NDAwNDMSCTY2OTE5MzU4NRgHIgpJTUFHRV9URVhU", "action_card": false}, {"id": "46_1750898459.481", "type": "feed", "offset": 46, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898459, "updated_time": 1750898459, "target": {"id": "1921407544175329759", "type": "answer", "url": "https://api.zhihu.com/answers/1921407544175329759", "author": {"id": "a52d1a77a557d952c6d303e074d6c0aa", "url": "https://api.zhihu.com/people/a52d1a77a557d952c6d303e074d6c0aa", "user_type": "people", "url_token": "qiao-qiao-lei-82", "name": "研究卖课的罗老师", "headline": "知识付费产品运营砖家", "avatar_url": "https://pica.zhimg.com/50/v2-7431eb2592c81c3fa89516ea923d1ee8_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 11, "is_following": false, "is_followed": false}, "created_time": 1750879256, "updated_time": 1750879256, "voteup_count": 0, "thanks_count": 0, "comment_count": 0, "is_copyable": true, "question": {"id": "353208175", "type": "question", "url": "https://api.zhihu.com/questions/353208175", "author": {"id": "bfd1485b77554342793ccc82515e0a08", "url": "https://api.zhihu.com/people/bfd1485b77554342793ccc82515e0a08", "user_type": "people", "url_token": "wu-zhong-wei-90-7", "name": "pierwzw", "headline": "人生一场虚空大梦，韶华白首，不过转瞬，惟有天道恒在，往复循环", "avatar_url": "https://pic1.zhimg.com/50/v2-4884c478972ef8b3b5897749dc65397f_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 20, "is_following": false, "is_followed": false}, "title": "好用的知识付费平台有哪些？", "created": 1572407925, "answer_count": 0, "follower_count": 0, "comment_count": 4, "bound_topic_ids": [1050, 1240, 1865, 602452, 176227], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "thumbnail": "https://picx.zhimg.com/50/v2-98822fa311cd34c5cd5d2c3a5219b424_720w.jpg?source=b6762063", "excerpt": "首先，我本人从2016年开始为一位财经大V做知识付费运营以来，接触使用过20家知识付费平台，在互联网行业当过5年软件产品经理，后来又自己创办了一家知识服务费平台（我暂时不说，看观众老爷们能不能猜出来），所以我对这个行业的所有产品的熟悉程度，我敢说第二，怕是没几个人敢说第一。 在准备这篇稿子之前，我又经过了半个月的调研确认，才汇总了这篇《12家知识付费平台，深度评测》，希望帮助各位老师从众多的卖课平台中，快…", "excerpt_new": "首先，我本人从2016年开始为一位财经大V做知识付费运营以来，接触使用过20家知识付费平台，在互联网行业当过5年软件产品经理，后来又自己创办了一家知识服务费平台（我暂时不说，看观众老爷们能不能猜出来），所以我对这个行业的所有产品的熟悉程度，我敢说第二，怕是没几个人敢说第一。 在准备这篇稿子之前，我又经过了半个月的调研确认，才汇总了这篇《12家知识付费平台，深度评测》，希望帮助各位老师从众多的卖课平台中，快…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"Jnya4wsP\">首先，我本人从2016年开始为一位财经大V做知识付费运营以来，接触使用过20家知识付费平台，在互联网行业当过5年软件产品经理，后来又自己创办了一家知识服务费平台（我暂时不说，看观众老爷们能不能猜出来），所以我对这个行业的所有产品的熟悉程度，我敢说第二，怕是没几个人敢说第一。</p><p data-pid=\"Iiux0Alw\"><br/>在准备这篇稿子之前，我又经过了半个月的调研确认，才汇总了这篇《12家知识付费平台，深度评测》，希望帮助各位老师从众多的卖课平台中，快速选出一家适合自己的平台，并且提前避坑避雷。<br/>其实老师在选知识付费平台时，无非关注以下几点：</p><p data-pid=\"6xtxRucI\"><br/><b>入住前，老师们需要关注的9点注意事项</b></p><p data-pid=\"DD5hI7Sx\"><br/><b>1、入驻费</b><br/>知识付费平台大致分为三派：</p><ul><li data-pid=\"_7rOF3fi\">一派是高入驻费，低抽成模式，典型代表小鹅通；</li><li data-pid=\"0NbidDGQ\">一派是低入驻费，高抽成的模式，典型代表是易知课堂；</li><li data-pid=\"GskhjQ0z\">还有一派是同时支持高价入驻买断0抽佣，和低价入驻但有抽佣两种模式的，典型代表是像素学堂。</li></ul><p data-pid=\"Mmup759D\">以上没有好坏之分，只是看哪种更适合你。<br/><br/><b>2、抽佣</b><br/>抽佣的高低肯定是我们关注的，这个是肯定的，但我们在对比抽佣高低的同时也要关注入驻费，你不能拿一个入驻费大几千甚至几万的平台去和一个999入驻的平台比抽；<br/>第二，关注抽佣是否有阶梯，例如你一年卖课50万，一个平台无阶梯，抽佣比例10%，拿一年抽佣就是50*10%=5万，但如果另一个平台的抽佣有阶梯：</p><p data-pid=\"72uab6Gs\">LV 1：0-1万：10%佣金</p><p data-pid=\"wj0ZEZsP\">LV 2：1-5万：9%佣金</p><p data-pid=\"anEg_sSl\">LV 3：5-30万：7%佣金</p><p data-pid=\"OudnGp9o\">LV 4：30万以上：3%佣金</p><p data-pid=\"YyKXvSCm\">那一年抽佣就是￥28500，将近差了一倍，不可小觑。<br/><br/><b>3、是否打通抖快视红四大平台</b><br/></p><p data-pid=\"5uYrPH3C\">首先，啥叫打通，为啥要打通？</p><p data-pid=\"dSe3KV3C\"><br/>因为你用的知识付费平台都不是大平台自己家开发的，所以是两个完全隔离的系统，那比如有一个学生在抖音买了你的课，你用的某鹅，那某鹅他怎么知道18512345678这个手机号买了你的课呢？这就需要平台去申请服务商，通过API的方式获取到订单信息，从而给指定手机号发听课短信。</p><p data-pid=\"spTu7a4R\"><br/>但也可以通过一个中间商获取到订单信息，从而给指定手机号发送听课短信。<br/><br/><b>4、支持的听课方式：</b></p><p data-pid=\"9KT0iJ8A\"><br/>小程序、H5、PC网页、App</p><p data-pid=\"XCPeuYFE\"><br/>大部分平台都支持微信小程序听课和H5听课，但如果你的课是类似办公软件教学、PS、剪辑教学、IT编程教学等需要在电脑大屏观看的课程，就需要特别关注他是否支持在电脑上听课，或者说在电脑上听课支持的体验好不好。比如说千聊就不支持PC独立店铺，或者准确说对PC的支持不好。<br/></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-56d9d50fdf06f85ff561ba20e3528513_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"2560\" data-rawheight=\"1440\" data-original-token=\"v2-9eda55a1177a6dbcf76f35ce516c44aa\" class=\"origin_image zh-lightbox-thumb\" width=\"2560\" data-original=\"https://pic2.zhimg.com/v2-56d9d50fdf06f85ff561ba20e3528513_r.jpg\"/></figure><p data-pid=\"a9W0Aumh\"><br/>你看他只能显示在中间一小块，这就浪费了电脑的宽屏，由此可见他家不重视电脑听课学习体验，此时你就需要注意了。<br/></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-676e50e7f3a55c2782ea3cf19bfc22fe_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"2560\" data-rawheight=\"1440\" data-original-token=\"v2-f0996577c1a7f9d99b21bac799c30829\" class=\"origin_image zh-lightbox-thumb\" width=\"2560\" data-original=\"https://pic1.zhimg.com/v2-676e50e7f3a55c2782ea3cf19bfc22fe_r.jpg\"/></figure><p data-pid=\"lEgdlzuP\"><br/>另外一家像素学堂，对PC的听课学习体验就支持的更好。</p><p data-pid=\"-b4o5wYq\"><br/>有很多人不知道什么是H5，其实可以简单的理解H5就是一个运行在微信内的手机网页。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-af0b90f428488795e2de585f73e3a743_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1836\" data-rawheight=\"1159\" data-original-token=\"v2-9a14de8a87b1c26b9c8f7bdbb94c8242\" class=\"origin_image zh-lightbox-thumb\" width=\"1836\" data-original=\"https://pic4.zhimg.com/v2-af0b90f428488795e2de585f73e3a743_r.jpg\"/><figcaption>左图是小程序，右图是H5</figcaption></figure><p data-pid=\"IyMKVcAa\"><br/>这是同样的一个课在小程序和在H5上的不同呈现方式，直观来看，右上角有那个圆角矩形里面小圆圈+小圆点的是小程序，右上角仅仅有个…按钮的是H5。</p><p data-pid=\"JOBElMjO\"><br/>明显能看到，相同大小的手机屏幕，小程序页面显示的内容比H5多一点，对比页面底部会看的很清楚，H5页面底部被一黑色的长条遮住了，这只是表面，在其他方面小程序的体验都比H5要好。</p><p data-pid=\"BatSZWHj\"><br/>但H5也有他独特的优势，就是支持IOS支付买课，因为苹果税的原因，腾讯和苹果神仙打架，苹果认为课程属于虚拟商品，需要给他交30%苹果税，腾讯不愿妥协两家没谈拢，所以腾讯干脆把小程序的IOS支付功能给关掉了，但H5不受影响课照常支付。<br/></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-838221fa7bb245bf6b3798119333e7c3_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1560\" data-rawheight=\"939\" data-original-token=\"v2-2c1207443dba88d5ef0bc714d1ab8a2a\" class=\"origin_image zh-lightbox-thumb\" width=\"1560\" data-original=\"https://pic4.zhimg.com/v2-838221fa7bb245bf6b3798119333e7c3_r.jpg\"/><figcaption>左图是小程序，右图是H5</figcaption></figure><p data-pid=\"F0mLiM1p\"><br/>另外，少数平台还支持App听课，为什么有小程序听课还要App听课呢？App听课在以下几个方面有优势：<br/>1、有些老师卖的课属于给小学生学习的，家长不想让学生用微信，也不愿意在学习的iPad上登录自己的微信，那此时就没法用微信小程序听课了，就需要在App上听课。<br/>2、App在技术上支持视频的下载缓存，虽然现在的网络无处不在，基本上没人会担心没网，需要提现缓存下载视频，但这种案例我真遇到过，哭笑不得！小程序和H5在技术上没法支持视频的缓存下载。<br/>3、坦诚讲小程序听课在听课时不方便回微信消息，这点就不如App。<br/><br/><b>5、独立店铺：</b><br/>首先解释什么是独立店铺。独立店铺就是听课系统专属于你，学生进入后看不到其他老师的课，只能看到你的课，像是给你私人定制开发的课程系统。有些平台也叫私域店铺。</p><p data-pid=\"qOoAqsBz\"><br/>但你不能只问到这里，你需要深究他所谓的独立店铺是独立H5，还是独立小程序，还是PC独立店铺。</p><p data-pid=\"8U7FR6xA\"><br/>一般来说平台宣称支持独立店铺仅仅只是支持独立H5，像小鹅通、海豚知道你想要一个独立的小程序得多花很多钱，而且还要注册微信服务号，注册小程序，他们每年的认证年审费用各300元。另外有些商家喜欢厚着脸皮造概念，比如某豚自己造了一个概念叫小程序内独立店铺，其实根本不是独立店铺，用的还是海豚知道小程序，只是你的主页没法跳转出去，他们销售就管这叫“小程序内独立店铺”，各位老师一定要擦亮双眼。</p><p data-pid=\"mGxeoOY8\"><br/>有些老师他的课适合在电脑上大屏看，就需要PC独立店铺，这时你就需要问清楚是否支持，多少钱才能支持，就比如小鹅通的PC独立店铺￥25800的旗舰版才支持，有些是给钱都不支持的，比如千聊，有些是多花几百块钱就可以支持，比如像素学堂。<br/><br/><b>6、流量费：</b><br/>有些平台是送一定的流量包，超过就需要自己加购流量包，比如小鹅通；</p><p data-pid=\"WYuetrU0\"><br/>有些是完全不收流量费，比如像素学堂；</p><p data-pid=\"oiw85-3T\"><br/>有些平台是720P视频不收流量费，1080P就要收流量费，比如千聊。<br/>怎么估算自己的流量费呢？这个取决于你有多少学员，课程时长多长，视频分辨率和码率。例如你的课程是10个小时，合起来10G，100个学生，那需要消耗的流量就是：10*10*100=10000G流量，当然这是按照平均一个学生看完一遍课程估算的，如果看2遍就需要再乘以2。<br/><br/><b>7、扩容费：</b><br/>有些平台是有扩容费的，比如小鹅通、像素学堂；</p><p data-pid=\"iI0UWFNG\"><br/>有些平台是没有扩容费的，比如千聊、海豚知道；</p><p data-pid=\"3pz_C_M4\"><br/>一般你自己看看你自己的课程合起来多少个小时，按1小时1G去估算，例如你的课程合起来30个小时，那估算占用空间30G，此时即便收扩容费的平台也无所谓，因为他送的100G、200G空间足够用了，即便有扩容费，你也不需要扩容。</p><p data-pid=\"RjnfLokZ\"><br/>另外根据我的经验大家需要注意，几乎所有不限制空间，不收扩容费的平台都会对课程视频压缩的比较厉害，最后导致课程视频模糊，所有如果你希望你的学员在听课时更清晰，尽量避坑这类平台，像素学堂平台的视频压缩码率比较高，清晰度更好一些，我知道的海豚知道压缩视频很厉害，非常模糊。</p><p data-pid=\"r3KoR6Rn\"><br/>最后一个小细节，如果你课程时长比较长，需要扩容，那就需要关注扩容价格：</p><p data-pid=\"gEIKdQly\">小鹅通：7.2元/G/年</p><p data-pid=\"JwSt_P0I\">荔课：18元/G/年</p><p data-pid=\"atOTPl2K\">像素学堂：3.6元/G/年</p><p data-pid=\"pRHFIZYT\">这只是表面价格，你还需要关注这个空间占用是按本地文件大小计算的，还是按上传到他们平台后压缩转码后的文件大小算的，例如小鹅通就是按本地文件大小算的，像素学堂是按压缩转码后的文件大小算的。例如你上传一个1小时的2k视频，在小鹅可能需要扩容3G，但在像素学堂只需要扩容1G，因为像素学堂是按照压缩转码后的文件大小计算占用空间大小的。<br/><br/><b>8、其他费用：</b><br/>短信费、转码费、小程序和服务号认证年费、软件授权年费等其他费用</p><p data-pid=\"u5fPu61P\"><br/>短信费：有些平台还收取短信费，就是学员下单后他会发短信通知，其实这个短信费没多少钱，不用太在意。<br/>有些自建卖课平台你需要注意还有转码费，软件授权年费、微信服务号和小程序每年各300元的认证年审费，合起来就是每年￥600。<br/><br/><b>9、服务：</b></p><p data-pid=\"7uyl2R9b\"><br/>是否协助开店上架课程：</p><p data-pid=\"5CfUnQAZ\"><br/>因为在抖音开抖店和在视频号开微信小店上架课程，如果你不是专业做电商的，确实挺头疼的，流程挺复杂，你需要问清楚他们平台是否支持辅导开店和上架课程。</p><p data-pid=\"cpwda69A\"><br/>是否有1V1或者专属服务群，这样有问题可以第一时间找到平台解决，这点也很重要，你大概率是会遇到各种各样的问题的。</p><p data-pid=\"dV0ucydQ\"><b>下面我一一评测这12家知识付费平台：</b><br/><br/><b>1、</b><a href=\"https://link.zhihu.com/?target=https%3A//www.xiaoe-tech.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">小鹅通</a></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-2e89c64ee5e5ddc0a327ca81378cf8dd_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1068\" data-rawheight=\"487\" data-original-token=\"v2-5be5704fb8c98231b6d8b0f6e0675a4f\" class=\"origin_image zh-lightbox-thumb\" width=\"1068\" data-original=\"https://pic2.zhimg.com/v2-2e89c64ee5e5ddc0a327ca81378cf8dd_r.jpg\"/><figcaption>小鹅通</figcaption></figure><p data-pid=\"4khtDGtD\">小鹅通做的比较早，是在2015/16年就开始做的，这么多年功能已经非常完善，技术也比较稳定。他家的优点销售会给你讲，我就不讲了，我给你讲讲他家的缺点：<br/>1.功能太多简直冗余，因为开发这些功能是要研发成本的，所以就导致他的入驻费很高，降不下来，￥6800的基础版只是丐版，很多功能没法用。<br/>2.他家除了视频号支持共享店铺外，抖音是不支持共享店铺的，所以如果你没有营业执照想卖课，而且还想实现在抖音挂车卖课那就不合适。<br/>3.注意他家的￥6800的基础版很多功能是没法用的，比如公域打通，这个几乎是抖音视频号挂车卖课的刚需，但对不起，6800不能用，得12800才能用。再比如小程序也是需要12800的专业版才能用的，最离谱的是1080P视频、PC独立店铺需要￥25800的旗舰版才能用。相比之下，像素学堂+699选购就可以用就显得太香了。</p><p data-pid=\"I137iZLC\"><b>2、</b><a href=\"https://link.zhihu.com/?target=https%3A//www.htknow.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">海豚知道</a></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-eaade901a926fee899802cf32f52fffa_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1047\" data-rawheight=\"509\" data-original-token=\"v2-3d9ebff94d620ffc6b3e64468c3915cf\" class=\"origin_image zh-lightbox-thumb\" width=\"1047\" data-original=\"https://pic1.zhimg.com/v2-eaade901a926fee899802cf32f52fffa_r.jpg\"/><figcaption>海豚知道</figcaption></figure><p data-pid=\"QP08KtLX\">海豚知道本来和小鹅通一个年代做知识付费工具的，一直打不过小鹅通，直到借助抖音实现飞跃发展。他家以超强的销售团队闻名，只要你把电话号码泄露给他，接下来将是无穷无尽的销售电话，而且他家销售的话你听一半信一半就行了，甚至我说信一半都说多了。他家的优点销售会给你讲，比如打通了抖音、快手、视频号、小红书，这个确实做的不错，优点我就不讲了，我给你讲讲他家的缺点：<br/>1.承诺很多服务没法兑现；<br/>2.为了节约成本视频压缩的很厉害，只支持720，导致视频模糊，如果你跟他闹，他会说给你申请走绿色通道不压缩视频，但这样一来就会造成视频卡顿，反正就是模糊和卡顿你必须选一个；<br/>3.不支持独立店铺，我前面说的造概念“小程序内独立店铺”说的就是他家；<br/>4.很多课没法上架他告诉你可以上，等你入驻后上不了课要退款他会磨磨唧唧；<br/>5.另外，他家的公域打通如果你2999入驻只能选一个，比如只能选抖音，如果要多选一个视频号+小红书得6999的版本才行，这点不是缺点，明码标价就看你觉得值不值。</p><p data-pid=\"vEOX_fCu\"><b>3、</b><a href=\"https://link.zhihu.com/?target=https%3A//www.pixelstudio.space/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">像素学堂</a></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-207b8839a9bfd3e25c82a4db41d1bab7_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1118\" data-rawheight=\"533\" data-original-token=\"v2-78f8d90dcb971f094c10e92de72b1f94\" class=\"origin_image zh-lightbox-thumb\" width=\"1118\" data-original=\"https://pic2.zhimg.com/v2-207b8839a9bfd3e25c82a4db41d1bab7_r.jpg\"/><figcaption>像素学堂</figcaption></figure><p data-pid=\"rgTxMBsO\">像素学堂公司在北京，去年刚刚成立，明显优势是价格很低，支持万粉0元入驻，并且比如独立店铺、1080P超清视频没有和版本绑定，而是你可以花钱选购，即便0元入驻也可以选购，也支持抖音、视频号挂车卖课。他家的优点销售会给你讲，我就不讲了，我给你讲讲他家的缺点：<br/>1.不支持私域直播，只支持录播视频课、音频课；<br/>2.不支持独立小程序，即便多花钱也不行，销售说得至少8、9月份之后才能支持，也就是说虽然支持小程序听课，但小程序里还有其他老师的课。但他家的独立PC店铺真的不错，+￥699就能用，这在其他家比如小鹅通需要￥25800的版本才能用。</p><p data-pid=\"bGjV2WHh\"><b>4、</b><a href=\"https://link.zhihu.com/?target=https%3A//pc.qlchat.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">千聊</a></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-ba4eec49ea934f972fd40fe94c4db17f_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1119\" data-rawheight=\"651\" data-original-token=\"v2-3d32ee19f9d334b110768d9229c9c14c\" class=\"origin_image zh-lightbox-thumb\" width=\"1119\" data-original=\"https://picx.zhimg.com/v2-ba4eec49ea934f972fd40fe94c4db17f_r.jpg\"/><figcaption>千聊</figcaption></figure><p data-pid=\"RjFxSaIV\">千聊总部在广州，同样是在2016年和小鹅同一年成立的，小鹅在深圳，千聊在广州，同时获得腾讯投资，但商业模式不同。小鹅是高价店铺费0抽成模式，千聊是低价店铺费+抽成模式。他家的优点销售会给你讲，我就不讲了，我给你讲讲他家的缺点：<br/>1.3699/年的入驻费，还有抽成，价格不算低；<br/>2.对PC听课的支持不好</p><p data-pid=\"d63XyoBx\"><b>5、易知课堂</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-e189a92fe522cecafa1d200e9652d02c_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1115\" data-rawheight=\"455\" data-original-token=\"v2-171b5080a4b30e625da1f61cb7099e80\" class=\"origin_image zh-lightbox-thumb\" width=\"1115\" data-original=\"https://pic3.zhimg.com/v2-e189a92fe522cecafa1d200e9652d02c_r.jpg\"/><figcaption>易知课堂</figcaption></figure><p data-pid=\"_vwt9_hD\">易知课堂和海豚知道非常像，都是兴起于短视频时代，不同于海豚借助抖音平台，易知课堂是借助快手起来的。<br/>易知课堂和像素学堂一样，都支持万粉0元入驻，而且打通公域实现小黄车挂课无需再次付费。他家的优点销售会给你讲，我就不讲了，我给你讲讲他家的缺点：<br/>1.对私域的支持差，比如微信给你红包转账的学员，你就需要给他发激活码、导入学员，这些像素学堂有的功能他没有。<br/>2.不支持独立店铺，只有￥19999的企业版才支持独立小程序。<br/>3.抽成高，不管是私域还是公域，都是10%抽佣无阶梯。<br/>4.用户体验差，讲师管理后台有点难用实在……</p><p data-pid=\"tcGInN-m\"><b>6、课堂街</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-05c353ab65b8fa19ceb9bffce6aee1e8_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1052\" data-rawheight=\"1084\" data-original-token=\"v2-29554eb343199378a4ee1681efaff3bc\" class=\"origin_image zh-lightbox-thumb\" width=\"1052\" data-original=\"https://pic1.zhimg.com/v2-05c353ab65b8fa19ceb9bffce6aee1e8_r.jpg\"/><figcaption>课堂街</figcaption></figure><p data-pid=\"xt3ONqdn\">课堂街对标的小鹅通，价格比小鹅低一些，他家的优点销售会给你讲，我就不讲了，我给你讲讲他家的缺点：<br/>1.1080P需要旗舰版才能用；<br/>2.基础版仅支持H5独立店铺，不支持PC独立店铺，小程序独立店铺，大家都知道H5独立店铺体验比较差，这是技术所限。</p><p data-pid=\"-FiNf00C\"><b>7、荔课</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-d470c1cd5366eca1aa612934c1576ad0_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1055\" data-rawheight=\"476\" data-original-token=\"v2-ec0891ebc4a95a9dc8ca39e74bcedec9\" class=\"origin_image zh-lightbox-thumb\" width=\"1055\" data-original=\"https://pic1.zhimg.com/v2-d470c1cd5366eca1aa612934c1576ad0_r.jpg\"/><figcaption>荔课</figcaption></figure><p data-pid=\"umSmEWvW\">荔课总部也在深圳，但干不过小鹅通，后来转模式了，从一个知识付费工具变成了职业教育机构，那个卖￥4980AI绘画课的轩轩老师，就是这个机构的。</p><p data-pid=\"udyOv8yT\"><br/>简单的说之前和小鹅一样是卖给老师用的课程系统的，现在主营业务是卖课了，从赚老师的钱变为赚学员的钱。<br/>我知道的他家的课投诉退款非常之多，因为我另一个号是教AI绘画的，很多粉丝跟我聊他报过十方教育的AI绘画课，最后闹退款。他们甚至引导学员使用花呗去买他们大几千块钱的课，这点让我个人很看不起这家公司，所以他家做出来的课程系统，也不会好到哪里去，而且功能也不好好更新了。</p><p data-pid=\"zaU9kf22\"><br/>如果你非要用他家，有几个点需要注意：<br/>1.虽然0元入驻，但只送15G空间，重点是这15G空间用完后，扩容费贵得离谱，我们来对比下扩容费：<br/></p><ul><li data-pid=\"ieNx0f1w\">小鹅通：7.2元/G/年</li><li data-pid=\"BKlBG1oe\">荔课：18元/G/年</li><li data-pid=\"-B6MH0hX\">像素学堂：3.6元/G/年</li></ul><p data-pid=\"413XmXrD\">所以这就有点想先引你入坑，然后再高价收费</p><p data-pid=\"LYCzrAa-\"><br/>2.不好好更新功能了，比如抖音的挂小雪花（也就是抖音小程序）卖课已经快1年都不能用了，现在都是都挂小黄车的路线，而他家官网，讲师管理后台还没改。</p><p data-pid=\"hEDlXP3y\"><br/>总之，荔课在行业内虽然有一定名气，以免费入驻出名，但是这块业务基本已经被公司放弃了，所以你最好不要为了贪便宜用他家。</p><p data-pid=\"kvavNAH1\"><b>8、知识搭档</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-ac7fb2b704dfbe18103f94430978104c_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1056\" data-rawheight=\"454\" data-original-token=\"v2-5b884d9768121c40c7404d80c48ba420\" class=\"origin_image zh-lightbox-thumb\" width=\"1056\" data-original=\"https://pica.zhimg.com/v2-ac7fb2b704dfbe18103f94430978104c_r.jpg\"/><figcaption>知识搭档</figcaption></figure><p data-pid=\"VB6aAxwv\">我在跟这家销售聊完后，对他家印象极差，是因为他家销售不老实，很多话要是外行可能真被他糊弄过去了，比如：</p><p data-pid=\"FWNBjD2t\"><br/>1.他声称0抽佣，但你细究为什么有6%技术服务费时，销售声称这是平台拿走了，但实际根本不是这样。<br/>2.再比如，使用永久有效这种营销套路，因为他承诺永久就是在堵你用一年第二年就不用了，赚的就是这一把，说的难听点就是：就割这一刀<br/>3.3999版本才支持独立店铺，而且还只是H5独立店铺，对比像素学堂只要加￥699就可以支持H5独立店铺+PC独立店铺。<br/>4.他家的公域打通是不支持自有店铺的，也就是如果你有营业执照，想接入自己的抖店或者微信小店他是不支持的，但销售会含糊其词这个问题，是我在穷追不舍下把他逼到墙角，销售才承认不支持接入自有店铺。</p><p data-pid=\"1gIyMqXw\"><b>9、人人讲</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-4cfa1acd742c8bf4e380f890ad05f4b5_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1056\" data-rawheight=\"451\" data-original-token=\"v2-6650b339176084c9095fa5eb69c04619\" class=\"origin_image zh-lightbox-thumb\" width=\"1056\" data-original=\"https://pic2.zhimg.com/v2-4cfa1acd742c8bf4e380f890ad05f4b5_r.jpg\"/><figcaption>人人讲</figcaption></figure><p data-pid=\"Aglhu7RX\">我记得在2018年的时候就已经有人人讲了，可能是中间做的不好，没打过小鹅、千聊等平台，就沉寂了几年，最近突然又打着0元入驻回来了，他的0元入驻是没有粉丝量门槛的，但我试用后很多按钮点击都没有反应，联系客服也联系不上，用户体验极差，有可能是又不想做了。缺点：<br/>1.抽成高，小红书15%，其他渠道都是10%<br/>2.说的是打通公域，实则：共享店铺“申请”按钮点击无反应，联系客服无果，自有店铺：显示“即将上线”<br/>3.完全不支持独立店铺；<br/>4.不支持1080P，花钱也不行，视频模糊</p><p data-pid=\"VmAiPgr0\"><b>10、星橙知道</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-d47a55d136d6cb4d7d5880c247a046bc_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1057\" data-rawheight=\"449\" data-original-token=\"v2-be92b3ca5edab2d242cb46e1155d7555\" class=\"origin_image zh-lightbox-thumb\" width=\"1057\" data-original=\"https://pic1.zhimg.com/v2-d47a55d136d6cb4d7d5880c247a046bc_r.jpg\"/><figcaption>星橙知道</figcaption></figure><p data-pid=\"8f2F--jf\">他家的销售总体来说并没有言过其实，满嘴跑火车，或者可以含糊其辞隐藏什么，缺点：<br/>1.和其他家对比入驻费还是挺贵的；<br/>2.如果你想实现无营业执照，无需自己开店就能挂车卖课，他家还不支持；<br/>3.如果你想要独立店铺，那必须3万的旗舰版才支持，而且还不支持PC独立店铺，成本很高。</p><p data-pid=\"kjkRYwxY\"><b>11、问到课堂</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-4d87abdfb5617dd38a6fa9c76148c05d_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1066\" data-rawheight=\"449\" data-original-token=\"v2-65e1237d6242fdd05bd42594b9b04a97\" class=\"origin_image zh-lightbox-thumb\" width=\"1066\" data-original=\"https://picx.zhimg.com/v2-4d87abdfb5617dd38a6fa9c76148c05d_r.jpg\"/><figcaption>问到课堂</figcaption></figure><p data-pid=\"K7qdb5iS\">销售套路比较多，比如：<br/>1.你问他是否支持1080P，不支持就不支持，他会模棱两可的回复你：支持高清版本；<br/>2.一上来就说999入驻，活动期间买一年送2年，但其实999的版本很多功能不能用，得升级到2999、6999、甚至19999的版本才能用；<br/>3.需要购买6999的版本才能用独立店铺和直播功能，有这价格我都可以直接用小鹅通了；<br/>4.和小鹅通以及其他大多平台一样，很多重要功能和版本绑定，到时候你没办法只能花大几千升级店铺才能用。</p><p data-pid=\"c-l0LpgR\"><b>12、自建卖课平台（以博主@博仔为例）</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-6111deda456c554d956fdd909dd0edc2_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1072\" data-rawheight=\"489\" data-original-token=\"v2-54e1b197d477a2f7a8af5f2a253e1dda\" class=\"origin_image zh-lightbox-thumb\" width=\"1072\" data-original=\"https://pic3.zhimg.com/v2-6111deda456c554d956fdd909dd0edc2_r.jpg\"/><figcaption>自建卖课平台@博仔</figcaption></figure><p data-pid=\"QZXkTWdZ\">自建卖课平台有好多博主在做，比如@沙师兄，@博仔，一般收费价格不菲，如果你不是干IT的，没懂IT技术尽量别碰，因为这里面隐藏的坑太多了。服务器、域名、域名证书、ICP备案、视频转码、CDN、存储包、短信、服务号认证年审￥300、小程序认证年审￥300，虽然大部分也不贵，但需要你操心。</p><p data-pid=\"DvrH4jI2\"><br/>我打个比方，就像你准备开个烧烤夜市，你直接去找个商场或者租个位置好的路边门面房就行了，没必要从建房子开始，市面上我列举了十几家知识付费平台，难道都找不到你满意的？</p><p data-pid=\"FpssVeef\"><br/>自建卖课平台的缺点是收费项目繁杂，有很多隐形的收费项目等到时候你入驻后才发现，另外，上面的@博仔 这个博主里面还有几个收费项目需要你格外注意：</p><p data-pid=\"44JCFtyn\"><br/>1.注意用词，“首年”，他是流量首年送1T，之后就需要你自己购买，对比很多平台比如像素学堂、海豚知道、千聊都是不收流量费的。而且1T流量包真心抠搜，1T流量差不多相当于100个学生把一个10小时的1080P视频课看完一遍就消耗完了。同样扩容也是首年赠送100G。</p><p data-pid=\"yWgydNCI\"><br/>2.开店上架课程服务单独收费，每个渠道1700-2200不等，对比像素学堂，只要不是0元入驻，都免费服务。</p><hr/><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"qNGbaQka\"><b>写在最后：</b></p><p data-pid=\"SQoSqmAm\"><br/>很多人嘲笑老师是割学生韭菜，这些卖课平台是割老师韭菜，其实不无道理，这个行业里确实存在很多割韭菜的平台，我有个数据跟大家分享下，据说某某知道超过50%的老师最后卖课连入驻费都赚不回来。</p><p data-pid=\"yvxesig6\"><br/>我对这个行业的几点非常不满：</p><p data-pid=\"ABHoenpR\"><br/>1.因为某鹅通开了一个坏头，后来的包括易知、课堂街、星橙知道、问道课堂、知识搭档全都是分为什么基础版、专业版、旗舰版，最难受的是他们会把某些重要功能和版本绑定，逼迫你在入驻后只能咬咬牙再花几千块钱升级。<br/>这里面只有千聊和像素学堂是没有这么恶心的，千聊是只有1个版本，￥3699/年，像素学堂虽然有0、999、3980三个版本，但重要功能例如你入驻后想升级为1080P，想要独立店铺，随时可以选购，即便0元入驻也可以选购。</p><p data-pid=\"MVdFVB1S\"><br/>2.过渡迎合老师，牺牲学生学习体验，甚至帮助老师坑蒙拐骗。还是拿视频清晰度举例子，现在免费的视频平台都是1080P了，2K甚至4K屏幕都普及了，但这些平台仍然只支持720P视频，而且把码率压的很低，这样做的好处就是节约成本，但这样做真的对得起花了几百甚至上千块钱买课的学生吗？</p><p data-pid=\"8nSb10jh\"><br/>再比如有些平台还会有“XXX3分钟前购买了课程”的这种假数据功能，还支持老师随意修改自己的课程销量数据，比如课程本来几个人买，为了营造热销的感觉，支持老师在后台配置假数据，比如改为几千人购买，这都是在帮老师作恶。</p><p data-pid=\"b_QljYhB\"><br/>3.理想的模式应该是老师卖课赚到钱了，平台分一点，大家共赢。但是很多平台推出的高价入驻费，完全以销售驱动而不是产品驱动，最后他们公司的营收全靠入驻费，超过一半的老师入驻之后卖课收入没法收回成本。<br/><br/><br/><b>总结：</b><br/>1.如果你不差钱，追求功能丰富强大，选小鹅通<br/>2.如果你重视PC听课体验并且预算有限，选像素学堂<br/>3.如果你的对课程的清晰度有追求，想要1080P，又预算有限，选像素学堂、千聊、荔课<br/>4.如果你想要一个独立的小程序，选小鹅、千聊、课堂街、自建卖课平台<br/>5.如果你想0元入驻，选像素学堂、易知课程、人人讲<br/>6.如果你对私域直播要求比较高，闭眼选小鹅，没有哪家直播技术能和他家比</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 9, "thumbnails": ["https://picx.zhimg.com/50/v2-98822fa311cd34c5cd5d2c3a5219b424_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-b33d8756cc3bfb9eff6d60bf22d8cfd9_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-a1f30fe1d4fdf07832b05f36ef8396eb_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-c1900e1efcb4d853c781741f7f905f53_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-3428f99f926fde4fad61e1610b460cd4_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-7c29a2029461061cf813e7fe78405bfb_720w.jpg?source=b6762063"], "favorite_count": 2, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1921407544175329759}", "attached_info": "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", "action_card": false}, {"id": "47_1750898459.538", "type": "feed", "offset": 47, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898459, "updated_time": 1750898459, "target": {"id": "1917168805316101418", "type": "answer", "url": "https://api.zhihu.com/answers/1917168805316101418", "author": {"id": "46b760f99fdcc4dcdbf46144d173a235", "url": "https://api.zhihu.com/people/46b760f99fdcc4dcdbf46144d173a235", "user_type": "people", "url_token": "craft-know", "name": "奉贤一理工直男", "headline": "于书无所不读，凡物皆有可观。博观而约取，厚积而薄发。", "avatar_url": "https://pic1.zhimg.com/50/v2-a53ecd22784f4301ac6d06e2c3f5c936_l.jpg?source=b6762063", "is_org": false, "gender": 1, "badge": [{"type": "identity_people", "description": "公务机关 从业人员"}], "followers_count": 1389, "is_following": false, "is_followed": false}, "created_time": 1749868662, "updated_time": 1749868662, "voteup_count": 4, "thanks_count": 0, "comment_count": 0, "is_copyable": true, "question": {"id": "655559317", "type": "question", "url": "https://api.zhihu.com/questions/655559317", "author": {"id": "c70d26c78493615bb39a6801e1e4ebe6", "url": "https://api.zhihu.com/people/c70d26c78493615bb39a6801e1e4ebe6", "user_type": "people", "url_token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "居居", "headline": "整点科技选题", "avatar_url": "https://picx.zhimg.com/50/v2-434a2d42f8c1524d5be33022a3ba8296_l.jpg?source=b6762063", "is_org": false, "gender": 0, "followers_count": 289, "is_following": false, "is_followed": false}, "title": "这一波 AI 创业的终局是什么？是少数几家独大还是百花齐放？", "created": 1715323212, "answer_count": 0, "follower_count": 0, "comment_count": 0, "bound_topic_ids": [350, 2143, 5757, 94102, 2431446], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "thumbnail": "https://pic1.zhimg.com/50/v2-22457404949ab8b7e630d4d1a76b3853_720w.jpg?source=b6762063", "excerpt": "看完这31家AI 公司的收入，我对打工彻底绝望 转自公众号AiBot机器人对话 当某互联网大厂还在用 200 人团队苦撑 1 亿营收时，一份内部榜单曝光： 31 家 AI 公司用不超过 50 人的团队，全部实现年均 500 万美元以上收入。 其中 20 人规模的团队人均年创收 279 万美元 —— 这意味着 1 个 AI 工程师的产出，相当于传统行业 10 个资深员工的总和。 在 AI 重构商业规则的今天，这些「小而狠」的团队正在撕开普通人看不见的财富裂缝。   …", "excerpt_new": "看完这31家AI 公司的收入，我对打工彻底绝望 转自公众号AiBot机器人对话 当某互联网大厂还在用 200 人团队苦撑 1 亿营收时，一份内部榜单曝光： 31 家 AI 公司用不超过 50 人的团队，全部实现年均 500 万美元以上收入。 其中 20 人规模的团队人均年创收 279 万美元 —— 这意味着 1 个 AI 工程师的产出，相当于传统行业 10 个资深员工的总和。 在 AI 重构商业规则的今天，这些「小而狠」的团队正在撕开普通人看不见的财富裂缝。   …", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"CRJM_wyS\">看完这31家AI 公司的收入，我对打工彻底绝望</p><p data-pid=\"5BUiwrm2\">转自公众号AiBot机器人对话</p><p data-pid=\"eiDVKpaA\">当某互联网大厂还在用 200 人团队苦撑 1 亿营收时，一份内部榜单曝光：<b>31 家 AI 公司用不超过 50 人的团队，全部实现年均 500 万美元以上收入。</b><br/><br/>其中 20 人规模的团队人均年创收 279 万美元 —— 这意味着 1 个 AI 工程师的产出，相当于传统行业 10 个资深员工的总和。<br/><br/>在 AI 重构商业规则的今天，这些<b>「小而狠」</b>的团队正在撕开普通人看不见的财富裂缝。<br/></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-7a89ef8cae7cfa049357910833a33767_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"913\" data-original-token=\"v2-9e240237bff45fda5fa340915b884f07\" data-default-watermark-src=\"https://pic4.zhimg.com/v2-83a8cbbfdd26aa28cfb13484c5688803_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-7a89ef8cae7cfa049357910833a33767_r.jpg\"/></figure><p data-pid=\"j0X6TPSI\"><br/><br/><b>一、惊悚数据：30 人团队干翻上市公司</b><br/>Telegram 用 30 人团队狂揽 10 亿美元年收入，人均创收 3333 万美元，公司估值 300 亿美金，相当于每个员工扛着 10 亿商业价值。<br/>Midjourney 更狠：40 人团队年收 5 亿美元，零融资却估值千亿，靠「AI 画画」把传统 VC 的「规模扩张论」踩在脚下。<br/><br/>细分赛道更炸裂：Cursor（AI 代码编辑器）20 人 3 年做到 1 亿收入，人均 500 万；Cal Al 这家 4 人小团队，靠 AI 卡路里追踪实现人均 300 万年收。<br/><br/>这些不是 PPT 估值，而是真金白银的现金流 —— 某上市公司 200 人团队累死累活做 1 亿营收，AI 团队直接玩「人数 ÷10，收入 ×10」的降维打击。<br/><br/><b>二、创业核爆：从烧钱游戏到精准猎杀</b><br/>榜单里<b>一半公司没拿过 A 轮融资</b>，Midjourney、SubMagic 这些狠角色甚至零外部投资。SubMagic 创始人直言：「我们没办公室没 PR，就死磕用户痛点。」<br/><br/>Arcads AI 更绝：5 个人配 10 个 AI 助手（管竞品监控、写文案、投广告），年收 500 万，正用 10 人团队冲 1 亿 ——AI 干 70% 的脏活累活，人只做决策，效率比传统模式猛提 15 倍。<br/></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-7087fb145bbaf4c9e6cc77138ee1b263_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"902\" data-rawheight=\"1454\" data-original-token=\"v2-5d660f97d29b8c999ee27ac366861110\" data-default-watermark-src=\"https://pica.zhimg.com/v2-91f761bbaeca4f0307f5c39549801090_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"902\" data-original=\"https://picx.zhimg.com/v2-7087fb145bbaf4c9e6cc77138ee1b263_r.jpg\"/></figure><p data-pid=\"BVP5FmMu\"><br/><br/>华人团队 GPTZero 直接教巨头做人：15 人做 AI 文本检测，年收 1000 万，业绩反超 OpenAI 自家产品。<br/><br/>秘密就在于<b>没大厂包袱</b> —— 当巨头还在走 3 个月审批流程时，小团队 3 天就能迭代上线。医疗领域的 Deveion Health（5 人年收 600 万）、虚拟形象的 Aragon AI（9 人年收 1000 万），全靠轻装上阵在细分领域杀出血路。<br/></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-a575ff863b3daff3c6ef8a17654cddb0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"540\" data-original-token=\"v2-fd04d40d2c35b427fe22b589707050d0\" data-default-watermark-src=\"https://picx.zhimg.com/v2-428464d8ef61acefa81b57722126bb7f_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-a575ff863b3daff3c6ef8a17654cddb0_r.jpg\"/></figure><p data-pid=\"mVLzrF-g\"><br/><br/><b>三、普通人破局：10 人团队干 1 亿的实操密码</b><br/>AI 把创业门槛从「百人团队 + 千万融资」砸到「10 人 + 启动资金」，普通人照抄这 3 步就能上车：<br/><b>1. 专挑巨头看不上的窄缝</b><br/>Rotell AI（7 人）不碰通用大模型，死磕客服场景的 AI 语音技术，年收 720 万；Qieve（4 人）扎进中小企业 AI 报表工具，人均 150 万创收还能盈利。数据证明：在 AI 时代，把一个小场景做透，比摊大饼更容易赚钱。<br/><b>2. 搭建「人机协作」抢钱流水线</b><br/>Arcads AI 的操作手册堪称抢钱指南：用 SpyAgent 实时盯竞品，ChatGPT 批量生产文案，自动化工具直接投广告，人每周只花 2 小时调策略。5 个人干出传统 20 人的活，成本还不到 1/3。<br/><b>3. 把成本砍到骨头里</b><br/>SubMagic 玩「全球远程协作」，运营成本砍掉 60%；Gamma（28 人年收 2000 万）用 AI 生成 PPT，内容成本直降 90%。当传统公司还在为房租和人力愁白头，这些团队早就用「极简成本」实现盈利自由。<br/></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-7188a755f266b1f4d0cdda434c06eb2f_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"464\" data-rawheight=\"315\" data-original-token=\"v2-4978b5b3e9c9ba658282a64aee97ebfc\" data-default-watermark-src=\"https://pic4.zhimg.com/v2-79f5c3266dd41edf0008f88bb18d45e3_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"464\" data-original=\"https://pic4.zhimg.com/v2-7188a755f266b1f4d0cdda434c06eb2f_r.jpg\"/></figure><p data-pid=\"rPGFeohx\"><br/><br/><b>四、打工人惊醒：你的价值正在被重新标价</b><br/>这份榜单最狠的不是数据，而是真相：当 30 人团队能年赚 10 亿，传统「上班换工资」的模式正在崩盘。对普通人来说，必须想清楚 3 件事：<br/></p><ul><li data-pid=\"SRGdSbNd\">你现在干的活，70% 能不能被 AI 替代？ </li><li data-pid=\"yw_11dFK\">你能不能用 AI 把自己效率提高 10 倍？ </li><li data-pid=\"sKbkYL6n\">你发现的某个小需求，能不能用 10 个人落地？ </li></ul><p data-pid=\"Vu633GQX\"><br/>AI 时代赚钱不靠堆人头，而是「精准需求 + AI 杠杆 + 极简团队」的黄金三角。Telegram 30 人干翻 90% 上市公司，Midjourney 40 人撑起千亿估值。<br/>这不是故事，而是在提醒你：如果还在用传统思维打工，你的价值很可能正在被 AI 团队的一个实习生甩在身后。现在不更新赚钱逻辑，未来连被裁员的资格都可能没有。</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 517, "thumbnails": ["https://pic1.zhimg.com/50/v2-22457404949ab8b7e630d4d1a76b3853_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-501c8c06cda6d697c9b1f205125926f8_720w.jpg?source=b6762063"], "favorite_count": 22, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1917168805316101418}", "attached_info": "Cv8HCOXSz7OH4ZH1mwEQBBoJNzMyMTM3MTMxIPbAs8IGKAQwAEAvSkEKLFRTX1NPVVJDRV9UV09UT1dFUl9TSE9SVElOVEVSRVNUX1JFQ0FMTF9URVhUEgEwGAAgADoKeyJyYXciOiIifVoJMTA3OTU3NTAyYiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3ITMTkxNzE2ODgwNTMxNjEwMTQxOIoBCTY1NTU1OTMxN6oBCXJlY29tbWVuZMIBIDQ2Yjc2MGY5OWZkY2M0ZGNkYmY0NjE0NGQxNzNhMjM18gEKCAwSBk5vcm1hbPIBKAgKEiRlM2E0NWI0My1kOTQ1LTQwN2YtODBkNC03NmRjYWU3NzIxOGbyAQUICxIBOIICAIgC0IO5zfoykgIgNDZiNzYwZjk5ZmRjYzRkY2RiZjQ2MTQ0ZDE3M2EyMzWaAgDKAhZTaG9ySW50ZXJlc3RXZWlnaHRSdWxlygIWQWN0aW9uU2hvckludGVyZXN0UnVsZcoCG0ludGVyYWN0aW9uU2hvckludGVyZXN0UnVsZcoCGFBlcmlvZEludGVyZXN0V2VpZ2h0UnVsZcoCFVVzZXJMY25FeGl0V2VpZ2h0UnVsZcoCHEJheWVzRmlyc3RMZXZlbElzb2xhdGlvblJ1bGXaAixUU19TT1VSQ0VfVFdPVE9XRVJfU0hPUlRJTlRFUkVTVF9SRUNBTExfVEVYVOgCA/oCC05PUk1BTF9GTE9XigMgZmRhNzBjZmIzOTZiNGQyNjg1MjhhNzNlZTJmMzdmMDeaAw0KAnYyEAAaBW90aGVyqAOFBNgDAOoDGmZlZWRfYXR0bV90d290b3dlcl92Ml90ZXh0+gPbARIMVU5LTk9XTl9NT0RFIAAqDU5PX0lNQUdFX01PREU6LQgCELgIGJEHIiN2Mi05ZTI0MDIzN2JmZjQ1ZmRhNWZhMzQwOTE1Yjg4NGYwNzotCAIQhgcYrgsiI3YyLTVkNjYwZjk3ZDI5YjhjOTk5ZWUyN2FjMzY2ODYxMTEwOi0IAxC4CBicBCIjdjItZmQwNGQ0MGQyYzM1YjQyN2ZlMjJiNTg5NzA3MDUwZDA6LQgDENADGLsCIiN2Mi00OTc4YjViM2U5YzliYTY1ODI4MmE2NGFlZTk3ZWJmY4AEAIgEAJIEBk5vcm1hbJoEATOgBACoBACwBAC6BAJhacIEAzQwMMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAYLhhvz+BBQAAAAAAAAAAiQWsYdagiXbSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUIkAYAoAYzqAYAkgIuCgk3MzIxMzcxMzESEzE5MTcxNjg4MDUzMTYxMDE0MTgYBCIKSU1BR0VfVEVYVA==", "action_card": false}], "paging": {"is_end": false, "is_start": false, "next": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=down&ad_interval=-10&after_id=47&desktop=true&end_offset=51&page_number=9&session_token=5901993897b2b671534b41dfe1703947", "previous": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=pull&ad_interval=-10&before_id=47&desktop=true&end_offset=51&page_number=9&session_token=5901993897b2b671534b41dfe1703947", "totals": 0}, "fresh_text": "推荐已更新"}