{"data": [{"id": "1591940087288", "type": "feed", "target": {"id": "54430650", "type": "article", "author": {"id": "7877379290d533427cce0d55ea4740e4", "name": "不加班程序员", "headline": "微信公众号：不加班程序员", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/python88", "url_token": "python88", "avatar_url": "https://picx.zhimg.com/v2-e9b3d6a844d1c295598634be7636f1b9_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1547011161, "updated": 1582438173, "title": "110道Python面试题（真题）", "excerpt_title": "", "content": "<p data-pid=\"5CvvPm9l\">python面试题：</p><a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s/SyC_LLQL8AU3i6wYNlOdNQ\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic3.zhimg.com/v2-43e53943fb3d6f6931a880c4cf0a4a08_180x120.jpg\" data-image-width=\"600\" data-image-height=\"255\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">110道python面试题</a><p data-pid=\"vAIAUqQC\"><b>1、一行代码实现1--100之和</b></p><p data-pid=\"QJ7UwMiG\">利用sum()函数求和</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-f8ecb9231a4b558a8c51fcd683de7e6a_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"257\" data-rawheight=\"60\" class=\"content_image\" width=\"257\" data-original-token=\"v2-705b50c78724bdb800cacceb0725a648\"/></figure><p data-pid=\"q9LjSUHg\"><b>2、如何在一个函数内部修改全局变量</b></p><p data-pid=\"bUxczVrh\">利用global在函数声明 修改全局变量</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-11b238f2c4a40c45c526e8a7d67aff92_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"329\" data-rawheight=\"195\" class=\"content_image\" width=\"329\" data-original-token=\"v2-700d8d055a56258b142f24168c544865\"/></figure><p data-pid=\"8Im8goRE\"><b>3、列出5个python标准库</b></p><p data-pid=\"p6aWk5R9\">    os：提供了不少与操作系统相关联的函数</p><p data-pid=\"Cvr5JYxN\">    sys:   通常用于命令行参数</p><p data-pid=\"_-wNidqY\">    re:   正则匹配</p><p data-pid=\"J1z05aPN\">    math: 数学运算</p><p data-pid=\"6nYbUhQH\">    datetime:处理日期时间</p><p data-pid=\"x5FIyP_O\"><b>4、字典如何删除键和合并两个字典</b></p><p data-pid=\"fHBMLWMz\">del和update方法</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-0265e5f95915f8c35a67143a8dcdbd95_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"636\" data-rawheight=\"228\" class=\"origin_image zh-lightbox-thumb\" width=\"636\" data-original=\"https://pic4.zhimg.com/v2-0265e5f95915f8c35a67143a8dcdbd95_r.jpg\" data-original-token=\"v2-54ed727c2bd80a0d6b39aeb6444a6518\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"KxY-BtiY\"><b>5、谈下python的GIL</b></p><p data-pid=\"2ntTznwZ\">    GIL 是python的全局解释器锁，同一进程中假如有多个线程运行，一个线程在运行python程序的时候会霸占python解释器（加了一把锁即GIL），使该进程内的其他线程无法运行，等该线程运行完后其他线程才能运行。如果线程运行过程中遇到耗时操作，则解释器锁解开，使其他线程运行。所以在多线程中，线程的运行仍是有先后顺序的，并不是同时进行。</p><p data-pid=\"J0AEQ4HQ\">多进程中因为每个进程都能被系统分配资源，相当于每个进程有了一个python解释器，所以多进程可以实现多个进程的同时运行，缺点是进程系统资源开销大</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"padJJ68c\"><b>6、python实现列表去重的方法</b></p><p data-pid=\"bnz1L0IU\">先通过集合去重，在转列表</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-b24b0f65f93687dcf200c869d177e23f_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"427\" data-rawheight=\"194\" class=\"origin_image zh-lightbox-thumb\" width=\"427\" data-original=\"https://picx.zhimg.com/v2-b24b0f65f93687dcf200c869d177e23f_r.jpg\" data-original-token=\"v2-4b69fc1742863d45fe54fbd29609647f\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WW9P_Dzh\"><b>7、fun(*args,**kwargs)中的*args,**kwargs什么意思？</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-48a5aab6bbc6a5b4e2cfe04e57a544d4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"891\" data-rawheight=\"503\" class=\"origin_image zh-lightbox-thumb\" width=\"891\" data-original=\"https://pic1.zhimg.com/v2-48a5aab6bbc6a5b4e2cfe04e57a544d4_r.jpg\" data-original-token=\"v2-de6f42d304770badd07aa51d447f26f7\"/></figure><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-8dd376ad89b095777ef80723039b2ab6_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"918\" data-rawheight=\"422\" class=\"origin_image zh-lightbox-thumb\" width=\"918\" data-original=\"https://pica.zhimg.com/v2-8dd376ad89b095777ef80723039b2ab6_r.jpg\" data-original-token=\"v2-6ec5122d6a362550bfe96857d07b22b3\"/></figure><p data-pid=\"LXBtRQQW\"><b>8、python2和python3的range（100）的区别</b></p><p data-pid=\"yrj2yOI_\">    python2返回列表，python3返回迭代器，节约内存</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"QYmG4rG_\"><b>9、一句话解释什么样的语言能够用装饰器?</b></p><p data-pid=\"tOyKJv1K\">    函数可以作为参数传递的语言，可以使用装饰器</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"k9fQhD3X\"><b>10、python内建数据类型有哪些</b></p><p data-pid=\"u7qoQP9D\">    整型--int</p><p data-pid=\"jF0gVhFE\">    布尔型--bool</p><p data-pid=\"xKUOr_uX\">    字符串--str</p><p data-pid=\"dDcEYbBz\">    列表--list</p><p data-pid=\"R6Gv7oIg\">    元组--tuple</p><p data-pid=\"q-napIWx\">    字典--dict</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"tB0BwN_-\"><b>11、简述面向对象中__new__和__init__区别</b></p><p data-pid=\"-ET48qSR\">__init__是初始化方法，创建对象后，就立刻被默认调用了，可接收参数，如图</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-a7738e73633ef0eb19bfb8d0b171f4cd_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"759\" data-rawheight=\"349\" class=\"origin_image zh-lightbox-thumb\" width=\"759\" data-original=\"https://pic4.zhimg.com/v2-a7738e73633ef0eb19bfb8d0b171f4cd_r.jpg\" data-original-token=\"v2-0cccc5e2d4df6a2d785b556d7bd445a7\"/></figure><p data-pid=\"Ed7ahmS3\">1、__new__至少要有一个参数cls，代表当前类，此参数在实例化时由Python解释器自动识别</p><p data-pid=\"Alik4vZb\">2、__new__必须要有返回值，返回实例化出来的实例，这点在自己实现__new__时要特别注意，可以return父类（通过super(当前类名, cls)）__new__出来的实例，或者直接是object的__new__出来的实例</p><p data-pid=\"XSGpZFLX\">3、__init__有一个参数self，就是这个__new__返回的实例，__init__在__new__的基础上可以完成一些其它初始化的动作，__init__不需要返回值</p><p data-pid=\"Rf3a9HA6\">4、如果__new__创建的是当前类的实例，会自动调用__init__函数，通过return语句里面调用的__new__函数的第一个参数是cls来保证是当前类实例，如果是其他类的类名，；那么实际创建返回的就是其他类的实例，其实就不会调用当前类的__init__函数，也不会调用其他类的__init__函数。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-550edacb6fe1881576c492c198e80040_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"397\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-550edacb6fe1881576c492c198e80040_r.jpg\" data-original-token=\"v2-76a7cc9f11637e5eb0ebafd3a8c578c3\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"vH5Ad1ky\"><b>12、简述with方法打开处理文件帮我我们做了什么？</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-a9652f62db3d794256823c82da485d59_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"433\" data-rawheight=\"222\" class=\"origin_image zh-lightbox-thumb\" width=\"433\" data-original=\"https://pic2.zhimg.com/v2-a9652f62db3d794256823c82da485d59_r.jpg\" data-original-token=\"v2-381db6f9c299d084a523f673c74ab73f\"/></figure><p data-pid=\"KS5atV1e\">打开文件在进行读写的时候可能会出现一些异常状况，如果按照常规的f.open</p><p data-pid=\"qvmnASV5\">写法，我们需要try,except,finally，做异常判断，并且文件最终不管遇到什么情况，都要执行finally f.close()关闭文件，with方法帮我们实现了finally中f.close</p><p data-pid=\"kLbsJIXL\">（当然还有其他自定义功能，有兴趣可以研究with方法源码）</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"j7yW2H2c\"><b>13、列表[1,2,3,4,5],请使用map()函数输出[1,4,9,16,25]，并使用列表推导式提取出大于10的数，最终输出[16,25]</b></p><p data-pid=\"quxyUq64\">map（）函数第一个参数是fun，第二个参数是一般是list，第三个参数可以写list，也可以不写，根据需求</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-3d965db5317efc53bed2f2dd71751320_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"472\" data-rawheight=\"321\" class=\"origin_image zh-lightbox-thumb\" width=\"472\" data-original=\"https://pic3.zhimg.com/v2-3d965db5317efc53bed2f2dd71751320_r.jpg\" data-original-token=\"v2-d437c39bb9569ffeb5aa790a353ee7de\"/></figure><p data-pid=\"m0l9h7ms\"><b>14、python中生成随机整数、随机小数、0--1之间小数方法</b></p><p data-pid=\"5gr61HXj\">随机整数：random.randint(a,b),生成区间内的整数</p><p data-pid=\"pAnrK8sS\">随机小数：习惯用numpy库，利用np.random.randn(5)生成5个随机小数</p><p data-pid=\"y7Fu9kTH\">0-1随机小数：random.random(),括号中不传参</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-e333869075c20a70868eb2ac91a4f897_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"984\" data-rawheight=\"367\" class=\"origin_image zh-lightbox-thumb\" width=\"984\" data-original=\"https://pic2.zhimg.com/v2-e333869075c20a70868eb2ac91a4f897_r.jpg\" data-original-token=\"v2-087353a99d354e052230e05907b7a7e8\"/></figure><p data-pid=\"Jd9rlyp2\"><b>15、避免转义给字符串加哪个字母表示原始字符串？</b></p><p data-pid=\"HnttHeae\">r , 表示需要原始字符串，不转义特殊字符</p><p data-pid=\"mMgby04V\"><b>16、&lt;div class=&#34;nam&#34;&gt;中国&lt;/div&gt;，用正则匹配出标签里面的内容（“中国”），其中class的类名是不确定的</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-0098894c183f04a48339b26d465f6459_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"846\" data-rawheight=\"243\" class=\"origin_image zh-lightbox-thumb\" width=\"846\" data-original=\"https://pic4.zhimg.com/v2-0098894c183f04a48339b26d465f6459_r.jpg\" data-original-token=\"v2-ed5a91a4d9c99c9f37b672c6c2c1175a\"/></figure><p data-pid=\"FVuLz4iz\"><b>17、python中断言方法举例</b></p><p data-pid=\"V17tQAc0\">assert（）方法，断言成功，则程序继续执行，断言失败，则程序报错</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-7d496caffd738f080dc5a22cf82a9105_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1034\" data-rawheight=\"453\" class=\"origin_image zh-lightbox-thumb\" width=\"1034\" data-original=\"https://pic2.zhimg.com/v2-7d496caffd738f080dc5a22cf82a9105_r.jpg\" data-original-token=\"v2-9d7a699782139803e535d44c3ad08de7\"/></figure><p data-pid=\"SSWWd6Y_\"><b>18、数据表student有id,name,score,city字段，其中name中的名字可有重复，需要消除重复行,请写sql语句</b></p><p data-pid=\"A-JGYTjH\">    select  distinct  name  from  student</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"a_Rg0otx\"><b>19、10个Linux常用命令</b></p><p data-pid=\"zmdgCmTp\">    ls  pwd  cd  touch  rm  mkdir  tree  cp  mv  cat  more  grep  echo </p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"lMX00Xts\"><b>20、python2和python3区别？列举5个</b></p><p data-pid=\"LsVGzz7D\">    1、Python3 使用 print 必须要以小括号包裹打印内容，比如 print(&#39;hi&#39;)</p><p data-pid=\"P_5jynCq\">    Python2 既可以使用带小括号的方式，也可以使用一个空格来分隔打印内容，比        如 print &#39;hi&#39;</p><p data-pid=\"4dYbncw0\">     2、python2 range(1,10)返回列表，python3中返回迭代器，节约内存</p><p data-pid=\"yrS1XRB0\">    3、python2中使用ascii编码，python中使用utf-8编码</p><p data-pid=\"r-WRGaKV\">    4、python2中unicode表示字符串序列，str表示字节序列</p><p data-pid=\"tNfaQmyP\">      python3中str表示字符串序列，byte表示字节序列</p><p data-pid=\"FmfQqIrg\">    5、python2中为正常显示中文，引入coding声明，python3中不需要</p><p data-pid=\"4p7Xo168\">    6、python2中是raw_input()函数，python3中是input()函数</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Y7ZSSKLU\"><b>21、列出python中可变数据类型和不可变数据类型，并简述原理</b></p><p data-pid=\"aHM_N8Sv\">不可变数据类型：数值型、字符串型string和元组tuple</p><p data-pid=\"Beqr-k3M\">不允许变量的值发生变化，如果改变了变量的值，相当于是新建了一个对象，而对于相同的值的对象，在内存中则只有一个对象（一个地址），如下图用id()方法可以打印对象的id</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-ce7d69f83506cbb6c3330c78e7891145_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"390\" data-rawheight=\"251\" class=\"content_image\" width=\"390\" data-original-token=\"v2-8ecad21cbbc1122caa2a16164e684672\"/></figure><p data-pid=\"shIQk9Xh\">可变数据类型：列表list和字典dict；</p><p data-pid=\"-t_0aylX\">允许变量的值发生变化，即如果对变量进行append、+=等这种操作后，只是改变了变量的值，而不会新建一个对象，变量引用的对象的地址也不会变化，不过对于相同的值的不同对象，在内存中则会存在不同的对象，即每个对象都有自己的地址，相当于内存中对于同值的对象保存了多份，这里不存在引用计数，是实实在在的对象。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-024ed10c48051b00bdd5263c4a5c619d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"318\" data-rawheight=\"235\" class=\"content_image\" width=\"318\" data-original-token=\"v2-8cf19a4bbb813f898306533c3efc210a\"/></figure><p data-pid=\"eL-UcmpN\"><b>22、s = &#34;ajldjlajfdljfddd&#34;，去重并从小到大排序输出&#34;adfjl&#34;</b></p><p data-pid=\"F3WGZdL-\">set去重，去重转成list,利用sort方法排序，reeverse=False是从小到大排</p><p data-pid=\"4VrK-66F\">list是不 变数据类型，s.sort时候没有返回值，所以注释的代码写法不正确</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-290a5084f2086f28f73ba5bef59ab515_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"590\" data-rawheight=\"289\" class=\"origin_image zh-lightbox-thumb\" width=\"590\" data-original=\"https://picx.zhimg.com/v2-290a5084f2086f28f73ba5bef59ab515_r.jpg\" data-original-token=\"v2-ee2d153a3ec86e94828fef171417241e\"/></figure><p data-pid=\"rnMad6tW\"><b>23、用lambda函数实现两个数相乘</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-16c36ade78eecfba4162aa69e3a83c46_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"816\" data-rawheight=\"204\" class=\"origin_image zh-lightbox-thumb\" width=\"816\" data-original=\"https://pic1.zhimg.com/v2-16c36ade78eecfba4162aa69e3a83c46_r.jpg\" data-original-token=\"v2-a4483c4e3968ba782c674a0a1727b1bb\"/></figure><p data-pid=\"bs6JgCNG\"><b>24、字典根据键从小到大排序</b></p><p data-pid=\"vGOpRXy1\">dic={&#34;name&#34;:&#34;zs&#34;,&#34;age&#34;:18,&#34;city&#34;:&#34;深圳&#34;,&#34;tel&#34;:&#34;1362626627&#34;}</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-48bc770043992892d32dbeca1797de5e_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"941\" data-rawheight=\"210\" class=\"origin_image zh-lightbox-thumb\" width=\"941\" data-original=\"https://pic3.zhimg.com/v2-48bc770043992892d32dbeca1797de5e_r.jpg\" data-original-token=\"v2-eb6049d7b55c6991065a63a90105d69b\"/></figure><p data-pid=\"rsJ1N2Lk\"><b>25、利用collections库的Counter方法统计字符串每个单词出现的次数&#34;kjalfj;ldsjafl;hdsllfdhg;lahfbl;hl;ahlf;h&#34;</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-8fee132f1f95c3ba36a60b3c0b6b5fa4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"193\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-8fee132f1f95c3ba36a60b3c0b6b5fa4_r.jpg\" data-original-token=\"v2-0945d47f579a969344158e7124e704e7\"/></figure><p data-pid=\"86gas7D8\"><b>26、字符串a = &#34;not 404 found 张三 99 深圳&#34;，每个词中间是空格，用正则过滤掉英文和数字，最终输出&#34;张三  深圳&#34;</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-bd1cacc332cf481f2ba555fd08036e7f_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"881\" data-rawheight=\"504\" class=\"origin_image zh-lightbox-thumb\" width=\"881\" data-original=\"https://pic4.zhimg.com/v2-bd1cacc332cf481f2ba555fd08036e7f_r.jpg\" data-original-token=\"v2-d9b4b045e1363ca64ae24f835d4246e5\"/></figure><p data-pid=\"XbfxP3UH\">顺便贴上匹配小数的代码，虽然能匹配，但是健壮性有待进一步确认</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-6262c079911bf47e8602ba17a5aa93b2_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"947\" data-rawheight=\"523\" class=\"origin_image zh-lightbox-thumb\" width=\"947\" data-original=\"https://pic3.zhimg.com/v2-6262c079911bf47e8602ba17a5aa93b2_r.jpg\" data-original-token=\"v2-d2acd50c6ae89eb56e76948219a1b4db\"/></figure><p data-pid=\"MfZQsnVq\"><b>27、filter方法求出列表所有奇数并构造新列表，a =  [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]</b></p><p data-pid=\"nLI_hIls\">filter() 函数用于过滤序列，过滤掉不符合条件的元素，返回由符合条件元素组成的新列表。该接收两个参数，第一个为函数，第二个为序列，序列的每个元素作为参数传递给函数进行判，然后返回 True 或 False，最后将返回 True 的元素放到新列表</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-b7f4785f20590acd5cbc9db6f53892b1_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"730\" data-rawheight=\"282\" class=\"origin_image zh-lightbox-thumb\" width=\"730\" data-original=\"https://pic2.zhimg.com/v2-b7f4785f20590acd5cbc9db6f53892b1_r.jpg\" data-original-token=\"v2-b8877f0c807a7cce0627f9088e2f875d\"/></figure><p data-pid=\"cdw17i01\"><b>28、列表推导式求列表所有奇数并构造新列表，a =  [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-b80cfb50ba8569348a4e60de3da7c334_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"676\" data-rawheight=\"135\" class=\"origin_image zh-lightbox-thumb\" width=\"676\" data-original=\"https://pic3.zhimg.com/v2-b80cfb50ba8569348a4e60de3da7c334_r.jpg\" data-original-token=\"v2-86b5f0cf715c39d568bc67b5efb2addb\"/></figure><p data-pid=\"ZRAobZGQ\"><b>29、正则re.complie作用</b></p><p data-pid=\"WsX9QHpU\">re.compile是将正则表达式编译成一个对象，加快速度，并重复使用</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"4jS48Rij\"><b>30、a=（1，）b=(1)，c=(&#34;1&#34;) 分别是什么类型的数据？</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-2e7fc26773c7daf0f100be119e02fbc4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"365\" data-rawheight=\"184\" class=\"content_image\" width=\"365\" data-original-token=\"v2-e5d9bf009f4d9deb8bcfbf153687e8b0\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"hQNZk7OK\"><b>31、两个列表[1,5,7,9]和[2,2,6,8]合并为[1,2,2,3,6,7,8,9]</b></p><p data-pid=\"mUs_aKqf\">extend可以将另一个集合中的元素逐一添加到列表中，区别于append整体添加</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-ad02f5bc07f53b0ab55d326b3e7aea3b_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"573\" data-rawheight=\"345\" class=\"origin_image zh-lightbox-thumb\" width=\"573\" data-original=\"https://pic4.zhimg.com/v2-ad02f5bc07f53b0ab55d326b3e7aea3b_r.jpg\" data-original-token=\"v2-535103b257c9250b4c94f4b620ead3b4\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"w3S2yJzJ\"><b>32、用python删除文件和用linux命令删除文件方法</b></p><p data-pid=\"2ZpJr_OO\">python：os.remove(文件名)</p><p data-pid=\"OuMVWGiP\">linux:       rm  文件名</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"rsy-keDu\"><b>33、log日志中，我们需要用时间戳记录error,warning等的发生时间，请用datetime模块打印当前时间戳 “2018-04-01 11:38:54”</b></p><p data-pid=\"-bsVNVyS\">顺便把星期的代码也贴上了</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-1e7bc11a92c370b2d233ba5bac6805af_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"155\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-1e7bc11a92c370b2d233ba5bac6805af_r.jpg\" data-original-token=\"v2-1e7bc11a92c370b2d233ba5bac6805af\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"mNO1XNeB\"><b>34、数据库优化查询方法</b></p><p data-pid=\"gUUqf1sb\">    外键、索引、联合查询、选择特定字段等等</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wMq-M0Hx\"><b>35、请列出你会的任意一种统计图（条形图、折线图等）绘制的开源库，第三方也行</b></p><p data-pid=\"ySizO4Ks\">    pyecharts、matplotlib</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"r3n2LI8p\"><b>36、写一段自定义异常代码</b></p><p data-pid=\"xY4xQPYs\">自定义异常用raise抛出异常</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-7aae69975c783f19681945c8e50877cd_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"826\" data-rawheight=\"380\" class=\"origin_image zh-lightbox-thumb\" width=\"826\" data-original=\"https://picx.zhimg.com/v2-7aae69975c783f19681945c8e50877cd_r.jpg\" data-original-token=\"v2-8a5854e12ab0f389096ca96a320b7ce5\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"o-fq5tuF\"><b>37、正则表达式匹配中，（.*）和（.*?）匹配区别？</b></p><p data-pid=\"KB4KNQEI\">（.*）是贪婪匹配，会把满足正则的尽可能多的往后匹配</p><p data-pid=\"t2WpZzsM\">（.*?）是非贪婪匹配，会把满足正则的尽可能少匹配</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-6f8cb740cb184f165f8f06f2abbb97f0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"746\" data-rawheight=\"292\" class=\"origin_image zh-lightbox-thumb\" width=\"746\" data-original=\"https://pic1.zhimg.com/v2-6f8cb740cb184f165f8f06f2abbb97f0_r.jpg\" data-original-token=\"v2-882272899470272988b1efc30660d636\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"lyg9dDMH\"><b>38、简述Django的orm</b></p><p data-pid=\"6hQkyuI7\">ORM，全拼Object-Relation Mapping，意为对象-关系映射</p><p data-pid=\"CPK-Y-D5\">实现了数据模型与数据库的解耦，通过简单的配置就可以轻松更换数据库，而不需要修改代码只需要面向对象编程,orm操作本质上会根据对接的数据库引擎，翻译成对应的sql语句,所有使用Django开发的项目无需关心程序底层使用的是MySQL、Oracle、sqlite....，如果数据库迁移，只需要更换Django的数据库引擎即可</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-6691dd8adc3792e16279e8fa06bac422_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"989\" data-rawheight=\"419\" class=\"origin_image zh-lightbox-thumb\" width=\"989\" data-original=\"https://pic1.zhimg.com/v2-6691dd8adc3792e16279e8fa06bac422_r.jpg\" data-original-token=\"v2-4a29c08a7fb6863a61d947225156c47b\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Vd4QhHS2\"><b>39、[[1,2],[3,4],[5,6]]一行代码展开该列表，得出[1,2,3,4,5,6]</b></p><p data-pid=\"rUyeH0Ys\">列表推导式的骚操作</p><p data-pid=\"8xM6B8qZ\">运行过程：for i in a ,每个i是【1,2】，【3,4】，【5,6】，for j in i，每个j就是1,2,3,4,5,6,合并后就是结果</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-e4f296d94599b217b69d7503e3dc7aa6_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"600\" data-rawheight=\"218\" class=\"origin_image zh-lightbox-thumb\" width=\"600\" data-original=\"https://pic1.zhimg.com/v2-e4f296d94599b217b69d7503e3dc7aa6_r.jpg\" data-original-token=\"v2-e90867bdf112b6ddf64ba10ba9f36ba1\"/></figure><p data-pid=\"35c5M9TQ\">还有更骚的方法，将列表转成numpy矩阵，通过numpy的flatten（）方法，代码永远是只有更骚，没有最骚</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-6b57b78359b5c651f143ae88d9c31939_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"718\" data-rawheight=\"387\" class=\"origin_image zh-lightbox-thumb\" width=\"718\" data-original=\"https://pic2.zhimg.com/v2-6b57b78359b5c651f143ae88d9c31939_r.jpg\" data-original-token=\"v2-ff077505e4e8207d86148783f860242a\"/></figure><p data-pid=\"FbPK9i52\"><b>40、x=&#34;abc&#34;,y=&#34;def&#34;,z=[&#34;d&#34;,&#34;e&#34;,&#34;f&#34;],分别求出x.join(y)和x.join(z)返回的结果</b></p><p data-pid=\"TbJCvnLP\">join()括号里面的是可迭代对象，x插入可迭代对象中间，形成字符串，结果一致，有没有突然感觉字符串的常见操作都不会玩了</p><p data-pid=\"68CkLnZj\">顺便建议大家学下os.path.join()方法，拼接路径经常用到，也用到了join,和字符串操作中的join有什么区别，该问题大家可以查阅相关文档，后期会有答案</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-efd298ae334ceac82079545345734d72_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"502\" data-rawheight=\"387\" class=\"origin_image zh-lightbox-thumb\" width=\"502\" data-original=\"https://pic3.zhimg.com/v2-efd298ae334ceac82079545345734d72_r.jpg\" data-original-token=\"v2-df25020dfcbda96bb50e56aa776aaf7f\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"VpgAe12N\"><b>41、举例说明异常模块中try except else finally的相关意义</b></p><p data-pid=\"-JDFoRXj\">try..except..else没有捕获到异常，执行else语句</p><p data-pid=\"GCM6qxY9\">try..except..finally不管是否捕获到异常，都执行finally语句</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-6e51a1d67aa69f6b0f8332c35f534afe_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"863\" data-rawheight=\"588\" class=\"origin_image zh-lightbox-thumb\" width=\"863\" data-original=\"https://pica.zhimg.com/v2-6e51a1d67aa69f6b0f8332c35f534afe_r.jpg\" data-original-token=\"v2-be874549047f7a25da04dd0f607bf892\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"zMRzG3zO\"><b>42、python中交换两个数值</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-baef943baca9ba2c621d6c38445311f2_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"443\" data-rawheight=\"275\" class=\"origin_image zh-lightbox-thumb\" width=\"443\" data-original=\"https://pica.zhimg.com/v2-baef943baca9ba2c621d6c38445311f2_r.jpg\" data-original-token=\"v2-a1f0f9c73ca15dfa5ddf41564d06645f\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"OXerYvGL\"><b>43、举例说明zip（）函数用法</b></p><p data-pid=\"tmhoky8T\">zip()函数在运算时，会以一个或多个序列（可迭代对象）做为参数，返回一个元组的列表。同时将这些序列中并排的元素配对。</p><p data-pid=\"URr8jIfR\">zip()参数可以接受任何类型的序列，同时也可以有两个以上的参数;当传入参数的长度不同时，zip能自动以最短序列长度为准进行截取，获得元组。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-6e29d7a331e179f082f2efad79fc350b_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"773\" data-rawheight=\"559\" class=\"origin_image zh-lightbox-thumb\" width=\"773\" data-original=\"https://pic2.zhimg.com/v2-6e29d7a331e179f082f2efad79fc350b_r.jpg\" data-original-token=\"v2-68848822138dc0b9c8d4bca40e23bd6d\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"9tqyPCBj\"><b>44、a=&#34;张明 98分&#34;，用re.sub，将98替换为100</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-fb1fb9875124832980e31d29102c7fd7_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"654\" data-rawheight=\"183\" class=\"origin_image zh-lightbox-thumb\" width=\"654\" data-original=\"https://pic4.zhimg.com/v2-fb1fb9875124832980e31d29102c7fd7_r.jpg\" data-original-token=\"v2-7cdb95ccd8c422910212376aa6bf01d6\"/></figure><p data-pid=\"hQ30sH4W\"><b>45、写5条常用sql语句</b></p><p data-pid=\"8-Y3Og8T\">show databases;</p><p data-pid=\"0uy3UrA-\">show tables;</p><p data-pid=\"wGVdQpga\">desc 表名;</p><p data-pid=\"ij-HlQ07\">select * from 表名;</p><p data-pid=\"qb4eT4_Q\">delete from 表名 where id=5;</p><p data-pid=\"1eC2xLul\">update students set gender=0,hometown=&#34;北京&#34; where id=5</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"XPU_OHxJ\"><b>46、a=&#34;hello&#34;和b=&#34;你好&#34;编码成bytes类型</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-9dd4fd715a521aa6ef547e05c6a61c75_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"679\" data-rawheight=\"311\" class=\"origin_image zh-lightbox-thumb\" width=\"679\" data-original=\"https://pic2.zhimg.com/v2-9dd4fd715a521aa6ef547e05c6a61c75_r.jpg\" data-original-token=\"v2-718fcf3b02b975ccce88053631cb6bd4\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"MD9W1EAB\"><b>47、[1,2,3]+[4,5,6]的结果是多少？</b></p><p data-pid=\"08fXusJC\">两个列表相加，等价于extend</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-ce586b3697256afff94998134e010591_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"534\" data-rawheight=\"221\" class=\"origin_image zh-lightbox-thumb\" width=\"534\" data-original=\"https://pic2.zhimg.com/v2-ce586b3697256afff94998134e010591_r.jpg\" data-original-token=\"v2-119d0efaec63511975049e9e615e0a93\"/></figure><p data-pid=\"GXeXLp0O\"><b>48、提高python运行效率的方法</b></p><p data-pid=\"2KxPB01O\">1、使用生成器，因为可以节约大量内存</p><p data-pid=\"5V-hI9yT\">2、循环代码优化，避免过多重复代码的执行</p><p data-pid=\"y6jsFAZw\">3、核心模块用Cython  PyPy等，提高效率</p><p data-pid=\"HBovR5XV\">4、多进程、多线程、协程</p><p data-pid=\"m2P6HztP\">5、多个if elif条件判断，可以把最有可能先发生的条件放到前面写，这样可以减少程序判断的次数，提高效率</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ZUKe3WIf\"><b>49、简述mysql和redis区别</b></p><p data-pid=\"8DMXrAUX\">redis： 内存型非关系数据库，数据保存在内存中，速度快</p><p data-pid=\"CS0xwZ3n\">mysql：关系型数据库，数据保存在磁盘中，检索的话，会有一定的Io操作，访问速度相对慢</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"YnIJW6sq\"><b>50、遇到bug如何处理</b></p><p data-pid=\"AU3o1xZi\">1、细节上的错误，通过print（）打印，能执行到print（）说明一般上面的代码没有问题，分段检测程序是否有问题，如果是js的话可以alert或console.log</p><p data-pid=\"4wVH7XbO\">2、如果涉及一些第三方框架，会去查官方文档或者一些技术博客。</p><p data-pid=\"YGiD9t2A\">3、对于bug的管理与归类总结，一般测试将测试出的bug用teambin等bug管理工具进行记录，然后我们会一条一条进行修改，修改的过程也是理解业务逻辑和提高自己编程逻辑缜密性的方法，我也都会收藏做一些笔记记录。</p><p data-pid=\"CjiCIcw_\">4、导包问题、城市定位多音字造成的显示错误问题</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"C6qzMIgY\"><b>51、正则匹配，匹配日期2018-03-20</b></p><p data-pid=\"7HCzlc3_\">url=&#39;<a href=\"https://link.zhihu.com/?target=https%3A//sycm.taobao.com/bda/tradinganaly/overview/get_summary.json%3FdateRange%3D2018-03-20%257C2018-03-20%26dateType%3Drecent1%26device%3D1%26token%3Dff25b109b%26_%3D1521595613462\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">sycm.taobao.com/bda/tra</span><span class=\"invisible\">dinganaly/overview/get_summary.json?dateRange=2018-03-20%7C2018-03-20&amp;dateType=recent1&amp;device=1&amp;token=ff25b109b&amp;_=1521595613462</span><span class=\"ellipsis\"></span></a>&#39;</p><p data-pid=\"z1xH0xYC\">仍有同学问正则，其实匹配并不难，提取一段特征语句，用（.*?）匹配即可</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-3deec7c53fba28fe15e4d368b7c334fe_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"157\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-3deec7c53fba28fe15e4d368b7c334fe_r.jpg\" data-original-token=\"v2-3deec7c53fba28fe15e4d368b7c334fe\"/></figure><p data-pid=\"v_LqBWRe\"><b>52、list=[2,3,5,4,9,6]，从小到大排序，不许用sort，输出[2,3,4,5,6,9]</b></p><p data-pid=\"fHJjTgY5\">利用min()方法求出最小值，原列表删除最小值，新列表加入最小值，递归调用获取最小值的函数，反复操作</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-1ca15986ba67b76c5c32d4262dbe4d72_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"696\" data-rawheight=\"495\" class=\"origin_image zh-lightbox-thumb\" width=\"696\" data-original=\"https://pica.zhimg.com/v2-1ca15986ba67b76c5c32d4262dbe4d72_r.jpg\" data-original-token=\"v2-7b57bdd150f93554547c85c3ce9e5846\"/></figure><p data-pid=\"jcX1rnRl\"><b>53、写一个单列模式</b></p><p data-pid=\"UtNfQ3BK\">因为创建对象时__new__方法执行，并且必须return 返回实例化出来的对象所cls.__instance是否存在，不存在的话就创建对象，存在的话就返回该对象，来保证只有一个实例对象存在（单列），打印ID，值一样，说明对象同一个</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-ddc41190082913f2271a935b9b7f0299_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"603\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-ddc41190082913f2271a935b9b7f0299_r.jpg\" data-original-token=\"v2-13bb9a9c2b0cae73c99d60c9f1f7ac05\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"HyLBfSXW\"><b>54、保留两位小数</b></p><p data-pid=\"rjmcFLXa\">题目本身只有a=&#34;%.03f&#34;%1.3335,让计算a的结果，为了扩充保留小数的思路，提供round方法（数值，保留位数）</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-ff47f01de4060b74e4c0d88170082ccf_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"755\" data-rawheight=\"394\" class=\"origin_image zh-lightbox-thumb\" width=\"755\" data-original=\"https://pic2.zhimg.com/v2-ff47f01de4060b74e4c0d88170082ccf_r.jpg\" data-original-token=\"v2-1b5bfd76391159eb433bf6cc72bc8bf7\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Mk05cUZm\"><b>55、求三个方法打印结果</b></p><p data-pid=\"jrlSv-pe\">fn(&#34;one&#34;,1）直接将键值对传给字典；</p><p data-pid=\"Lt-yV-EN\">fn(&#34;two&#34;,2)因为字典在内存中是可变数据类型，所以指向同一个地址，传了新的额参数后，会相当于给字典增加键值对</p><p data-pid=\"myreEbTd\">fn(&#34;three&#34;,3,{})因为传了一个新字典，所以不再是原先默认参数的字典</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-80d4e8bb26e97fc3384a7238800e9361_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"657\" data-rawheight=\"308\" class=\"origin_image zh-lightbox-thumb\" width=\"657\" data-original=\"https://pic4.zhimg.com/v2-80d4e8bb26e97fc3384a7238800e9361_r.jpg\" data-original-token=\"v2-287649fe9f1f8c394b892e536ac1711f\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"MWwQ7Dll\"><b>56、列出常见的状态码和意义</b></p><p data-pid=\"wiNMP9cJ\">200 OK </p><p data-pid=\"Y-JBRRPu\">请求正常处理完毕</p><p data-pid=\"g6hxnEiU\">204 No Content </p><p data-pid=\"N0QW21hM\">请求成功处理，没有实体的主体返回</p><p data-pid=\"Uzr24aAh\">206 Partial Content </p><p data-pid=\"VuOPBZMe\">GET范围请求已成功处理</p><p data-pid=\"6B7v1u1-\">301 Moved Permanently </p><p data-pid=\"wgUUAM0V\">永久重定向，资源已永久分配新URI</p><p data-pid=\"Qrq2Wa6g\">302 Found </p><p data-pid=\"bUpIi3rV\">临时重定向，资源已临时分配新URI</p><p data-pid=\"HVSPNcPA\">303 See Other </p><p data-pid=\"RXY6W9X7\">临时重定向，期望使用GET定向获取</p><p data-pid=\"NrABkblG\">304 Not Modified </p><p data-pid=\"B3TWL1Wy\">发送的附带条件请求未满足</p><p data-pid=\"VoZj5tsd\">307 Temporary Redirect </p><p data-pid=\"h13F0Uqv\">临时重定向，POST不会变成GET</p><p data-pid=\"6ayWzALa\">400 Bad Request </p><p data-pid=\"AFxZbA4o\">请求报文语法错误或参数错误</p><p data-pid=\"RKrKg1XQ\">401 Unauthorized </p><p data-pid=\"_VIgKuW0\">需要通过HTTP认证，或认证失败</p><p data-pid=\"CziBKMHj\">403 Forbidden </p><p data-pid=\"bYxaIERB\">请求资源被拒绝</p><p data-pid=\"pfJRcc2H\">404 Not Found </p><p data-pid=\"1Id2qdbO\">无法找到请求资源（服务器无理由拒绝）</p><p data-pid=\"sp1BuEg7\">500 Internal Server Error </p><p data-pid=\"D9wSejIV\">服务器故障或Web应用故障</p><p data-pid=\"cuvSo7As\">503 Service Unavailable </p><p data-pid=\"wtV76u7l\">服务器超负载或停机维护</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"yhftLqW1\"><b>57、分别从前端、后端、数据库阐述web项目的性能优化</b></p><p data-pid=\"2WI1t6to\">该题目网上有很多方法，我不想截图网上的长串文字，看的头疼，按我自己的理解说几点</p><p data-pid=\"HenajAdm\"><b>前端优化：</b></p><p data-pid=\"R0_vVkfb\">1、减少http请求、例如制作精灵图</p><p data-pid=\"4rBLnZr7\">2、html和CSS放在页面上部，javascript放在页面下面，因为js加载比HTML和Css加载慢，所以要优先加载html和css,以防页面显示不全，性能差，也影响用户体验差</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"_zrs4IYG\"><b>后端优化：</b></p><p data-pid=\"e6KaSH3v\">1、缓存存储读写次数高，变化少的数据，比如网站首页的信息、商品的信息等。应用程序读取数据时，一般是先从缓存中读取，如果读取不到或数据已失效，再访问磁盘数据库，并将数据再次写入缓存。</p><p data-pid=\"p7MJH4y_\">2、异步方式，如果有耗时操作，可以采用异步，比如celery</p><p data-pid=\"LlNasz1m\">3、代码优化，避免循环和判断次数太多，如果多个if else判断，优先判断最有可能先发生的情况</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"N2M4lN4v\"><b>数据库优化：</b></p><p data-pid=\"azeQ72kU\">1、如有条件，数据可以存放于redis，读取速度快</p><p data-pid=\"ssfuQpu1\">2、建立索引、外键等</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"RSWBfIdu\"><b>58、使用pop和del删除字典中的&#34;name&#34;字段，dic={&#34;name&#34;:&#34;zs&#34;,&#34;age&#34;:18}</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-8e8189b589d2036c6280f71dad867a4f_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"778\" data-rawheight=\"285\" class=\"origin_image zh-lightbox-thumb\" width=\"778\" data-original=\"https://pic4.zhimg.com/v2-8e8189b589d2036c6280f71dad867a4f_r.jpg\" data-original-token=\"v2-855a7d5ab0bd3f87e68705cafd4ce6bf\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ul-dt_O2\"><b>59、列出常见MYSQL数据存储引擎</b></p><p data-pid=\"RrWTo9J9\"><b>InnoDB</b>：支持事务处理，支持外键，支持崩溃修复能力和并发控制。如果需要对事务的完整性要求比较高（比如银行），要求实现并发控制（比如售票），那选择InnoDB有很大的优势。如果需要频繁的更新、删除操作的数据库，也可以选择InnoDB，因为支持事务的提交（commit）和回滚（rollback）。 </p><p data-pid=\"QuG1BjxB\"><b>MyISAM</b>：插入数据快，空间和内存使用比较低。如果表主要是用于插入新记录和读出记录，那么选择MyISAM能实现处理高效率。如果应用的完整性、并发性要求比 较低，也可以使用。</p><p data-pid=\"jkYgpVNx\"><b>MEMORY</b>：所有的数据都在内存中，数据的处理速度快，但是安全性不高。如果需要很快的读写速度，对数据的安全性要求较低，可以选择MEMOEY。它对表的大小有要求，不能建立太大的表。所以，这类数据库只使用在相对较小的数据库表。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"DdNPYZfS\"><b>60、计算代码运行结果，zip函数历史文章已经说了，得出[(&#34;a&#34;,1),(&#34;b&#34;,2)，(&#34;c&#34;,3),(&#34;d&#34;,4),(&#34;e&#34;,5)]</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-3becbb671b27ea573732eb0beb514ce9_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"845\" data-rawheight=\"356\" class=\"origin_image zh-lightbox-thumb\" width=\"845\" data-original=\"https://pic2.zhimg.com/v2-3becbb671b27ea573732eb0beb514ce9_r.jpg\" data-original-token=\"v2-522b4232bb5921ef63f33c59688cd8a3\"/></figure><p data-pid=\"Z1bZkfbO\">dict()创建字典新方法</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-568912f2502524e8f688d553c25641e3_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"981\" data-rawheight=\"495\" class=\"origin_image zh-lightbox-thumb\" width=\"981\" data-original=\"https://pic2.zhimg.com/v2-568912f2502524e8f688d553c25641e3_r.jpg\" data-original-token=\"v2-99115c549379aa0c5a9d322e76d00aae\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"EOrzjpCv\"><b>61、简述同源策略</b></p><p data-pid=\"V0HoBF9N\"> 同源策略需要同时满足以下三点要求： </p><p data-pid=\"aYOmT7Qp\">1）协议相同 </p><p data-pid=\"NmU09Dad\"> 2）域名相同 </p><p data-pid=\"x1j309xS\">3）端口相同 </p><p data-pid=\"lRDwXsjJ\"> http:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com</span><span class=\"invisible\"></span></a>与https:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com</span><span class=\"invisible\"></span></a> 不同源——协议不同 </p><p data-pid=\"TfLgNKOu\"> http:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com</span><span class=\"invisible\"></span></a>与http:<a href=\"https://link.zhihu.com/?target=http%3A//www.admin.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">admin.com</span><span class=\"invisible\"></span></a> 不同源——域名不同 </p><p data-pid=\"QUhhsoQI\"> http:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com</span><span class=\"invisible\"></span></a>与http:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com%3A8081\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com:8081</span><span class=\"invisible\"></span></a> 不同源——端口不同</p><p data-pid=\"mjVOsMCQ\"> 只要不满足其中任意一个要求，就不符合同源策略，就会出现“跨域”</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"GE4VmzJx\"><b>62、简述cookie和session的区别</b></p><p data-pid=\"xGVxIJ1M\">1，session 在服务器端，cookie 在客户端（浏览器）</p><p data-pid=\"r69d1JWI\">2、session 的运行依赖 session id，而 session id 是存在 cookie 中的，也就是说，如果浏览器禁用了 cookie ，同时 session 也会失效，存储Session时，键与Cookie中的sessionid相同，值是开发人员设置的键值对信息，进行了base64编码，过期时间由开发人员设置</p><p data-pid=\"CFVwq5J_\">3、cookie安全性比session差</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TvNdn4Ap\"><b>63、简述多线程、多进程</b></p><p data-pid=\"BTFCipwL\"><b>进程：</b></p><p data-pid=\"yAMoup4q\">1、操作系统进行资源分配和调度的基本单位，多个进程之间相互独立</p><p data-pid=\"ZlujXz8Y\">2、稳定性好，如果一个进程崩溃，不影响其他进程，但是进程消耗资源大，开启的进程数量有限制</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"YkCSg-2W\"><b>线程：</b></p><p data-pid=\"uMie7wJ8\">1、CPU进行资源分配和调度的基本单位，线程是进程的一部分，是比进程更小的能独立运行的基本单位，一个进程下的多个线程可以共享该进程的所有资源</p><p data-pid=\"iaWnE3cE\">2、如果IO操作密集，则可以多线程运行效率高，缺点是如果一个线程崩溃，都会造成进程的崩溃</p><p data-pid=\"MDyxNPIi\"><b>应用：</b></p><p data-pid=\"KlmHD1gb\">IO密集的用多线程，在用户输入，sleep 时候，可以切换到其他线程执行，减少等待的时间</p><p data-pid=\"MU0pDdrl\">CPU密集的用多进程，因为假如IO操作少，用多线程的话，因为线程共享一个全局解释器锁，当前运行的线程会霸占GIL，其他线程没有GIL，就不能充分利用多核CPU的优势</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"6UpFwEA0\"><b>64、简述any()和all()方法</b></p><p data-pid=\"cvrV2cRF\">any():只要迭代器中有一个元素为真就为真</p><p data-pid=\"uZOq6pIA\">all():迭代器中所有的判断项返回都是真，结果才为真</p><p data-pid=\"UHBoX0Ii\">python中什么元素为假？</p><p data-pid=\"95oLBOVh\">答案：（0，空字符串，空列表、空字典、空元组、None, False）</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-a29b93463b95cbb30bc9b0bdf68d22d5_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"239\" data-rawheight=\"406\" class=\"content_image\" width=\"239\" data-original-token=\"v2-aaa6370df990a208f7999ed3fa7018e4\"/></figure><p data-pid=\"WCenIBtd\">测试all()和any()方法</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-b65bda1c52cb62eea5a23384be3839e4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"655\" data-rawheight=\"741\" class=\"origin_image zh-lightbox-thumb\" width=\"655\" data-original=\"https://pic3.zhimg.com/v2-b65bda1c52cb62eea5a23384be3839e4_r.jpg\" data-original-token=\"v2-30c661e8dfd25dc73aa31205d7a535a6\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"cjBh03qn\"><b>65、IOError、AttributeError、ImportError、IndentationError、IndexError、KeyError、SyntaxError、NameError分别代表什么异常</b></p><p data-pid=\"TWuZU5K7\">IOError：输入输出异常</p><p data-pid=\"2rHYp5HG\">AttributeError：试图访问一个对象没有的属性</p><p data-pid=\"0Nu_29Ag\">ImportError：无法引入模块或包，基本是路径问题</p><p data-pid=\"LIGunM43\">IndentationError：语法错误，代码没有正确的对齐</p><p data-pid=\"Xe9sTUEU\">IndexError：下标索引超出序列边界</p><p data-pid=\"w-u519wF\">KeyError:试图访问你字典里不存在的键</p><p data-pid=\"iGYqPNan\">SyntaxError:Python代码逻辑语法出错，不能执行</p><p data-pid=\"hqM8LgSr\">NameError:使用一个还未赋予对象的变量</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"LzeSlf4l\"><b>66、python中copy和deepcopy区别</b></p><p data-pid=\"wGUtRmkc\">1、复制不可变数据类型，不管copy还是deepcopy,都是同一个地址当浅复制的值是不可变对象（数值，字符串，元组）时和=“赋值”的情况一样，对象的id值与浅复制原来的值相同。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-a5e9735f28c7fb2b8fdc586bc2c22427_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"770\" data-rawheight=\"417\" class=\"origin_image zh-lightbox-thumb\" width=\"770\" data-original=\"https://picx.zhimg.com/v2-a5e9735f28c7fb2b8fdc586bc2c22427_r.jpg\" data-original-token=\"v2-e6d997cfe6648e832d4fede66a2c3405\"/></figure><p data-pid=\"n7LM5rgP\">2、复制的值是可变对象（列表和字典）</p><p data-pid=\"0YHzXGVE\">浅拷贝copy有两种情况：</p><p data-pid=\"ifrTZDaV\">第一种情况：复制的 对象中无 复杂 子对象，原来值的改变并不会影响浅复制的值，同时浅复制的值改变也并不会影响原来的值。原来值的id值与浅复制原来的值不同。</p><p data-pid=\"pSd-9WGK\">第二种情况：复制的对象中有 复杂 子对象 （例如列表中的一个子元素是一个列表）， 改变原来的值 中的复杂子对象的值  ，会影响浅复制的值。</p><p data-pid=\"gAz-w80Z\">深拷贝deepcopy：完全复制独立，包括内层列表和字典</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-6d5c879af339a0c35a925648ba3afadf_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"545\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-6d5c879af339a0c35a925648ba3afadf_r.jpg\" data-original-token=\"v2-105cdcf5006394d7c06fddf490428075\"/></figure><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-bb5abd389e0b9cd1b3b163897e623fe7_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"354\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-bb5abd389e0b9cd1b3b163897e623fe7_r.jpg\" data-original-token=\"v2-a4baebbe34ef0e7170fdd822256f6392\"/></figure><p data-pid=\"wR07DSUC\"><b>67、列出几种魔法方法并简要介绍用途</b></p><p data-pid=\"Wni9lrPg\">__init__:对象初始化方法</p><p data-pid=\"1Qmuvu2W\">__new__:创建对象时候执行的方法，单列模式会用到</p><p data-pid=\"Z7caB_uj\">__str__:当使用print输出对象的时候，只要自己定义了__str__(self)方法，那么就会打印从在这个方法中return的数据</p><p data-pid=\"Whs8kw2w\">__del__:删除对象执行的方法</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"m3CO0y5n\"><b>68、C:\\Users\\<USER>\\Desktop&gt;python 1.py 22 33命令行启动程序并传参，print(sys.argv)会输出什么数据？</b></p><p data-pid=\"eEnarajA\">文件名和参数构成的列表</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-29361d7dd6cc2a70e6080047cf779bcd_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"511\" data-rawheight=\"67\" class=\"origin_image zh-lightbox-thumb\" width=\"511\" data-original=\"https://pic2.zhimg.com/v2-29361d7dd6cc2a70e6080047cf779bcd_r.jpg\" data-original-token=\"v2-29361d7dd6cc2a70e6080047cf779bcd\"/></figure><p data-pid=\"QRiTqBZW\"><b>69、请将[i for i in range(3)]改成生成器</b></p><p data-pid=\"JJTX6K-s\">生成器是特殊的迭代器，</p><p data-pid=\"vOGNhxcb\">1、列表表达式的【】改为（）即可变成生成器</p><p data-pid=\"Vq97vQax\">2、函数在返回值得时候出现yield就变成生成器，而不是函数了；</p><p data-pid=\"_HRCRiLJ\">中括号换成小括号即可，有没有惊呆了</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-db416f76770c675609b75a74135f1979_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"450\" data-rawheight=\"147\" class=\"origin_image zh-lightbox-thumb\" width=\"450\" data-original=\"https://picx.zhimg.com/v2-db416f76770c675609b75a74135f1979_r.jpg\" data-original-token=\"v2-d6c3a48e57e7acf45f785a57cad36cd1\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"o-mDcyTv\"><b>70、a = &#34;  hehheh  &#34;,去除收尾空格</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-9ecb056190485d46c23cb4cc039fb963_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"327\" data-rawheight=\"141\" class=\"content_image\" width=\"327\" data-original-token=\"v2-03e5f7879c68a779762bb8e59e09f0ab\"/></figure><p data-pid=\"i2kClkAZ\"><b>71、举例sort和sorted对列表排序，list=[0,-1,3,-10,5,9]</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-d4497b05cc42514e38b95fc00f79afe4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"842\" data-rawheight=\"380\" class=\"origin_image zh-lightbox-thumb\" width=\"842\" data-original=\"https://pica.zhimg.com/v2-d4497b05cc42514e38b95fc00f79afe4_r.jpg\" data-original-token=\"v2-44a969b5adf63cafcc1150cca1df3301\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"dZTfIFJd\"><b>72、对list排序foo = [-5,8,0,4,9,-4,-20,-2,8,2,-4],使用lambda函数从小到大排序</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-0b3786be700e8a7f5acf4ccef27a9359_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"704\" data-rawheight=\"202\" class=\"origin_image zh-lightbox-thumb\" width=\"704\" data-original=\"https://pic4.zhimg.com/v2-0b3786be700e8a7f5acf4ccef27a9359_r.jpg\" data-original-token=\"v2-9260e0bb8730df43bf7e10c8edac25ab\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"M1O9b-5V\"><b>73、使用lambda函数对list排序foo = [-5,8,0,4,9,-4,-20,-2,8,2,-4]，输出结果为</b></p><p data-pid=\"g8qHST1X\"><b>[0,2,4,8,8,9,-2,-4,-4,-5,-20]，正数从小到大，负数从大到小</b></p><p data-pid=\"07G5m787\">（传两个条件，x&lt;0和abs(x)）</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-b96b7d4ff4e49a69fedca0a2cc16f880_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"741\" data-rawheight=\"205\" class=\"origin_image zh-lightbox-thumb\" width=\"741\" data-original=\"https://pica.zhimg.com/v2-b96b7d4ff4e49a69fedca0a2cc16f880_r.jpg\" data-original-token=\"v2-f6e6f5d3591575c3d7d387782be394de\"/></figure><p data-pid=\"Ox9jiYCF\"><b>74、列表嵌套字典的排序，分别根据年龄和姓名排序</b></p><p data-pid=\"Mx6595iC\">foo = [{&#34;name&#34;:&#34;zs&#34;,&#34;age&#34;:19},{&#34;name&#34;:&#34;ll&#34;,&#34;age&#34;:54},</p><p data-pid=\"NLUMrpPq\">        {&#34;name&#34;:&#34;wa&#34;,&#34;age&#34;:17},{&#34;name&#34;:&#34;df&#34;,&#34;age&#34;:23}]</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-17daf4837bafb07c884bbf5fa3cf6b73_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"322\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-17daf4837bafb07c884bbf5fa3cf6b73_r.jpg\" data-original-token=\"v2-dab81670c0635136d38c7fd67186be39\"/></figure><p data-pid=\"RN0HMyu9\"><b>75、列表嵌套元组，分别按字母和数字排序</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-cdc06f1f5b50f8c47911993080769bfe_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"794\" data-rawheight=\"322\" class=\"origin_image zh-lightbox-thumb\" width=\"794\" data-original=\"https://pica.zhimg.com/v2-cdc06f1f5b50f8c47911993080769bfe_r.jpg\" data-original-token=\"v2-20cc603e170c39db53b94fb16910731c\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"VonSPLJl\"><b>76、列表嵌套列表排序，年龄数字相同怎么办？</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-acd639c002d9f79e368e1352a86a9fff_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"278\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-acd639c002d9f79e368e1352a86a9fff_r.jpg\" data-original-token=\"v2-fc08e5bb8748523e6eb39b3e1f69f687\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"8wEgDjK_\"><b>77、根据键对字典排序（方法一，zip函数）</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-23b296ec6adedcfb32edbf8ee45b2034_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"986\" data-rawheight=\"415\" class=\"origin_image zh-lightbox-thumb\" width=\"986\" data-original=\"https://pic3.zhimg.com/v2-23b296ec6adedcfb32edbf8ee45b2034_r.jpg\" data-original-token=\"v2-4317148267814738ae76baeb0ac15599\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"xseAEFf0\"><b>78、根据键对字典排序（方法二,不用zip)</b></p><p data-pid=\"cpZ4WmH5\">有没有发现dic.items和zip(dic.keys(),dic.values())都是为了构造列表嵌套字典的结构，方便后面用sorted()构造排序规则</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-a0b33ead2742d03a20a3abb4a9c70c02_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1036\" data-rawheight=\"387\" class=\"origin_image zh-lightbox-thumb\" width=\"1036\" data-original=\"https://pic1.zhimg.com/v2-a0b33ead2742d03a20a3abb4a9c70c02_r.jpg\" data-original-token=\"v2-394c763dc081336b7ae289a9a1319d8b\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"zM7bk0fM\"><b>79、列表推导式、字典推导式、生成器</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-58b94b7b8ab51d7eb7adc9a9a1f23ad1_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"364\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-58b94b7b8ab51d7eb7adc9a9a1f23ad1_r.jpg\" data-original-token=\"v2-dbc41ef91f450ef404beacc62fbace72\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Jj_fFve0\"><b>80、最后出一道检验题目，根据字符串长度排序，看排序是否灵活运用</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-7e249299caa95a3a2d6c97be0e6c0d66_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"929\" data-rawheight=\"343\" class=\"origin_image zh-lightbox-thumb\" width=\"929\" data-original=\"https://pica.zhimg.com/v2-7e249299caa95a3a2d6c97be0e6c0d66_r.jpg\" data-original-token=\"v2-7e45060c3667c590015368080804f1ae\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"gEh1adc9\"><b>81、举例说明SQL注入和解决办法</b></p><p data-pid=\"lpTAD-NN\">当以字符串格式化书写方式的时候，如果用户输入的有;+SQL语句，后面的SQL语句会执行，比如例子中的SQL注入会删除数据库demo</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-d30912de4dbe9a0c1cc0e86c55581208_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"406\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-d30912de4dbe9a0c1cc0e86c55581208_r.jpg\" data-original-token=\"v2-82f1f4d0661e4b0c6038b7c22751f3d3\"/></figure><p data-pid=\"EyKn-ta_\">解决方式：通过传参数方式解决SQL注入</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-4c84a53da2889ea7fc06c19b8c0ae39c_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"919\" data-rawheight=\"114\" class=\"origin_image zh-lightbox-thumb\" width=\"919\" data-original=\"https://pic1.zhimg.com/v2-4c84a53da2889ea7fc06c19b8c0ae39c_r.jpg\" data-original-token=\"v2-4c84a53da2889ea7fc06c19b8c0ae39c\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WJfVs2cV\"><b>82、s=&#34;info:xiaoZhang 33 shandong&#34;,用正则切分字符串输出[&#39;info&#39;, &#39;xiaoZhang&#39;, &#39;33&#39;, &#39;shandong&#39;]</b></p><p data-pid=\"UH3yDEyT\">|表示或，根据冒号或者空格切分</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-40e7a3816f94cbc2d2228d703bed4f57_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"675\" data-rawheight=\"186\" class=\"origin_image zh-lightbox-thumb\" width=\"675\" data-original=\"https://picx.zhimg.com/v2-40e7a3816f94cbc2d2228d703bed4f57_r.jpg\" data-original-token=\"v2-448d8589c7728286a38b861f5705962d\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"KIpaf9Pl\"><b>83、正则匹配以<a href=\"https://link.zhihu.com/?target=http%3A//163.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">163.com</span><span class=\"invisible\"></span></a>结尾的邮箱</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-7dc3bd8be8b1ec04f92a46defcc954e8_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"284\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-7dc3bd8be8b1ec04f92a46defcc954e8_r.jpg\" data-original-token=\"v2-d001cb4b73f36c1c2b2a9b25167ae3cd\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"bjng8PoV\"><b>84、递归求和</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-baf349ecbe28869c13d33ee3526e4783_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"785\" data-rawheight=\"444\" class=\"origin_image zh-lightbox-thumb\" width=\"785\" data-original=\"https://pic4.zhimg.com/v2-baf349ecbe28869c13d33ee3526e4783_r.jpg\" data-original-token=\"v2-8745690eac8a06cfefa10b9c5c19fb20\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"DheO0kvN\"><b>85、python字典和json字符串相互转化方法</b></p><p data-pid=\"l2jE1cDj\">json.dumps()字典转json字符串，json.loads()json转字典</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-345e3ac873e5ab011f6988d45991d124_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"624\" data-rawheight=\"325\" class=\"origin_image zh-lightbox-thumb\" width=\"624\" data-original=\"https://pic1.zhimg.com/v2-345e3ac873e5ab011f6988d45991d124_r.jpg\" data-original-token=\"v2-87b97f5db70f12ec9d2795499020a211\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"25ZMgR8D\"><b>86、MyISAM 与 InnoDB 区别：</b></p><p data-pid=\"BJ5lBPBM\">1、InnoDB 支持事务，MyISAM 不支持，这一点是非常之重要。事务是一种高</p><p data-pid=\"Y6904iIl\">级的处理方式，如在一些列增删改中只要哪个出错还可以回滚还原，而 MyISAM</p><p data-pid=\"F772eE4r\">就不可以了；</p><p data-pid=\"63e_-NvN\">2、MyISAM 适合查询以及插入为主的应用，InnoDB 适合频繁修改以及涉及到</p><p data-pid=\"dE9LL_8g\">安全性较高的应用；</p><p data-pid=\"9Ai84XKL\">3、InnoDB 支持外键，MyISAM 不支持；</p><p data-pid=\"cyopH7go\">4、对于自增长的字段，InnoDB 中必须包含只有该字段的索引，但是在 MyISAM</p><p data-pid=\"uYKRYScF\">表中可以和其他字段一起建立联合索引；</p><p data-pid=\"cU50_ylG\">5、清空整个表时，InnoDB 是一行一行的删除，效率非常慢。MyISAM 则会重</p><p data-pid=\"89iQ5xUx\">建表；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"eQQg6wwz\"><b>87、统计字符串中某字符出现次数</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-31459244718a8be60205914c405959fa_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"746\" data-rawheight=\"208\" class=\"origin_image zh-lightbox-thumb\" width=\"746\" data-original=\"https://pic1.zhimg.com/v2-31459244718a8be60205914c405959fa_r.jpg\" data-original-token=\"v2-dcfdf0c7feda1181ba0065badd065b4e\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"4PMqY6Y9\"><b>88、字符串转化大小写</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-89d846a9995fafe27f6b621c684fa05d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"598\" data-rawheight=\"304\" class=\"origin_image zh-lightbox-thumb\" width=\"598\" data-original=\"https://pic2.zhimg.com/v2-89d846a9995fafe27f6b621c684fa05d_r.jpg\" data-original-token=\"v2-8cf7cdff661cd94b99083fd9640ad9ce\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"K7Se5xFA\"><b>89、用两种方法去空格</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-dd1c39d490b5c7285c7e7a06675d15a9_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"727\" data-rawheight=\"278\" class=\"origin_image zh-lightbox-thumb\" width=\"727\" data-original=\"https://picx.zhimg.com/v2-dd1c39d490b5c7285c7e7a06675d15a9_r.jpg\" data-original-token=\"v2-41c32f54df7e2da01d6e546b4ce16c3a\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"qIAypobA\"><b>90、正则匹配不是以4和7结尾的手机号</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-f05ccc0f0ab4a3ba39338419c84b81b7_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1067\" data-rawheight=\"434\" class=\"origin_image zh-lightbox-thumb\" width=\"1067\" data-original=\"https://picx.zhimg.com/v2-f05ccc0f0ab4a3ba39338419c84b81b7_r.jpg\" data-original-token=\"v2-84b21bf08e4a2521c50507fd9e8c0945\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"dEgAYltu\"><b>91、简述python引用计数机制</b></p><p data-pid=\"JMHj1_lV\">python垃圾回收主要以引用计数为主，标记-清除和分代清除为辅的机制，其中标记-清除和分代回收主要是为了处理循环引用的难题。<br/><b>引用计数算法</b></p><p data-pid=\"P_hZ99bi\">当有1个变量保存了对象的引用时，此对象的引用计数就会加1</p><p data-pid=\"xRlrcHA6\">当使用del删除变量指向的对象时，如果对象的引用计数不为1，比如3，那么此时只会让这个引用计数减1，即变为2，当再次调用del时，变为1，如果再调用1次del，此时会真的把对象进行删除</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-ab367fbba3d3b06b8518babbf141e7c8_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"786\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-ab367fbba3d3b06b8518babbf141e7c8_r.jpg\" data-original-token=\"v2-56ddc7b9713850d8b7dde80513058b38\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WVnsfETc\"><b>92、int(&#34;1.4&#34;),int(1.4)输出结果？</b></p><p data-pid=\"FrJa-8m8\">int(&#34;1.4&#34;)报错，int(1.4)输出1</p><p data-pid=\"KQD_gDlb\"><b>93、列举3条以上PEP8编码规范</b></p><p data-pid=\"ynan2D-W\">1、顶级定义之间空两行，比如函数或者类定义。</p><p data-pid=\"lbEBN1RE\">2、方法定义、类定义与第一个方法之间，都应该空一行</p><p data-pid=\"P9JgFLcQ\">3、三引号进行注释</p><p data-pid=\"BMNEEUv1\">4、使用Pycharm、Eclipse一般使用4个空格来缩进代码</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"j6ewtueD\"><b>94、正则表达式匹配第一个URL</b></p><p data-pid=\"bROc9_2J\">findall结果无需加group(),search需要加group()提取</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-163f85d4d3d57c1122635a31fdd3e4b2_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"229\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-163f85d4d3d57c1122635a31fdd3e4b2_r.jpg\" data-original-token=\"v2-ae2cfa210330ba3c0290ad42f8e5fac8\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Zy1RQ0wA\"><b>95、正则匹配中文</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-ece9de9c763e9b50a5f5525a7d733b7a_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"825\" data-rawheight=\"303\" class=\"origin_image zh-lightbox-thumb\" width=\"825\" data-original=\"https://pica.zhimg.com/v2-ece9de9c763e9b50a5f5525a7d733b7a_r.jpg\" data-original-token=\"v2-65a69e412574a15419f5da564866b222\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"rkzM9egM\"><b>96、简述乐观锁和悲观锁</b></p><p data-pid=\"in5R_GJh\">悲观锁, 就是很悲观，每次去拿数据的时候都认为别人会修改，所以每次在拿数据的时候都会上锁，这样别人想拿这个数据就会block直到它拿到锁。传统的关系型数据库里边就用到了很多这种锁机制，比如行锁，表锁等，读锁，写锁等，都是在做操作之前先上锁。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"QCl6ygqE\">乐观锁，就是很乐观，每次去拿数据的时候都认为别人不会修改，所以不会上锁，但是在更新的时候会判断一下在此期间别人有没有去更新这个数据，可以使用版本号等机制，乐观锁适用于多读的应用类型，这样可以提高吞吐量</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"48FYu6aO\"><b>97、r、r+、rb、rb+文件打开模式区别</b></p><p data-pid=\"93rADy6a\">模式较多，比较下背背记记即可</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-a4853bc1e3b7cbcef70d43fa6dbdd75d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"768\" data-rawheight=\"776\" class=\"origin_image zh-lightbox-thumb\" width=\"768\" data-original=\"https://pic4.zhimg.com/v2-a4853bc1e3b7cbcef70d43fa6dbdd75d_r.jpg\" data-original-token=\"v2-d53504480b1ff3273b85be4d7a6dda0b\"/></figure><p data-pid=\"gqr10g1V\"><b>98、Linux命令重定向 &gt; 和 &gt;&gt;</b></p><p data-pid=\"frUPmArl\">Linux 允许将命令执行结果 重定向到一个 文件</p><p data-pid=\"BulZbzIa\">将本应显示在终端上的内容 输出／追加 到指定文件中</p><p data-pid=\"x_oxc9u-\">&gt; 表示输出，会覆盖文件原有的内容</p><p data-pid=\"n-Hbpp43\">&gt;&gt; 表示追加，会将内容追加到已有文件的末尾</p><p data-pid=\"kztrCoeW\">用法示例：</p><div class=\"highlight\"><pre><code class=\"language-text\">将 echo 输出的信息保存到 1.txt 里echo Hello Python &gt; 1.txt\n将 tree 输出的信息追加到 1.txt 文件的末尾tree &gt;&gt; 1.txt</code></pre></div><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"8cgn0E8T\"><b>99、正则表达式匹配出&lt;html&gt;&lt;h1&gt;<a href=\"https://link.zhihu.com/?target=http%3A//www.itcast.cn\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">itcast.cn</span><span class=\"invisible\"></span></a>&lt;/h1&gt;&lt;/html&gt;</b></p><p data-pid=\"GiFoGpgx\">前面的&lt;&gt;和后面的&lt;&gt;是对应的，可以用此方法</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-8ad04aa71274607b166b291407856565_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"371\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-8ad04aa71274607b166b291407856565_r.jpg\" data-original-token=\"v2-28665c142b50da77a39c90d1cd606b11\"/></figure><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-b9d4b3ace83dedeba36976df6eeb704e_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"566\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic1.zhimg.com/v2-b9d4b3ace83dedeba36976df6eeb704e_r.jpg\" data-original-token=\"v2-746f4559da0b287c4c59c21f6c86afb1\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"J8bLNsK9\"><b>100、python传参数是传值还是传址？</b></p><p data-pid=\"_ZD8134o\">Python中函数参数是引用传递（注意不是值传递）。对于不可变类型（数值型、字符串、元组），因变量不能修改，所以运算不会影响到变量自身；而对于可变类型（列表字典）来说，函数体运算可能会更改传入的参数变量。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TiD61ygD\"><b>101、求两个列表的交集、差集、并集</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-df282836f4ae90ea9bb0af6cd7f7954d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"463\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-df282836f4ae90ea9bb0af6cd7f7954d_r.jpg\" data-original-token=\"v2-3dd18e79c9e7af236b7c871746d0a9f1\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"1A9GHnS9\"><b>102、生成0-100的随机数</b></p><p data-pid=\"gJXHX5X8\">random.random()生成0-1之间的随机小数，所以乘以100</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-501177a01208cbbd22e44326dc885fdc_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"771\" data-rawheight=\"295\" class=\"origin_image zh-lightbox-thumb\" width=\"771\" data-original=\"https://pic3.zhimg.com/v2-501177a01208cbbd22e44326dc885fdc_r.jpg\" data-original-token=\"v2-d7950e205d18f33d84635bfc3c1a2893\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wT-IZrtz\"><b>103、lambda匿名函数好处</b></p><p data-pid=\"HT09V8RM\">精简代码，lambda省去了定义函数，map省去了写for循环过程</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-b9c32d8012e1802068bb184e42d7de26_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"204\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-b9c32d8012e1802068bb184e42d7de26_r.jpg\" data-original-token=\"v2-2311bd94fdfd8fca80839b3bb66a96c0\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TJG3Bvxy\"><b>104、常见的网络传输协议</b></p><p data-pid=\"2hNmSBYN\">UDP、TCP、FTP、HTTP、SMTP等等</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"sNKGr2a4\"><b>105、单引号、双引号、三引号用法</b></p><p data-pid=\"8y2fNDlx\">1、单引号和双引号没有什么区别，不过单引号不用按shift，打字稍微快一点。表示字符串的时候，单引号里面可以用双引号，而不用转义字符,反之亦然。</p><div class=\"highlight\"><pre><code class=\"language-text\">&#39;She said:&#34;Yes.&#34; &#39; or &#34;She said: &#39;Yes.&#39; &#34;</code></pre></div><p data-pid=\"-WL5LRWs\"><code>2、但是如果直接用单引号扩住单引号，则需要转义，像这样：</code></p><div class=\"highlight\"><pre><code class=\"language-text\"> &#39; She said:\\&#39;Yes.\\&#39; &#39;</code></pre></div><p data-pid=\"dbUR2tds\">3、三引号可以直接书写多行，通常用于大段，大篇幅的字符串</p><div class=\"highlight\"><pre><code class=\"language-text\">&#34;&#34;&#34;\nhello\nworld\n&#34;&#34;&#34; </code></pre></div><p data-pid=\"hodIwKMx\"><b>106、python垃圾回收机制</b></p><p data-pid=\"vbtlvHno\">python垃圾回收主要以引用计数为主，标记-清除和分代清除为辅的机制，其中标记-清除和分代回收主要是为了处理循环引用的难题。</p><h2><b>引用计数算法</b></h2><p data-pid=\"waIEOIlB\">当有1个变量保存了对象的引用时，此对象的引用计数就会加1</p><p data-pid=\"PCmOcVcA\">当使用del删除变量指向的对象时，如果对象的引用计数不为1，比如3，那么此时只会让这个引用计数减1，即变为2，当再次调用del时，变为1，如果再调用1次del，此时会真的把对象进行删除</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-4f65883a0cd845aef0d65a17b06d8c65_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"497\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-4f65883a0cd845aef0d65a17b06d8c65_r.jpg\" data-original-token=\"v2-a09660b30eb9a8de6ab0d3603b06c8cf\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"M7NNCmgN\"><b>107、HTTP请求中get和post区别</b></p><p data-pid=\"9HL874cW\">1、GET请求是通过URL直接请求数据，数据信息可以在URL中直接看到，比如浏览器访问；而POST请求是放在请求头中的，我们是无法直接看到的；</p><p data-pid=\"7ta2hLNW\">2、GET提交有数据大小的限制，一般是不超过1024个字节，而这种说法也不完全准确，HTTP协议并没有设定URL字节长度的上限，而是浏览器做了些处理，所以长度依据浏览器的不同有所不同；POST请求在HTTP协议中也没有做说明，一般来说是没有设置限制的，但是实际上浏览器也有默认值。总体来说，少量的数据使用GET，大量的数据使用POST。</p><p data-pid=\"TrIZ-HCb\">3、GET请求因为数据参数是暴露在URL中的，所以安全性比较低，比如密码是不能暴露的，就不能使用GET请求；POST请求中，请求参数信息是放在请求头的，所以安全性较高，可以使用。在实际中，涉及到登录操作的时候，尽量使用HTTPS请求，安全性更好。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"JZH9aJGh\"><b>108、python中读取Excel文件的方法</b></p><p data-pid=\"PS54VI_N\">应用数据分析库pandas</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-8edcefb5fd9aae0236b501ce2c8324f0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"965\" data-rawheight=\"318\" class=\"origin_image zh-lightbox-thumb\" width=\"965\" data-original=\"https://pic1.zhimg.com/v2-8edcefb5fd9aae0236b501ce2c8324f0_r.jpg\" data-original-token=\"v2-b418c47faf847ec3f148fd4a49d0cc81\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"RBlSRPBM\"><b>109、简述多线程、多进程</b></p><p data-pid=\"7fNB-bAR\"><b>进程：</b></p><p data-pid=\"wuXDVxzu\">1、操作系统进行资源分配和调度的基本单位，多个进程之间相互独立</p><p data-pid=\"wgpdrHvs\">2、稳定性好，如果一个进程崩溃，不影响其他进程，但是进程消耗资源大，开启的进程数量有限制</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wPAr0NnT\"><b>线程：</b></p><p data-pid=\"lvhaYQLz\">1、CPU进行资源分配和调度的基本单位，线程是进程的一部分，是比进程更小的能独立运行的基本单位，一个进程下的多个线程可以共享该进程的所有资源</p><p data-pid=\"DyT4heKM\">2、如果IO操作密集，则可以多线程运行效率高，缺点是如果一个线程崩溃，都会造成进程的崩溃</p><p data-pid=\"AlkTY2MM\"><b>应用：</b></p><p data-pid=\"QFyAebDO\">IO密集的用多线程，在用户输入，sleep 时候，可以切换到其他线程执行，减少等待的时间</p><p data-pid=\"X1nAjZ-b\">CPU密集的用多进程，因为假如IO操作少，用多线程的话，因为线程共享一个全局解释器锁，当前运行的线程会霸占GIL，其他线程没有GIL，就不能充分利用多核CPU的优势</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"kHcN_1ZO\"><b>110、python正则中search和match</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-bd3bfbb3df809744844f7263eefb2296_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"510\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-bd3bfbb3df809744844f7263eefb2296_r.jpg\" data-original-token=\"v2-9d39ef1216fabc3eeda92fb6fae29fde\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"cK2rYcjh\">我是  <a class=\"member_mention\" href=\"https://www.zhihu.com/people/7877379290d533427cce0d55ea4740e4\" data-hash=\"7877379290d533427cce0d55ea4740e4\" data-hovercard=\"p$b$7877379290d533427cce0d55ea4740e4\">@程序员资源社区</a> </p><p data-pid=\"P2F5Oxn1\"><b>资料已整理到Excel了，需要所有excel的话，仔细看我个人资料说明，仔细看我个人资料，仔细看我个人资料</b></p><p data-pid=\"PHl7OpB8\"><b>已经说的很明显了，大家稍微注意一下</b></p>", "excerpt": "python面试题： <a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s/SyC_LLQL8AU3i6wYNlOdNQ\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic3.zhimg.com/v2-43e53943fb3d6f6931a880c4cf0a4a08_180x120.jpg\" data-image-width=\"600\" data-image-height=\"255\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">110道python面试题</a> <b>1、一行代码实现1--100之和</b>利用sum()函数求和 <b>2、如何在一个函数内部修改全局变量</b>利用global在函数声明 修改全局变量 <b>3、列出5个python标准库</b> os：提供了不少与操作系统相关联的函数 sys: 通常用于命令行参数 re: 正则匹配 math: 数学运算 datetime:处理日期时间 <b>4、字典如何删除键和合并两个字典</b>del和update方法 <b>5、谈下python的GIL</b> GIL 是python的全局解释器锁，同一进程中假如有多个线程运…", "excerpt_new": "python面试题： <a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s/SyC_LLQL8AU3i6wYNlOdNQ\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic3.zhimg.com/v2-43e53943fb3d6f6931a880c4cf0a4a08_180x120.jpg\" data-image-width=\"600\" data-image-height=\"255\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">110道python面试题</a> <b>1、一行代码实现1--100之和</b>利用sum()函数求和 <b>2、如何在一个函数内部修改全局变量</b>利用global在函数声明 修改全局变量 <b>3、列出5个python标准库</b> os：提供了不少与操作系统相关联的函数 sys: 通常用于命令行参数 re: 正则匹配 math: 数学运算 datetime:处理日期时间 <b>4、字典如何删除键和合并两个字典</b>del和update方法 <b>5、谈下python的GIL</b> GIL 是python的全局解释器锁，同一进程中假如有多个线程运…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/54430650", "comment_permission": "all", "voteup_count": 9316, "comment_count": 156, "image_url": "https://picx.zhimg.com/v2-fb300ebb5c58f34702eb42e7a1341027_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_COLLECT_ARTICLE", "created_time": 1591940087, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "收藏了文章", "is_sticky": false}, {"id": "1591940074178", "type": "feed", "target": {"id": "54430650", "type": "article", "author": {"id": "7877379290d533427cce0d55ea4740e4", "name": "不加班程序员", "headline": "微信公众号：不加班程序员", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/python88", "url_token": "python88", "avatar_url": "https://pic1.zhimg.com/v2-e9b3d6a844d1c295598634be7636f1b9_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1547011161, "updated": 1582438173, "title": "110道Python面试题（真题）", "excerpt_title": "", "content": "<p data-pid=\"5CvvPm9l\">python面试题：</p><a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s/SyC_LLQL8AU3i6wYNlOdNQ\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic3.zhimg.com/v2-43e53943fb3d6f6931a880c4cf0a4a08_180x120.jpg\" data-image-width=\"600\" data-image-height=\"255\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">110道python面试题</a><p data-pid=\"vAIAUqQC\"><b>1、一行代码实现1--100之和</b></p><p data-pid=\"QJ7UwMiG\">利用sum()函数求和</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-f8ecb9231a4b558a8c51fcd683de7e6a_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"257\" data-rawheight=\"60\" class=\"content_image\" width=\"257\" data-original-token=\"v2-705b50c78724bdb800cacceb0725a648\"/></figure><p data-pid=\"q9LjSUHg\"><b>2、如何在一个函数内部修改全局变量</b></p><p data-pid=\"bUxczVrh\">利用global在函数声明 修改全局变量</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-11b238f2c4a40c45c526e8a7d67aff92_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"329\" data-rawheight=\"195\" class=\"content_image\" width=\"329\" data-original-token=\"v2-700d8d055a56258b142f24168c544865\"/></figure><p data-pid=\"8Im8goRE\"><b>3、列出5个python标准库</b></p><p data-pid=\"p6aWk5R9\">    os：提供了不少与操作系统相关联的函数</p><p data-pid=\"Cvr5JYxN\">    sys:   通常用于命令行参数</p><p data-pid=\"_-wNidqY\">    re:   正则匹配</p><p data-pid=\"J1z05aPN\">    math: 数学运算</p><p data-pid=\"6nYbUhQH\">    datetime:处理日期时间</p><p data-pid=\"x5FIyP_O\"><b>4、字典如何删除键和合并两个字典</b></p><p data-pid=\"fHBMLWMz\">del和update方法</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-0265e5f95915f8c35a67143a8dcdbd95_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"636\" data-rawheight=\"228\" class=\"origin_image zh-lightbox-thumb\" width=\"636\" data-original=\"https://pic4.zhimg.com/v2-0265e5f95915f8c35a67143a8dcdbd95_r.jpg\" data-original-token=\"v2-54ed727c2bd80a0d6b39aeb6444a6518\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"KxY-BtiY\"><b>5、谈下python的GIL</b></p><p data-pid=\"2ntTznwZ\">    GIL 是python的全局解释器锁，同一进程中假如有多个线程运行，一个线程在运行python程序的时候会霸占python解释器（加了一把锁即GIL），使该进程内的其他线程无法运行，等该线程运行完后其他线程才能运行。如果线程运行过程中遇到耗时操作，则解释器锁解开，使其他线程运行。所以在多线程中，线程的运行仍是有先后顺序的，并不是同时进行。</p><p data-pid=\"J0AEQ4HQ\">多进程中因为每个进程都能被系统分配资源，相当于每个进程有了一个python解释器，所以多进程可以实现多个进程的同时运行，缺点是进程系统资源开销大</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"padJJ68c\"><b>6、python实现列表去重的方法</b></p><p data-pid=\"bnz1L0IU\">先通过集合去重，在转列表</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-b24b0f65f93687dcf200c869d177e23f_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"427\" data-rawheight=\"194\" class=\"origin_image zh-lightbox-thumb\" width=\"427\" data-original=\"https://picx.zhimg.com/v2-b24b0f65f93687dcf200c869d177e23f_r.jpg\" data-original-token=\"v2-4b69fc1742863d45fe54fbd29609647f\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WW9P_Dzh\"><b>7、fun(*args,**kwargs)中的*args,**kwargs什么意思？</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-48a5aab6bbc6a5b4e2cfe04e57a544d4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"891\" data-rawheight=\"503\" class=\"origin_image zh-lightbox-thumb\" width=\"891\" data-original=\"https://pic1.zhimg.com/v2-48a5aab6bbc6a5b4e2cfe04e57a544d4_r.jpg\" data-original-token=\"v2-de6f42d304770badd07aa51d447f26f7\"/></figure><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-8dd376ad89b095777ef80723039b2ab6_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"918\" data-rawheight=\"422\" class=\"origin_image zh-lightbox-thumb\" width=\"918\" data-original=\"https://pica.zhimg.com/v2-8dd376ad89b095777ef80723039b2ab6_r.jpg\" data-original-token=\"v2-6ec5122d6a362550bfe96857d07b22b3\"/></figure><p data-pid=\"LXBtRQQW\"><b>8、python2和python3的range（100）的区别</b></p><p data-pid=\"yrj2yOI_\">    python2返回列表，python3返回迭代器，节约内存</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"QYmG4rG_\"><b>9、一句话解释什么样的语言能够用装饰器?</b></p><p data-pid=\"tOyKJv1K\">    函数可以作为参数传递的语言，可以使用装饰器</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"k9fQhD3X\"><b>10、python内建数据类型有哪些</b></p><p data-pid=\"u7qoQP9D\">    整型--int</p><p data-pid=\"jF0gVhFE\">    布尔型--bool</p><p data-pid=\"xKUOr_uX\">    字符串--str</p><p data-pid=\"dDcEYbBz\">    列表--list</p><p data-pid=\"R6Gv7oIg\">    元组--tuple</p><p data-pid=\"q-napIWx\">    字典--dict</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"tB0BwN_-\"><b>11、简述面向对象中__new__和__init__区别</b></p><p data-pid=\"-ET48qSR\">__init__是初始化方法，创建对象后，就立刻被默认调用了，可接收参数，如图</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-a7738e73633ef0eb19bfb8d0b171f4cd_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"759\" data-rawheight=\"349\" class=\"origin_image zh-lightbox-thumb\" width=\"759\" data-original=\"https://pic4.zhimg.com/v2-a7738e73633ef0eb19bfb8d0b171f4cd_r.jpg\" data-original-token=\"v2-0cccc5e2d4df6a2d785b556d7bd445a7\"/></figure><p data-pid=\"Ed7ahmS3\">1、__new__至少要有一个参数cls，代表当前类，此参数在实例化时由Python解释器自动识别</p><p data-pid=\"Alik4vZb\">2、__new__必须要有返回值，返回实例化出来的实例，这点在自己实现__new__时要特别注意，可以return父类（通过super(当前类名, cls)）__new__出来的实例，或者直接是object的__new__出来的实例</p><p data-pid=\"XSGpZFLX\">3、__init__有一个参数self，就是这个__new__返回的实例，__init__在__new__的基础上可以完成一些其它初始化的动作，__init__不需要返回值</p><p data-pid=\"Rf3a9HA6\">4、如果__new__创建的是当前类的实例，会自动调用__init__函数，通过return语句里面调用的__new__函数的第一个参数是cls来保证是当前类实例，如果是其他类的类名，；那么实际创建返回的就是其他类的实例，其实就不会调用当前类的__init__函数，也不会调用其他类的__init__函数。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-550edacb6fe1881576c492c198e80040_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"397\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-550edacb6fe1881576c492c198e80040_r.jpg\" data-original-token=\"v2-76a7cc9f11637e5eb0ebafd3a8c578c3\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"vH5Ad1ky\"><b>12、简述with方法打开处理文件帮我我们做了什么？</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-a9652f62db3d794256823c82da485d59_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"433\" data-rawheight=\"222\" class=\"origin_image zh-lightbox-thumb\" width=\"433\" data-original=\"https://pic2.zhimg.com/v2-a9652f62db3d794256823c82da485d59_r.jpg\" data-original-token=\"v2-381db6f9c299d084a523f673c74ab73f\"/></figure><p data-pid=\"KS5atV1e\">打开文件在进行读写的时候可能会出现一些异常状况，如果按照常规的f.open</p><p data-pid=\"qvmnASV5\">写法，我们需要try,except,finally，做异常判断，并且文件最终不管遇到什么情况，都要执行finally f.close()关闭文件，with方法帮我们实现了finally中f.close</p><p data-pid=\"kLbsJIXL\">（当然还有其他自定义功能，有兴趣可以研究with方法源码）</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"j7yW2H2c\"><b>13、列表[1,2,3,4,5],请使用map()函数输出[1,4,9,16,25]，并使用列表推导式提取出大于10的数，最终输出[16,25]</b></p><p data-pid=\"quxyUq64\">map（）函数第一个参数是fun，第二个参数是一般是list，第三个参数可以写list，也可以不写，根据需求</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-3d965db5317efc53bed2f2dd71751320_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"472\" data-rawheight=\"321\" class=\"origin_image zh-lightbox-thumb\" width=\"472\" data-original=\"https://pic3.zhimg.com/v2-3d965db5317efc53bed2f2dd71751320_r.jpg\" data-original-token=\"v2-d437c39bb9569ffeb5aa790a353ee7de\"/></figure><p data-pid=\"m0l9h7ms\"><b>14、python中生成随机整数、随机小数、0--1之间小数方法</b></p><p data-pid=\"5gr61HXj\">随机整数：random.randint(a,b),生成区间内的整数</p><p data-pid=\"pAnrK8sS\">随机小数：习惯用numpy库，利用np.random.randn(5)生成5个随机小数</p><p data-pid=\"y7Fu9kTH\">0-1随机小数：random.random(),括号中不传参</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-e333869075c20a70868eb2ac91a4f897_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"984\" data-rawheight=\"367\" class=\"origin_image zh-lightbox-thumb\" width=\"984\" data-original=\"https://pic2.zhimg.com/v2-e333869075c20a70868eb2ac91a4f897_r.jpg\" data-original-token=\"v2-087353a99d354e052230e05907b7a7e8\"/></figure><p data-pid=\"Jd9rlyp2\"><b>15、避免转义给字符串加哪个字母表示原始字符串？</b></p><p data-pid=\"HnttHeae\">r , 表示需要原始字符串，不转义特殊字符</p><p data-pid=\"mMgby04V\"><b>16、&lt;div class=&#34;nam&#34;&gt;中国&lt;/div&gt;，用正则匹配出标签里面的内容（“中国”），其中class的类名是不确定的</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-0098894c183f04a48339b26d465f6459_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"846\" data-rawheight=\"243\" class=\"origin_image zh-lightbox-thumb\" width=\"846\" data-original=\"https://pic4.zhimg.com/v2-0098894c183f04a48339b26d465f6459_r.jpg\" data-original-token=\"v2-ed5a91a4d9c99c9f37b672c6c2c1175a\"/></figure><p data-pid=\"FVuLz4iz\"><b>17、python中断言方法举例</b></p><p data-pid=\"V17tQAc0\">assert（）方法，断言成功，则程序继续执行，断言失败，则程序报错</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-7d496caffd738f080dc5a22cf82a9105_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1034\" data-rawheight=\"453\" class=\"origin_image zh-lightbox-thumb\" width=\"1034\" data-original=\"https://pic2.zhimg.com/v2-7d496caffd738f080dc5a22cf82a9105_r.jpg\" data-original-token=\"v2-9d7a699782139803e535d44c3ad08de7\"/></figure><p data-pid=\"SSWWd6Y_\"><b>18、数据表student有id,name,score,city字段，其中name中的名字可有重复，需要消除重复行,请写sql语句</b></p><p data-pid=\"A-JGYTjH\">    select  distinct  name  from  student</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"a_Rg0otx\"><b>19、10个Linux常用命令</b></p><p data-pid=\"zmdgCmTp\">    ls  pwd  cd  touch  rm  mkdir  tree  cp  mv  cat  more  grep  echo </p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"lMX00Xts\"><b>20、python2和python3区别？列举5个</b></p><p data-pid=\"LsVGzz7D\">    1、Python3 使用 print 必须要以小括号包裹打印内容，比如 print(&#39;hi&#39;)</p><p data-pid=\"P_5jynCq\">    Python2 既可以使用带小括号的方式，也可以使用一个空格来分隔打印内容，比        如 print &#39;hi&#39;</p><p data-pid=\"4dYbncw0\">     2、python2 range(1,10)返回列表，python3中返回迭代器，节约内存</p><p data-pid=\"yrS1XRB0\">    3、python2中使用ascii编码，python中使用utf-8编码</p><p data-pid=\"r-WRGaKV\">    4、python2中unicode表示字符串序列，str表示字节序列</p><p data-pid=\"tNfaQmyP\">      python3中str表示字符串序列，byte表示字节序列</p><p data-pid=\"FmfQqIrg\">    5、python2中为正常显示中文，引入coding声明，python3中不需要</p><p data-pid=\"4p7Xo168\">    6、python2中是raw_input()函数，python3中是input()函数</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Y7ZSSKLU\"><b>21、列出python中可变数据类型和不可变数据类型，并简述原理</b></p><p data-pid=\"aHM_N8Sv\">不可变数据类型：数值型、字符串型string和元组tuple</p><p data-pid=\"Beqr-k3M\">不允许变量的值发生变化，如果改变了变量的值，相当于是新建了一个对象，而对于相同的值的对象，在内存中则只有一个对象（一个地址），如下图用id()方法可以打印对象的id</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-ce7d69f83506cbb6c3330c78e7891145_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"390\" data-rawheight=\"251\" class=\"content_image\" width=\"390\" data-original-token=\"v2-8ecad21cbbc1122caa2a16164e684672\"/></figure><p data-pid=\"shIQk9Xh\">可变数据类型：列表list和字典dict；</p><p data-pid=\"-t_0aylX\">允许变量的值发生变化，即如果对变量进行append、+=等这种操作后，只是改变了变量的值，而不会新建一个对象，变量引用的对象的地址也不会变化，不过对于相同的值的不同对象，在内存中则会存在不同的对象，即每个对象都有自己的地址，相当于内存中对于同值的对象保存了多份，这里不存在引用计数，是实实在在的对象。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-024ed10c48051b00bdd5263c4a5c619d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"318\" data-rawheight=\"235\" class=\"content_image\" width=\"318\" data-original-token=\"v2-8cf19a4bbb813f898306533c3efc210a\"/></figure><p data-pid=\"eL-UcmpN\"><b>22、s = &#34;ajldjlajfdljfddd&#34;，去重并从小到大排序输出&#34;adfjl&#34;</b></p><p data-pid=\"F3WGZdL-\">set去重，去重转成list,利用sort方法排序，reeverse=False是从小到大排</p><p data-pid=\"4VrK-66F\">list是不 变数据类型，s.sort时候没有返回值，所以注释的代码写法不正确</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-290a5084f2086f28f73ba5bef59ab515_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"590\" data-rawheight=\"289\" class=\"origin_image zh-lightbox-thumb\" width=\"590\" data-original=\"https://picx.zhimg.com/v2-290a5084f2086f28f73ba5bef59ab515_r.jpg\" data-original-token=\"v2-ee2d153a3ec86e94828fef171417241e\"/></figure><p data-pid=\"rnMad6tW\"><b>23、用lambda函数实现两个数相乘</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-16c36ade78eecfba4162aa69e3a83c46_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"816\" data-rawheight=\"204\" class=\"origin_image zh-lightbox-thumb\" width=\"816\" data-original=\"https://pic1.zhimg.com/v2-16c36ade78eecfba4162aa69e3a83c46_r.jpg\" data-original-token=\"v2-a4483c4e3968ba782c674a0a1727b1bb\"/></figure><p data-pid=\"bs6JgCNG\"><b>24、字典根据键从小到大排序</b></p><p data-pid=\"vGOpRXy1\">dic={&#34;name&#34;:&#34;zs&#34;,&#34;age&#34;:18,&#34;city&#34;:&#34;深圳&#34;,&#34;tel&#34;:&#34;1362626627&#34;}</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-48bc770043992892d32dbeca1797de5e_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"941\" data-rawheight=\"210\" class=\"origin_image zh-lightbox-thumb\" width=\"941\" data-original=\"https://pic3.zhimg.com/v2-48bc770043992892d32dbeca1797de5e_r.jpg\" data-original-token=\"v2-eb6049d7b55c6991065a63a90105d69b\"/></figure><p data-pid=\"rsJ1N2Lk\"><b>25、利用collections库的Counter方法统计字符串每个单词出现的次数&#34;kjalfj;ldsjafl;hdsllfdhg;lahfbl;hl;ahlf;h&#34;</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-8fee132f1f95c3ba36a60b3c0b6b5fa4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"193\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-8fee132f1f95c3ba36a60b3c0b6b5fa4_r.jpg\" data-original-token=\"v2-0945d47f579a969344158e7124e704e7\"/></figure><p data-pid=\"86gas7D8\"><b>26、字符串a = &#34;not 404 found 张三 99 深圳&#34;，每个词中间是空格，用正则过滤掉英文和数字，最终输出&#34;张三  深圳&#34;</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-bd1cacc332cf481f2ba555fd08036e7f_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"881\" data-rawheight=\"504\" class=\"origin_image zh-lightbox-thumb\" width=\"881\" data-original=\"https://pic4.zhimg.com/v2-bd1cacc332cf481f2ba555fd08036e7f_r.jpg\" data-original-token=\"v2-d9b4b045e1363ca64ae24f835d4246e5\"/></figure><p data-pid=\"XbfxP3UH\">顺便贴上匹配小数的代码，虽然能匹配，但是健壮性有待进一步确认</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-6262c079911bf47e8602ba17a5aa93b2_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"947\" data-rawheight=\"523\" class=\"origin_image zh-lightbox-thumb\" width=\"947\" data-original=\"https://pic3.zhimg.com/v2-6262c079911bf47e8602ba17a5aa93b2_r.jpg\" data-original-token=\"v2-d2acd50c6ae89eb56e76948219a1b4db\"/></figure><p data-pid=\"MfZQsnVq\"><b>27、filter方法求出列表所有奇数并构造新列表，a =  [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]</b></p><p data-pid=\"nLI_hIls\">filter() 函数用于过滤序列，过滤掉不符合条件的元素，返回由符合条件元素组成的新列表。该接收两个参数，第一个为函数，第二个为序列，序列的每个元素作为参数传递给函数进行判，然后返回 True 或 False，最后将返回 True 的元素放到新列表</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-b7f4785f20590acd5cbc9db6f53892b1_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"730\" data-rawheight=\"282\" class=\"origin_image zh-lightbox-thumb\" width=\"730\" data-original=\"https://pic2.zhimg.com/v2-b7f4785f20590acd5cbc9db6f53892b1_r.jpg\" data-original-token=\"v2-b8877f0c807a7cce0627f9088e2f875d\"/></figure><p data-pid=\"cdw17i01\"><b>28、列表推导式求列表所有奇数并构造新列表，a =  [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-b80cfb50ba8569348a4e60de3da7c334_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"676\" data-rawheight=\"135\" class=\"origin_image zh-lightbox-thumb\" width=\"676\" data-original=\"https://pic3.zhimg.com/v2-b80cfb50ba8569348a4e60de3da7c334_r.jpg\" data-original-token=\"v2-86b5f0cf715c39d568bc67b5efb2addb\"/></figure><p data-pid=\"ZRAobZGQ\"><b>29、正则re.complie作用</b></p><p data-pid=\"WsX9QHpU\">re.compile是将正则表达式编译成一个对象，加快速度，并重复使用</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"4jS48Rij\"><b>30、a=（1，）b=(1)，c=(&#34;1&#34;) 分别是什么类型的数据？</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-2e7fc26773c7daf0f100be119e02fbc4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"365\" data-rawheight=\"184\" class=\"content_image\" width=\"365\" data-original-token=\"v2-e5d9bf009f4d9deb8bcfbf153687e8b0\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"hQNZk7OK\"><b>31、两个列表[1,5,7,9]和[2,2,6,8]合并为[1,2,2,3,6,7,8,9]</b></p><p data-pid=\"mUs_aKqf\">extend可以将另一个集合中的元素逐一添加到列表中，区别于append整体添加</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-ad02f5bc07f53b0ab55d326b3e7aea3b_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"573\" data-rawheight=\"345\" class=\"origin_image zh-lightbox-thumb\" width=\"573\" data-original=\"https://pic4.zhimg.com/v2-ad02f5bc07f53b0ab55d326b3e7aea3b_r.jpg\" data-original-token=\"v2-535103b257c9250b4c94f4b620ead3b4\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"w3S2yJzJ\"><b>32、用python删除文件和用linux命令删除文件方法</b></p><p data-pid=\"2ZpJr_OO\">python：os.remove(文件名)</p><p data-pid=\"OuMVWGiP\">linux:       rm  文件名</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"rsy-keDu\"><b>33、log日志中，我们需要用时间戳记录error,warning等的发生时间，请用datetime模块打印当前时间戳 “2018-04-01 11:38:54”</b></p><p data-pid=\"-bsVNVyS\">顺便把星期的代码也贴上了</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-1e7bc11a92c370b2d233ba5bac6805af_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"155\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-1e7bc11a92c370b2d233ba5bac6805af_r.jpg\" data-original-token=\"v2-1e7bc11a92c370b2d233ba5bac6805af\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"mNO1XNeB\"><b>34、数据库优化查询方法</b></p><p data-pid=\"gUUqf1sb\">    外键、索引、联合查询、选择特定字段等等</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wMq-M0Hx\"><b>35、请列出你会的任意一种统计图（条形图、折线图等）绘制的开源库，第三方也行</b></p><p data-pid=\"ySizO4Ks\">    pyecharts、matplotlib</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"r3n2LI8p\"><b>36、写一段自定义异常代码</b></p><p data-pid=\"xY4xQPYs\">自定义异常用raise抛出异常</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-7aae69975c783f19681945c8e50877cd_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"826\" data-rawheight=\"380\" class=\"origin_image zh-lightbox-thumb\" width=\"826\" data-original=\"https://picx.zhimg.com/v2-7aae69975c783f19681945c8e50877cd_r.jpg\" data-original-token=\"v2-8a5854e12ab0f389096ca96a320b7ce5\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"o-fq5tuF\"><b>37、正则表达式匹配中，（.*）和（.*?）匹配区别？</b></p><p data-pid=\"KB4KNQEI\">（.*）是贪婪匹配，会把满足正则的尽可能多的往后匹配</p><p data-pid=\"t2WpZzsM\">（.*?）是非贪婪匹配，会把满足正则的尽可能少匹配</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-6f8cb740cb184f165f8f06f2abbb97f0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"746\" data-rawheight=\"292\" class=\"origin_image zh-lightbox-thumb\" width=\"746\" data-original=\"https://pic1.zhimg.com/v2-6f8cb740cb184f165f8f06f2abbb97f0_r.jpg\" data-original-token=\"v2-882272899470272988b1efc30660d636\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"lyg9dDMH\"><b>38、简述Django的orm</b></p><p data-pid=\"6hQkyuI7\">ORM，全拼Object-Relation Mapping，意为对象-关系映射</p><p data-pid=\"CPK-Y-D5\">实现了数据模型与数据库的解耦，通过简单的配置就可以轻松更换数据库，而不需要修改代码只需要面向对象编程,orm操作本质上会根据对接的数据库引擎，翻译成对应的sql语句,所有使用Django开发的项目无需关心程序底层使用的是MySQL、Oracle、sqlite....，如果数据库迁移，只需要更换Django的数据库引擎即可</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-6691dd8adc3792e16279e8fa06bac422_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"989\" data-rawheight=\"419\" class=\"origin_image zh-lightbox-thumb\" width=\"989\" data-original=\"https://pic1.zhimg.com/v2-6691dd8adc3792e16279e8fa06bac422_r.jpg\" data-original-token=\"v2-4a29c08a7fb6863a61d947225156c47b\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Vd4QhHS2\"><b>39、[[1,2],[3,4],[5,6]]一行代码展开该列表，得出[1,2,3,4,5,6]</b></p><p data-pid=\"rUyeH0Ys\">列表推导式的骚操作</p><p data-pid=\"8xM6B8qZ\">运行过程：for i in a ,每个i是【1,2】，【3,4】，【5,6】，for j in i，每个j就是1,2,3,4,5,6,合并后就是结果</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-e4f296d94599b217b69d7503e3dc7aa6_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"600\" data-rawheight=\"218\" class=\"origin_image zh-lightbox-thumb\" width=\"600\" data-original=\"https://pic1.zhimg.com/v2-e4f296d94599b217b69d7503e3dc7aa6_r.jpg\" data-original-token=\"v2-e90867bdf112b6ddf64ba10ba9f36ba1\"/></figure><p data-pid=\"35c5M9TQ\">还有更骚的方法，将列表转成numpy矩阵，通过numpy的flatten（）方法，代码永远是只有更骚，没有最骚</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-6b57b78359b5c651f143ae88d9c31939_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"718\" data-rawheight=\"387\" class=\"origin_image zh-lightbox-thumb\" width=\"718\" data-original=\"https://pic2.zhimg.com/v2-6b57b78359b5c651f143ae88d9c31939_r.jpg\" data-original-token=\"v2-ff077505e4e8207d86148783f860242a\"/></figure><p data-pid=\"FbPK9i52\"><b>40、x=&#34;abc&#34;,y=&#34;def&#34;,z=[&#34;d&#34;,&#34;e&#34;,&#34;f&#34;],分别求出x.join(y)和x.join(z)返回的结果</b></p><p data-pid=\"TbJCvnLP\">join()括号里面的是可迭代对象，x插入可迭代对象中间，形成字符串，结果一致，有没有突然感觉字符串的常见操作都不会玩了</p><p data-pid=\"68CkLnZj\">顺便建议大家学下os.path.join()方法，拼接路径经常用到，也用到了join,和字符串操作中的join有什么区别，该问题大家可以查阅相关文档，后期会有答案</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-efd298ae334ceac82079545345734d72_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"502\" data-rawheight=\"387\" class=\"origin_image zh-lightbox-thumb\" width=\"502\" data-original=\"https://pic3.zhimg.com/v2-efd298ae334ceac82079545345734d72_r.jpg\" data-original-token=\"v2-df25020dfcbda96bb50e56aa776aaf7f\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"VpgAe12N\"><b>41、举例说明异常模块中try except else finally的相关意义</b></p><p data-pid=\"-JDFoRXj\">try..except..else没有捕获到异常，执行else语句</p><p data-pid=\"GCM6qxY9\">try..except..finally不管是否捕获到异常，都执行finally语句</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-6e51a1d67aa69f6b0f8332c35f534afe_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"863\" data-rawheight=\"588\" class=\"origin_image zh-lightbox-thumb\" width=\"863\" data-original=\"https://pica.zhimg.com/v2-6e51a1d67aa69f6b0f8332c35f534afe_r.jpg\" data-original-token=\"v2-be874549047f7a25da04dd0f607bf892\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"zMRzG3zO\"><b>42、python中交换两个数值</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-baef943baca9ba2c621d6c38445311f2_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"443\" data-rawheight=\"275\" class=\"origin_image zh-lightbox-thumb\" width=\"443\" data-original=\"https://pica.zhimg.com/v2-baef943baca9ba2c621d6c38445311f2_r.jpg\" data-original-token=\"v2-a1f0f9c73ca15dfa5ddf41564d06645f\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"OXerYvGL\"><b>43、举例说明zip（）函数用法</b></p><p data-pid=\"tmhoky8T\">zip()函数在运算时，会以一个或多个序列（可迭代对象）做为参数，返回一个元组的列表。同时将这些序列中并排的元素配对。</p><p data-pid=\"URr8jIfR\">zip()参数可以接受任何类型的序列，同时也可以有两个以上的参数;当传入参数的长度不同时，zip能自动以最短序列长度为准进行截取，获得元组。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-6e29d7a331e179f082f2efad79fc350b_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"773\" data-rawheight=\"559\" class=\"origin_image zh-lightbox-thumb\" width=\"773\" data-original=\"https://pic2.zhimg.com/v2-6e29d7a331e179f082f2efad79fc350b_r.jpg\" data-original-token=\"v2-68848822138dc0b9c8d4bca40e23bd6d\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"9tqyPCBj\"><b>44、a=&#34;张明 98分&#34;，用re.sub，将98替换为100</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-fb1fb9875124832980e31d29102c7fd7_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"654\" data-rawheight=\"183\" class=\"origin_image zh-lightbox-thumb\" width=\"654\" data-original=\"https://pic4.zhimg.com/v2-fb1fb9875124832980e31d29102c7fd7_r.jpg\" data-original-token=\"v2-7cdb95ccd8c422910212376aa6bf01d6\"/></figure><p data-pid=\"hQ30sH4W\"><b>45、写5条常用sql语句</b></p><p data-pid=\"8-Y3Og8T\">show databases;</p><p data-pid=\"0uy3UrA-\">show tables;</p><p data-pid=\"wGVdQpga\">desc 表名;</p><p data-pid=\"ij-HlQ07\">select * from 表名;</p><p data-pid=\"qb4eT4_Q\">delete from 表名 where id=5;</p><p data-pid=\"1eC2xLul\">update students set gender=0,hometown=&#34;北京&#34; where id=5</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"XPU_OHxJ\"><b>46、a=&#34;hello&#34;和b=&#34;你好&#34;编码成bytes类型</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-9dd4fd715a521aa6ef547e05c6a61c75_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"679\" data-rawheight=\"311\" class=\"origin_image zh-lightbox-thumb\" width=\"679\" data-original=\"https://pic2.zhimg.com/v2-9dd4fd715a521aa6ef547e05c6a61c75_r.jpg\" data-original-token=\"v2-718fcf3b02b975ccce88053631cb6bd4\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"MD9W1EAB\"><b>47、[1,2,3]+[4,5,6]的结果是多少？</b></p><p data-pid=\"08fXusJC\">两个列表相加，等价于extend</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-ce586b3697256afff94998134e010591_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"534\" data-rawheight=\"221\" class=\"origin_image zh-lightbox-thumb\" width=\"534\" data-original=\"https://pic2.zhimg.com/v2-ce586b3697256afff94998134e010591_r.jpg\" data-original-token=\"v2-119d0efaec63511975049e9e615e0a93\"/></figure><p data-pid=\"GXeXLp0O\"><b>48、提高python运行效率的方法</b></p><p data-pid=\"2KxPB01O\">1、使用生成器，因为可以节约大量内存</p><p data-pid=\"5V-hI9yT\">2、循环代码优化，避免过多重复代码的执行</p><p data-pid=\"y6jsFAZw\">3、核心模块用Cython  PyPy等，提高效率</p><p data-pid=\"HBovR5XV\">4、多进程、多线程、协程</p><p data-pid=\"m2P6HztP\">5、多个if elif条件判断，可以把最有可能先发生的条件放到前面写，这样可以减少程序判断的次数，提高效率</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ZUKe3WIf\"><b>49、简述mysql和redis区别</b></p><p data-pid=\"8DMXrAUX\">redis： 内存型非关系数据库，数据保存在内存中，速度快</p><p data-pid=\"CS0xwZ3n\">mysql：关系型数据库，数据保存在磁盘中，检索的话，会有一定的Io操作，访问速度相对慢</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"YnIJW6sq\"><b>50、遇到bug如何处理</b></p><p data-pid=\"AU3o1xZi\">1、细节上的错误，通过print（）打印，能执行到print（）说明一般上面的代码没有问题，分段检测程序是否有问题，如果是js的话可以alert或console.log</p><p data-pid=\"4wVH7XbO\">2、如果涉及一些第三方框架，会去查官方文档或者一些技术博客。</p><p data-pid=\"YGiD9t2A\">3、对于bug的管理与归类总结，一般测试将测试出的bug用teambin等bug管理工具进行记录，然后我们会一条一条进行修改，修改的过程也是理解业务逻辑和提高自己编程逻辑缜密性的方法，我也都会收藏做一些笔记记录。</p><p data-pid=\"CjiCIcw_\">4、导包问题、城市定位多音字造成的显示错误问题</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"C6qzMIgY\"><b>51、正则匹配，匹配日期2018-03-20</b></p><p data-pid=\"7HCzlc3_\">url=&#39;<a href=\"https://link.zhihu.com/?target=https%3A//sycm.taobao.com/bda/tradinganaly/overview/get_summary.json%3FdateRange%3D2018-03-20%257C2018-03-20%26dateType%3Drecent1%26device%3D1%26token%3Dff25b109b%26_%3D1521595613462\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">sycm.taobao.com/bda/tra</span><span class=\"invisible\">dinganaly/overview/get_summary.json?dateRange=2018-03-20%7C2018-03-20&amp;dateType=recent1&amp;device=1&amp;token=ff25b109b&amp;_=1521595613462</span><span class=\"ellipsis\"></span></a>&#39;</p><p data-pid=\"z1xH0xYC\">仍有同学问正则，其实匹配并不难，提取一段特征语句，用（.*?）匹配即可</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-3deec7c53fba28fe15e4d368b7c334fe_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"157\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-3deec7c53fba28fe15e4d368b7c334fe_r.jpg\" data-original-token=\"v2-3deec7c53fba28fe15e4d368b7c334fe\"/></figure><p data-pid=\"v_LqBWRe\"><b>52、list=[2,3,5,4,9,6]，从小到大排序，不许用sort，输出[2,3,4,5,6,9]</b></p><p data-pid=\"fHJjTgY5\">利用min()方法求出最小值，原列表删除最小值，新列表加入最小值，递归调用获取最小值的函数，反复操作</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-1ca15986ba67b76c5c32d4262dbe4d72_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"696\" data-rawheight=\"495\" class=\"origin_image zh-lightbox-thumb\" width=\"696\" data-original=\"https://pica.zhimg.com/v2-1ca15986ba67b76c5c32d4262dbe4d72_r.jpg\" data-original-token=\"v2-7b57bdd150f93554547c85c3ce9e5846\"/></figure><p data-pid=\"jcX1rnRl\"><b>53、写一个单列模式</b></p><p data-pid=\"UtNfQ3BK\">因为创建对象时__new__方法执行，并且必须return 返回实例化出来的对象所cls.__instance是否存在，不存在的话就创建对象，存在的话就返回该对象，来保证只有一个实例对象存在（单列），打印ID，值一样，说明对象同一个</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-ddc41190082913f2271a935b9b7f0299_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"603\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-ddc41190082913f2271a935b9b7f0299_r.jpg\" data-original-token=\"v2-13bb9a9c2b0cae73c99d60c9f1f7ac05\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"HyLBfSXW\"><b>54、保留两位小数</b></p><p data-pid=\"rjmcFLXa\">题目本身只有a=&#34;%.03f&#34;%1.3335,让计算a的结果，为了扩充保留小数的思路，提供round方法（数值，保留位数）</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-ff47f01de4060b74e4c0d88170082ccf_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"755\" data-rawheight=\"394\" class=\"origin_image zh-lightbox-thumb\" width=\"755\" data-original=\"https://pic2.zhimg.com/v2-ff47f01de4060b74e4c0d88170082ccf_r.jpg\" data-original-token=\"v2-1b5bfd76391159eb433bf6cc72bc8bf7\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Mk05cUZm\"><b>55、求三个方法打印结果</b></p><p data-pid=\"jrlSv-pe\">fn(&#34;one&#34;,1）直接将键值对传给字典；</p><p data-pid=\"Lt-yV-EN\">fn(&#34;two&#34;,2)因为字典在内存中是可变数据类型，所以指向同一个地址，传了新的额参数后，会相当于给字典增加键值对</p><p data-pid=\"myreEbTd\">fn(&#34;three&#34;,3,{})因为传了一个新字典，所以不再是原先默认参数的字典</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-80d4e8bb26e97fc3384a7238800e9361_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"657\" data-rawheight=\"308\" class=\"origin_image zh-lightbox-thumb\" width=\"657\" data-original=\"https://pic4.zhimg.com/v2-80d4e8bb26e97fc3384a7238800e9361_r.jpg\" data-original-token=\"v2-287649fe9f1f8c394b892e536ac1711f\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"MWwQ7Dll\"><b>56、列出常见的状态码和意义</b></p><p data-pid=\"wiNMP9cJ\">200 OK </p><p data-pid=\"Y-JBRRPu\">请求正常处理完毕</p><p data-pid=\"g6hxnEiU\">204 No Content </p><p data-pid=\"N0QW21hM\">请求成功处理，没有实体的主体返回</p><p data-pid=\"Uzr24aAh\">206 Partial Content </p><p data-pid=\"VuOPBZMe\">GET范围请求已成功处理</p><p data-pid=\"6B7v1u1-\">301 Moved Permanently </p><p data-pid=\"wgUUAM0V\">永久重定向，资源已永久分配新URI</p><p data-pid=\"Qrq2Wa6g\">302 Found </p><p data-pid=\"bUpIi3rV\">临时重定向，资源已临时分配新URI</p><p data-pid=\"HVSPNcPA\">303 See Other </p><p data-pid=\"RXY6W9X7\">临时重定向，期望使用GET定向获取</p><p data-pid=\"NrABkblG\">304 Not Modified </p><p data-pid=\"B3TWL1Wy\">发送的附带条件请求未满足</p><p data-pid=\"VoZj5tsd\">307 Temporary Redirect </p><p data-pid=\"h13F0Uqv\">临时重定向，POST不会变成GET</p><p data-pid=\"6ayWzALa\">400 Bad Request </p><p data-pid=\"AFxZbA4o\">请求报文语法错误或参数错误</p><p data-pid=\"RKrKg1XQ\">401 Unauthorized </p><p data-pid=\"_VIgKuW0\">需要通过HTTP认证，或认证失败</p><p data-pid=\"CziBKMHj\">403 Forbidden </p><p data-pid=\"bYxaIERB\">请求资源被拒绝</p><p data-pid=\"pfJRcc2H\">404 Not Found </p><p data-pid=\"1Id2qdbO\">无法找到请求资源（服务器无理由拒绝）</p><p data-pid=\"sp1BuEg7\">500 Internal Server Error </p><p data-pid=\"D9wSejIV\">服务器故障或Web应用故障</p><p data-pid=\"cuvSo7As\">503 Service Unavailable </p><p data-pid=\"wtV76u7l\">服务器超负载或停机维护</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"yhftLqW1\"><b>57、分别从前端、后端、数据库阐述web项目的性能优化</b></p><p data-pid=\"2WI1t6to\">该题目网上有很多方法，我不想截图网上的长串文字，看的头疼，按我自己的理解说几点</p><p data-pid=\"HenajAdm\"><b>前端优化：</b></p><p data-pid=\"R0_vVkfb\">1、减少http请求、例如制作精灵图</p><p data-pid=\"4rBLnZr7\">2、html和CSS放在页面上部，javascript放在页面下面，因为js加载比HTML和Css加载慢，所以要优先加载html和css,以防页面显示不全，性能差，也影响用户体验差</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"_zrs4IYG\"><b>后端优化：</b></p><p data-pid=\"e6KaSH3v\">1、缓存存储读写次数高，变化少的数据，比如网站首页的信息、商品的信息等。应用程序读取数据时，一般是先从缓存中读取，如果读取不到或数据已失效，再访问磁盘数据库，并将数据再次写入缓存。</p><p data-pid=\"p7MJH4y_\">2、异步方式，如果有耗时操作，可以采用异步，比如celery</p><p data-pid=\"LlNasz1m\">3、代码优化，避免循环和判断次数太多，如果多个if else判断，优先判断最有可能先发生的情况</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"N2M4lN4v\"><b>数据库优化：</b></p><p data-pid=\"azeQ72kU\">1、如有条件，数据可以存放于redis，读取速度快</p><p data-pid=\"ssfuQpu1\">2、建立索引、外键等</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"RSWBfIdu\"><b>58、使用pop和del删除字典中的&#34;name&#34;字段，dic={&#34;name&#34;:&#34;zs&#34;,&#34;age&#34;:18}</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-8e8189b589d2036c6280f71dad867a4f_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"778\" data-rawheight=\"285\" class=\"origin_image zh-lightbox-thumb\" width=\"778\" data-original=\"https://pic4.zhimg.com/v2-8e8189b589d2036c6280f71dad867a4f_r.jpg\" data-original-token=\"v2-855a7d5ab0bd3f87e68705cafd4ce6bf\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ul-dt_O2\"><b>59、列出常见MYSQL数据存储引擎</b></p><p data-pid=\"RrWTo9J9\"><b>InnoDB</b>：支持事务处理，支持外键，支持崩溃修复能力和并发控制。如果需要对事务的完整性要求比较高（比如银行），要求实现并发控制（比如售票），那选择InnoDB有很大的优势。如果需要频繁的更新、删除操作的数据库，也可以选择InnoDB，因为支持事务的提交（commit）和回滚（rollback）。 </p><p data-pid=\"QuG1BjxB\"><b>MyISAM</b>：插入数据快，空间和内存使用比较低。如果表主要是用于插入新记录和读出记录，那么选择MyISAM能实现处理高效率。如果应用的完整性、并发性要求比 较低，也可以使用。</p><p data-pid=\"jkYgpVNx\"><b>MEMORY</b>：所有的数据都在内存中，数据的处理速度快，但是安全性不高。如果需要很快的读写速度，对数据的安全性要求较低，可以选择MEMOEY。它对表的大小有要求，不能建立太大的表。所以，这类数据库只使用在相对较小的数据库表。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"DdNPYZfS\"><b>60、计算代码运行结果，zip函数历史文章已经说了，得出[(&#34;a&#34;,1),(&#34;b&#34;,2)，(&#34;c&#34;,3),(&#34;d&#34;,4),(&#34;e&#34;,5)]</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-3becbb671b27ea573732eb0beb514ce9_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"845\" data-rawheight=\"356\" class=\"origin_image zh-lightbox-thumb\" width=\"845\" data-original=\"https://pic2.zhimg.com/v2-3becbb671b27ea573732eb0beb514ce9_r.jpg\" data-original-token=\"v2-522b4232bb5921ef63f33c59688cd8a3\"/></figure><p data-pid=\"Z1bZkfbO\">dict()创建字典新方法</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-568912f2502524e8f688d553c25641e3_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"981\" data-rawheight=\"495\" class=\"origin_image zh-lightbox-thumb\" width=\"981\" data-original=\"https://pic2.zhimg.com/v2-568912f2502524e8f688d553c25641e3_r.jpg\" data-original-token=\"v2-99115c549379aa0c5a9d322e76d00aae\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"EOrzjpCv\"><b>61、简述同源策略</b></p><p data-pid=\"V0HoBF9N\"> 同源策略需要同时满足以下三点要求： </p><p data-pid=\"aYOmT7Qp\">1）协议相同 </p><p data-pid=\"NmU09Dad\"> 2）域名相同 </p><p data-pid=\"x1j309xS\">3）端口相同 </p><p data-pid=\"lRDwXsjJ\"> http:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com</span><span class=\"invisible\"></span></a>与https:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com</span><span class=\"invisible\"></span></a> 不同源——协议不同 </p><p data-pid=\"TfLgNKOu\"> http:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com</span><span class=\"invisible\"></span></a>与http:<a href=\"https://link.zhihu.com/?target=http%3A//www.admin.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">admin.com</span><span class=\"invisible\"></span></a> 不同源——域名不同 </p><p data-pid=\"QUhhsoQI\"> http:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com</span><span class=\"invisible\"></span></a>与http:<a href=\"https://link.zhihu.com/?target=http%3A//www.test.com%3A8081\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">test.com:8081</span><span class=\"invisible\"></span></a> 不同源——端口不同</p><p data-pid=\"mjVOsMCQ\"> 只要不满足其中任意一个要求，就不符合同源策略，就会出现“跨域”</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"GE4VmzJx\"><b>62、简述cookie和session的区别</b></p><p data-pid=\"xGVxIJ1M\">1，session 在服务器端，cookie 在客户端（浏览器）</p><p data-pid=\"r69d1JWI\">2、session 的运行依赖 session id，而 session id 是存在 cookie 中的，也就是说，如果浏览器禁用了 cookie ，同时 session 也会失效，存储Session时，键与Cookie中的sessionid相同，值是开发人员设置的键值对信息，进行了base64编码，过期时间由开发人员设置</p><p data-pid=\"CFVwq5J_\">3、cookie安全性比session差</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TvNdn4Ap\"><b>63、简述多线程、多进程</b></p><p data-pid=\"BTFCipwL\"><b>进程：</b></p><p data-pid=\"yAMoup4q\">1、操作系统进行资源分配和调度的基本单位，多个进程之间相互独立</p><p data-pid=\"ZlujXz8Y\">2、稳定性好，如果一个进程崩溃，不影响其他进程，但是进程消耗资源大，开启的进程数量有限制</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"YkCSg-2W\"><b>线程：</b></p><p data-pid=\"uMie7wJ8\">1、CPU进行资源分配和调度的基本单位，线程是进程的一部分，是比进程更小的能独立运行的基本单位，一个进程下的多个线程可以共享该进程的所有资源</p><p data-pid=\"iaWnE3cE\">2、如果IO操作密集，则可以多线程运行效率高，缺点是如果一个线程崩溃，都会造成进程的崩溃</p><p data-pid=\"MDyxNPIi\"><b>应用：</b></p><p data-pid=\"KlmHD1gb\">IO密集的用多线程，在用户输入，sleep 时候，可以切换到其他线程执行，减少等待的时间</p><p data-pid=\"MU0pDdrl\">CPU密集的用多进程，因为假如IO操作少，用多线程的话，因为线程共享一个全局解释器锁，当前运行的线程会霸占GIL，其他线程没有GIL，就不能充分利用多核CPU的优势</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"6UpFwEA0\"><b>64、简述any()和all()方法</b></p><p data-pid=\"cvrV2cRF\">any():只要迭代器中有一个元素为真就为真</p><p data-pid=\"uZOq6pIA\">all():迭代器中所有的判断项返回都是真，结果才为真</p><p data-pid=\"UHBoX0Ii\">python中什么元素为假？</p><p data-pid=\"95oLBOVh\">答案：（0，空字符串，空列表、空字典、空元组、None, False）</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-a29b93463b95cbb30bc9b0bdf68d22d5_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"239\" data-rawheight=\"406\" class=\"content_image\" width=\"239\" data-original-token=\"v2-aaa6370df990a208f7999ed3fa7018e4\"/></figure><p data-pid=\"WCenIBtd\">测试all()和any()方法</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-b65bda1c52cb62eea5a23384be3839e4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"655\" data-rawheight=\"741\" class=\"origin_image zh-lightbox-thumb\" width=\"655\" data-original=\"https://pic3.zhimg.com/v2-b65bda1c52cb62eea5a23384be3839e4_r.jpg\" data-original-token=\"v2-30c661e8dfd25dc73aa31205d7a535a6\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"cjBh03qn\"><b>65、IOError、AttributeError、ImportError、IndentationError、IndexError、KeyError、SyntaxError、NameError分别代表什么异常</b></p><p data-pid=\"TWuZU5K7\">IOError：输入输出异常</p><p data-pid=\"2rHYp5HG\">AttributeError：试图访问一个对象没有的属性</p><p data-pid=\"0Nu_29Ag\">ImportError：无法引入模块或包，基本是路径问题</p><p data-pid=\"LIGunM43\">IndentationError：语法错误，代码没有正确的对齐</p><p data-pid=\"Xe9sTUEU\">IndexError：下标索引超出序列边界</p><p data-pid=\"w-u519wF\">KeyError:试图访问你字典里不存在的键</p><p data-pid=\"iGYqPNan\">SyntaxError:Python代码逻辑语法出错，不能执行</p><p data-pid=\"hqM8LgSr\">NameError:使用一个还未赋予对象的变量</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"LzeSlf4l\"><b>66、python中copy和deepcopy区别</b></p><p data-pid=\"wGUtRmkc\">1、复制不可变数据类型，不管copy还是deepcopy,都是同一个地址当浅复制的值是不可变对象（数值，字符串，元组）时和=“赋值”的情况一样，对象的id值与浅复制原来的值相同。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-a5e9735f28c7fb2b8fdc586bc2c22427_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"770\" data-rawheight=\"417\" class=\"origin_image zh-lightbox-thumb\" width=\"770\" data-original=\"https://picx.zhimg.com/v2-a5e9735f28c7fb2b8fdc586bc2c22427_r.jpg\" data-original-token=\"v2-e6d997cfe6648e832d4fede66a2c3405\"/></figure><p data-pid=\"n7LM5rgP\">2、复制的值是可变对象（列表和字典）</p><p data-pid=\"0YHzXGVE\">浅拷贝copy有两种情况：</p><p data-pid=\"ifrTZDaV\">第一种情况：复制的 对象中无 复杂 子对象，原来值的改变并不会影响浅复制的值，同时浅复制的值改变也并不会影响原来的值。原来值的id值与浅复制原来的值不同。</p><p data-pid=\"pSd-9WGK\">第二种情况：复制的对象中有 复杂 子对象 （例如列表中的一个子元素是一个列表）， 改变原来的值 中的复杂子对象的值  ，会影响浅复制的值。</p><p data-pid=\"gAz-w80Z\">深拷贝deepcopy：完全复制独立，包括内层列表和字典</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-6d5c879af339a0c35a925648ba3afadf_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"545\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-6d5c879af339a0c35a925648ba3afadf_r.jpg\" data-original-token=\"v2-105cdcf5006394d7c06fddf490428075\"/></figure><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-bb5abd389e0b9cd1b3b163897e623fe7_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"354\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-bb5abd389e0b9cd1b3b163897e623fe7_r.jpg\" data-original-token=\"v2-a4baebbe34ef0e7170fdd822256f6392\"/></figure><p data-pid=\"wR07DSUC\"><b>67、列出几种魔法方法并简要介绍用途</b></p><p data-pid=\"Wni9lrPg\">__init__:对象初始化方法</p><p data-pid=\"1Qmuvu2W\">__new__:创建对象时候执行的方法，单列模式会用到</p><p data-pid=\"Z7caB_uj\">__str__:当使用print输出对象的时候，只要自己定义了__str__(self)方法，那么就会打印从在这个方法中return的数据</p><p data-pid=\"Whs8kw2w\">__del__:删除对象执行的方法</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"m3CO0y5n\"><b>68、C:\\Users\\<USER>\\Desktop&gt;python 1.py 22 33命令行启动程序并传参，print(sys.argv)会输出什么数据？</b></p><p data-pid=\"eEnarajA\">文件名和参数构成的列表</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-29361d7dd6cc2a70e6080047cf779bcd_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"511\" data-rawheight=\"67\" class=\"origin_image zh-lightbox-thumb\" width=\"511\" data-original=\"https://pic2.zhimg.com/v2-29361d7dd6cc2a70e6080047cf779bcd_r.jpg\" data-original-token=\"v2-29361d7dd6cc2a70e6080047cf779bcd\"/></figure><p data-pid=\"QRiTqBZW\"><b>69、请将[i for i in range(3)]改成生成器</b></p><p data-pid=\"JJTX6K-s\">生成器是特殊的迭代器，</p><p data-pid=\"vOGNhxcb\">1、列表表达式的【】改为（）即可变成生成器</p><p data-pid=\"Vq97vQax\">2、函数在返回值得时候出现yield就变成生成器，而不是函数了；</p><p data-pid=\"_HRCRiLJ\">中括号换成小括号即可，有没有惊呆了</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-db416f76770c675609b75a74135f1979_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"450\" data-rawheight=\"147\" class=\"origin_image zh-lightbox-thumb\" width=\"450\" data-original=\"https://picx.zhimg.com/v2-db416f76770c675609b75a74135f1979_r.jpg\" data-original-token=\"v2-d6c3a48e57e7acf45f785a57cad36cd1\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"o-mDcyTv\"><b>70、a = &#34;  hehheh  &#34;,去除收尾空格</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-9ecb056190485d46c23cb4cc039fb963_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"327\" data-rawheight=\"141\" class=\"content_image\" width=\"327\" data-original-token=\"v2-03e5f7879c68a779762bb8e59e09f0ab\"/></figure><p data-pid=\"i2kClkAZ\"><b>71、举例sort和sorted对列表排序，list=[0,-1,3,-10,5,9]</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-d4497b05cc42514e38b95fc00f79afe4_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"842\" data-rawheight=\"380\" class=\"origin_image zh-lightbox-thumb\" width=\"842\" data-original=\"https://pica.zhimg.com/v2-d4497b05cc42514e38b95fc00f79afe4_r.jpg\" data-original-token=\"v2-44a969b5adf63cafcc1150cca1df3301\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"dZTfIFJd\"><b>72、对list排序foo = [-5,8,0,4,9,-4,-20,-2,8,2,-4],使用lambda函数从小到大排序</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-0b3786be700e8a7f5acf4ccef27a9359_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"704\" data-rawheight=\"202\" class=\"origin_image zh-lightbox-thumb\" width=\"704\" data-original=\"https://pic4.zhimg.com/v2-0b3786be700e8a7f5acf4ccef27a9359_r.jpg\" data-original-token=\"v2-9260e0bb8730df43bf7e10c8edac25ab\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"M1O9b-5V\"><b>73、使用lambda函数对list排序foo = [-5,8,0,4,9,-4,-20,-2,8,2,-4]，输出结果为</b></p><p data-pid=\"g8qHST1X\"><b>[0,2,4,8,8,9,-2,-4,-4,-5,-20]，正数从小到大，负数从大到小</b></p><p data-pid=\"07G5m787\">（传两个条件，x&lt;0和abs(x)）</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-b96b7d4ff4e49a69fedca0a2cc16f880_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"741\" data-rawheight=\"205\" class=\"origin_image zh-lightbox-thumb\" width=\"741\" data-original=\"https://pica.zhimg.com/v2-b96b7d4ff4e49a69fedca0a2cc16f880_r.jpg\" data-original-token=\"v2-f6e6f5d3591575c3d7d387782be394de\"/></figure><p data-pid=\"Ox9jiYCF\"><b>74、列表嵌套字典的排序，分别根据年龄和姓名排序</b></p><p data-pid=\"Mx6595iC\">foo = [{&#34;name&#34;:&#34;zs&#34;,&#34;age&#34;:19},{&#34;name&#34;:&#34;ll&#34;,&#34;age&#34;:54},</p><p data-pid=\"NLUMrpPq\">        {&#34;name&#34;:&#34;wa&#34;,&#34;age&#34;:17},{&#34;name&#34;:&#34;df&#34;,&#34;age&#34;:23}]</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-17daf4837bafb07c884bbf5fa3cf6b73_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"322\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-17daf4837bafb07c884bbf5fa3cf6b73_r.jpg\" data-original-token=\"v2-dab81670c0635136d38c7fd67186be39\"/></figure><p data-pid=\"RN0HMyu9\"><b>75、列表嵌套元组，分别按字母和数字排序</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-cdc06f1f5b50f8c47911993080769bfe_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"794\" data-rawheight=\"322\" class=\"origin_image zh-lightbox-thumb\" width=\"794\" data-original=\"https://pica.zhimg.com/v2-cdc06f1f5b50f8c47911993080769bfe_r.jpg\" data-original-token=\"v2-20cc603e170c39db53b94fb16910731c\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"VonSPLJl\"><b>76、列表嵌套列表排序，年龄数字相同怎么办？</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-acd639c002d9f79e368e1352a86a9fff_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"278\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-acd639c002d9f79e368e1352a86a9fff_r.jpg\" data-original-token=\"v2-fc08e5bb8748523e6eb39b3e1f69f687\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"8wEgDjK_\"><b>77、根据键对字典排序（方法一，zip函数）</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-23b296ec6adedcfb32edbf8ee45b2034_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"986\" data-rawheight=\"415\" class=\"origin_image zh-lightbox-thumb\" width=\"986\" data-original=\"https://pic3.zhimg.com/v2-23b296ec6adedcfb32edbf8ee45b2034_r.jpg\" data-original-token=\"v2-4317148267814738ae76baeb0ac15599\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"xseAEFf0\"><b>78、根据键对字典排序（方法二,不用zip)</b></p><p data-pid=\"cpZ4WmH5\">有没有发现dic.items和zip(dic.keys(),dic.values())都是为了构造列表嵌套字典的结构，方便后面用sorted()构造排序规则</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-a0b33ead2742d03a20a3abb4a9c70c02_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1036\" data-rawheight=\"387\" class=\"origin_image zh-lightbox-thumb\" width=\"1036\" data-original=\"https://pic1.zhimg.com/v2-a0b33ead2742d03a20a3abb4a9c70c02_r.jpg\" data-original-token=\"v2-394c763dc081336b7ae289a9a1319d8b\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"zM7bk0fM\"><b>79、列表推导式、字典推导式、生成器</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-58b94b7b8ab51d7eb7adc9a9a1f23ad1_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"364\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-58b94b7b8ab51d7eb7adc9a9a1f23ad1_r.jpg\" data-original-token=\"v2-dbc41ef91f450ef404beacc62fbace72\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Jj_fFve0\"><b>80、最后出一道检验题目，根据字符串长度排序，看排序是否灵活运用</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-7e249299caa95a3a2d6c97be0e6c0d66_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"929\" data-rawheight=\"343\" class=\"origin_image zh-lightbox-thumb\" width=\"929\" data-original=\"https://pica.zhimg.com/v2-7e249299caa95a3a2d6c97be0e6c0d66_r.jpg\" data-original-token=\"v2-7e45060c3667c590015368080804f1ae\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"gEh1adc9\"><b>81、举例说明SQL注入和解决办法</b></p><p data-pid=\"lpTAD-NN\">当以字符串格式化书写方式的时候，如果用户输入的有;+SQL语句，后面的SQL语句会执行，比如例子中的SQL注入会删除数据库demo</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-d30912de4dbe9a0c1cc0e86c55581208_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"406\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-d30912de4dbe9a0c1cc0e86c55581208_r.jpg\" data-original-token=\"v2-82f1f4d0661e4b0c6038b7c22751f3d3\"/></figure><p data-pid=\"EyKn-ta_\">解决方式：通过传参数方式解决SQL注入</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-4c84a53da2889ea7fc06c19b8c0ae39c_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"919\" data-rawheight=\"114\" class=\"origin_image zh-lightbox-thumb\" width=\"919\" data-original=\"https://pic1.zhimg.com/v2-4c84a53da2889ea7fc06c19b8c0ae39c_r.jpg\" data-original-token=\"v2-4c84a53da2889ea7fc06c19b8c0ae39c\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WJfVs2cV\"><b>82、s=&#34;info:xiaoZhang 33 shandong&#34;,用正则切分字符串输出[&#39;info&#39;, &#39;xiaoZhang&#39;, &#39;33&#39;, &#39;shandong&#39;]</b></p><p data-pid=\"UH3yDEyT\">|表示或，根据冒号或者空格切分</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-40e7a3816f94cbc2d2228d703bed4f57_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"675\" data-rawheight=\"186\" class=\"origin_image zh-lightbox-thumb\" width=\"675\" data-original=\"https://picx.zhimg.com/v2-40e7a3816f94cbc2d2228d703bed4f57_r.jpg\" data-original-token=\"v2-448d8589c7728286a38b861f5705962d\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"KIpaf9Pl\"><b>83、正则匹配以<a href=\"https://link.zhihu.com/?target=http%3A//163.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">163.com</span><span class=\"invisible\"></span></a>结尾的邮箱</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-7dc3bd8be8b1ec04f92a46defcc954e8_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"284\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-7dc3bd8be8b1ec04f92a46defcc954e8_r.jpg\" data-original-token=\"v2-d001cb4b73f36c1c2b2a9b25167ae3cd\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"bjng8PoV\"><b>84、递归求和</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-baf349ecbe28869c13d33ee3526e4783_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"785\" data-rawheight=\"444\" class=\"origin_image zh-lightbox-thumb\" width=\"785\" data-original=\"https://pic4.zhimg.com/v2-baf349ecbe28869c13d33ee3526e4783_r.jpg\" data-original-token=\"v2-8745690eac8a06cfefa10b9c5c19fb20\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"DheO0kvN\"><b>85、python字典和json字符串相互转化方法</b></p><p data-pid=\"l2jE1cDj\">json.dumps()字典转json字符串，json.loads()json转字典</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-345e3ac873e5ab011f6988d45991d124_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"624\" data-rawheight=\"325\" class=\"origin_image zh-lightbox-thumb\" width=\"624\" data-original=\"https://pic1.zhimg.com/v2-345e3ac873e5ab011f6988d45991d124_r.jpg\" data-original-token=\"v2-87b97f5db70f12ec9d2795499020a211\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"25ZMgR8D\"><b>86、MyISAM 与 InnoDB 区别：</b></p><p data-pid=\"BJ5lBPBM\">1、InnoDB 支持事务，MyISAM 不支持，这一点是非常之重要。事务是一种高</p><p data-pid=\"Y6904iIl\">级的处理方式，如在一些列增删改中只要哪个出错还可以回滚还原，而 MyISAM</p><p data-pid=\"F772eE4r\">就不可以了；</p><p data-pid=\"63e_-NvN\">2、MyISAM 适合查询以及插入为主的应用，InnoDB 适合频繁修改以及涉及到</p><p data-pid=\"dE9LL_8g\">安全性较高的应用；</p><p data-pid=\"9Ai84XKL\">3、InnoDB 支持外键，MyISAM 不支持；</p><p data-pid=\"cyopH7go\">4、对于自增长的字段，InnoDB 中必须包含只有该字段的索引，但是在 MyISAM</p><p data-pid=\"uYKRYScF\">表中可以和其他字段一起建立联合索引；</p><p data-pid=\"cU50_ylG\">5、清空整个表时，InnoDB 是一行一行的删除，效率非常慢。MyISAM 则会重</p><p data-pid=\"89iQ5xUx\">建表；</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"eQQg6wwz\"><b>87、统计字符串中某字符出现次数</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-31459244718a8be60205914c405959fa_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"746\" data-rawheight=\"208\" class=\"origin_image zh-lightbox-thumb\" width=\"746\" data-original=\"https://pic1.zhimg.com/v2-31459244718a8be60205914c405959fa_r.jpg\" data-original-token=\"v2-dcfdf0c7feda1181ba0065badd065b4e\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"4PMqY6Y9\"><b>88、字符串转化大小写</b></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-89d846a9995fafe27f6b621c684fa05d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"598\" data-rawheight=\"304\" class=\"origin_image zh-lightbox-thumb\" width=\"598\" data-original=\"https://pic2.zhimg.com/v2-89d846a9995fafe27f6b621c684fa05d_r.jpg\" data-original-token=\"v2-8cf7cdff661cd94b99083fd9640ad9ce\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"K7Se5xFA\"><b>89、用两种方法去空格</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-dd1c39d490b5c7285c7e7a06675d15a9_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"727\" data-rawheight=\"278\" class=\"origin_image zh-lightbox-thumb\" width=\"727\" data-original=\"https://picx.zhimg.com/v2-dd1c39d490b5c7285c7e7a06675d15a9_r.jpg\" data-original-token=\"v2-41c32f54df7e2da01d6e546b4ce16c3a\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"qIAypobA\"><b>90、正则匹配不是以4和7结尾的手机号</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-f05ccc0f0ab4a3ba39338419c84b81b7_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1067\" data-rawheight=\"434\" class=\"origin_image zh-lightbox-thumb\" width=\"1067\" data-original=\"https://picx.zhimg.com/v2-f05ccc0f0ab4a3ba39338419c84b81b7_r.jpg\" data-original-token=\"v2-84b21bf08e4a2521c50507fd9e8c0945\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"dEgAYltu\"><b>91、简述python引用计数机制</b></p><p data-pid=\"JMHj1_lV\">python垃圾回收主要以引用计数为主，标记-清除和分代清除为辅的机制，其中标记-清除和分代回收主要是为了处理循环引用的难题。<br/><b>引用计数算法</b></p><p data-pid=\"P_hZ99bi\">当有1个变量保存了对象的引用时，此对象的引用计数就会加1</p><p data-pid=\"xRlrcHA6\">当使用del删除变量指向的对象时，如果对象的引用计数不为1，比如3，那么此时只会让这个引用计数减1，即变为2，当再次调用del时，变为1，如果再调用1次del，此时会真的把对象进行删除</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-ab367fbba3d3b06b8518babbf141e7c8_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"786\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-ab367fbba3d3b06b8518babbf141e7c8_r.jpg\" data-original-token=\"v2-56ddc7b9713850d8b7dde80513058b38\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"WVnsfETc\"><b>92、int(&#34;1.4&#34;),int(1.4)输出结果？</b></p><p data-pid=\"FrJa-8m8\">int(&#34;1.4&#34;)报错，int(1.4)输出1</p><p data-pid=\"KQD_gDlb\"><b>93、列举3条以上PEP8编码规范</b></p><p data-pid=\"ynan2D-W\">1、顶级定义之间空两行，比如函数或者类定义。</p><p data-pid=\"lbEBN1RE\">2、方法定义、类定义与第一个方法之间，都应该空一行</p><p data-pid=\"P9JgFLcQ\">3、三引号进行注释</p><p data-pid=\"BMNEEUv1\">4、使用Pycharm、Eclipse一般使用4个空格来缩进代码</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"j6ewtueD\"><b>94、正则表达式匹配第一个URL</b></p><p data-pid=\"bROc9_2J\">findall结果无需加group(),search需要加group()提取</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-163f85d4d3d57c1122635a31fdd3e4b2_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"229\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-163f85d4d3d57c1122635a31fdd3e4b2_r.jpg\" data-original-token=\"v2-ae2cfa210330ba3c0290ad42f8e5fac8\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Zy1RQ0wA\"><b>95、正则匹配中文</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-ece9de9c763e9b50a5f5525a7d733b7a_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"825\" data-rawheight=\"303\" class=\"origin_image zh-lightbox-thumb\" width=\"825\" data-original=\"https://pica.zhimg.com/v2-ece9de9c763e9b50a5f5525a7d733b7a_r.jpg\" data-original-token=\"v2-65a69e412574a15419f5da564866b222\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"rkzM9egM\"><b>96、简述乐观锁和悲观锁</b></p><p data-pid=\"in5R_GJh\">悲观锁, 就是很悲观，每次去拿数据的时候都认为别人会修改，所以每次在拿数据的时候都会上锁，这样别人想拿这个数据就会block直到它拿到锁。传统的关系型数据库里边就用到了很多这种锁机制，比如行锁，表锁等，读锁，写锁等，都是在做操作之前先上锁。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"QCl6ygqE\">乐观锁，就是很乐观，每次去拿数据的时候都认为别人不会修改，所以不会上锁，但是在更新的时候会判断一下在此期间别人有没有去更新这个数据，可以使用版本号等机制，乐观锁适用于多读的应用类型，这样可以提高吞吐量</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"48FYu6aO\"><b>97、r、r+、rb、rb+文件打开模式区别</b></p><p data-pid=\"93rADy6a\">模式较多，比较下背背记记即可</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-a4853bc1e3b7cbcef70d43fa6dbdd75d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"768\" data-rawheight=\"776\" class=\"origin_image zh-lightbox-thumb\" width=\"768\" data-original=\"https://pic4.zhimg.com/v2-a4853bc1e3b7cbcef70d43fa6dbdd75d_r.jpg\" data-original-token=\"v2-d53504480b1ff3273b85be4d7a6dda0b\"/></figure><p data-pid=\"gqr10g1V\"><b>98、Linux命令重定向 &gt; 和 &gt;&gt;</b></p><p data-pid=\"frUPmArl\">Linux 允许将命令执行结果 重定向到一个 文件</p><p data-pid=\"BulZbzIa\">将本应显示在终端上的内容 输出／追加 到指定文件中</p><p data-pid=\"x_oxc9u-\">&gt; 表示输出，会覆盖文件原有的内容</p><p data-pid=\"n-Hbpp43\">&gt;&gt; 表示追加，会将内容追加到已有文件的末尾</p><p data-pid=\"kztrCoeW\">用法示例：</p><div class=\"highlight\"><pre><code class=\"language-text\">将 echo 输出的信息保存到 1.txt 里echo Hello Python &gt; 1.txt\n将 tree 输出的信息追加到 1.txt 文件的末尾tree &gt;&gt; 1.txt</code></pre></div><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"8cgn0E8T\"><b>99、正则表达式匹配出&lt;html&gt;&lt;h1&gt;<a href=\"https://link.zhihu.com/?target=http%3A//www.itcast.cn\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">itcast.cn</span><span class=\"invisible\"></span></a>&lt;/h1&gt;&lt;/html&gt;</b></p><p data-pid=\"GiFoGpgx\">前面的&lt;&gt;和后面的&lt;&gt;是对应的，可以用此方法</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-8ad04aa71274607b166b291407856565_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"371\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-8ad04aa71274607b166b291407856565_r.jpg\" data-original-token=\"v2-28665c142b50da77a39c90d1cd606b11\"/></figure><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-b9d4b3ace83dedeba36976df6eeb704e_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"566\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic1.zhimg.com/v2-b9d4b3ace83dedeba36976df6eeb704e_r.jpg\" data-original-token=\"v2-746f4559da0b287c4c59c21f6c86afb1\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"J8bLNsK9\"><b>100、python传参数是传值还是传址？</b></p><p data-pid=\"_ZD8134o\">Python中函数参数是引用传递（注意不是值传递）。对于不可变类型（数值型、字符串、元组），因变量不能修改，所以运算不会影响到变量自身；而对于可变类型（列表字典）来说，函数体运算可能会更改传入的参数变量。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TiD61ygD\"><b>101、求两个列表的交集、差集、并集</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-df282836f4ae90ea9bb0af6cd7f7954d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"463\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-df282836f4ae90ea9bb0af6cd7f7954d_r.jpg\" data-original-token=\"v2-3dd18e79c9e7af236b7c871746d0a9f1\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"1A9GHnS9\"><b>102、生成0-100的随机数</b></p><p data-pid=\"gJXHX5X8\">random.random()生成0-1之间的随机小数，所以乘以100</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-501177a01208cbbd22e44326dc885fdc_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"771\" data-rawheight=\"295\" class=\"origin_image zh-lightbox-thumb\" width=\"771\" data-original=\"https://pic3.zhimg.com/v2-501177a01208cbbd22e44326dc885fdc_r.jpg\" data-original-token=\"v2-d7950e205d18f33d84635bfc3c1a2893\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wT-IZrtz\"><b>103、lambda匿名函数好处</b></p><p data-pid=\"HT09V8RM\">精简代码，lambda省去了定义函数，map省去了写for循环过程</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-b9c32d8012e1802068bb184e42d7de26_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"204\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-b9c32d8012e1802068bb184e42d7de26_r.jpg\" data-original-token=\"v2-2311bd94fdfd8fca80839b3bb66a96c0\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TJG3Bvxy\"><b>104、常见的网络传输协议</b></p><p data-pid=\"2hNmSBYN\">UDP、TCP、FTP、HTTP、SMTP等等</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"sNKGr2a4\"><b>105、单引号、双引号、三引号用法</b></p><p data-pid=\"8y2fNDlx\">1、单引号和双引号没有什么区别，不过单引号不用按shift，打字稍微快一点。表示字符串的时候，单引号里面可以用双引号，而不用转义字符,反之亦然。</p><div class=\"highlight\"><pre><code class=\"language-text\">&#39;She said:&#34;Yes.&#34; &#39; or &#34;She said: &#39;Yes.&#39; &#34;</code></pre></div><p data-pid=\"-WL5LRWs\"><code>2、但是如果直接用单引号扩住单引号，则需要转义，像这样：</code></p><div class=\"highlight\"><pre><code class=\"language-text\"> &#39; She said:\\&#39;Yes.\\&#39; &#39;</code></pre></div><p data-pid=\"dbUR2tds\">3、三引号可以直接书写多行，通常用于大段，大篇幅的字符串</p><div class=\"highlight\"><pre><code class=\"language-text\">&#34;&#34;&#34;\nhello\nworld\n&#34;&#34;&#34; </code></pre></div><p data-pid=\"hodIwKMx\"><b>106、python垃圾回收机制</b></p><p data-pid=\"vbtlvHno\">python垃圾回收主要以引用计数为主，标记-清除和分代清除为辅的机制，其中标记-清除和分代回收主要是为了处理循环引用的难题。</p><h2><b>引用计数算法</b></h2><p data-pid=\"waIEOIlB\">当有1个变量保存了对象的引用时，此对象的引用计数就会加1</p><p data-pid=\"PCmOcVcA\">当使用del删除变量指向的对象时，如果对象的引用计数不为1，比如3，那么此时只会让这个引用计数减1，即变为2，当再次调用del时，变为1，如果再调用1次del，此时会真的把对象进行删除</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-4f65883a0cd845aef0d65a17b06d8c65_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"497\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-4f65883a0cd845aef0d65a17b06d8c65_r.jpg\" data-original-token=\"v2-a09660b30eb9a8de6ab0d3603b06c8cf\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"M7NNCmgN\"><b>107、HTTP请求中get和post区别</b></p><p data-pid=\"9HL874cW\">1、GET请求是通过URL直接请求数据，数据信息可以在URL中直接看到，比如浏览器访问；而POST请求是放在请求头中的，我们是无法直接看到的；</p><p data-pid=\"7ta2hLNW\">2、GET提交有数据大小的限制，一般是不超过1024个字节，而这种说法也不完全准确，HTTP协议并没有设定URL字节长度的上限，而是浏览器做了些处理，所以长度依据浏览器的不同有所不同；POST请求在HTTP协议中也没有做说明，一般来说是没有设置限制的，但是实际上浏览器也有默认值。总体来说，少量的数据使用GET，大量的数据使用POST。</p><p data-pid=\"TrIZ-HCb\">3、GET请求因为数据参数是暴露在URL中的，所以安全性比较低，比如密码是不能暴露的，就不能使用GET请求；POST请求中，请求参数信息是放在请求头的，所以安全性较高，可以使用。在实际中，涉及到登录操作的时候，尽量使用HTTPS请求，安全性更好。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"JZH9aJGh\"><b>108、python中读取Excel文件的方法</b></p><p data-pid=\"PS54VI_N\">应用数据分析库pandas</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-8edcefb5fd9aae0236b501ce2c8324f0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"965\" data-rawheight=\"318\" class=\"origin_image zh-lightbox-thumb\" width=\"965\" data-original=\"https://pic1.zhimg.com/v2-8edcefb5fd9aae0236b501ce2c8324f0_r.jpg\" data-original-token=\"v2-b418c47faf847ec3f148fd4a49d0cc81\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"RBlSRPBM\"><b>109、简述多线程、多进程</b></p><p data-pid=\"7fNB-bAR\"><b>进程：</b></p><p data-pid=\"wuXDVxzu\">1、操作系统进行资源分配和调度的基本单位，多个进程之间相互独立</p><p data-pid=\"wgpdrHvs\">2、稳定性好，如果一个进程崩溃，不影响其他进程，但是进程消耗资源大，开启的进程数量有限制</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wPAr0NnT\"><b>线程：</b></p><p data-pid=\"lvhaYQLz\">1、CPU进行资源分配和调度的基本单位，线程是进程的一部分，是比进程更小的能独立运行的基本单位，一个进程下的多个线程可以共享该进程的所有资源</p><p data-pid=\"DyT4heKM\">2、如果IO操作密集，则可以多线程运行效率高，缺点是如果一个线程崩溃，都会造成进程的崩溃</p><p data-pid=\"AlkTY2MM\"><b>应用：</b></p><p data-pid=\"QFyAebDO\">IO密集的用多线程，在用户输入，sleep 时候，可以切换到其他线程执行，减少等待的时间</p><p data-pid=\"X1nAjZ-b\">CPU密集的用多进程，因为假如IO操作少，用多线程的话，因为线程共享一个全局解释器锁，当前运行的线程会霸占GIL，其他线程没有GIL，就不能充分利用多核CPU的优势</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"kHcN_1ZO\"><b>110、python正则中search和match</b></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-bd3bfbb3df809744844f7263eefb2296_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"510\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-bd3bfbb3df809744844f7263eefb2296_r.jpg\" data-original-token=\"v2-9d39ef1216fabc3eeda92fb6fae29fde\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"cK2rYcjh\">我是  <a class=\"member_mention\" href=\"https://www.zhihu.com/people/7877379290d533427cce0d55ea4740e4\" data-hash=\"7877379290d533427cce0d55ea4740e4\" data-hovercard=\"p$b$7877379290d533427cce0d55ea4740e4\">@程序员资源社区</a> </p><p data-pid=\"P2F5Oxn1\"><b>资料已整理到Excel了，需要所有excel的话，仔细看我个人资料说明，仔细看我个人资料，仔细看我个人资料</b></p><p data-pid=\"PHl7OpB8\"><b>已经说的很明显了，大家稍微注意一下</b></p>", "excerpt": "python面试题： <a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s/SyC_LLQL8AU3i6wYNlOdNQ\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic3.zhimg.com/v2-43e53943fb3d6f6931a880c4cf0a4a08_180x120.jpg\" data-image-width=\"600\" data-image-height=\"255\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">110道python面试题</a> <b>1、一行代码实现1--100之和</b>利用sum()函数求和 <b>2、如何在一个函数内部修改全局变量</b>利用global在函数声明 修改全局变量 <b>3、列出5个python标准库</b> os：提供了不少与操作系统相关联的函数 sys: 通常用于命令行参数 re: 正则匹配 math: 数学运算 datetime:处理日期时间 <b>4、字典如何删除键和合并两个字典</b>del和update方法 <b>5、谈下python的GIL</b> GIL 是python的全局解释器锁，同一进程中假如有多个线程运…", "excerpt_new": "python面试题： <a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s/SyC_LLQL8AU3i6wYNlOdNQ\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic3.zhimg.com/v2-43e53943fb3d6f6931a880c4cf0a4a08_180x120.jpg\" data-image-width=\"600\" data-image-height=\"255\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">110道python面试题</a> <b>1、一行代码实现1--100之和</b>利用sum()函数求和 <b>2、如何在一个函数内部修改全局变量</b>利用global在函数声明 修改全局变量 <b>3、列出5个python标准库</b> os：提供了不少与操作系统相关联的函数 sys: 通常用于命令行参数 re: 正则匹配 math: 数学运算 datetime:处理日期时间 <b>4、字典如何删除键和合并两个字典</b>del和update方法 <b>5、谈下python的GIL</b> GIL 是python的全局解释器锁，同一进程中假如有多个线程运…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/54430650", "comment_permission": "all", "voteup_count": 9316, "comment_count": 156, "image_url": "https://picx.zhimg.com/v2-fb300ebb5c58f34702eb42e7a1341027_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1591940074, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1591918875973", "type": "feed", "target": {"id": "41054864", "type": "answer", "url": "https://api.zhihu.com/answers/41054864", "voteup_count": 45, "thanks_count": 30, "question": {"id": "28420688", "title": "职场中只会干活不爱表现要怎么改善？", "url": "https://api.zhihu.com/questions/28420688", "type": "question", "question_type": "normal", "created": 1425089333, "answer_count": 74, "comment_count": 0, "follower_count": 2026, "detail": "职场中认真做好分内的事好像已经不够了，不如爱表现把一说成十甚至投机取巧。有时一次演讲，一次表演，一次考试，一次和领导的对话就能改变一个人的职场生涯。如果性格原因导致不爱表现，甚至做好的成绩也不爱宣扬，这明显对职业发展不利，这是出于什么心理，这样的性格要怎么改善？", "excerpt": "职场中认真做好分内的事好像已经不够了，不如爱表现把一说成十甚至投机取巧。有时一…", "bound_topic_ids": [1537, 2566, 3479, 3946], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1447853593, "created_time": 1425400285, "author": {"id": "b336a7cba8f6e3d6a5314cff459c8a86", "name": "左飞<PERSON><PERSON>", "headline": "美国心理协会(APA)成员，新书：《不吼不叫陪孩子写作业》", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/z-u-o-f-e-i", "url_token": "z-u-o-f-e-i", "avatar_url": "https://picx.zhimg.com/v2-aec1173221590800b8fd28d5da8ddae0_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["儿童心理", "儿童教育", "幼儿教育", "亲子关系"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "儿童心理等 4 个话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "764d4e76e5e280da2c7904c3dd4da10b", "avatar_url": "https://picx.zhimg.com/764d4e76e5e280da2c7904c3dd4da10b_720w.jpg?source=32738c0c", "description": "", "id": "19586374", "name": "儿童心理", "priority": 0, "token": "19586374", "type": "topic", "url": "https://www.zhihu.com/topic/19586374"}, {"avatar_path": "b1ff5998c", "avatar_url": "https://pic1.zhimg.com/b1ff5998c_720w.jpg?source=32738c0c", "description": "", "id": "19556671", "name": "儿童教育", "priority": 0, "token": "19556671", "type": "topic", "url": "https://www.zhihu.com/topic/19556671"}, {"avatar_path": "91ca733a9", "avatar_url": "https://picx.zhimg.com/91ca733a9_720w.jpg?source=32738c0c", "description": "", "id": "19561528", "name": "幼儿教育", "priority": 0, "token": "19561528", "type": "topic", "url": "https://www.zhihu.com/topic/19561528"}, {"avatar_path": "v2-033ab8dac943c0dd39dea04caafe7700", "avatar_url": "https://picx.zhimg.com/v2-033ab8dac943c0dd39dea04caafe7700_720w.jpg?source=32738c0c", "description": "", "id": "19745291", "name": "亲子关系", "priority": 0, "token": "19745291", "type": "topic", "url": "https://www.zhihu.com/topic/19745291"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}], "title": "儿童心理等 4 个话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"0zV1Ayvy\">我个人认为，不是说现在这个社会就是一个成就爱表现的人的社会。而是说现在这个社会竞争越来越激烈，埋头做事很难被伯乐发现。因此，<strong>你不但要做的好，还要做的好看！</strong>这才是职场成就的正解。</p><br/><p data-pid=\"PhaJaNo7\">这里告诉你几条又实用又有效的小方法：</p><p data-pid=\"4OSwnkc7\">1.多打招呼</p><p data-pid=\"5dZ6_k5g\">见到公司的任何人都打招呼，大的叫姐、哥，小的叫姓名和职位，不熟悉的人的不要叫小名和外号。</p><br/><p data-pid=\"T0JjSeLn\">2.当别人向你索取帮助时，能帮尽量帮</p><p data-pid=\"LuJvW5Vl\">这显示自己为公的心态，领导会看到，同事也会看在眼里。对别人的好，别人总会记着。</p><br/><p data-pid=\"We4VlNxv\">3.做事注意细节，尤其是要给领导看的东西</p><p data-pid=\"AWfIFrk4\">ppt,文书，场地布置等细小但是很外表的东西一定要做的好看，这个好看就是要做到一丝不苟注意细节，最好能加点适当的创意。领导看到了会觉得你做事用心负责，值得信赖。</p><br/><p data-pid=\"9V69w_K2\">4.多创新，多出想法</p><p data-pid=\"k3vIsRsi\">想法不在好坏，而是要在于让大家看到你一直在思考和学习，在上进，并且多向领导讨教。能出个很好的想法最好，就算没有什么好想法，也能提高在领导面前的曝光率，混个脸熟，让领导看见自己的上进心，机会就会更多。</p><br/><p data-pid=\"ZuD1mf_S\">以上这些微不足道的小技巧是建立在自己的充分的工作能力的基础上的。最根本的还是要不断提升自己，学习专业知识。</p><br/><p data-pid=\"DEIzStRv\">最后推荐两本可以帮助题主提高自身职场生存能力的书，一本是《不是教你诈》还有一本是小说《浮沉》。</p><br/><p data-pid=\"jORHDrbz\">祝工作顺利！</p>", "excerpt": "我个人认为，不是说现在这个社会就是一个成就爱表现的人的社会。而是说现在这个社会竞争越来越激烈，埋头做事很难被伯乐发现。因此， <strong>你不但要做的好，还要做的好看！</strong>这才是职场成就的正解。 这里告诉你几条又实用又有效的小方法： 1.多打招呼 见到公司的任何人都打招呼，大的叫姐、哥，小的叫姓名和职位，不熟悉的人的不要叫小名和外号。 2.当别人向你索取帮助时，能帮尽量帮 这显示自己为公的心态，领导会看到，同事也会看在眼…", "excerpt_new": "我个人认为，不是说现在这个社会就是一个成就爱表现的人的社会。而是说现在这个社会竞争越来越激烈，埋头做事很难被伯乐发现。因此， <strong>你不但要做的好，还要做的好看！</strong>这才是职场成就的正解。 这里告诉你几条又实用又有效的小方法： 1.多打招呼 见到公司的任何人都打招呼，大的叫姐、哥，小的叫姓名和职位，不熟悉的人的不要叫小名和外号。 2.当别人向你索取帮助时，能帮尽量帮 这显示自己为公的心态，领导会看到，同事也会看在眼…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1591918875, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1589759646905", "type": "feed", "target": {"id": "38717093", "type": "article", "author": {"id": "21561cac50fb712d4edb987cd6e2841e", "name": "米妮", "headline": "<PERSON><PERSON>, LinkedIn产品经理｜Google数据分析师", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/minnie-laoshi", "url_token": "minnie-la<PERSON>", "avatar_url": "https://picx.zhimg.com/v2-d4dc4cb7fb444ce475e35ac70aafd457_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1530342007, "updated": 1530342007, "title": "这道Facebook面试题挂掉了97%的人（二）", "excerpt_title": "", "content": "<p data-pid=\"ZYnTdhzH\"><i>本文1000字，阅读时间3分钟。</i><br/></p><h2><b>前情提要</b></h2><p data-pid=\"EzZx1LlR\">上一篇文章（<a href=\"https://zhuanlan.zhihu.com/p/38471891\" class=\"internal\" target=\"_blank\">米妮：听说这道Facebook面试题通过率是3%</a>）里，我们给大家介绍了Facebook的一道面试题。在分析问题的过程中，我们遇到了一个难搞的问题，就是如何在有网络效应（network effect）的情况下选取实验样本。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"32de3Oqp\"><b>做A/B实验的一般做法，是以用户作为样本点。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"lEL-V6mA\">这样处理固然简单，但由于用户A和用户B之间正向影响（A认识B，所以A变活跃了，B也会变活跃），实验组和对照组的对比结果会比真实的差别要小（under estimate)。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-1e5bc3a374fd34f0f697d5b7abd4bc6b_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"600\" data-rawheight=\"371\" class=\"origin_image zh-lightbox-thumb\" width=\"600\" data-original=\"https://picx.zhimg.com/v2-1e5bc3a374fd34f0f697d5b7abd4bc6b_r.jpg\" data-original-token=\"v2-c8658e8c0b773340b48c523e663c6bb6\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"IF3olW_D\">那么，这个问题到底应该如何解决？这里我提供一种思路，一共有四步：<br/></p><h2><b>第一步</b></h2><p data-pid=\"vodsaxJN\"><b>找出这么互相不认识的两群人，分别标记为A和B。</b>一种靠谱的操作方式是按照地区进行筛选，比如，A组是阿拉巴马州（阿甘老家）用户，B组是加州（米妮老巢）用户。基本上可以认为A组和B组用户由于隔的远，老死不相往来。由于两组用户无法交流，我们消除了两组之间的网络效应。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-3fa6c7886ff4f12ad29c1b90e203c8ba_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"780\" data-rawheight=\"504\" class=\"origin_image zh-lightbox-thumb\" width=\"780\" data-original=\"https://pic3.zhimg.com/v2-3fa6c7886ff4f12ad29c1b90e203c8ba_r.jpg\" data-original-token=\"v2-1adb2a6e2dfe35aba1d5bb44c3046909\"/></figure><h2><b>第二步</b></h2><p data-pid=\"tEN7-s3c\"><b>测量A组和B组用户的default行为。</b>在实验开始之前，两组用户看到的是完全一样的体验。假设我们确定是实验指标是平均每个用户的日均回复数量，我们可以对这两组用户分别做出统计。假定我们得到了如下结果：<br/></p><ul><li data-pid=\"idNRtt3a\"><b>实验开始前，A组人均回复数量＝10</b></li><li data-pid=\"p00xZHo2\"><b>实验开始前，B组人均回复数量＝11</b></li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"oZc41MD4\">虽然A组和B组目前的体验完全一样，但是两组用户的数据不一定一样，因为两组用户作为独立的两个人群，用户行为是不同的。比如，B组人均回复数量本身较高，是因为加州人更习惯线上社交。<br/></p><p class=\"ztext-empty-paragraph\"><br/></p><h2><b>第三步</b></h2><p data-pid=\"m13Tbh-P\"><b>把A组用户作为实验组，B组用户作为对照组，进行实验。</b>试验运行一段时间后，再次对两组的指标分别作出统计。假定我们得到了如下结果：</p><ul><li data-pid=\"FYxSY4K4\"><b>实验开始后，A组人均回复数量＝14</b></li><li data-pid=\"OFnng8b1\"><b>实验开始后，B组人均回复数量＝14</b></li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Avn0A5fU\">这里注意，A组看到的是新版本，B组看到的旧版本。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-d96f13564419b9da6effe7b2305d06d0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"665\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-d96f13564419b9da6effe7b2305d06d0_r.jpg\" data-original-token=\"v2-c26bfeb54d83b641b52cbca667d62491\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><h2><b>第四步</b></h2><p data-pid=\"KXn5tlFy\">分别计算出两组用户前后的差别：</p><ul><li data-pid=\"U57oFlGT\"><b>A组+40%</b> (从10涨到14)</li><li data-pid=\"e4WGqf1x\"><b>B组+28%</b> (从11涨到14)</li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"zw1yYD55\"><b>什么造成了前后变化？</b></p><ul><li data-pid=\"vJjemspv\"><b>新旧版本的区别。</b>如果新版本好，A组的变化应该比B组大一些。这个是我们想要找到的那个增长。</li><li data-pid=\"hhIrowsW\"><b>时间的影响。</b>如果在实验开始运行的第二天，特朗普对北朝鲜宣战，那美国人民的FB活跃度一定增长400％。这400%是我们的实验造成的？不太可能。所以，这类外部因素会错误的影响到我们的实验结果。</li></ul><p class=\"ztext-empty-paragraph\"><br/></p><h2><b>第五步</b></h2><p data-pid=\"UGRNN_Ni\">如何消除时间的影响？通过计算两组用户前后差别的差别：</p><ul><li data-pid=\"up4PMFNZ\">A组前后变化 +40%</li><li data-pid=\"G3i-tiYS\">B组前后变化 +28%</li><li data-pid=\"GFzWiqTq\"><b>A组的前后变化比B组增加了43%</b></li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"t5QKZi-e\"><b>这个增长的百分比(43％)，便是我们的实验的真实影响。</b>第一次做减法，我们得到了两组用户各自的的前后变化；第二次做减法，我们抵消了时间前后的影响。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"gIf4HP7U\">在工作中，这种处理方法叫做difference of difference，老司机都叫做diff-n-diff，一般人我不告诉他。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"j15F8hXX\">问题解决了，希望给大家提供了一种思路。</p>", "excerpt": "<i>本文1000字，阅读时间3分钟。</i> <b>前情提要</b>上一篇文章（ <a href=\"https://zhuanlan.zhihu.com/p/38471891\" class=\"internal\" target=\"_blank\">米妮：听说这道Facebook面试题通过率是3%</a>）里，我们给大家介绍了Facebook的一道面试题。在分析问题的过程中，我们遇到了一个难搞的问题，就是如何在有网络效应（network effect）的情况下选取实验样本。 <b>做A/B实验的一般做法，是以用户作为样本点。</b> 这样处理固然简单，但由于用户A和用户B之间正向影响（A认识B，所以A变活跃了，B也会变活跃），实验组和对照组的对比结果会比真…", "excerpt_new": "<i>本文1000字，阅读时间3分钟。</i> <b>前情提要</b>上一篇文章（ <a href=\"https://zhuanlan.zhihu.com/p/38471891\" class=\"internal\" target=\"_blank\">米妮：听说这道Facebook面试题通过率是3%</a>）里，我们给大家介绍了Facebook的一道面试题。在分析问题的过程中，我们遇到了一个难搞的问题，就是如何在有网络效应（network effect）的情况下选取实验样本。 <b>做A/B实验的一般做法，是以用户作为样本点。</b> 这样处理固然简单，但由于用户A和用户B之间正向影响（A认识B，所以A变活跃了，B也会变活跃），实验组和对照组的对比结果会比真…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/38717093", "comment_permission": "all", "voteup_count": 61, "comment_count": 24, "image_url": "https://pic1.zhimg.com/v2-020e852ca37044751ffa178d980df09d_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1589759646, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1589695347625", "type": "feed", "target": {"id": "100765905", "type": "article", "author": {"id": "9abefd946c75a00ac82dda076a1ad049", "name": "徐子行", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xujunhui520", "url_token": "xujunhui520", "avatar_url": "https://picx.zhimg.com/v2-e53f716d7ee672192fcf69fc11c00868_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1578051803, "updated": 1628556856, "title": "什么样的人适合做产品经理", "excerpt_title": "", "content": "<p data-pid=\"tca2_xtG\">工作原因，面试过数百位产品经理，包括从应届到资深各个阶段。其中一半是应届生。</p><p data-pid=\"bnpG6F64\"><b> 应届生做产品经理，经验并不重要，反倒是沟通，学习能力，同理心，行动能力 几个素质相对来讲比较重要，基本素质，高于技能能力。</b></p><p data-pid=\"8XgQkezM\"> 基本素质不错，能力都可以慢慢培养。 </p><p data-pid=\"-ZTO5CWO\"> 下面是面试时考查最多的几个基本素质，和业内同事也有过比较多的探讨，意见是相同的。 </p><p data-pid=\"wzjkmioz\"><b>1 沟通能力： </b></p><p data-pid=\"vYepuE4a\">产品工作，是连接，在企业内部是连接点最多的工作工种，会和研发，设计，市场（销售或者推广），测试，数据等多部门协同作战，产品工作甚至大部分时间在做沟通工作，少部分时间在做需求设计，所以沟通能力是基础能力，沟通能力好，是加分项。</p><p data-pid=\"xHj96D_H\"> 沟通能力差，无法开展工作 </p><p data-pid=\"X9LMJKRp\"><b>2 同理心: </b></p><p data-pid=\"2h0bFwLs\"> 同理心，是指能够站在别人的角度思考，作为产品经理，其作品往往是用户使用，需要经常站在用户角度思考，并且不能强把自己代替成用户，那样是犯了很大的错误。</p><p data-pid=\"kKzxF8n7\"> “我以为”这句话在某些产品企业，是产品工作忌语，是不能讲的，标准的说话是 用户A，是这样的，用户B如何。 同理心，是站在用户角度感受和思考问题的起点，没有同理心，不能站在用户立场，无法设计产品。</p><p data-pid=\"e34_lUni\"><b>3 学习能力 </b><br/>从产品助理，到产品总监，产品岗位提供了长长的进阶升级之路。</p><p data-pid=\"ocI1UWd9\">从做一个个小小的需求到掌管大的项目，垮度是非常之大。基本没有多少工种对知识领域的广度要求有如此之多。</p><p data-pid=\"QSD9MoL9\">技能举例： </p><p data-pid=\"iamZXHup\"><b>需要懂点技术</b>，否则没法和开发沟通 </p><p data-pid=\"rzgpfE0f\"><b>需要数学不要太差</b>，否则高级产品经理不懂数据，没法开展工作 </p><p data-pid=\"z845d1Ya\"><b>需要对美术设计略有所通</b>，否则C端产品在视觉，美术，体验上面，丝毫没有竞争力  </p><p data-pid=\"QB7MseJG\"><b>需要懂点市场推广</b>，拉新，推广本身是产品核心能力之一，一点不懂如何开展工作。 </p><p data-pid=\"PT7lXjG6\"><b>需要对商业有了解</b>，设计产品，主要在于设计商业，而不是设计需求，只会设计需求的产品经理没有任何竞争力 </p><p data-pid=\"LQf36-MQ\"><b>需要了解产品思维</b>，<b>方法论</b>，比如：MVP精益创业思想等等，没有MVP，设计一堆功能一路狂奔，掌握不好节奏，会害了项目组，害了公司。 <br/><br/><b>需要学习项目管理</b>，现在各公司更倾向于让产品经理管理项目，而不是对产品并不了解的项目经理，不懂项目管理，如何掌管项目进度及风险把控 …………</p><p data-pid=\"SKrXtXQ-\">还有许多没有列举，知识宽度垮度非常大，当然未来可提升空间也很大，请问，准备进入产品领域工作，并且后面准备升级的你，准备好了嘛<br/><br/><b>(也不要太害怕，一下子掌握不了这么多。在产品工作前期，产品助理和初级产品经理，并不需要掌握上述大多工作技能，后期逐渐锻炼就好啦)</b></p><p data-pid=\"op9iXYgG\"><b>4 推动力 </b> （包含行动力，执行力）</p><p data-pid=\"UAaNagPl\">产品的最终能力还是推动商业。</p><p data-pid=\"kOVk1jgV\">只在日常工作中，则体现在产品对于其连接相关部门推动。</p><p data-pid=\"2g04zEp4\">犹其是研发，技术，等相关部门连同作战的能力，如果没有好的推动力，试问，领导会把产品交给一个，无法保证工期，无法协调起相应资源，达成目标的产品经理嘛，短期工期尚无法达成，何以达成商业 推动力是产品基本能力，没有推动力，项目基本推进无法保证，企业无法接受这样弱势的产品经理。</p><p data-pid=\"SAnrlIPY\">（此项能力和第二项同理心，偶尔甚至是不兼容的，你的同理心特别好，可能推动力偏弱，甚至初期工作会有一些矛盾之处）</p><p data-pid=\"wylVlkob\"><b>如果继续后面推可能还有，逻辑能力，数据能力，商业能力，判断力。每一项均是加分项。</b></p><p data-pid=\"fXfeGdCI\"> 都没有前面这四项重要，这四项是产品经理基本能力，甚至可以说和技术无关，和专业无关。相比专业而言，这四个基础素质更重要。</p><p data-pid=\"kmZHgrqN\"> 如果这四个基本能力能够垮越，那么其它的阻碍，就会小很多了，比如技术，数据，设计等。</p><p data-pid=\"yJlEpia8\">这四项能力，大家觉得都OK，而又喜欢产品，那么请大步的向前迈吧，你会是其中优秀的那一个。<br/><br/>(专栏的开篇，放上的第一篇。这一篇相对来讲，也比较重要，它或者会是所有产品人的开始。它是一面镜子，我甚至会拿它对照一下，工作了七八年后的自己，发现，还有很多可以提高的地方，希望大家可以认真看，并且收藏。)</p><p data-pid=\"5g4ZXdzQ\">大家在学习，职业上面需要一些指导，也可以在知乎上，通过音频，图文问答和我互动，我会尽力帮助大家答疑解惑，相信会对你有帮助。包括但不限于产品经理，数据分析，以及职业相关的一些问题。</p><a data-draft-node=\"block\" data-draft-type=\"ad-link-card\" data-ad-id=\"fee_9abefd946c75a00ac82dda076a1ad049\"></a><p></p>", "excerpt": "工作原因，面试过数百位产品经理，包括从应届到资深各个阶段。其中一半是应届生。 <b> 应届生做产品经理，经验并不重要，反倒是沟通，学习能力，同理心，行动能力 几个素质相对来讲比较重要，基本素质，高于技能能力。</b> 基本素质不错，能力都可以慢慢培养。 下面是面试时考查最多的几个基本素质，和业内同事也有过比较多的探讨，意见是相同的。 <b>1 沟通能力： </b>产品工作，是连接，在企业内部是连接点最多的工作工种，会和研发，设计，…", "excerpt_new": "工作原因，面试过数百位产品经理，包括从应届到资深各个阶段。其中一半是应届生。 <b> 应届生做产品经理，经验并不重要，反倒是沟通，学习能力，同理心，行动能力 几个素质相对来讲比较重要，基本素质，高于技能能力。</b> 基本素质不错，能力都可以慢慢培养。 下面是面试时考查最多的几个基本素质，和业内同事也有过比较多的探讨，意见是相同的。 <b>1 沟通能力： </b>产品工作，是连接，在企业内部是连接点最多的工作工种，会和研发，设计，…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/100765905", "comment_permission": "all", "voteup_count": 691, "comment_count": 62, "image_url": "", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1589695347, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1589695332303", "type": "feed", "target": {"id": "1218579259", "type": "answer", "url": "https://api.zhihu.com/answers/1218579259", "voteup_count": 5, "thanks_count": 4, "question": {"id": "26015692", "title": "我想当一名产品经理，应该如何入行？", "url": "https://api.zhihu.com/questions/26015692", "type": "question", "question_type": "normal", "created": 1413204851, "answer_count": 157, "comment_count": 11, "follower_count": 1787, "detail": "都需要学些什么，然后怎么做呢？", "excerpt": "都需要学些什么，然后怎么做呢？", "bound_topic_ids": [307, 368, 1309, 20802, 26129], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "51452ed7fc1dd98bfac7d47c2bd00125", "name": "霜叶", "headline": "我什么也不知道", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/shuangye", "url_token": "shuangye", "avatar_url": "https://picx.zhimg.com/v2-c423bfab23a597c86b70351c6d568a19_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1590133872, "created_time": 1589291574, "author": {"id": "a71fd3d07ac0d6f8ae2d18fda0e72eea", "name": "三爷", "headline": "著有多本产品领域畅销书/MBA特约讲师/公号：三爷茶馆", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/sanye_blog", "url_token": "sanye_blog", "avatar_url": "https://pic1.zhimg.com/v2-915f018072cdac9d2da09bc23d3887d0_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": false, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"DGrntrlE\">本产品经理入行指南全网阅读量破万，这里放出需要学习的2.0！</p><p data-pid=\"T74sTEpL\">送给那些想入产品经理行当又找不到系统学习思路的同学！</p><p data-pid=\"etcHo4Sp\"><b><i>过来人说两句：产品岗真正想入门的话其实并不难，特别是在现在互联网时代随时都有很多免费的资料可以去学习。但是如果真想在产品经理这个岗位有长远发展的话，前期入行时就必须要打下扎实的基础。</i></b></p><h3>一、明确目标</h3><p data-pid=\"vnJOQyQP\">在转行之前大家首先要做的就是必须要明确自己的目标，量化下来就是要问自己如下6个问题：</p><ol><li data-pid=\"NfJKZpvF\">思考下自己为什么要转行？</li><li data-pid=\"nh4zl8Hp\">自己的职业目标是什么？</li><li data-pid=\"JN4DdQ2n\">为什么自己想做产品经理？</li><li data-pid=\"50Zzd_qv\">是否了解产品经理是做什么的?</li><li data-pid=\"eCk4tVgS\">和自己期待的那个岗位工作性质是否一样？</li><li data-pid=\"yRYvfdi0\">自己做产品经理有什么优势?</li></ol><p data-pid=\"HCePs01O\">在回答这些问题前，我建议大家可以像我曾经给很多有同样迷惑的同学说过的，先去打开拉钩，BOSS等招聘网站，在上面看看各大互联网公司招聘的产品经理要求是怎么样的，然后一条一条的去核对去了解之后回答这些问题。</p><blockquote data-pid=\"qFAs5ppv\"><b><i>这几个问题的核心目的就是要刨根问底的搞清楚自己到底是对产品是一时冲动还是真的心有所属！</i></b></blockquote><p data-pid=\"wR1OMXeS\">当然如果在看完后你还是觉得很感兴趣，那就赶紧往下看去学习入行吧！</p><h3>二、了解产品经理是什么？</h3><p data-pid=\"HS6w9BK-\">首先产品经理不是只动动嘴就完成的职业，如果大家以为看了几场苹果，Facebook的发布会觉得这样就是产品经理的话，那我只能说太天真了。</p><figure data-size=\"normal\"><noscript><img src=\"https://pica.zhimg.com/v2-2b04a26a839b5d52d1e9331b04c08ecc_b.jpg\" data-rawwidth=\"1000\" data-rawheight=\"563\" data-size=\"normal\" data-original-token=\"v2-2b04a26a839b5d52d1e9331b04c08ecc\" class=\"origin_image zh-lightbox-thumb\" width=\"1000\" data-original=\"https://pica.zhimg.com/v2-2b04a26a839b5d52d1e9331b04c08ecc_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1000&#39; height=&#39;563&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1000\" data-rawheight=\"563\" data-size=\"normal\" data-original-token=\"v2-2b04a26a839b5d52d1e9331b04c08ecc\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1000\" data-original=\"https://pica.zhimg.com/v2-2b04a26a839b5d52d1e9331b04c08ecc_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-2b04a26a839b5d52d1e9331b04c08ecc_b.jpg\"/><figcaption>就算是他，不也是忙的没时间换衣服吗（玩笑！）</figcaption></figure><p data-pid=\"93icTh1D\">真正的产品产品经理需要做的事情非常多，以在整个产品的生命周期中为例，几乎每一项工作都不能缺少他。</p><blockquote data-pid=\"dRoCyXtk\">产品调研-&gt;需求文档输出-&gt;设计原型-&gt;视觉设计协调-&gt;开发进度跟踪-&gt;产品测试-&gt;产品上线准备（市场/品宣）-&gt;产品发布-&gt;数据分析-&gt;新一轮产品迭代。</blockquote><p data-pid=\"g6_wO2TJ\">这整个流程中每个地方都有产品经理的痕迹。所以要入行的你要做好准备！</p><p data-pid=\"X7vVZXFa\">那么产品具体在生产流程的每个环节中要干些什么呢？</p><p data-pid=\"TwsUDf7B\">我在网上找到了一张图说的很明确：</p><figure data-size=\"normal\"><noscript><img src=\"https://pica.zhimg.com/v2-f6921409d2d295e75ff5214fdf4b11a8_b.jpg\" data-rawwidth=\"943\" data-rawheight=\"613\" data-size=\"normal\" data-caption=\"\" data-original-token=\"v2-f6921409d2d295e75ff5214fdf4b11a8\" class=\"origin_image zh-lightbox-thumb\" width=\"943\" data-original=\"https://pica.zhimg.com/v2-f6921409d2d295e75ff5214fdf4b11a8_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;943&#39; height=&#39;613&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"943\" data-rawheight=\"613\" data-size=\"normal\" data-caption=\"\" data-original-token=\"v2-f6921409d2d295e75ff5214fdf4b11a8\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"943\" data-original=\"https://pica.zhimg.com/v2-f6921409d2d295e75ff5214fdf4b11a8_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-f6921409d2d295e75ff5214fdf4b11a8_b.jpg\"/></figure><p data-pid=\"v021TIqQ\">介绍完产品经理是干什么的，让我们再来看看产品经理的发展路线，一般来说在大公司产品经理这个行当会被细分为如下级别（从小到大排列）：</p><blockquote data-pid=\"lph28jwi\"><b>产品专员/产品助理 -&gt; 产品经理 -&gt; 高级产品经理 -&gt; 产品专家 -&gt; 产品总监 -&gt; 产品副总裁</b></blockquote><p data-pid=\"R7oozd9Y\">看到这相信大家对产品这个行当应该有了个相对清晰的了解了！</p><h3>三、如何学习（划重点）</h3><p data-pid=\"iVE_crBc\">首先要了解产品经理需要具备的能力，其次要知道怎样培养相应的能力。通过招聘网站产品经理可以查询到产品经理所需具备的能力，大致可以总结为两方面：</p><ul><li data-pid=\"57pmaeWw\">软实力：包括逻辑思维能力、学习能力、沟通表达、行业融入感，同理心等；</li><li data-pid=\"TQqPTIIm\">硬实力：产品理解力、产品设计、执行力。</li></ul><h3>四、学习大纲</h3><p data-pid=\"3y6NWNR2\">在学习层面我们可以分为如下几个角度</p><ol><li data-pid=\"Jt2nN3iB\">系统理论学习：对于零基础转行的同学，一上来必须要先建立一个完整的知识体系，去了解这个行业的日常工作与行业规则。这里可以通过阅读大部头的书，网上现有的产品经理培训视频（看免费的就够了）去全面了解这个行业；</li><li data-pid=\"QduOC_nL\">产品经理常用的办公工具学习：作为产品经理最基本工具可以说只要学会这几个常用工具就够了：Axure/墨刀：主流的产品原型设计软件；Xmind：思维导图、脑图工具；visio：流程图工具；word、excel、ppt ：这些基础的office办公软件的使用也是需要掌握的；</li><li data-pid=\"qjC42l5L\">产品日常工作流学习：对于大多数产品经理来说每天的工作可以总结为如下三个：PRD文档书写、需求规划、原型设计，那么我们要做的就是全方位去了解这三个流程，并阶段性的尝试与实战撰写。</li><li data-pid=\"7uM_5kW0\">互联网趋势关注习惯学习：去看这两个网站就够了：36氪、虎嗅，关注每天互联网圈发生的大事与最新行业资讯；</li></ol><p data-pid=\"pCgXRuZ-\">关于入门写PRD可以参考下我的这篇文章！</p><a data-draft-node=\"block\" data-draft-type=\"link-card\" href=\"https://zhuanlan.zhihu.com/p/55597786\" data-image=\"https://pic2.zhimg.com/v2-45489f66ee47684f3d260a06f1977a01_180x120.jpg\" data-image-width=\"1080\" data-image-height=\"613\" class=\"internal\">三爷：写给年后换工作的你：如何“理解”一份打动面试官的PRD</a><blockquote data-pid=\"dziGYrOQ\"><i>如果能扎实的完成这四个层面的学习，可以说恭喜你，你已经成为一个合格的产品经理了！</i></blockquote><h3>五、学习资源</h3><p data-pid=\"q47ejh3u\">正所谓授人以鱼不如授人以渔，这里我来给大家说下常见的学习资料来源</p><ul><li data-pid=\"k48WHCar\">学习视频：百度传课、网易云课堂、腾讯课堂、淘宝教育以及各种机构的官网试看视频都有很多资料，也很系统。视频PPT都是取之不尽的。</li><li data-pid=\"rjYddTD3\">兴趣群，根据自己所缺的知识多加一些群，和更多的人一群讨论，这样获取知识的能力更快。</li><li data-pid=\"2JF4JLbg\">知乎/贴吧，多去看一些别人写的心得，产品技能知识的总结</li><li data-pid=\"n8LGNeP_\">书籍等其他学习材料，不知道大家是不是有成天向别人要学习资料的坏习惯，其实作为未来的产品你们必须要掌握的能力就是资料收集能力，这里我想说你们最好的资料收集工具就是百度，Google！</li></ul><h2><b>六、面试前准备材料</b></h2><p data-pid=\"fxH-zUjY\">作为转岗的人，我们必须尽可能全的展示我们对产品这个岗位的理解，面试官才会同意将你录用。</p><p data-pid=\"8l-Ql1r0\">理也很简单，只有这样他才不会需要在录用你后，一面处理自己手头的工作一面还要带你。</p><blockquote data-pid=\"6c5QprGH\"><b><i>因为你是过来为公司工作的挣钱的，不是来公司学习的！</i></b></blockquote><p data-pid=\"pHU94p3A\">这点一定记住，非常重要。所以请不要再对面试官说我是来学习的了，好吗？</p><p data-pid=\"FHqudwZl\">因此我们需要在面试前去整理一些材料，来帮助我们证明自己的能力与认知，具体来说可以归为如下几类：</p><h3><b>Part1.文档类</b></h3><p data-pid=\"7u9tONLL\">BRD，一份对任意产品的商业模式的分析文档；</p><p data-pid=\"zaBLEgZp\">MRD，一份对任意产品的市场发展的分析文档；</p><p data-pid=\"skRA9hbN\">PRD，一份对任意产品的产品功能的设计文档；</p><h3>Part2.<b>图形类</b></h3><p data-pid=\"K43qeWfG\"><b>在你的文档中最好包含常见产品经理日常为了解释需求而设计的图表，主要有如下类别：</b></p><p data-pid=\"9UTy3SMu\">流程图，泳道图，思维导图，信息架构图，原型图（描述页面跳转的线框图就够了）等；</p><p data-pid=\"2E_8ucGB\">这里比较复杂的也就是时序图了，怎么画可以参考我这篇文章：</p><a data-draft-node=\"block\" data-draft-type=\"link-card\" href=\"https://zhuanlan.zhihu.com/p/59991598\" data-image=\"https://pic2.zhimg.com/zhihu-card-default.jpg\" class=\"internal\">三爷：产品经理基本功——时序图绘制</a><h3>Part3. <b>观点积累</b></h3><p data-pid=\"1Ys8wD_N\">自己手机上去下载所面试公司的APP，去分析这个产品，看看有无要改进的部分与意见。</p>", "excerpt": "本产品经理入行指南全网阅读量破万，这里放出需要学习的2.0！ 送给那些想入产品经理行当又找不到系统学习思路的同学！ <b><i>过来人说两句：产品岗真正想入门的话其实并不难，特别是在现在互联网时代随时都有很多免费的资料可以去学习。但是如果真想在产品经理这个岗位有长远发展的话，前期入行时就必须要打下扎实的基础。</i></b>一、明确目标在转行之前大家首先要做的就是必须要明确自己的目标，量化下来就是要问自己如下6个问题： 思考下自…", "excerpt_new": "本产品经理入行指南全网阅读量破万，这里放出需要学习的2.0！ 送给那些想入产品经理行当又找不到系统学习思路的同学！ <b><i>过来人说两句：产品岗真正想入门的话其实并不难，特别是在现在互联网时代随时都有很多免费的资料可以去学习。但是如果真想在产品经理这个岗位有长远发展的话，前期入行时就必须要打下扎实的基础。</i></b>一、明确目标在转行之前大家首先要做的就是必须要明确自己的目标，量化下来就是要问自己如下6个问题： 思考下自…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "disallowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1589695332, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1589695235457", "type": "feed", "target": {"id": "31829065", "type": "answer", "url": "https://api.zhihu.com/answers/31829065", "voteup_count": 465, "thanks_count": 289, "question": {"id": "26015692", "title": "我想当一名产品经理，应该如何入行？", "url": "https://api.zhihu.com/questions/26015692", "type": "question", "question_type": "normal", "created": 1413204851, "answer_count": 157, "comment_count": 11, "follower_count": 1787, "detail": "都需要学些什么，然后怎么做呢？", "excerpt": "都需要学些什么，然后怎么做呢？", "bound_topic_ids": [307, 368, 1309, 20802, 26129], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "51452ed7fc1dd98bfac7d47c2bd00125", "name": "霜叶", "headline": "我什么也不知道", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/shuangye", "url_token": "shuangye", "avatar_url": "https://pic1.zhimg.com/v2-c423bfab23a597c86b70351c6d568a19_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1413360499, "created_time": 1413249667, "author": {"id": "520a2f71c3ce3b273b2e234b71946c11", "name": "卿词", "headline": "非典型性95后/产品设计/垂直电商/伪宅/中立善良", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/raocheche", "url_token": "raocheche", "avatar_url": "https://picx.zhimg.com/6c36b5dda0a07ed4e7398820320e4fdf_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://pic1.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 39, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"EUSh_f7c\"><strong>首先，好的产品经理一定是一个在知识广度与思维深度上，都有所追求有所积累的人，这绝对不是一个不需要专业门槛的谋生的职业。</strong></p><p data-pid=\"mxJmZyFE\">作为一个走在半路上撒欢的产品汪，我仅仅是发表一下我对于这条方向的规划以及看法，也希望能对其他热爱这个行业，渴望站在风口的猪们有所帮助，借用张小龙的一句话：我所说的都是错的。是的，如果把这篇文章当做产品来看的话，你做出你真实的反馈就好，如果真的适合你，解决了你的痛点，那么，我会很高兴的。</p><p data-pid=\"eF1NqcVn\"><strong>首先做一个目录摘要，主要分为基础篇，学生入门篇和业内人士转行篇</strong></p><p data-pid=\"J3mpIu8x\"><strong>1.基础篇</strong></p><p data-pid=\"mX6bjJDL\"><strong>1）自我规划与定位</strong></p><p data-pid=\"Z0oNruNl\"><strong>2）能力表</strong></p><p data-pid=\"WJ0kmNe2\"><strong>3）习惯养成</strong></p><p data-pid=\"GL-lRPo1\"><strong>4）流程化思维</strong></p><p data-pid=\"SxMfLYjr\"><strong>5 ) 文档/工具入门</strong></p><br/><p data-pid=\"FxRtbnav\"><strong>2.学生入门篇</strong></p><p data-pid=\"diF9aZ3A\"><strong>1）起步规划</strong></p><p data-pid=\"vkv9sK4i\"><strong>2）日常修养</strong></p><p data-pid=\"YXtBsbJP\"><strong>3）价值提高</strong></p><p data-pid=\"5yZ_mlkZ\"><strong>4）入职准备</strong></p><p data-pid=\"3rMxVYbp\"><strong>5）具体建议</strong></p><br/><p data-pid=\"2L5CWe4D\"><strong>3.业内人士转行篇</strong></p><p data-pid=\"9S_Y-MHQ\"><strong>1）定位转变</strong></p><p data-pid=\"WXAOGkuQ\"><strong>2）快速上手</strong></p><p data-pid=\"zi6_gEzs\"><strong>3）团队职能</strong></p><br/><p data-pid=\"TdaIS_uh\"><strong>1.基础篇</strong></p><p data-pid=\"dYTR2mmX\"><strong>1）自我规划与定位</strong></p><p data-pid=\"HZq0W_rR\">       产品是一个很需要坚持很考验能力的岗位，如果你真的考虑成为一个产品汪的话，你首先得把自己看做一个产品。那么，第一步要做的就是问自己：什么是产品经理？我为什么能当产品经理？为了成为一款优秀的产品，我应该如何设计自己的产品能力才能达到市场的预期，获得市场认可？                  </p><p data-pid=\"5C4Nhslg\">       无论你现在是处于什么样的身份，只要你是真的想走这条路的话，那么请规划好自己的未来，产品迭代的过程中，你不希望别的组员拖进度，那么，你有什么理由，违背自己的规划？至于定位，找准自己的兴趣点和长处，选择PC端/移动端，游戏/社区/电商/工具作为一个切入点强化自己。编程过的人都知道，你只要学精通了一门语言，那么其他上手起来也会很快，同样，<strong>你只有在某个方向上走的够远够高，你才能有足够的格局去拓宽自己的能力，入门的时候，深度比广度重要。</strong></p><br/><p data-pid=\"KRI2z032\"><strong>2）能力表</strong></p><p data-pid=\"6pg_wGfi\">列一张产品经理的能力列表，只是希望大家能对此先有所了解。</p><p data-pid=\"K-GA5iw5\"><strong>个人素质</strong>：执行力，领导力，思维能力，有敏锐嗅觉，完美主义者，身体素质好，有艺术修养，爱好阅读，关注互联网，大方，没有架子......另外：会吹会扯，口才好.......长得漂亮= =</p><p data-pid=\"_BbHY-pn\"><strong>能力技术</strong>：1.可以不会打代码，但是必须要懂编程，也就是说，知道功能的可行性，能够粗略的分析开发的权重，以及，更好的与程序猿沟通。</p><p data-pid=\"H9VhFYq2\">2.懂设计。相信我，很多公司会把产品当交互用！！！咳，懂设计很有必要，将想法画出来，表达的效果和效率绝对比边说边比划强。</p><p data-pid=\"PlSW9AkI\">3.办公软件都要会用，PPT玩的越溜越好（阿里对PPT爱的深沉）PS技术能够应付基础的信息图层的设计（必要的时候能够帮设计师分担苦力任务）axure用来做原型（这个用来应付老板和VC天使什么的最好用了）会用visio来做流程图，会用思维导图（在分析设计信息架构，理清思路的时候很有用）</p><p data-pid=\"dXi-adGh\"><strong>其他：</strong>其实说一个很残酷的事实，区分一个产品经理的好坏的标准是什么？是他的产品的成功与否。所以，在没有产品可以标榜之前，别嫌自己懂得东西多，有机会就多做多积累，对于大学生来说，多几次比赛经验，多尝试做自己的产品，非常有必要。<br/></p><br/><p data-pid=\"R_APjF9i\"><strong>3）习惯养成：</strong></p><p data-pid=\"zk7gBygd\"> 为了入行，我建议你养成这些习惯</p><p data-pid=\"ja01JOan\">1.坚持阅读，持续关注互联网资讯：不要觉得阿里上市和你没关系，知识的积累是一个持续的过程，先积累，然后再体悟。阅读方向包括互联网资讯，产品相关的文章博客，甚至是哲学，情感等各种书籍。</p><p data-pid=\"qDueGELa\">2.生活中追求完美：这里的完美不是说让你万事苛责，而是在日常生活中善于发现问题，发现痛点，并因此挖掘需求。吴欣鸿发现很多人会在网上求大神P图，于是有了美图秀秀~所谓的灵感的诞生，很多时候就是源于你对生活的不妥协。</p><p data-pid=\"y-38M-f6\">3.学会用思维导图：这个是我的个人习惯，当我要深层的提取一个需求，设计一个产品的时候，我会习惯于用思维导图来层层挖掘，这个过程很有必要，很多新入行的产品经理会过于扣功能扣细节，就是因为他们在产品的信息架构上并没有理清。</p><p data-pid=\"qixzXyGj\">4.写博客：尝试写博客，发表自己的看法，不要担心自己还能力不够，写博客的过程是对自己思维的一种锻炼，也是对自己学过的知识的再提炼。并且，面试的时候，如果你有发表过大量产品相关的博客，这是会有加分的。</p><p data-pid=\"o3pxoq5m\">5.不要闭门造车：没入行之前，多学会去论坛，群，微信上混，里面还是有挺多的业内人士的，这对于你的自我认知很有帮助，多勾搭大神（不是骚扰），如果你够厉害，没准人家就给你提供机会了也说不定。</p><br/><p data-pid=\"wszF-9dL\"><strong>4）流程化思维</strong></p><p data-pid=\"5qihYrr9\">不是说让你如同流水线固化自己的思维，而是学会用流程化的思维去分析问题，去设计产品。在产品设计的过程中，我倾向于这样的流程：<strong>信息结构设计、流程设计、功能设计、交互设计、视觉设计。<br/></strong></p><p data-pid=\"fUNz5CRT\">信息结构设计：从最根本上决定一款产品可以解决什么问题，由哪些部分组成，以及理清他们的逻辑关系，我建议大家用思维导图来完成这个过程。<br/></p><p data-pid=\"T2uxyXnw\">流程设计：也就是产品的操作流程的设计，模拟产品的使用场景，来流程化模拟用户使用过程。</p><p data-pid=\"Uz42x0S1\">功能设计：很多新人喜欢死扣功能设计，其实这个很没必要，我的一个学妹在设计一款陌生人社交应用的时候，一味的琢磨陌生人社交的功能，构思了一大堆类似摇一摇性质的功能，并且集成在主页上，我就很头疼，我对她说：<strong>功能只是手段，社交才是目的</strong>，忽略了一个产品的核心定位，这个就是所谓的大局观缺失。</p><p data-pid=\"qWI_-S3I\">交互设计:产品经理也应该是一个优秀的交互设计师，多用AXURE，用户体验可不是吹出来的。</p><p data-pid=\"n0M7-3D6\">视觉设计：千万不要向设计师提出高端大气这样的需求，丫的谁能告诉我什么是大气？</p><br/><p data-pid=\"Oye3_SoZ\"><strong>5）文档/工具入门</strong></p><p data-pid=\"FokGq7Gq\"><a href=\"https://link.zhihu.com/?target=http%3A//www.woshipm.com/pmd/105124.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">嗨！菜鸟~我们来谈谈你的新工作（文档篇）</a><br/></p><p data-pid=\"NIUIFaDb\">关于文档方面这位朋友已经写的非常详细了，我就不赘述了，至于工具的入门，有耐心的就慢慢的琢磨每一个功能，多玩多尝试多看视频多看书，如果需要速成的，可以下载别人的模板用心琢磨，学起来也很快。当然，题外话，我可以教你PS，一晚上就可以精通大部分的小工具使用，可是，这样你就是优秀的设计师了么？所以多用多尝试，不外乎如此也。</p><br/><p data-pid=\"TLmuiQVu\"><strong>2.学生入门篇</strong></p><p data-pid=\"L07Hm_ku\"> 如果你是一名大学生，你发现你对于互联网产品非常有兴趣，立志做一名产品汪的话，下面的内容应该能够对你有所启发</p><br/><p data-pid=\"WOT3k0PQ\"><strong>1）起步规划</strong><br/></p><p data-pid=\"TmHZFjaM\">1.查阅资料，搞清楚什么是产品经理，以及产品经理的职能，职业要求</p><p data-pid=\"UIMYC-aQ\">2.加入一个技术团队，无论是进入什么样的部门</p><p data-pid=\"GzuCpw_9\">3.在团队中有意识的承担产品职责，规划整个产品线。</p><p data-pid=\"SdT7O2Bl\">4.代码水平要能达到（通过借鉴参考）架出一个博客，以及至少能够写个指针实现的贪吃蛇什么的。</p><p data-pid=\"p7OXm2fr\">5.设计鉴赏能力需要培养，阅读网页/安卓/IOS设计规范，懂得一些交互设计与用户体验的知识，期望的PS水平是能够独立画出信息层级别的界面，用于AXURE做低保真原型。</p><p data-pid=\"09WHABBY\">6.可以的话，安卓/IOS系统并行</p><br/><p data-pid=\"xzHvCJwH\"><strong>2）日常修炼</strong></p><p data-pid=\"TMZ5ANjO\">因为大学很多人并没有太多的机会接触项目，而产品经理又是一个非常需要实战的职业，因此：</p><p data-pid=\"ox5owyAf\">1.每天花一定的时间去体验市面上的应用，尝试多用多分析，可以的话，同时锻炼自己的竞品分析文档的撰写能力</p><p data-pid=\"DNHUwold\">2.每日阅读:36kr,虎嗅，早读啦，PM265，人人都是产品经理，这些都是不错的资讯网站，里面有很多干货可以吸收。一个ZAKER阅读平台就可以订阅他们。</p><p data-pid=\"FV6IBGHb\">3.学会用思维导图：你可以用思维导图来辅助阅读，分析问题，设计产品等，总之，经常使用思维导图，这是一个锻炼自己产品思维的好方法。</p><p data-pid=\"2L7et4F9\">4.有计划的学习各类工具</p><p data-pid=\"I2XPwPP4\">就我个人的建议的话，你可以按照word-&gt;photoshop-&gt;excel-&gt;ppt-&gt;axure/UID-&gt;visio的顺序来一个个攻克每一个工具，当然，平常多使用，会用和用的好可不是一回事，其他的辅助性工具还有很多，你可以自己的有计划的学习。</p><br/><p data-pid=\"wqgxpx5D\"><strong>3）价值提高</strong></p><p data-pid=\"XPxPEo6A\"> 如果这些你都有尝试在做，可是你还是对自己的产品之路感到忐忑怎么办？产品入门不像技术，很难有一个标准去衡量每个人的实力，因此，你需要在一些方面提高自己的产品价值。</p><p data-pid=\"5OVY03TW\">1.博客，大量的产品文章。</p><p data-pid=\"foOYuL1f\">2.可以尝试运营一个微信公众号或者微博，运营的经验对于你的产品之路还是会有很大的帮助的。</p><p data-pid=\"KKsgyOkV\">3.大量的idea：平常多积累一些想法，并且努力完善，这对于你在面试过程中的交流与表达很有帮助。如果能够以成型的文档和界面图片来交付的话更好，你可以向面试官表达，抛开实现，你在这个自己构建的产品线中做到了哪些事情，又体现了你的哪些能力。</p><p data-pid=\"BGX7L4Iz\">4.如果可以的话，在技术团队中孵化出了自己的产品，这样的话，你已经入门了。</p><br/><p data-pid=\"Df8bzZTT\"><strong>4）入职准备</strong></p><p data-pid=\"9_Ozigmj\">已经是应届生，或者已经拿到了offer即将入职，那么你又可以做哪些准备呢？</p><p data-pid=\"0B7po4-9\">1.针对你心仪/拿到offer的公司，主动的去做一番市场调查和产品调研，并且给出成型的文档，相信我，没人会拒绝你积极主动的分析报告的，当然，不要在文档中自曝其短。</p><p data-pid=\"bkXIQjDD\">2.强化训练，简历中的精通XXX是不是真的精通，熟练掌握是不是真的就得心应手了？入职后，你的能力将会被很生动的体现出来，这个时候，繁重的任务会让你明白，你的工具掌握的水平还远远不够，所以，为了让入职的前几个月过得尽量舒心，提前给自己来一个强化训练吧。</p><p data-pid=\"U3RsVe83\">3.调整好作息规律。</p><br/><p data-pid=\"vTIt12cT\"><strong>5）具体建议</strong></p><p data-pid=\"IpxiUCxn\">1.36kr,虎嗅，早读啦，PM265，人人都是产品经理，这些都是不错的资讯网站这些网站要常去。</p><p data-pid=\"8CiuXeZD\">2.书籍的话，产品知识方面：结网，启示录，人人都是产品经理，这三本粗略读一遍就好，让你有一个大体的认知。</p><p data-pid=\"79WXZ4gW\">设计相关：用户体验要素，设计中的设计，交互设计之路还不错，情感化设计</p><p data-pid=\"AJODgFXq\">经济营销相关：赢在用户，影响力，长尾理论，水平营销等</p><p data-pid=\"Afnvi0pN\">其他的心理学哲学，公司管理的书也有很多，我读的不算多，就不草率推荐了。</p><p data-pid=\"WqSucdhn\">3.刘文智的手把手教你做产品还不错（不过是付费的），如果有能力的话可以购买看看。</p><p data-pid=\"pLLs3wT5\">4.AXURE的学习完全可以自学，但是如果想通过视频资料学习的话推荐小楼老师的AXURE7.0从入门到精通，同样是需要付费的，不过老实说，前面免费的十几节其实就足够了。</p><p data-pid=\"g072FX4v\">5.勇敢的拿着自己的idea去向学院申请创建团队，申请资金，拉着小伙伴打比赛什么的，当然，别盲目坑人。</p><br/><p data-pid=\"NJ3a9A50\"><strong>3.业内人士转行篇</strong></p><p data-pid=\"3vnBlMwq\"> 这一部分，大家看看就好，没有切身体验，我也只能是发表一下自己的看法而已。</p><br/><p data-pid=\"uxszs2z0\"><strong>1）定位转变</strong><br/></p><p data-pid=\"4QUyldYL\">1.程序员转产品：懂行，注重实现，执行力高是程序员的优势，但是要注意不要被严谨的思维禁锢思想的广度，同时很多资深程序员不太习惯与其他成员打交道这个毛病，也需要学会慢慢转变，你已经从需求实现方变成了需求提供方，因此你要想的尽可能更多一点。</p><p data-pid=\"IF73xp8l\">2.运营转产品：很多公司运营产品不分家，因此运营转产品的例子还是挺多的，运营和产品的区别在于</p><p data-pid=\"CL7lUKFf\"><strong>Product Manager 主要负责上游工作</strong></p><p data-pid=\"kg_jVSvM\">1. 产品设计，保证有用，关注可用性与易用性</p><p data-pid=\"G4Nl8Kvu\">2. “指导”工程师实现产品灵魂</p><p data-pid=\"tXlcUhW-\">3. 管理并协调人力与时间资源</p><p data-pid=\"GXb_Ho8A\"><strong>Product Marketing主要负责下游工作</strong></p><p data-pid=\"epxjT7Xv\">1. 管理产品的发布</p><p data-pid=\"EYwT_J8N\">2. 营销产品——各种&#34;tell the world&#34;的营销计划</p><p data-pid=\"E2e5FvU_\">3. 为销售提供相应工具</p><p data-pid=\"A6TsK5Si\"> 这里面的区别，你需要好好体会。</p><p data-pid=\"41dhRbwR\">3.其他的，销售，市场，HR转产品，这个，具体的我还真的很难说。</p><br/><p data-pid=\"VQ1xlPZc\"><strong>2）快速上手</strong></p><p data-pid=\"PXfPKL5N\">对于业内人士，显然那种慢慢积累的过程是不合适的。因此，要如何快速的上手，作为一个PM来负责整个产品线呢？</p><p data-pid=\"bGe-79vC\">你有过相关的从业经验，你从其他视角了解整个产品的开发流程，因此你会比应届生有更多的体悟和经验。这时，面对一个即将来临的项目，如何解决它？下面是一套模板式的流程。</p><p data-pid=\"7CnV2VQa\">1.分析你们要做的产品是什么？市场定位，用户人群，应用场景等。分析你们做的产品解决了什么样的问题？如何解决的？你们这个产品的主要功能是什么，你针对这个核心功能做了哪些功能优化以及应用设计？做一份完整的功能列表。然后，分析一下市面上有哪些应用和你们要做的产品相似，根据自己的经验做一份竞品分析，这时候你完全可以有取舍的借鉴（好吧，适度的算是抄袭）。最后，找做产品的朋友要一份规范的PRD,BRD,MRD文档，有样学样的填充。可以的话，做一份炫酷的PPT去说服老板，说服成员，说服乙方。</p><p data-pid=\"6nNoIy2m\">2.项目已经立项，你的文档，你提的需求都得到通过，这时候，就是你拉着伙伴们开始做迭代开发的时候了。因为你经验不足，所以，你不要过多的干涉实现的过程，你需要做的是合理的维稳，以及持续的跟进，对于功能性，非功能性需求都需要做到及时的记录与反馈。这个过程中，你可以通过做各种需求小卡片，每周记录表单等来帮助你理清思路，与小伙伴沟通，并且，多和你那些产品的朋友聊天，他们能提供给你很多有用的建议的。</p><p data-pid=\"NwPIK5Nl\">3.大家都开始开发了。不意味着你就没事做了，因为你才刚刚开始，所以，这个过程中你要绷劲神经，一方面做好跟进，一方面，赶紧给自己充电，要学的东西实在不少。</p><p data-pid=\"ynZ0glkc\">4.至于具体的和不同成员的沟通，以及在不同的产品里程碑中你需要做的东西，太细，就不深入展开了，有心的话，网上这样的文章很多的。</p><p data-pid=\"TskyzFB6\">5.对了，切记，你必须比别人想得更多，你的小小的考虑不周带来的需求变动，都可能是对项目的一个致命打击。需求的频繁变动，是大忌。</p><br/><p data-pid=\"SqmH5kdp\"><strong>3）个人职能</strong><br/></p><p data-pid=\"p76oh2Ks\">根据你们团队的性质，根据你们所做的产品的性质，你所需要了解的东西会有很大不同1.社区类的你要琢磨功能上如何促进用户粘度，如何营造社区氛围，​如何提高用户的UGC。2.游戏类的，你则要和策划通力合作，对于游戏的玩法，游戏的生命周期，游戏的功能迭代做到合理的把控，甚至，你需要深入的思考玩法。3.电商类的话，相关经验太重要。<br/></p><p data-pid=\"jcr7UQMw\">总而言之，产品这行，入场券不好拿，但是作为业内人士，有自己的人脉，有一定的经验，其实要入门还是相对容易的多的。</p><br/><p data-pid=\"-ljrCCpT\">最后，与各位共勉，来自,Pmer。</p><p data-pid=\"BHltzo88\">-----------------------------------------------------------------------</p><p data-pid=\"DQImy7MP\">各种感谢但是不点赞的闹哪样= =||！！！太欺负人了</p>", "excerpt": "<strong>首先，好的产品经理一定是一个在知识广度与思维深度上，都有所追求有所积累的人，这绝对不是一个不需要专业门槛的谋生的职业。</strong>作为一个走在半路上撒欢的产品汪，我仅仅是发表一下我对于这条方向的规划以及看法，也希望能对其他热爱这个行业，渴望站在风口的猪们有所帮助，借用张小龙的一句话：我所说的都是错的。是的，如果把这篇文章当做产品来看的话，你做出你真实的反馈就好，如果真的适合你，解决了你的痛点，那么，我会很高…", "excerpt_new": "<strong>首先，好的产品经理一定是一个在知识广度与思维深度上，都有所追求有所积累的人，这绝对不是一个不需要专业门槛的谋生的职业。</strong>作为一个走在半路上撒欢的产品汪，我仅仅是发表一下我对于这条方向的规划以及看法，也希望能对其他热爱这个行业，渴望站在风口的猪们有所帮助，借用张小龙的一句话：我所说的都是错的。是的，如果把这篇文章当做产品来看的话，你做出你真实的反馈就好，如果真的适合你，解决了你的痛点，那么，我会很高…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1589695235, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1589695235457&page_num=55"}}