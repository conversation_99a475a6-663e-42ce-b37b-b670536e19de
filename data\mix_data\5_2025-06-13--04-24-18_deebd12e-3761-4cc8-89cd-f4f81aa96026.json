{"data": [{"id": "1729932902544", "type": "feed", "target": {"id": "15357327195", "type": "answer", "url": "https://api.zhihu.com/answers/15357327195", "voteup_count": 3, "thanks_count": 0, "question": {"id": "28480234", "title": "我们对印度的认识是否有误？", "url": "https://api.zhihu.com/questions/28480234", "type": "question", "question_type": "normal", "created": 1425313870, "answer_count": 381, "comment_count": 36, "follower_count": 9044, "detail": "一直对印度这个国家很感兴趣，发现网上对印度的主流看法是印度是一个很悲哀的很有喜感的国家，基础建设糟糕，种姓制度严重，腐败，落后。但是再看了很多宝莱坞的电影（三哥真是爱跳舞啊），发现展现的也不是很糟糕，虽然我知道电影里的画面不可全部相信，但也不会全是空中楼阁吧？然后又看到了一些歪果仁对2000年左右天朝的看法，突然觉得我们现在对印度的看法是否就是像那时歪果仁对我们的看法一样，太短视了或者说充满偏见了呢？如果真的是那么糟糕的一个国家，如何保持着那么高的发展速度呢？要知道在天朝发展速度似乎要缓下来之后，印度很可能成为GDP增长速度最快的国家了？是什么推动着印度这样的发展？<br/>————————————<br/>看完你们的回答之后，感觉更乱了。请允许我根据你们的回答问几个问题？<br/><ol><li>是否可以理解为，印度的制度很好，但是印度有很多人不好，是这些人拖累了印度，所以很多被拖累了的优秀的印度人离开印度之后，都变的非常厉害？<br/></li><li>有一个答案说印度没有种姓制度了？这是什么情况？<br/></li><li>如果种姓制度还存在的话，根据<a href=\"http://www.zhihu.com/people/zhong-en-63\" class=\"internal\">John中恩</a>的回答，是否可以理解为种姓制度其实是一种适合印度国情的制度？</li></ol>最后，感谢大家的回答！", "excerpt": "一直对印度这个国家很感兴趣，发现网上对印度的主流看法是印度是一个很悲哀的很有喜…", "bound_topic_ids": [2025, 3853, 4014, 27080], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "d2e4df1d01a2d713b494f6814e459800", "name": "谷户", "headline": "一个无业游民", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/gu-hu-57", "url_token": "gu-hu-57", "avatar_url": "https://picx.zhimg.com/20f1c45d803d42a639930058fd43912b_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1729932901, "created_time": 1729932901, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"8fTTOMqP\">| 撰文：凯莉；版面：Anjana </p><p data-pid=\"9DH91wll\"><b>在美国，印度人不仅盛产大公司高管及 CEO，还在另一种神奇职业上表现突出，那就是旅馆老板。</b></p><p data-pid=\"k_zawZYE\">在美国的旅馆中，超过 60% 由印度人掌控。其中，80% 至 90% 的中小城镇汽车旅馆，皆由印度裔中姓氏为 Patel 的人把控。 </p><p data-pid=\"WWmVdzxi\">换言之，倘若你曾到过美国，游览过大峡谷、黄石公园这些地方，那么你大概率住过汽车旅馆；而如若你住过汽车旅馆，那很可能入住的就是印度人掌管的汽车旅馆。 </p><p data-pid=\"atgnlNvS\">难怪有传闻称，就连美国的土匪们都有一个禁忌：<b>“只要你在江湖上惹怒了一个印度人，整个美利坚大陆都将没有你的藏身之地。”</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pica.zhimg.com/v2-8060397a4a06290f967fcb0fb6566022_b.jpg\" data-size=\"normal\" data-rawwidth=\"554\" data-rawheight=\"422\" data-original-token=\"v2-9fa3f923b8478ec8f11265b508adfd34\" class=\"origin_image zh-lightbox-thumb\" width=\"554\" data-original=\"https://pica.zhimg.com/v2-8060397a4a06290f967fcb0fb6566022_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;554&#39; height=&#39;422&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"554\" data-rawheight=\"422\" data-original-token=\"v2-9fa3f923b8478ec8f11265b508adfd34\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"554\" data-original=\"https://pica.zhimg.com/v2-8060397a4a06290f967fcb0fb6566022_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-8060397a4a06290f967fcb0fb6566022_b.jpg\"/><figcaption>汽车旅馆，图片来源于网络 </figcaption></figure><p data-pid=\"8IPQhp2s\">他们是如何做到的呢？ </p><p data-pid=\"ARV-FKq1\">这个帕特尔（Patel）族群来到美国后，充分发挥了印度人团结互助的精神。哥哥带动弟弟，弟弟拉扯妹妹，如此这般，一个又一个，一代接一代，逐渐垄断了旅馆业。 </p><p data-pid=\"1vVlFuqW\">帕特尔人来自印度古吉拉特省，该省位于印度与巴基斯坦交界处，濒临阿拉伯海。独特的地理位置使其成为印度与邻国贸易的理想之地。这里的人也非常热衷于前往亚洲和非洲的邻国旅行及从事贸易活动。 </p><p data-pid=\"t2Y4PPq8\">在古吉拉特的大部分村庄，都有一位由统治者册封的地主，负责收取地租和经营农场。这群人被称为帕特尔人（Patel），这也是他们的姓氏，字面意思为 “地主”。 </p><p data-pid=\"hx6x5nLx\">后来，随着土地逐渐被划分得越来越小，农耕难以维持生计。19 世纪末至 20 世纪初，帕特尔人大量移民至东非的乌干达从事贸易和铁路合同工工作。 </p><p data-pid=\"kTmvdMdI\">1972 年，阿明掌控乌干达政权后，开始驱赶境内非非洲族群，并抢占他们的生意。共有 7 万古吉拉特人被驱逐出乌干达。这些无家可归的难民一部分回到了印度，另一部分则移民到了英国、加拿大和美国。 </p><p data-pid=\"WErhLd5m\">了解了这段历史，我们就可以知道，<b>帕特尔人世世代代都非常擅长做生意，可类比为 “印度的温州人”。</b></p><p data-pid=\"nnUghwCh\">帕特尔人以难民身份进入美国，生活极为艰难，但天生擅长做生意的他们却发现了汽车旅馆的巨大机遇。 </p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-e739f6e88ea93efd86859d24dfe823c9_b.jpg\" data-size=\"normal\" data-rawwidth=\"904\" data-rawheight=\"682\" data-original-token=\"v2-b1821db22775eba241bb7122ee1e6f43\" class=\"origin_image zh-lightbox-thumb\" width=\"904\" data-original=\"https://pic2.zhimg.com/v2-e739f6e88ea93efd86859d24dfe823c9_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;904&#39; height=&#39;682&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"904\" data-rawheight=\"682\" data-original-token=\"v2-b1821db22775eba241bb7122ee1e6f43\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"904\" data-original=\"https://pic2.zhimg.com/v2-e739f6e88ea93efd86859d24dfe823c9_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-e739f6e88ea93efd86859d24dfe823c9_b.jpg\"/><figcaption>美国汽车旅馆，图片来源于网络 </figcaption></figure><p data-pid=\"WautNnar\"><b>首先是天时。</b>当时，美国修改移民法案，任何移民只要在美国投资 4 万美元，就可以申请永久居留身份。这促使大量印度裔移民投资购买美国的汽车旅馆。 </p><p data-pid=\"l_MlqbXZ\">1973 年，由于石油禁运和全国汽油短缺，汽车旅馆行业严重不景气。在这个时候，既需要勇气去抄底，也需要智慧。 </p><p data-pid=\"OV_PDqbS\"><b>其次是地利。</b>当时被迫转让的汽车旅馆价格极其低廉，使得投资回报率很高。 </p><p data-pid=\"PvDb_UkN\"><b>最后是人和。</b>帕特尔人生活十分简朴，吃苦耐劳且非常团结。 </p><p data-pid=\"voDR-ldG\">他们买下汽车旅馆后，会裁掉所有员工，一家人日夜忙碌，共同经营旅馆，甚至孩子在假期也要帮忙。 </p><p data-pid=\"jcMPl1yz\">他们很少娱乐，一家人的生活花销很低。买下汽车旅馆后，也省掉了租房或者住房按揭的费用。这就使得他们可以用更低的房价争夺客户，而利润率并未下降。 </p><p data-pid=\"ABbjV7eW\">他们随后进行翻新和升级以提高现金流，出售房产并换购更好的汽车旅馆。但传统保险公司不愿提供保险，银行不提供抵押贷款，于是帕特尔人便相互融资，并为自己的房产投保。 </p><p data-pid=\"3llBq2hv\">假设一个帕特尔家庭要收购一个 20 间房的小型汽车旅馆，大概需要 5 万美元，但可从银行贷款到 80% 至 90%。 当时平均房价是 12 至 13 美元，入住率为 50% 至 60%。 </p><p data-pid=\"3Ej8KDSx\">汽车旅馆大概能让帕特尔家庭每年获得 5 万美元的收入。 </p><p data-pid=\"qLkx-Qy3\">一部分帕特尔人成功后，就会收购更大的汽车旅馆，把生意扩大。其他的帕特尔人看到他人成功后也开始模仿。久而久之，帕特尔人就完全控制了汽车旅馆市场。 </p><p data-pid=\"cn0H-Yug\">由于加入这个行业的帕特尔人越来越多且缺乏经验，于是他们选择加盟品牌，直接复制前辈的成功经验，于是小型汽车旅馆快速连锁化。 </p><p data-pid=\"hGo9aet6\"><b>1962 年美国连锁汽车旅馆仅占 2%，而到了 1987 年，汽车旅馆的连锁化率达到了 64%。</b></p><p data-pid=\"FQHF20P8\">后来，随着美国经济的发展，顾客需求日益多元化，汽车旅馆也摇身一变，演变成各式各样的精品旅馆。 </p><p data-pid=\"Z9nuXdJl\">帕特尔家族的成功故事告诉我们：<b>找到一个稳定的商业模式，哪怕看起来平凡，也能带来财务自由。</b></p><p data-pid=\"1IKHkMIo\">创业并不需要追求高大上的机会，低成本、可复制、经过验证的方案，反而更容易成功。一步一步扎实做起，逐步扩大，最终你也能把握住改变命运的机会。 </p><p data-pid=\"0phvPfik\"><b>财富的积累，不在于开始时有多华丽，而在于你能否耐心地走好每一步。</b></p><p data-pid=\"bJqBJF0R\">本文来自微信公众号<a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s/PPzR_Bpi1Xasv9uj8nQTRw\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">“凯莉彭”</a>，作者：凯莉彭，36氪经授权发布。</p>", "excerpt": "| 撰文：凯莉；版面：Anjana <b>在美国，印度人不仅盛产大公司高管及 CEO，还在另一种神奇职业上表现突出，那就是旅馆老板。</b>在美国的旅馆中，超过 60% 由印度人掌控。其中，80% 至 90% 的中小城镇汽车旅馆，皆由印度裔中姓氏为 Patel 的人把控。 换言之，倘若你曾到过美国，游览过大峡谷、黄石公园这些地方，那么你大概率住过汽车旅馆；而如若你住过汽车旅馆，那很可能入住的就是印度人掌管的汽车旅馆。 难怪有传闻称，就连美国的土…", "excerpt_new": "| 撰文：凯莉；版面：Anjana <b>在美国，印度人不仅盛产大公司高管及 CEO，还在另一种神奇职业上表现突出，那就是旅馆老板。</b>在美国的旅馆中，超过 60% 由印度人掌控。其中，80% 至 90% 的中小城镇汽车旅馆，皆由印度裔中姓氏为 Patel 的人把控。 换言之，倘若你曾到过美国，游览过大峡谷、黄石公园这些地方，那么你大概率住过汽车旅馆；而如若你住过汽车旅馆，那很可能入住的就是印度人掌管的汽车旅馆。 难怪有传闻称，就连美国的土…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1729932902, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}, {"id": "1729932900322", "type": "feed", "target": {"id": "3314022678", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1729932900, "updated": 1729932949, "title": "在美国的印度人，竟然垄断了这个行业", "excerpt_title": "", "content": "<p data-pid=\"jyVXNSYS\">| 撰文：凯莉；版面：Anjana </p><p data-pid=\"0g_aQMXA\"><b>在美国，印度人不仅盛产大公司高管及 CEO，还在另一种神奇职业上表现突出，那就是旅馆老板。</b></p><p data-pid=\"ZtLQixOV\">在美国的旅馆中，超过 60% 由印度人掌控。其中，80% 至 90% 的中小城镇汽车旅馆，皆由印度裔中姓氏为 Patel 的人把控。 </p><p data-pid=\"YPx5Mzhi\">换言之，倘若你曾到过美国，游览过大峡谷、黄石公园这些地方，那么你大概率住过汽车旅馆；而如若你住过汽车旅馆，那很可能入住的就是印度人掌管的汽车旅馆。 </p><p data-pid=\"KGO6dqOh\">难怪有传闻称，就连美国的土匪们都有一个禁忌：<b>“只要你在江湖上惹怒了一个印度人，整个美利坚大陆都将没有你的藏身之地。”</b></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-8060397a4a06290f967fcb0fb6566022_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"554\" data-rawheight=\"422\" class=\"origin_image zh-lightbox-thumb\" width=\"554\" data-original=\"https://pica.zhimg.com/v2-8060397a4a06290f967fcb0fb6566022_r.jpg\" data-original-token=\"v2-9fa3f923b8478ec8f11265b508adfd34\"/><figcaption>汽车旅馆，图片来源于网络 </figcaption></figure><p data-pid=\"0VjCRykI\">他们是如何做到的呢？ </p><p data-pid=\"DKwIILkx\">这个帕特尔（Patel）族群来到美国后，充分发挥了印度人团结互助的精神。哥哥带动弟弟，弟弟拉扯妹妹，如此这般，一个又一个，一代接一代，逐渐垄断了旅馆业。 </p><p data-pid=\"yGGDDe-R\">帕特尔人来自印度古吉拉特省，该省位于印度与巴基斯坦交界处，濒临阿拉伯海。独特的地理位置使其成为印度与邻国贸易的理想之地。这里的人也非常热衷于前往亚洲和非洲的邻国旅行及从事贸易活动。 </p><p data-pid=\"wlbSoNWQ\">在古吉拉特的大部分村庄，都有一位由统治者册封的地主，负责收取地租和经营农场。这群人被称为帕特尔人（Patel），这也是他们的姓氏，字面意思为 “地主”。 </p><p data-pid=\"kQrsf80E\">后来，随着土地逐渐被划分得越来越小，农耕难以维持生计。19 世纪末至 20 世纪初，帕特尔人大量移民至东非的乌干达从事贸易和铁路合同工工作。 </p><p data-pid=\"Wfyn0Dhb\">1972 年，阿明掌控乌干达政权后，开始驱赶境内非非洲族群，并抢占他们的生意。共有 7 万古吉拉特人被驱逐出乌干达。这些无家可归的难民一部分回到了印度，另一部分则移民到了英国、加拿大和美国。 </p><p data-pid=\"dD_Od2oa\">了解了这段历史，我们就可以知道，<b>帕特尔人世世代代都非常擅长做生意，可类比为 “印度的温州人”。</b></p><p data-pid=\"aF_9Db-o\">帕特尔人以难民身份进入美国，生活极为艰难，但天生擅长做生意的他们却发现了汽车旅馆的巨大机遇。 </p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-e739f6e88ea93efd86859d24dfe823c9_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"904\" data-rawheight=\"682\" class=\"origin_image zh-lightbox-thumb\" width=\"904\" data-original=\"https://pic2.zhimg.com/v2-e739f6e88ea93efd86859d24dfe823c9_r.jpg\" data-original-token=\"v2-b1821db22775eba241bb7122ee1e6f43\"/><figcaption>美国汽车旅馆，图片来源于网络 </figcaption></figure><p data-pid=\"03dXHeob\"><b>首先是天时。</b>当时，美国修改移民法案，任何移民只要在美国投资 4 万美元，就可以申请永久居留身份。这促使大量印度裔移民投资购买美国的汽车旅馆。 </p><p data-pid=\"BCJ59lGu\">1973 年，由于石油禁运和全国汽油短缺，汽车旅馆行业严重不景气。在这个时候，既需要勇气去抄底，也需要智慧。 </p><p data-pid=\"ZXb_qg2n\"><b>其次是地利。</b>当时被迫转让的汽车旅馆价格极其低廉，使得投资回报率很高。 </p><p data-pid=\"zj-BF-eb\"><b>最后是人和。</b>帕特尔人生活十分简朴，吃苦耐劳且非常团结。 </p><p data-pid=\"2tguDsuQ\">他们买下汽车旅馆后，会裁掉所有员工，一家人日夜忙碌，共同经营旅馆，甚至孩子在假期也要帮忙。 </p><p data-pid=\"q5ekXig7\">他们很少娱乐，一家人的生活花销很低。买下汽车旅馆后，也省掉了租房或者住房按揭的费用。这就使得他们可以用更低的房价争夺客户，而利润率并未下降。 </p><p data-pid=\"bANgEeVM\">他们随后进行翻新和升级以提高现金流，出售房产并换购更好的汽车旅馆。但传统保险公司不愿提供保险，银行不提供抵押贷款，于是帕特尔人便相互融资，并为自己的房产投保。 </p><p data-pid=\"Tzfu_oAV\">假设一个帕特尔家庭要收购一个 20 间房的小型汽车旅馆，大概需要 5 万美元，但可从银行贷款到 80% 至 90%。 当时平均房价是 12 至 13 美元，入住率为 50% 至 60%。 </p><p data-pid=\"rS8GG8n0\">汽车旅馆大概能让帕特尔家庭每年获得 5 万美元的收入。 </p><p data-pid=\"e5kYgUyh\">一部分帕特尔人成功后，就会收购更大的汽车旅馆，把生意扩大。其他的帕特尔人看到他人成功后也开始模仿。久而久之，帕特尔人就完全控制了汽车旅馆市场。 </p><p data-pid=\"HtVXqbMM\">由于加入这个行业的帕特尔人越来越多且缺乏经验，于是他们选择加盟品牌，直接复制前辈的成功经验，于是小型汽车旅馆快速连锁化。 </p><p data-pid=\"4r0vMqc4\"><b>1962 年美国连锁汽车旅馆仅占 2%，而到了 1987 年，汽车旅馆的连锁化率达到了 64%。</b></p><p data-pid=\"vaE_0n2V\">后来，随着美国经济的发展，顾客需求日益多元化，汽车旅馆也摇身一变，演变成各式各样的精品旅馆。 </p><p data-pid=\"WI7aHOZs\">帕特尔家族的成功故事告诉我们：<b>找到一个稳定的商业模式，哪怕看起来平凡，也能带来财务自由。</b></p><p data-pid=\"7dpz2qNI\">创业并不需要追求高大上的机会，低成本、可复制、经过验证的方案，反而更容易成功。一步一步扎实做起，逐步扩大，最终你也能把握住改变命运的机会。 </p><p data-pid=\"pl5iCKV0\"><b>财富的积累，不在于开始时有多华丽，而在于你能否耐心地走好每一步。</b></p><hr/><p data-pid=\"jRAJgTy_\">我是凯莉彭，创始人IP商业教练，APEA亚太青年企业家，前硅谷资深数据科学家，很高兴遇见你～<br/>更多内容，欢迎链接kelly71017，备注“知乎”，领取《内容力变现指南》。</p>", "excerpt": "| 撰文：凯莉；版面：Anjana <b>在美国，印度人不仅盛产大公司高管及 CEO，还在另一种神奇职业上表现突出，那就是旅馆老板。</b>在美国的旅馆中，超过 60% 由印度人掌控。其中，80% 至 90% 的中小城镇汽车旅馆，皆由印度裔中姓氏为 Patel 的人把控。 换言之，倘若你曾到过美国，游览过大峡谷、黄石公园这些地方，那么你大概率住过汽车旅馆；而如若你住过汽车旅馆，那很可能入住的就是印度人掌管的汽车旅馆。 难怪有传闻称，就连美国的土…", "excerpt_new": "| 撰文：凯莉；版面：Anjana <b>在美国，印度人不仅盛产大公司高管及 CEO，还在另一种神奇职业上表现突出，那就是旅馆老板。</b>在美国的旅馆中，超过 60% 由印度人掌控。其中，80% 至 90% 的中小城镇汽车旅馆，皆由印度裔中姓氏为 Patel 的人把控。 换言之，倘若你曾到过美国，游览过大峡谷、黄石公园这些地方，那么你大概率住过汽车旅馆；而如若你住过汽车旅馆，那很可能入住的就是印度人掌管的汽车旅馆。 难怪有传闻称，就连美国的土…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/3314022678", "comment_permission": "all", "voteup_count": 1, "comment_count": 0, "image_url": "https://pic1.zhimg.com/v2-c8e4665e857e80d2d1621d27a159cf21_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1729932900, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1729238946085", "type": "feed", "target": {"id": "8681743859", "type": "answer", "url": "https://api.zhihu.com/answers/8681743859", "voteup_count": 2, "thanks_count": 0, "question": {"id": "64080129", "title": "如何理解 Elon Musk 的第一性原理？", "url": "https://api.zhihu.com/questions/64080129", "type": "question", "question_type": "normal", "created": 1503025400, "answer_count": 426, "comment_count": 35, "follower_count": 4785, "detail": "<p>Elon Musk认为他的思维方法是符合物理学的第一性原理的，就是把事物的基本原理搞清楚，然后再把后面的知识结构捋下来。我的问题是：这样做，为什么会提高效率呢？难道这样做不是需要耗费更多的时间和精力吗？而且，尤其是统计学做出来的结论，恐怕是很难找到背后的原理吧。我们应该怎么看待这个第一性原理呢？</p>", "excerpt": "Elon Musk认为他的思维方法是符合物理学的第一性原理的，就是把事物的基本原理搞清…", "bound_topic_ids": [962, 2253], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "d2e3caff48665b092534b1de11f64633", "name": "发哥", "headline": "孩儿们的父亲", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/fa-ge", "url_token": "fa-ge", "avatar_url": "https://picx.zhimg.com/v2-a969f12e3af0f6ab6fe32937e4f6bde3_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1729238945, "created_time": 1729238945, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-454db06d019006f7333275288a92fa29_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"900\" data-rawheight=\"383\" data-original-token=\"v2-c9603574318ff4b831fa760d327ca5eb\" class=\"origin_image zh-lightbox-thumb\" width=\"900\" data-original=\"https://picx.zhimg.com/v2-454db06d019006f7333275288a92fa29_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;900&#39; height=&#39;383&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"900\" data-rawheight=\"383\" data-original-token=\"v2-c9603574318ff4b831fa760d327ca5eb\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"900\" data-original=\"https://picx.zhimg.com/v2-454db06d019006f7333275288a92fa29_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-454db06d019006f7333275288a92fa29_b.jpg\"/></figure><p data-pid=\"AKqJ2cWV\">| 撰文：凯莉；版面：Anjana </p><p data-pid=\"4IsLk8y7\">前两天我在斯坦福大学参加了一次设计人生课，主讲的老师就是《斯坦福大学人生设计课》这本书的作者Bill Burnett。 </p><p data-pid=\"6bEjA-MI\">一下午的课程中有一个游戏互动环节让我印象特别深，这个游戏是这样的，Bill Burnet让我们在纸上画30个圆。</p><p data-pid=\"NbmWrogA\">然后他故意给了一些指示说，比如，在这30个圆圈里面可以画一些笑脸、画一些表情，他只是举了个例子，然后给了我们三分钟的时间去填满纸上的这30个圆圈。 </p><p data-pid=\"IFuahHxN\">然后我就真的傻呵呵的，就在30个圆上画上了不同的表情，哭的笑的皱眉的，各种表情，很快就画完了。 </p><p data-pid=\"hTafE1Yn\">我说为什么这个活动需要三分钟的时间，我每一个表情两三笔就画完了，然后会去看看同桌其他人画的是什么样子。 </p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-98a59cbc7fb300f7d1749c933864e264_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"1000\" data-original-token=\"v2-03d7b7ae7279ac14aae871741acc8551\" class=\"origin_image zh-lightbox-thumb\" width=\"1000\" data-original=\"https://pic1.zhimg.com/v2-98a59cbc7fb300f7d1749c933864e264_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1000&#39; height=&#39;1000&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"1000\" data-original-token=\"v2-03d7b7ae7279ac14aae871741acc8551\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1000\" data-original=\"https://pic1.zhimg.com/v2-98a59cbc7fb300f7d1749c933864e264_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-98a59cbc7fb300f7d1749c933864e264_b.jpg\"/></figure><p data-pid=\"LXeRmm5s\">| 《斯坦福大学人生设计课》，图片来源于网络 </p><p data-pid=\"GDtZLCg4\">三分钟的时间到了以后，Bill就说为什么要玩这个游戏，因为其实他一开始是故意给了一个模糊的指令，那就是三分钟的时间去 do something with the 30 circles，他并没有说你一定要用上每一个圆，或者一定要画表情。 </p><p data-pid=\"woA2fbSu\">但是我就作为一个“too young too naive”的新手，就被他给的这些框架给限定住了，然后真的就是在他给的框架里面去发挥了。 </p><p data-pid=\"VLrXRzdW\">然后Bill就给了一些例子说，以往大家会对这30个圆做什么样的动作。 </p><p data-pid=\"IDZj52nB\">有些人可能是在每个圆上画表情，有的人会把一些圆给它串联起来，让它成为一个新的东西，有的人可能会把这张甚至把这张纸叠成一个纸飞机，这些其实都是“did something with 30 circles”。 </p><p data-pid=\"ARVlAVg_\"><b>创造性思维就是我们不被模糊的指示局限，去揣测别人想要的标准答案是什么，而是有自己的创造性发挥。</b></p><p data-pid=\"WgRu2Mi5\">然后他说，“好，那接下来我们进入第2轮，继续画30个圆，然后继续让你们对着这30个圆做什么。” </p><p data-pid=\"qdMP98ih\">这下好了，你看现在思路应该被打开了吧。因为知道原来还能这么玩。但我们都觉得比上一次更难了，因为想做得更好，想跳出他给的例子框架再创新。 </p><p data-pid=\"aeUSNnPT\">第一轮画之前可能毫不犹豫，这一轮就得想半天。因为你想跳出他给出的更大的框架。 </p><p data-pid=\"XySGRoUC\">我就深刻的感受到，<b>我从小受到的教育，一直在让我去寻求一个标准答案和别人的指示。</b></p><p data-pid=\"RGVHyWyR\">咱们中国人呢，还经常把一句话当成是一句夸奖，叫做“听话照做”，而不是批判性思维，听话照做这句话不能说是害死人吧，至少是不全对。 </p><p data-pid=\"aXLpcjo1\">因为真的听话照做以后，你发现无论是在创业的道路上，还是在职业道路上，你都在寻求一个答案。 </p><p data-pid=\"Ws-9tGi8\">但是如果说总是在走别人走过的路，看别人做了什么，自己也去做什么，听别人说应该怎么做，自己也是怎么做，那么你永远是走在别人后面，也许能帮你少走一些弯路，但是你永远是跟在别人后面。 </p><p data-pid=\"-XyhaMe3\">要想不走在别人后面，你就是<b>需要有批判性思维，需要有原创性的思考。</b></p><p data-pid=\"f9g3teKp\">我就在想，唉呀，这怎么办呢？我就想到我自己的创业项目，我就发现很多时候我们都是，看看那个谁是怎么做的，看看这个人是怎么做的，其实就是这一种寻求标准答案的思维方式的延续。 </p><p data-pid=\"NDAeA3Gm\">我们缺少的就是这种跳出框架的创造性思维。 </p><p data-pid=\"IeB72Isf\"><b>怎么去培养自己的创造性思维呢？</b></p><p data-pid=\"-lkSIe9M\">这里就得提到<b>马斯克强调的第一性原理。</b>也就是回归事物最基本的条件，把其结构成各种要素进行分析，从而找到实现目标最优路径。 </p><p data-pid=\"MNA9B5lU\"><b>多读书，建立多元思维模型，多和人聊，开阔自己的眼界。</b>面对问题的时候，首先觉察自己的答案是否被条条框框限制了，要解决的问题到底是什么，是否可以自己创造出一套解决方案。 </p><p data-pid=\"b8mZczIa\">简单来说，就是：别老想着怎么在别人已经铺好的路上走，而要从最基础的层面去想问题，然后铺出自己的道路。 </p><p data-pid=\"I8cRnH3l\">说起来容易做起来难，<b>首先从自我觉察开始，当你意识到自己被框住了，你才有跳出去的机会。</b></p><hr/><p data-pid=\"-luAFR0L\">我是凯莉彭，创始人IP商业教练，APEA亚太青年企业家，前硅谷资深数据科学家，很高兴遇见你～<br/>更多内容，欢迎链接kelly71017，备注“知乎”，领取《内容力变现指南》。</p>", "excerpt": "| 撰文：凯莉；版面：Anjana 前两天我在斯坦福大学参加了一次设计人生课，主讲的老师就是《斯坦福大学人生设计课》这本书的作者Bill Burnett。 一下午的课程中有一个游戏互动环节让我印象特别深，这个游戏是这样的，Bill Burnet让我们在纸上画30个圆。 然后他故意给了一些指示说，比如，在这30个圆圈里面可以画一些笑脸、画一些表情，他只是举了个例子，然后给了我们三分钟的时间去填满纸上的这30个圆圈。 然后我就真的傻呵呵的…", "excerpt_new": "| 撰文：凯莉；版面：Anjana 前两天我在斯坦福大学参加了一次设计人生课，主讲的老师就是《斯坦福大学人生设计课》这本书的作者Bill Burnett。 一下午的课程中有一个游戏互动环节让我印象特别深，这个游戏是这样的，Bill Burnet让我们在纸上画30个圆。 然后他故意给了一些指示说，比如，在这30个圆圈里面可以画一些笑脸、画一些表情，他只是举了个例子，然后给了我们三分钟的时间去填满纸上的这30个圆圈。 然后我就真的傻呵呵的…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1729238946, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}, {"id": "1729238944868", "type": "feed", "target": {"id": "1837511197", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1729238943, "updated": 1729239235, "title": "为什么我们不擅长创新，斯坦福大学给我上了一课", "excerpt_title": "", "content": "<p data-pid=\"LRpRby9I\">| 撰文：凯莉；版面：Anjana </p><p data-pid=\"SgUaRUP1\">前两天我在斯坦福大学参加了一次设计人生课，主讲的老师就是《斯坦福大学人生设计课》这本书的作者Bill Burnett。 </p><p data-pid=\"o3c6W0mv\">一下午的课程中有一个游戏互动环节让我印象特别深，这个游戏是这样的，Bill Burnet让我们在纸上画30个圆。</p><p data-pid=\"0JDsk_G0\">然后他故意给了一些指示说，比如，在这30个圆圈里面可以画一些笑脸、画一些表情，他只是举了个例子，然后给了我们三分钟的时间去填满纸上的这30个圆圈。 </p><p data-pid=\"ilP6ZXBf\">然后我就真的傻呵呵的，就在30个圆上画上了不同的表情，哭的笑的皱眉的，各种表情，很快就画完了。 </p><p data-pid=\"lopwigGO\">我说为什么这个活动需要三分钟的时间，我每一个表情两三笔就画完了，然后会去看看同桌其他人画的是什么样子。 </p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-98a59cbc7fb300f7d1749c933864e264_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1000\" data-rawheight=\"1000\" class=\"origin_image zh-lightbox-thumb\" width=\"1000\" data-original=\"https://pic1.zhimg.com/v2-98a59cbc7fb300f7d1749c933864e264_r.jpg\" data-original-token=\"v2-03d7b7ae7279ac14aae871741acc8551\"/></figure><p data-pid=\"UY8mOE-B\">| 《斯坦福大学人生设计课》，图片来源于网络 </p><p data-pid=\"Z1_DzoHi\">三分钟的时间到了以后，Bill就说为什么要玩这个游戏，因为其实他一开始是故意给了一个模糊的指令，那就是三分钟的时间去 do something with the 30 circles，他并没有说你一定要用上每一个圆，或者一定要画表情。 </p><p data-pid=\"CH9XTJ5Z\">但是我就作为一个“too young too naive”的新手，就被他给的这些框架给限定住了，然后真的就是在他给的框架里面去发挥了。 </p><p data-pid=\"D1yXXgaM\">然后Bill就给了一些例子说，以往大家会对这30个圆做什么样的动作。 </p><p data-pid=\"jzFFh6jG\">有些人可能是在每个圆上画表情，有的人会把一些圆给它串联起来，让它成为一个新的东西，有的人可能会把这张甚至把这张纸叠成一个纸飞机，这些其实都是“did something with 30 circles”。 </p><p data-pid=\"2EwHAm30\"><b>创造性思维就是我们不被模糊的指示局限，去揣测别人想要的标准答案是什么，而是有自己的创造性发挥。</b></p><p data-pid=\"tYvdbptL\">然后他说，“好，那接下来我们进入第2轮，继续画30个圆，然后继续让你们对着这30个圆做什么。” </p><p data-pid=\"ghAjv81e\">这下好了，你看现在思路应该被打开了吧。因为知道原来还能这么玩。但我们都觉得比上一次更难了，因为想做得更好，想跳出他给的例子框架再创新。 </p><p data-pid=\"pHwLB8RE\">第一轮画之前可能毫不犹豫，这一轮就得想半天。因为你想跳出他给出的更大的框架。 </p><p data-pid=\"gSMcaK7i\">我就深刻的感受到，<b>我从小受到的教育，一直在让我去寻求一个标准答案和别人的指示。</b></p><p data-pid=\"aB6Wos4p\">咱们中国人呢，还经常把一句话当成是一句夸奖，叫做“听话照做”，而不是批判性思维，听话照做这句话不能说是害死人吧，至少是不全对。 </p><p data-pid=\"ORBDIwdY\">因为真的听话照做以后，你发现无论是在创业的道路上，还是在职业道路上，你都在寻求一个答案。 </p><p data-pid=\"gO-y4OiF\">但是如果说总是在走别人走过的路，看别人做了什么，自己也去做什么，听别人说应该怎么做，自己也是怎么做，那么你永远是走在别人后面，也许能帮你少走一些弯路，但是你永远是跟在别人后面。 </p><p data-pid=\"lapM2GZB\">要想不走在别人后面，你就是<b>需要有批判性思维，需要有原创性的思考。</b></p><p data-pid=\"xW273Kc0\">我就在想，唉呀，这怎么办呢？我就想到我自己的创业项目，我就发现很多时候我们都是，看看那个谁是怎么做的，看看这个人是怎么做的，其实就是这一种寻求标准答案的思维方式的延续。 </p><p data-pid=\"5uOhI6UR\">我们缺少的就是这种跳出框架的创造性思维。 </p><p data-pid=\"2NJA6Lcf\"><b>怎么去培养自己的创造性思维呢？</b></p><p data-pid=\"-SVaxD9U\">这里就得提到<b>马斯克强调的第一性原理。</b>也就是回归事物最基本的条件，把其结构成各种要素进行分析，从而找到实现目标最优路径。 </p><p data-pid=\"4ycI9mUQ\"><b>多读书，建立多元思维模型，多和人聊，开阔自己的眼界。</b>面对问题的时候，首先觉察自己的答案是否被条条框框限制了，要解决的问题到底是什么，是否可以自己创造出一套解决方案。 </p><p data-pid=\"bJWtZdri\">简单来说，就是：别老想着怎么在别人已经铺好的路上走，而要从最基础的层面去想问题，然后铺出自己的道路。 </p><p data-pid=\"TazABLzS\">说起来容易做起来难，<b>首先从自我觉察开始，当你意识到自己被框住了，你才有跳出去的机会。</b></p><hr/><p data-pid=\"3b4pq7CU\">我是凯莉彭，创始人IP商业教练，APEA亚太青年企业家，前硅谷资深数据科学家，很高兴遇见你～<br/>更多内容，欢迎链接kelly71017，备注“知乎”，领取《内容力变现指南》。</p>", "excerpt": "| 撰文：凯莉；版面：Anjana 前两天我在斯坦福大学参加了一次设计人生课，主讲的老师就是《斯坦福大学人生设计课》这本书的作者Bill Burnett。 一下午的课程中有一个游戏互动环节让我印象特别深，这个游戏是这样的，Bill Burnet让我们在纸上画30个圆。 然后他故意给了一些指示说，比如，在这30个圆圈里面可以画一些笑脸、画一些表情，他只是举了个例子，然后给了我们三分钟的时间去填满纸上的这30个圆圈。 然后我就真的傻呵呵的…", "excerpt_new": "| 撰文：凯莉；版面：Anjana 前两天我在斯坦福大学参加了一次设计人生课，主讲的老师就是《斯坦福大学人生设计课》这本书的作者Bill Burnett。 一下午的课程中有一个游戏互动环节让我印象特别深，这个游戏是这样的，Bill Burnet让我们在纸上画30个圆。 然后他故意给了一些指示说，比如，在这30个圆圈里面可以画一些笑脸、画一些表情，他只是举了个例子，然后给了我们三分钟的时间去填满纸上的这30个圆圈。 然后我就真的傻呵呵的…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/1837511197", "comment_permission": "all", "voteup_count": 3, "comment_count": 1, "image_url": "https://picx.zhimg.com/v2-d2ca37e00be6a36fd50cc825a96a1431_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1729238944, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1729238721926", "type": "feed", "target": {"id": "8678087414", "type": "answer", "url": "https://api.zhihu.com/answers/8678087414", "voteup_count": 2, "thanks_count": 1, "question": {"id": "23592179", "title": "30岁到35岁的人生该如何规划？", "url": "https://api.zhihu.com/questions/23592179", "type": "question", "question_type": "normal", "created": 1398762374, "answer_count": 7, "comment_count": 0, "follower_count": 118, "detail": "即将跨入30岁，工作也过了菜鸟的阶段，需要沉淀积累提升；20岁年代的人生可以说学习积累为核心，那30岁年代的呢？目标是什么？重点是什么？", "excerpt": "即将跨入30岁，工作也过了菜鸟的阶段，需要沉淀积累提升；20岁年代的人生可以说学习…", "bound_topic_ids": [1537, 3479, 4761, 101545], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "aadebb60d75ace6b2759ab8dbd9c8788", "name": "哈哈哈哈哈哈哈哈", "headline": "学而时习之", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/wu-zhi-hui", "url_token": "wu-zhi-hui", "avatar_url": "https://pic1.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1729238720, "created_time": 1729238720, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"FVQ9SaaW\">| 撰文：凯莉；版面：Anjana </p><p data-pid=\"XJwO2MfV\">我花了一天时间读完了这本书，迫不及待地想与你分享。 </p><p data-pid=\"WtNa9AyZ\"><b>这本书简直就是30-60岁人群的人生攻略，特别适合那些面临转型、正在寻找更深层次人生意义的人。</b></p><p data-pid=\"k1Uf7vPJ\">为什么我选择读这本书？ </p><p data-pid=\"8XT_zPsE\">最近，我的很多学员都遇到了一个共同问题：他们事业已经非常成功，许多人是公司的高管、老板，或有着令人羡慕的职业背景。 </p><p data-pid=\"u-Roi280\">然而，当他们脱下职业标签时，反而不清楚自己是谁，还能怎样创造价值。<b>而在外人看来，他们很光鲜很成功，无法共情他们的痛苦，只有他们自己知道，自己正在经历怎样的挣扎。</b></p><p data-pid=\"QmMSFPLh\">正是因为我看到很多人有这种困惑，当我看到这本书时，毫不犹豫地读了它，这本书的英文名是<b>《Finding Success, Happiness, and Deep Purpose in the Second Half of Life》</b>，意为“<b>在人生下半场找到成功、幸福与深层意义</b>”。 </p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-09a0a3a5a506370003e5b3d0c498d080_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"696\" data-rawheight=\"708\" data-original-token=\"v2-ade3bf49638a8875949a73cfd60649ca\" class=\"origin_image zh-lightbox-thumb\" width=\"696\" data-original=\"https://pic3.zhimg.com/v2-09a0a3a5a506370003e5b3d0c498d080_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;696&#39; height=&#39;708&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"696\" data-rawheight=\"708\" data-original-token=\"v2-ade3bf49638a8875949a73cfd60649ca\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"696\" data-original=\"https://pic3.zhimg.com/v2-09a0a3a5a506370003e5b3d0c498d080_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-09a0a3a5a506370003e5b3d0c498d080_b.jpg\"/></figure><p data-pid=\"m6wDqnA9\">| 《中年觉醒》，图片来源于网络 </p><p data-pid=\"JmbYjCWD\">这个名字就很有吸引力，也完美地总结了人生下半场的觉醒目标：从以往用来衡量成功和幸福的标准，转变到一种新的自我认知和价值追求。</p><p data-pid=\"Gx3MMeZ2\">这本书的作者是哈佛大学的教授，写书的灵感来源于他的一次飞行旅程中，他在商务舱里听到一位中年男人跟太太说：“我觉得自己已经没用了，没人再需要我了。”这句话激起了他的好奇心。 </p><p data-pid=\"feBojATN\">等到灯亮时，他发现说这话的人竟然是美国家喻户晓的一个知名人物。这位被人们认为十分成功的人，却感到自己无比失败。 </p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-a6228ae05f850eacab7a641c45009607_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"426\" data-rawheight=\"640\" data-original-token=\"v2-1979f1ff07e057be121746efff5a3992\" class=\"origin_image zh-lightbox-thumb\" width=\"426\" data-original=\"https://pic2.zhimg.com/v2-a6228ae05f850eacab7a641c45009607_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;426&#39; height=&#39;640&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"426\" data-rawheight=\"640\" data-original-token=\"v2-1979f1ff07e057be121746efff5a3992\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"426\" data-original=\"https://pic2.zhimg.com/v2-a6228ae05f850eacab7a641c45009607_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-a6228ae05f850eacab7a641c45009607_b.jpg\"/></figure><p data-pid=\"giBLVjp5\">| 作者Arthur C. Brooks，图片来源于网络 </p><p data-pid=\"CTB6IAMM\">在历史上，这样的案例并不少见，达尔文，这位改变了人类对生物学认知的伟大科学家，晚年却对自己感到极度不满意。</p><p data-pid=\"sEKLG7sK\">尽管他的进化论理论在27岁时就已确立，50岁时出版了《物种起源》，彻底改变了科学世界。但晚年时，他的创造力停滞不前，生活无法再让他感到幸福。这让他在生命的最后阶段陷入了长期的郁郁寡欢，直到73岁去世。 </p><p data-pid=\"jLehtLcs\">如果我们很多人从小到大一帆风顺，上名校、进名企，职业发展顺利，创业成功，一直追求卓越、力争上游，那么我们一定会面临相同的境遇。 </p><p data-pid=\"zAucElg2\">但是，这本书并不是为了让我们感到绝望，而是提供了一条全新的方向：<b>发展“第二曲线”。</b></p><p data-pid=\"xmoAgeRe\"><b>这个“第二曲线”不再依赖于过去那些流体智力（Fluid Intelligence），即推理、解决问题和创新的能力，而是转向晶体智力（Crystallized Intelligence），即经验和知识的积累。</b></p><p data-pid=\"O2RBOAkZ\">举个例子，年轻时我们可能以创新、提出新观点为骄傲，年长时则可以通过积累的经验、智慧，帮助他人更好地理解这些观点。 </p><p data-pid=\"RXzjSgVi\">正如一位年长的教授，可能不再有当年研究新领域的激情，但他的教学评分往往是最高的，因为他能够清晰而深入地传授知识。 </p><p data-pid=\"Xrlpy9Rv\">罗马哲学家西塞罗在《论责任》里也写过，<b>认为老年人的天生优势就是提供劝导、指导、劝告、教导他人，不去追求金钱、权力、声望这些回报。</b>他不仅提出了这些建议，还一直知行合一的实践自己的理念。活出了精彩的人生。 </p><p data-pid=\"8mZLNA2x\">比如搞科研、当程序员写代码，都算是在用你的流体智力。当教练、老师、做自媒体内容分享，都是在发挥你的晶体智力。 </p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-1aeaaebe8f417b9e7c6fe4fe05198f67_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"896\" data-original-token=\"v2-d2682670c469e10143cb6923198fd9cf\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-1aeaaebe8f417b9e7c6fe4fe05198f67_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;896&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"896\" data-original-token=\"v2-d2682670c469e10143cb6923198fd9cf\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-1aeaaebe8f417b9e7c6fe4fe05198f67_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-1aeaaebe8f417b9e7c6fe4fe05198f67_b.jpg\"/></figure><p data-pid=\"at4rt_s8\">| 图片来源于网络 </p><p data-pid=\"7-eWXA9V\">发挥晶体智力的职业，是不受年龄限制的，甚至你会在晚年才达到职业巅峰。<b>所以，如果你还在和别人拼你的流体智力，不妨看看怎么往晶体智力转型，找到人生第二曲线。</b></p><p data-pid=\"VTjCgynP\">但是，注意，找到人生第二曲线的目的，不是为了取得别人眼中更大的成就，活得比别人好，拥有的比别人多， </p><p data-pid=\"2lWCMAz1\">我们大多数人都习惯了以成就感来衡量自我价值，而成就感来源于和别人的比较。 </p><p data-pid=\"Q0RzvFmG\">这种“成功上瘾症”在现代社会非常普遍。追逐金钱、权力、名望，成了衡量人生价值的唯一标准。 </p><p data-pid=\"p2btfo08\">人们就像在跑步机上不断追赶前方的胡萝卜。然而，无论人们跑多快，这根胡萝卜始终在那里，永远不会带来持久的满足。 </p><p data-pid=\"p5NPllFh\"><b>真正的中年觉醒，来自于转向内在，开始从智慧、信仰、意义等维度重新定义自己。</b></p><p data-pid=\"-j07Y3z2\">追求卓越和成功可能是很多人的本性，但是本性不是命运，如果我们想要获得幸福，就必须与自己的本性做斗争。 </p><p data-pid=\"2qLc6MQc\">最后，把我特别喜欢的一句话分享给大家。 </p><p data-pid=\"-e2E97DM\">2018 年作者在印度的时候，他问印度的老师，会给这些四五十岁的工作狂和成功成瘾者什么建议。 </p><p data-pid=\"Xmv_Obeq\">那个老师的回答是：<b>“认识你自己，没有其他的了，除此之外没有什么可以给他们自由。”</b></p><p data-pid=\"QtuP10nD\">那怎么入手呢？作者问。 </p><p data-pid=\"jnSUUWtW\">老师回答说：<b>“从心入手，心境澄明，智慧自生。”</b></p><p data-pid=\"x8_3yI8c\">我相信不论你现在是接近 30 岁、还是已经 60 岁，你都会从这本书里收获启发， 所以我一定 要推荐给你。 </p><p data-pid=\"-ErYiXHb\">越早看清前方的路，就越有可能拥有人生下半场的幸福。 </p><hr/><p data-pid=\"zZZaz-yn\">我是凯莉彭，创始人IP商业教练，APEA亚太青年企业家，前硅谷资深数据科学家，很高兴遇见你～<br/>更多内容，欢迎链接kelly71017，备注“知乎”，领取《内容力变现指南》。</p>", "excerpt": "| 撰文：凯莉；版面：Anjana 我花了一天时间读完了这本书，迫不及待地想与你分享。 <b>这本书简直就是30-60岁人群的人生攻略，特别适合那些面临转型、正在寻找更深层次人生意义的人。</b>为什么我选择读这本书？ 最近，我的很多学员都遇到了一个共同问题：他们事业已经非常成功，许多人是公司的高管、老板，或有着令人羡慕的职业背景。 然而，当他们脱下职业标签时，反而不清楚自己是谁，还能怎样创造价值。 <b>而在外人看来，他们很光鲜很…</b>", "excerpt_new": "| 撰文：凯莉；版面：Anjana 我花了一天时间读完了这本书，迫不及待地想与你分享。 <b>这本书简直就是30-60岁人群的人生攻略，特别适合那些面临转型、正在寻找更深层次人生意义的人。</b>为什么我选择读这本书？ 最近，我的很多学员都遇到了一个共同问题：他们事业已经非常成功，许多人是公司的高管、老板，或有着令人羡慕的职业背景。 然而，当他们脱下职业标签时，反而不清楚自己是谁，还能怎样创造价值。 <b>而在外人看来，他们很光鲜很…</b>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1729238721, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}, {"id": "1729238719876", "type": "feed", "target": {"id": "1835764764", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1729238719, "updated": 1729238719, "title": "30岁到60岁人群的人生攻略", "excerpt_title": "", "content": "<p data-pid=\"wgZQHYpd\">| 撰文：凯莉；版面：Anjana </p><p data-pid=\"tBwa6P5j\">我花了一天时间读完了这本书，迫不及待地想与你分享。 </p><p data-pid=\"bL7QbNWg\"><b>这本书简直就是30-60岁人群的人生攻略，特别适合那些面临转型、正在寻找更深层次人生意义的人。</b></p><p data-pid=\"HCORwthz\">为什么我选择读这本书？ </p><p data-pid=\"McK2kXWb\">最近，我的很多学员都遇到了一个共同问题：他们事业已经非常成功，许多人是公司的高管、老板，或有着令人羡慕的职业背景。 </p><p data-pid=\"dnvJSOLc\">然而，当他们脱下职业标签时，反而不清楚自己是谁，还能怎样创造价值。<b>而在外人看来，他们很光鲜很成功，无法共情他们的痛苦，只有他们自己知道，自己正在经历怎样的挣扎。</b></p><p data-pid=\"ek3EwAXv\">正是因为我看到很多人有这种困惑，当我看到这本书时，毫不犹豫地读了它，这本书的英文名是<b>《Finding Success, Happiness, and Deep Purpose in the Second Half of Life》</b>，意为“<b>在人生下半场找到成功、幸福与深层意义</b>”。 </p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-09a0a3a5a506370003e5b3d0c498d080_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"696\" data-rawheight=\"708\" class=\"origin_image zh-lightbox-thumb\" width=\"696\" data-original=\"https://pic3.zhimg.com/v2-09a0a3a5a506370003e5b3d0c498d080_r.jpg\" data-original-token=\"v2-ade3bf49638a8875949a73cfd60649ca\"/></figure><p data-pid=\"lcuCY0Ea\">| 《中年觉醒》，图片来源于网络 </p><p data-pid=\"mVSnC_5D\">这个名字就很有吸引力，也完美地总结了人生下半场的觉醒目标：从以往用来衡量成功和幸福的标准，转变到一种新的自我认知和价值追求。</p><p data-pid=\"l_MRgkuG\">这本书的作者是哈佛大学的教授，写书的灵感来源于他的一次飞行旅程中，他在商务舱里听到一位中年男人跟太太说：“我觉得自己已经没用了，没人再需要我了。”这句话激起了他的好奇心。 </p><p data-pid=\"LNw1IKMM\">等到灯亮时，他发现说这话的人竟然是美国家喻户晓的一个知名人物。这位被人们认为十分成功的人，却感到自己无比失败。 </p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-a6228ae05f850eacab7a641c45009607_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"426\" data-rawheight=\"640\" class=\"origin_image zh-lightbox-thumb\" width=\"426\" data-original=\"https://pic2.zhimg.com/v2-a6228ae05f850eacab7a641c45009607_r.jpg\" data-original-token=\"v2-1979f1ff07e057be121746efff5a3992\"/></figure><p data-pid=\"mqnoTMr-\">| 作者Arthur C. Brooks，图片来源于网络 </p><p data-pid=\"vQ5ORvag\">在历史上，这样的案例并不少见，达尔文，这位改变了人类对生物学认知的伟大科学家，晚年却对自己感到极度不满意。</p><p data-pid=\"p21zud_k\">尽管他的进化论理论在27岁时就已确立，50岁时出版了《物种起源》，彻底改变了科学世界。但晚年时，他的创造力停滞不前，生活无法再让他感到幸福。这让他在生命的最后阶段陷入了长期的郁郁寡欢，直到73岁去世。 </p><p data-pid=\"CQKtUQrA\">如果我们很多人从小到大一帆风顺，上名校、进名企，职业发展顺利，创业成功，一直追求卓越、力争上游，那么我们一定会面临相同的境遇。 </p><p data-pid=\"HbFV6f-V\">但是，这本书并不是为了让我们感到绝望，而是提供了一条全新的方向：<b>发展“第二曲线”。</b></p><p data-pid=\"RvdbMyjB\"><b>这个“第二曲线”不再依赖于过去那些流体智力（Fluid Intelligence），即推理、解决问题和创新的能力，而是转向晶体智力（Crystallized Intelligence），即经验和知识的积累。</b></p><p data-pid=\"b4PaPC9C\">举个例子，年轻时我们可能以创新、提出新观点为骄傲，年长时则可以通过积累的经验、智慧，帮助他人更好地理解这些观点。 </p><p data-pid=\"nycb36eD\">正如一位年长的教授，可能不再有当年研究新领域的激情，但他的教学评分往往是最高的，因为他能够清晰而深入地传授知识。 </p><p data-pid=\"OPoB8bAb\">罗马哲学家西塞罗在《论责任》里也写过，<b>认为老年人的天生优势就是提供劝导、指导、劝告、教导他人，不去追求金钱、权力、声望这些回报。</b>他不仅提出了这些建议，还一直知行合一的实践自己的理念。活出了精彩的人生。 </p><p data-pid=\"eC0uKbNm\">比如搞科研、当程序员写代码，都算是在用你的流体智力。当教练、老师、做自媒体内容分享，都是在发挥你的晶体智力。 </p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-1aeaaebe8f417b9e7c6fe4fe05198f67_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"896\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-1aeaaebe8f417b9e7c6fe4fe05198f67_r.jpg\" data-original-token=\"v2-d2682670c469e10143cb6923198fd9cf\"/></figure><p data-pid=\"-RLDzGSx\">| 图片来源于网络 </p><p data-pid=\"VGh3cJeU\">发挥晶体智力的职业，是不受年龄限制的，甚至你会在晚年才达到职业巅峰。<b>所以，如果你还在和别人拼你的流体智力，不妨看看怎么往晶体智力转型，找到人生第二曲线。</b></p><p data-pid=\"QABUW1si\">但是，注意，找到人生第二曲线的目的，不是为了取得别人眼中更大的成就，活得比别人好，拥有的比别人多， </p><p data-pid=\"bZQwpbch\">我们大多数人都习惯了以成就感来衡量自我价值，而成就感来源于和别人的比较。 </p><p data-pid=\"lWKGdsCC\">这种“成功上瘾症”在现代社会非常普遍。追逐金钱、权力、名望，成了衡量人生价值的唯一标准。 </p><p data-pid=\"LSORqDzN\">人们就像在跑步机上不断追赶前方的胡萝卜。然而，无论人们跑多快，这根胡萝卜始终在那里，永远不会带来持久的满足。 </p><p data-pid=\"_N8XHYy8\"><b>真正的中年觉醒，来自于转向内在，开始从智慧、信仰、意义等维度重新定义自己。</b></p><p data-pid=\"rFTIKdTJ\">追求卓越和成功可能是很多人的本性，但是本性不是命运，如果我们想要获得幸福，就必须与自己的本性做斗争。 </p><p data-pid=\"orhDZWkK\">最后，把我特别喜欢的一句话分享给大家。 </p><p data-pid=\"KrT5h5ua\">2018 年作者在印度的时候，他问印度的老师，会给这些四五十岁的工作狂和成功成瘾者什么建议。 </p><p data-pid=\"t7kT5jPR\">那个老师的回答是：<b>“认识你自己，没有其他的了，除此之外没有什么可以给他们自由。”</b></p><p data-pid=\"cQcqJ6uG\">那怎么入手呢？作者问。 </p><p data-pid=\"VUhIWWxr\">老师回答说：<b>“从心入手，心境澄明，智慧自生。”</b></p><p data-pid=\"9fSqJ7sg\">我相信不论你现在是接近 30 岁、还是已经 60 岁，你都会从这本书里收获启发， 所以我一定 要推荐给你。 </p><p data-pid=\"XOI0udoT\">越早看清前方的路，就越有可能拥有人生下半场的幸福。 </p><hr/><p data-pid=\"JhPNkrEQ\">我是凯莉彭，创始人IP商业教练，APEA亚太青年企业家，前硅谷资深数据科学家，很高兴遇见你～<br/>更多内容，欢迎链接kelly71017，备注“知乎”，领取《内容力变现指南》。</p>", "excerpt": "| 撰文：凯莉；版面：Anjana 我花了一天时间读完了这本书，迫不及待地想与你分享。 <b>这本书简直就是30-60岁人群的人生攻略，特别适合那些面临转型、正在寻找更深层次人生意义的人。</b>为什么我选择读这本书？ 最近，我的很多学员都遇到了一个共同问题：他们事业已经非常成功，许多人是公司的高管、老板，或有着令人羡慕的职业背景。 然而，当他们脱下职业标签时，反而不清楚自己是谁，还能怎样创造价值。 <b>而在外人看来，他们很光鲜很…</b>", "excerpt_new": "| 撰文：凯莉；版面：Anjana 我花了一天时间读完了这本书，迫不及待地想与你分享。 <b>这本书简直就是30-60岁人群的人生攻略，特别适合那些面临转型、正在寻找更深层次人生意义的人。</b>为什么我选择读这本书？ 最近，我的很多学员都遇到了一个共同问题：他们事业已经非常成功，许多人是公司的高管、老板，或有着令人羡慕的职业背景。 然而，当他们脱下职业标签时，反而不清楚自己是谁，还能怎样创造价值。 <b>而在外人看来，他们很光鲜很…</b>", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/1835764764", "comment_permission": "all", "voteup_count": 2, "comment_count": 0, "image_url": "https://pic1.zhimg.com/v2-c3a67a198ab9f0642059836992a1b054_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1729238719, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1727428586889", "type": "feed", "target": {"id": "3671346227", "type": "answer", "url": "https://api.zhihu.com/answers/3671346227", "voteup_count": 2, "thanks_count": 0, "question": {"id": "265114039", "title": "真实的硅谷是怎么样的？", "url": "https://api.zhihu.com/questions/265114039", "type": "question", "question_type": "normal", "created": 1515118796, "answer_count": 169, "comment_count": 6, "follower_count": 6374, "detail": "<p>硅谷的传说很多，比如最新的「名利场」所写的堪比「华尔街之狼」的硅谷肾上腺素。</p><a href=\"https://link.zhihu.com/?target=http%3A//tech.sina.com.cn/i/2018-01-04/doc-ifyqkarr7037720.shtml\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic1.zhimg.com/v2-b899244286a225b1d5b92421b56e8a6a_180x120.jpg\" data-image-width=\"400\" data-image-height=\"267\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">美媒揭露硅谷高管荒淫派对文化 充斥毒品酒精</a><p>在那里工作、生活的你，或者曾经在那里的你，感受到的真实的硅谷是怎样的？</p>", "excerpt": "硅谷的传说很多，比如最新的「名利场」所写的堪比「华尔街之狼」的硅谷肾上腺素。<a href=\"https://link.zhihu.com/?target=http%3A//tech.sina.com.cn/i/2018-01-04/doc-ifyqkarr7037720.shtml\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic1.zhimg.com/v2-b899244286a225b1d5b92421b56e8a6a_180x120.jpg\" data-image-width=\"400\" data-image-height=\"267\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">美…</a>", "bound_topic_ids": [54, 99, 520, 2143, 5231], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "3c04c22ecffad98c2535c80e44ff4376", "name": "叶竹", "headline": "否极泰来", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/shu-shu-68-97", "url_token": "shu-shu-68-97", "avatar_url": "https://picx.zhimg.com/v2-0f6a00419a363d9e0610717d48268572_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1727428586, "created_time": 1727428586, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-2951737a8cc7fb415b1735791a21f754_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"900\" data-rawheight=\"383\" data-original-token=\"v2-8ac5152dcd5bf2bd8f45fcdeb9868190\" class=\"origin_image zh-lightbox-thumb\" width=\"900\" data-original=\"https://pic3.zhimg.com/v2-2951737a8cc7fb415b1735791a21f754_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;900&#39; height=&#39;383&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"900\" data-rawheight=\"383\" data-original-token=\"v2-8ac5152dcd5bf2bd8f45fcdeb9868190\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"900\" data-original=\"https://pic3.zhimg.com/v2-2951737a8cc7fb415b1735791a21f754_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-2951737a8cc7fb415b1735791a21f754_b.jpg\"/></figure><p data-pid=\"NvdSBbga\">| 撰文：凯莉；版面：Anjana </p><p data-pid=\"gTQBKfRX\">最近硅谷有一个词特别火，叫做<b>“创始人模式”founder mode。</b></p><p data-pid=\"wW2h-ApW\">提出这个词的人，是我前公司Airbnb的创始人Brian Chesky。 </p><p data-pid=\"ZGywTAGR\">其实他在去年就提出这个词了，之所以最近被热议，是因为Y Combinator孵 化器 的创始人Paul Graham最近发了一篇博客， 主题就是《F ounder M od e》 。 </p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-55e36d335ecf0ef040ae0d5929540af6_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"998\" data-rawheight=\"1016\" data-original-token=\"v2-18c46c45ae722ee2d02f18f24bbb4663\" class=\"origin_image zh-lightbox-thumb\" width=\"998\" data-original=\"https://pic3.zhimg.com/v2-55e36d335ecf0ef040ae0d5929540af6_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;998&#39; height=&#39;1016&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"998\" data-rawheight=\"1016\" data-original-token=\"v2-18c46c45ae722ee2d02f18f24bbb4663\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"998\" data-original=\"https://pic3.zhimg.com/v2-55e36d335ecf0ef040ae0d5929540af6_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-55e36d335ecf0ef040ae0d5929540af6_b.jpg\"/></figure><p data-pid=\"A9pz2YNz\">｜Paul Graham的最新文章 </p><p data-pid=\"1DvBBhg3\">然后这个词就传开了，网上还有各种调侃的图片—— </p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-5d059634bbbf5e3e90938ec022933d35_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"750\" data-rawheight=\"500\" data-original-token=\"v2-b44f5f90298589e0eae58de0500f33ab\" class=\"origin_image zh-lightbox-thumb\" width=\"750\" data-original=\"https://pic4.zhimg.com/v2-5d059634bbbf5e3e90938ec022933d35_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;750&#39; height=&#39;500&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"750\" data-rawheight=\"500\" data-original-token=\"v2-b44f5f90298589e0eae58de0500f33ab\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"750\" data-original=\"https://pic4.zhimg.com/v2-5d059634bbbf5e3e90938ec022933d35_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-5d059634bbbf5e3e90938ec022933d35_b.jpg\"/></figure><p data-pid=\"PgxHsuUV\">｜网络梗图 </p><p data-pid=\"HSpF8ern\">事情是这样的，在前段时间一次YC活动上，Brian Chesky做了一次演讲，给所有人都留下了深刻的印象。 他在演讲中说， <b>人们关于应该如何管理大公司的理论是错的。</b></p><p data-pid=\"oXBsnOgu\">当Airbnb快速发展的时候，很多好心人给他出主意，说要想把公司做大，就得雇佣优秀的人才，然后给他们充分的发挥空间。 </p><p data-pid=\"2-dPEDaA\">于是Airbnb就这么做了，从各大公司雇来很多中高层人员，结果差点把公司搞垮。 </p><p data-pid=\"Wl1MtIaB\">我见证了这一段时间的变化，我当时所在的部门是核心民宿业务，最高领导是从亚马逊来的高管。 </p><p data-pid=\"zZXrME50\">在2019年，公司来了不少外部的高管。在2020年疫情爆发的时候，公司业绩大跳水，陷入生死危机，管理问题也暴露了出来。 </p><p data-pid=\"F5euio8S\">后来就是Brian Chesky让我们部门的高管走了，自己接手了一段时，<b>就像他当年创业那样来管理公司。我们改版网页、发布新功能，都是直接跟他汇报。</b></p><p data-pid=\"1SICpIcu\">后来Brian Chesky自己摸索，研究了乔布斯是怎么管理苹果的，最终找到了另一套方法。 </p><p data-pid=\"AcT3MaQb\">在他看来，这套方法效果不错，至少Airbnb的自由现金流利润率在硅谷名列前茅。这套方法，就被提炼成了“founder mode”。 </p><p data-pid=\"XniI9quI\">不仅是Brian Chesky，在yc活动现场，很多听众是非常成功的创始人，他们纷纷表示自己也有相似的经历。 </p><p data-pid=\"OlhkNRzB\">所以，现在我们可以把公司经营方式分为：<b>创始人模式</b> 和 <b>经理人模式。</b></p><p data-pid=\"XSH2Fywh\">在经理人模式中，组织结构一般有比较多的层级，管理层完全放权给下属，让下属和他们的团队想办法完成任务。<b>管理层和员工很少越级沟通，认为这是对中间层缺乏信任和尊重的表现。</b></p><p data-pid=\"vz1099NK\">人们普遍认为，随着企业的不断扩展，创始人必须切换到经理人模式。也就是创始人退位，找职业经理人接管。 </p><p data-pid=\"H5geYqDb\">但是问题是，很多职业经理人自己并没有创办公司的经验，之所以能够到高管的位置，因为他们都擅长向上管理。按照Paul Graham的话来说，<b>“C-level高管中“有一些世界上最善于伪装的骗子”。</b></p><p data-pid=\"6b-RU_as\">而创始人模式有一点是相当明确的，越级会议应该是常态。比如，<b>Steve Jobs 曾经每年为苹果公司最重要的 100 个人举办一次团建会议，而这些人并非公司组织结构上最高的 100 名高管。</b></p><p data-pid=\"8x-sXDFm\">不仅Steve Jobs是创始人模式的范例，<b>Elon Musk把自己描述为一个 “nano manager”</b> （ 纳米级管理者，也就是比“微观管理者”管的还要细）。 </p><p data-pid=\"z-jzcdaD\">Elon Musk不喜欢授权，他曾经告诉特斯拉员工，<b>他希望亲自批准公司所有新员工的录用。</b></p><p data-pid=\"r3PeSoUF\">他还在给员工的一封电子邮件中说过： </p><p data-pid=\"eG4QIbcg\">“在特斯拉，一切以对公司最有利的、最快解决问题的方式来沟通。你可以不经你经理的许可就与你经理的上级交谈，你可以直接与另一个部门的副总裁交谈，你可以与我交谈，你可以在没有任何人许可的情况下与任何人交谈。” </p><p data-pid=\"GdtyEZWl\"><b>Meta创始人Mark Zuckerberg也不喜欢授权，他说领导者应该 “做出尽可能多的决策，尽可能多地参与各种事情”。</b></p><p data-pid=\"acFDUF-G\">Zuckerberg还裁撤过很多中层管理岗位，让公司更加扁平化，因为他不喜欢 “经理管理经理” 的组织结构。 </p><p data-pid=\"zoV9CLC3\"><b>亚马逊创始人Jeff Bezos是一个知名的“微观管理者”</b> （micro manager） <b>。他对亚马逊零售网站的每一个像素都会管。</b></p><p data-pid=\"A7cNxvab\">Bezos还有一个让人闻风丧胆的习惯，他会收到很多客户投诉邮件，而且会亲自阅读这些邮件。 </p><p data-pid=\"yp46z6bV\">读完以后，他就会把这些邮件转发给相应的高管，内容只有一个问号。 收到邮件的人必须放下手头的一切事情，研究清楚情况，并写出一份精心撰写的回复。 </p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-ec9e493d73b60adafd7a3dbf1aded656_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"458\" data-rawheight=\"212\" data-original-token=\"v2-e930fb17b62e5acfee18fc81c0dacca5\" class=\"origin_image zh-lightbox-thumb\" width=\"458\" data-original=\"https://pic3.zhimg.com/v2-ec9e493d73b60adafd7a3dbf1aded656_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;458&#39; height=&#39;212&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"458\" data-rawheight=\"212\" data-original-token=\"v2-e930fb17b62e5acfee18fc81c0dacca5\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"458\" data-original=\"https://pic3.zhimg.com/v2-ec9e493d73b60adafd7a3dbf1aded656_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-ec9e493d73b60adafd7a3dbf1aded656_b.jpg\"/></figure><p data-pid=\"9hAbflpJ\">｜Jeff Bezos的标志性邮件——“？” </p><p data-pid=\"9pXZJG72\"><b>英伟达创始人黄仁勋</b> 也是创始人模式的典范，他认为首席执行官应该比任何人都有更多的直接下属，事实也确实如此——<b>跟他直接汇报的人，多达60人。</b></p><p data-pid=\"Be6XyxbO\">当然，<b>传统的经理人模式并不乏成功案例。大多数大公司，目前都是经理人模式。</b></p><p data-pid=\"sof2HGk1\">最近的一个案例，是星巴克给出1.13亿美金的大包裹，从美国排名前列的快餐连锁店Chipotle挖走了他们的明星CEO Brian Niccol。 </p><p data-pid=\"3J6Nxy5w\">Brian Niccol一直都在一线摸爬滚打，一路从基层干到了CEO的位置。在Chipotle担任CEO的几年时间里，帮助Chipotle度过危机，股价提升了770%。 </p><p data-pid=\"JxvoXO-l\">我认为，Brian Chesky提出“创始人模式”的价值是说明经理人模式不一定更优，但是带来的问题是没有针对创始人模式给出清晰的定义。所以就出现很多争论，人们认为创始人模式等同于创始人什么都要亲力亲为。 </p><p data-pid=\"m_eef6YX\">就像国家有独裁和民主，公司有创始人模式和经理人模式，这是天平的两端，在一端呆久了，就会看到这一端的问题，然后往另一端倾斜。<b>而人们最终会在天平的两端之间，找到一个动态平衡点。</b></p><p data-pid=\"1sYTWJKV\">在未来，创始人模式会越来越常见，因为随着AI的发展，企业不再需要那么多员工，也就不再需要设置很多层级。 </p><p data-pid=\"53Y2mBKn\">但是经理人模式也不会消失，因为创始人会老、会离开，需要让企业在脱离创始人以后也能良好运转。 </p><p data-pid=\"E4ArlEF8\">到底founder mode意味着什么，以及怎样把这个模式用好，期待看到Brian Chesky或者Paul Graham未来再仔细讲讲，指明企业管理的另一种选择吧。 </p><hr/><p data-pid=\"5BflAkqB\">我是凯莉彭，创始人IP商业教练，APEA亚太青年企业家，前硅谷资深数据科学家，很高兴遇见你～<br/>更多内容，欢迎链接kelly71017，备注“知乎”，领取《内容力变现指南》。</p>", "excerpt": "| 撰文：凯莉；版面：Anjana 最近硅谷有一个词特别火，叫做 <b>“创始人模式”founder mode。</b>提出这个词的人，是我前公司Airbnb的创始人Brian Chesky。 其实他在去年就提出这个词了，之所以最近被热议，是因为Y Combinator孵 化器 的创始人Paul Graham最近发了一篇博客， 主题就是《F ounder M od e》 。 ｜Paul Graham的最新文章 然后这个词就传开了，网上还有各种调侃的图片—— ｜网络梗图 事情是这样的，在前段时间一次YC活动上…", "excerpt_new": "| 撰文：凯莉；版面：Anjana 最近硅谷有一个词特别火，叫做 <b>“创始人模式”founder mode。</b>提出这个词的人，是我前公司Airbnb的创始人Brian Chesky。 其实他在去年就提出这个词了，之所以最近被热议，是因为Y Combinator孵 化器 的创始人Paul Graham最近发了一篇博客， 主题就是《F ounder M od e》 。 ｜Paul Graham的最新文章 然后这个词就传开了，网上还有各种调侃的图片—— ｜网络梗图 事情是这样的，在前段时间一次YC活动上…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1727428586, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1727428586889&page_num=5"}}