{"data": [{"id": "1515747446238", "type": "feed", "target": {"id": "32853683", "type": "article", "author": {"id": "5de1432f98d5a5d4209413803bed1458", "name": "池建强", "headline": "但行好事，墨问西东", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/sagacity", "url_token": "sagacity", "avatar_url": "https://pic1.zhimg.com/12abec9c0_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "created": 1515667350, "updated": 1515667382, "title": "你对推荐算法的认知，也许都是错的", "excerpt_title": "", "content": "<p data-pid=\"7JUADuht\">前几天写过一篇「<a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMjM5ODQ2MDIyMA%3D%3D%26mid%3D2650713845%26idx%3D1%26sn%3Da977810e1e076724fde295d7c5257b46%26chksm%3Dbec060a689b7e9b046df8c77775e10ef0510a61c8285baf396ee2476fc79edd6292d53d932fd%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">哪些职业容易被机器算法取代</a>」，很多人不以为然：我天天上网，怎么没感到机器算法呢？真那么智能，注册个账户又是密码又是安全问题两步验证，怎么不搞智能一点呢？</p><p data-pid=\"Qszw8LwD\">现阶段的机器算法，并不是指具备高等智能的机器人，也不是有人类情感的仿生人，不过算法确实在我们的生活中发挥着各种各样的作用。比如你打开浏览器在网上闲逛的时候，你会发现某个网站的某个广告会出现一个你心仪品牌的羽毛球鞋的图片，点进去之后你发现可以直接购买这款鞋子，出现这样的推荐是因为你前几天在这个网站上买了一只同品牌的羽毛球拍。为了学习人工智能，你买了一本《深度学习》，在付款的时候，你会发现页面下方会冒出了几本《机器学习实战》《Python 机器学习》的书，你忍不住又买了一本……</p><p data-pid=\"qZE_lHah\">这就是算法的力量，确切的说，是推荐算法在起作用。</p><p data-pid=\"YnkHz637\">随着信息技术和互联网的发展，人们逐渐从信息匮乏的盲区走入了信息过载（information overload）的时代。以推荐算法为核心技术的推荐系统凭借其个性化推荐和有效降低信息噪音的特点开始被广泛使用，比如国外的 Google、Facebook 和国内的今日头条。</p><p data-pid=\"05pxg1NH\">不过，就像谈到程序员和工程师就会想到修电脑的一样，很多人，尤其是非 IT 领域从业者，对算法的理解游走在「算数」与「魔法」两个边缘，有很大的认知误区。下面我主要以内容推荐领域的今日头条和商品推荐领域的亚马逊为例，跟大家聊聊推荐算法，帮助读者更好的理解这个时代的互联网生活。</p><h2><b>误区一：推荐算法是根据用户点击率来推荐</b></h2><p data-pid=\"xMMPmqLh\">这可能算是对算法最大的误解之一了。</p><p data-pid=\"OonvOdhD\">我们经常说，推荐算法实现了个性化推荐效果，每个人看到的东西都是不一样的。这个说法忽略了一个重要的事实：大多数人喜欢的东西实际上高度类似，比如最火的流行歌曲、最新的明星八卦。</p><p data-pid=\"fYFTEQa7\">多年前今日头条出现，喊出了你感兴趣的才是头条。门户网站之所以觉得很平常没有跟进，也是陷入了算法等于点击的陷阱 —— 按照热度排新闻，是各大门户网站早就有的功能，有什么新鲜的呢？</p><p data-pid=\"1O1eiBQv\">真正能挖掘长尾的个性化推荐，其实是反点击的，否则很难实现个性化的需求挖掘。系统需要跟进更多的用户信息维度和多种算法模型来发现和挖掘长尾需求。《长尾理论》曾经举过一个著名的例子。1988年，乔·辛普森写了一本登山类的书籍《触及巅峰》，但销量一直很普通。10年后，另一本讲述登山灾难的书《进入稀薄空气》引起了美国出版业的轰动。亚马逊发现有读者在评价《进入稀薄空气》时提到了《触及巅峰》，同时给出了高评价，于是将《触及巅峰》推荐给了《进入稀薄空气》的深度读者。很快，《触及巅峰》在经过十年的惨淡销量后，获得了巨大的成功。</p><p data-pid=\"f-CKnD0x\">实际上，亚马逊做的事情就是算法推荐现在做的事。推荐过程不仅要考虑用户的阅读轨迹，同时还要考虑用户的性别，年龄，甚至手机机型等信息，同时还要综合考虑新闻的时效性、以及地理位置等信息对内容进行相应推荐。而如果只看点击（销量），《触及巅峰》可能永远也不会获得推荐。</p><h2><b>误区二：冰箱都买完了还推荐冰箱，点了不喜欢还推荐，算法一点都不聪明</b></h2><p data-pid=\"AQxVtvsq\">假如你的微信只有一个好友联系人，会觉得朋友圈好玩吗？</p><p data-pid=\"27-8F2do\">朋友圈需要更多的好友，算法推荐也需要更多的数据。对新用户来说，一个系统或者平台可以推荐的内容是天文数字。以淘宝为例，2013 年的时候，淘宝在线商品数就超过了 8 亿，8 亿个候选，推哪一个？</p><p data-pid=\"lWfyqut3\">这时候，点击或者浏览过的商品/文章，显然权重是最高的。对直接销售物品的电商来说更是如此，所以无论是国外的亚马逊还是国内的淘宝、京东，实践下来，当前浏览内容都是最重要的推荐因素。</p><p data-pid=\"i6JT19bv\">而且，买过冰箱推荐冰箱，也未必是算法笨，这可能只是一个简单的策略问题 —— 你买了冰箱，周围的朋友可能会咨询你冰箱的问题；如果你看到了更喜欢的新款冰箱，很可能在退货时间内选择了退了原来商家的冰箱，买个新冰箱。并且这个策略很可能造成最后的销售数据的极大提升。</p><p data-pid=\"Uv3FDUA9\">对相关新闻点击「不敢兴趣」也类似。当你第一次对奥巴马演讲点击「不感兴趣」时，系统不知道你是对奥巴马不感兴趣还是对演讲不感兴趣，或者单纯不喜欢这次的演讲主题，所以反而会继续给你推荐相关的话题，从整体数据来看，这样的推荐策略有时候是更优的。</p><p data-pid=\"1q33s0B-\">当然，个性化推荐为了防止过渡拟合出现，会根据读者的阅读纪录通过严谨的数学理论分析计算，推测出同类用户偏好，依兴趣标签的关联程度，推测出同类用户其他偏好，并进行「联想式」的推荐。比如当机器发现阅读「总统大选」相关信息的用户群体中，有很大部分人都在同时关注「股票」信息，那么机器就会把「股票」信息推荐给那部分关注「总统大选」但尚未关注「股票」信息的人，而不会单一推荐「总统大选」的信息。</p><h2><b>误区三：推荐算法会导致「信息茧房」</b></h2><p data-pid=\"FUFtqkGh\">有一种论调是，由于算法只给你推送你喜欢的内容，从而造成了信息茧房。</p><p data-pid=\"UUzTv6OP\">展开来说，这个论调包括两层，一是大家只关心自己的小世界，看不到更重要、更有意义的公共事件。二是算法越来越懂你，你喜欢特朗普，就只给你推荐特朗普好的新闻。最终的结果，造成了「信息茧房」和偏食。</p><p data-pid=\"dyVUnZd8\">这其实是不成立的。在实际情况中，算法很难实现「信息茧房」。公共事件之所以成为公共事业，是因为其公共性，这决定了其天然具有穿透性，所有算法都会对此类事件赋予极高的权重，否则这将违反算法准确性的初衷。</p><p data-pid=\"cOJBsVI8\">其次，关于态度倾向。因为每个人可能感兴趣的文章非常多，用专业话就是数据非常稀疏，所以对算法来说，正向情绪和负向情绪，都是对某一个话题的正相关，这种相关性本身大于情绪。这句话翻译过来就是，无论你讨厌特朗普还是喜欢特朗普，在数据意义上的表现，都是对特朗普这个话题高度相关的。对于算法来说，正常情况下，所有关于特朗普的重要内容，都会被优先推荐给你。</p><p data-pid=\"18q7A0RT\">从哲学思辨的角度来看，「信息茧房」或许有其意义，但从实际操作中，不可能出现这样的极端情况。另外，互联网时代，由于信息的极大丰富，任何选择都会对信息本身进行过滤和筛选。你的微博、朋友圈也是「信息茧房」—— 因为你看到的都是朋友们关心的。</p><h2><b>误区四：推荐算法技术含量不高 按照算法模型拿 Cookie 信息套一下就行</b></h2><p data-pid=\"8CSoZ4AM\">首先，严格来说，算法是解决问题的一个过程，包括特定输入与特定输出。我们讲的数学公式只是算法的理论基础，无论是推荐算法还是深度学习网络不仅仅需要理论基础，也就是公式，还要有相应的数学模型实现，并且这个实现过程是动态的，需要不断调整的。</p><p data-pid=\"K18qpbYg\">实际上算法的自我修正和学习是非常重要的，比如阿尔法狗就是不断的和人类对弈来优化自身模型来提高算法准确性。推荐算法也不例外，个性化推荐会随着用户的阅读轨迹、用户的行为记录进行反馈优化，逐步提高其准确性。公开资料显示，今日头条每个星期都会对算法模型进行一些优化和调整，近一年内今日头条的算法进行了 4 次比较大的模型迭代。亚马逊在过去二十年间也对推荐系统进行了无数次改进和优化，才有今天非常精准的推荐结果。</p><p data-pid=\"njDltgpb\">PC 时代的推荐非常原始，无非是拿浏览器里的 Cookie 数据进行关键词匹配。很多人会觉得，现在的算法不也这样么，无非是多了一些用户年龄属性，性别属性，偏好属性，然后套入公式，性别＊0.3 + 年龄＊0.5 + 偏好＊0.2，再加上一些地理位置等属性，就可以进行推荐了。</p><p data-pid=\"L0lApoIC\">实际上，这大概是二十年前推荐 1.0 时代的做法。如今推荐系统建立、使用和优化是一个非常复杂的过程。比如推荐系统的建立方式就包括基于用户、基于关联规则和基于模型的推荐。现在做的好的推荐系统都不会只采用某一种推荐的机制和策略，往往是结合多种推荐方法，以达到更好的推荐效果。</p><h2><b>误区五：推荐算法发展的很快，未来可以洞察人性，无所不能</b></h2><p data-pid=\"4kOSHvNP\">推荐算法的出现提高了信息分发效率，很好的解决了信息过载的问题。尽管个性化推荐需要用到一定的用户特征，但都是以公开特征和定向内容为主，很难全面的刻画出一个人，了解人性更是谈何容易。真正做到了解人性，就需要算法比你还了解你自己，以现在的科技水平，算法想要达到科幻小说里的洞悉人性是不可能的。</p><p data-pid=\"9eRbsRZJ\">更重要的是，任何算法都会有反例。简单说，如果一个分类算法单纯按照头发长短区分男女，有些男生头发比较长就会出现分类错误。作为新技术，机器推荐还有不完美的地方，仍然需优化和改进，这也是众多科学家努力的方向。当然，从比例上看优秀的算法肯定对绝大多数的案例进行正确分类，并有效的推荐给用户。</p><h2><b>误区六：算法都是公开的，竞争壁垒不高</b></h2><p data-pid=\"YuOHeZrQ\">首先，数据是非常重要的壁垒。真正应用到工业的推荐系统需要大量数据进行建模计算的。并非简单的少量的数据即可，一般情况下需要上亿的数据和上亿的属性特征进行推荐，没有数据只有理论基础都是纸上谈兵。</p><p data-pid=\"AJHVzv7f\">因此，如果想要做出一套好的推荐系统模型，需要在大数据的基础上建立非常庞大和成熟的工程师团队。Google、微软聘用了大量的高端人才进行推荐算法优化，无非是针对一些特定的知识点做专门的 Feature Engineering，国内的今日头条也有近半数的员工都是技术工程师。</p><p data-pid=\"66lEk6KT\">一些算法可能会在推荐算法的相关比赛中取得非常好的结果，但并不是说这就是一个最优的算法模型。很可能是机器把样本数据的所有特征都学习到了，获得了过多的局部特征和假特征，形成过拟合。当你用它识别新的数据样本的时就会发现，推荐准确率有可能非常低。</p><p data-pid=\"n5fnO-qb\">算法模型必须经过大量数据的学习和演化，没有任何一种机器模型可以被当做权威规则来使用。算法的学习和演化本身也是一种壁垒。换句话说，哪怕张一鸣自己离开今日头条，重新做一套推荐算法，也无法达到现在今日头条推荐算法的水平。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"de7hbxXw\"><b>卖桃者说：</b></p><p data-pid=\"CKCfa4v2\"><b>如果你是一位工程师，如果你读到了这里，还会觉得数据、算法和数学不重要嘛？不说了，我去学习算法去了。</b></p>", "excerpt": "前几天写过一篇「 <a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMjM5ODQ2MDIyMA%3D%3D%26mid%3D2650713845%26idx%3D1%26sn%3Da977810e1e076724fde295d7c5257b46%26chksm%3Dbec060a689b7e9b046df8c77775e10ef0510a61c8285baf396ee2476fc79edd6292d53d932fd%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">哪些职业容易被机器算法取代</a>」，很多人不以为然：我天天上网，怎么没感到机器算法呢？真那么智能，注册个账户又是密码又是安全问题两步验证，怎么不搞智能一点呢？现阶段的机器算法，并不是指具备高等智能的机器人，也不是有人类情感的仿生人，不过算法确实在我们的生活中发挥着各种各样的作用。比如你打开浏览器在网上闲逛的时候，你会发现某个网站的某个广告会出现一个你心仪品牌的羽毛球鞋的图片，点进去之…", "excerpt_new": "前几天写过一篇「 <a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMjM5ODQ2MDIyMA%3D%3D%26mid%3D2650713845%26idx%3D1%26sn%3Da977810e1e076724fde295d7c5257b46%26chksm%3Dbec060a689b7e9b046df8c77775e10ef0510a61c8285baf396ee2476fc79edd6292d53d932fd%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">哪些职业容易被机器算法取代</a>」，很多人不以为然：我天天上网，怎么没感到机器算法呢？真那么智能，注册个账户又是密码又是安全问题两步验证，怎么不搞智能一点呢？现阶段的机器算法，并不是指具备高等智能的机器人，也不是有人类情感的仿生人，不过算法确实在我们的生活中发挥着各种各样的作用。比如你打开浏览器在网上闲逛的时候，你会发现某个网站的某个广告会出现一个你心仪品牌的羽毛球鞋的图片，点进去之…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/32853683", "comment_permission": "all", "voteup_count": 240, "comment_count": 18, "image_url": "https://picx.zhimg.com/v2-99f2bd03cec306b358b39291b7ace0ac_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_COLLECT_ARTICLE", "created_time": 1515747446, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "收藏了文章", "is_sticky": false}, {"id": "1515747396325", "type": "feed", "target": {"id": "32853683", "type": "article", "author": {"id": "5de1432f98d5a5d4209413803bed1458", "name": "池建强", "headline": "但行好事，墨问西东", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/sagacity", "url_token": "sagacity", "avatar_url": "https://picx.zhimg.com/12abec9c0_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "created": 1515667350, "updated": 1515667382, "title": "你对推荐算法的认知，也许都是错的", "excerpt_title": "", "content": "<p data-pid=\"7JUADuht\">前几天写过一篇「<a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMjM5ODQ2MDIyMA%3D%3D%26mid%3D2650713845%26idx%3D1%26sn%3Da977810e1e076724fde295d7c5257b46%26chksm%3Dbec060a689b7e9b046df8c77775e10ef0510a61c8285baf396ee2476fc79edd6292d53d932fd%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">哪些职业容易被机器算法取代</a>」，很多人不以为然：我天天上网，怎么没感到机器算法呢？真那么智能，注册个账户又是密码又是安全问题两步验证，怎么不搞智能一点呢？</p><p data-pid=\"Qszw8LwD\">现阶段的机器算法，并不是指具备高等智能的机器人，也不是有人类情感的仿生人，不过算法确实在我们的生活中发挥着各种各样的作用。比如你打开浏览器在网上闲逛的时候，你会发现某个网站的某个广告会出现一个你心仪品牌的羽毛球鞋的图片，点进去之后你发现可以直接购买这款鞋子，出现这样的推荐是因为你前几天在这个网站上买了一只同品牌的羽毛球拍。为了学习人工智能，你买了一本《深度学习》，在付款的时候，你会发现页面下方会冒出了几本《机器学习实战》《Python 机器学习》的书，你忍不住又买了一本……</p><p data-pid=\"qZE_lHah\">这就是算法的力量，确切的说，是推荐算法在起作用。</p><p data-pid=\"YnkHz637\">随着信息技术和互联网的发展，人们逐渐从信息匮乏的盲区走入了信息过载（information overload）的时代。以推荐算法为核心技术的推荐系统凭借其个性化推荐和有效降低信息噪音的特点开始被广泛使用，比如国外的 Google、Facebook 和国内的今日头条。</p><p data-pid=\"05pxg1NH\">不过，就像谈到程序员和工程师就会想到修电脑的一样，很多人，尤其是非 IT 领域从业者，对算法的理解游走在「算数」与「魔法」两个边缘，有很大的认知误区。下面我主要以内容推荐领域的今日头条和商品推荐领域的亚马逊为例，跟大家聊聊推荐算法，帮助读者更好的理解这个时代的互联网生活。</p><h2><b>误区一：推荐算法是根据用户点击率来推荐</b></h2><p data-pid=\"xMMPmqLh\">这可能算是对算法最大的误解之一了。</p><p data-pid=\"OonvOdhD\">我们经常说，推荐算法实现了个性化推荐效果，每个人看到的东西都是不一样的。这个说法忽略了一个重要的事实：大多数人喜欢的东西实际上高度类似，比如最火的流行歌曲、最新的明星八卦。</p><p data-pid=\"fYFTEQa7\">多年前今日头条出现，喊出了你感兴趣的才是头条。门户网站之所以觉得很平常没有跟进，也是陷入了算法等于点击的陷阱 —— 按照热度排新闻，是各大门户网站早就有的功能，有什么新鲜的呢？</p><p data-pid=\"1O1eiBQv\">真正能挖掘长尾的个性化推荐，其实是反点击的，否则很难实现个性化的需求挖掘。系统需要跟进更多的用户信息维度和多种算法模型来发现和挖掘长尾需求。《长尾理论》曾经举过一个著名的例子。1988年，乔·辛普森写了一本登山类的书籍《触及巅峰》，但销量一直很普通。10年后，另一本讲述登山灾难的书《进入稀薄空气》引起了美国出版业的轰动。亚马逊发现有读者在评价《进入稀薄空气》时提到了《触及巅峰》，同时给出了高评价，于是将《触及巅峰》推荐给了《进入稀薄空气》的深度读者。很快，《触及巅峰》在经过十年的惨淡销量后，获得了巨大的成功。</p><p data-pid=\"f-CKnD0x\">实际上，亚马逊做的事情就是算法推荐现在做的事。推荐过程不仅要考虑用户的阅读轨迹，同时还要考虑用户的性别，年龄，甚至手机机型等信息，同时还要综合考虑新闻的时效性、以及地理位置等信息对内容进行相应推荐。而如果只看点击（销量），《触及巅峰》可能永远也不会获得推荐。</p><h2><b>误区二：冰箱都买完了还推荐冰箱，点了不喜欢还推荐，算法一点都不聪明</b></h2><p data-pid=\"AQxVtvsq\">假如你的微信只有一个好友联系人，会觉得朋友圈好玩吗？</p><p data-pid=\"27-8F2do\">朋友圈需要更多的好友，算法推荐也需要更多的数据。对新用户来说，一个系统或者平台可以推荐的内容是天文数字。以淘宝为例，2013 年的时候，淘宝在线商品数就超过了 8 亿，8 亿个候选，推哪一个？</p><p data-pid=\"lWfyqut3\">这时候，点击或者浏览过的商品/文章，显然权重是最高的。对直接销售物品的电商来说更是如此，所以无论是国外的亚马逊还是国内的淘宝、京东，实践下来，当前浏览内容都是最重要的推荐因素。</p><p data-pid=\"i6JT19bv\">而且，买过冰箱推荐冰箱，也未必是算法笨，这可能只是一个简单的策略问题 —— 你买了冰箱，周围的朋友可能会咨询你冰箱的问题；如果你看到了更喜欢的新款冰箱，很可能在退货时间内选择了退了原来商家的冰箱，买个新冰箱。并且这个策略很可能造成最后的销售数据的极大提升。</p><p data-pid=\"Uv3FDUA9\">对相关新闻点击「不敢兴趣」也类似。当你第一次对奥巴马演讲点击「不感兴趣」时，系统不知道你是对奥巴马不感兴趣还是对演讲不感兴趣，或者单纯不喜欢这次的演讲主题，所以反而会继续给你推荐相关的话题，从整体数据来看，这样的推荐策略有时候是更优的。</p><p data-pid=\"1q33s0B-\">当然，个性化推荐为了防止过渡拟合出现，会根据读者的阅读纪录通过严谨的数学理论分析计算，推测出同类用户偏好，依兴趣标签的关联程度，推测出同类用户其他偏好，并进行「联想式」的推荐。比如当机器发现阅读「总统大选」相关信息的用户群体中，有很大部分人都在同时关注「股票」信息，那么机器就会把「股票」信息推荐给那部分关注「总统大选」但尚未关注「股票」信息的人，而不会单一推荐「总统大选」的信息。</p><h2><b>误区三：推荐算法会导致「信息茧房」</b></h2><p data-pid=\"FUFtqkGh\">有一种论调是，由于算法只给你推送你喜欢的内容，从而造成了信息茧房。</p><p data-pid=\"UUzTv6OP\">展开来说，这个论调包括两层，一是大家只关心自己的小世界，看不到更重要、更有意义的公共事件。二是算法越来越懂你，你喜欢特朗普，就只给你推荐特朗普好的新闻。最终的结果，造成了「信息茧房」和偏食。</p><p data-pid=\"dyVUnZd8\">这其实是不成立的。在实际情况中，算法很难实现「信息茧房」。公共事件之所以成为公共事业，是因为其公共性，这决定了其天然具有穿透性，所有算法都会对此类事件赋予极高的权重，否则这将违反算法准确性的初衷。</p><p data-pid=\"cOJBsVI8\">其次，关于态度倾向。因为每个人可能感兴趣的文章非常多，用专业话就是数据非常稀疏，所以对算法来说，正向情绪和负向情绪，都是对某一个话题的正相关，这种相关性本身大于情绪。这句话翻译过来就是，无论你讨厌特朗普还是喜欢特朗普，在数据意义上的表现，都是对特朗普这个话题高度相关的。对于算法来说，正常情况下，所有关于特朗普的重要内容，都会被优先推荐给你。</p><p data-pid=\"18q7A0RT\">从哲学思辨的角度来看，「信息茧房」或许有其意义，但从实际操作中，不可能出现这样的极端情况。另外，互联网时代，由于信息的极大丰富，任何选择都会对信息本身进行过滤和筛选。你的微博、朋友圈也是「信息茧房」—— 因为你看到的都是朋友们关心的。</p><h2><b>误区四：推荐算法技术含量不高 按照算法模型拿 Cookie 信息套一下就行</b></h2><p data-pid=\"8CSoZ4AM\">首先，严格来说，算法是解决问题的一个过程，包括特定输入与特定输出。我们讲的数学公式只是算法的理论基础，无论是推荐算法还是深度学习网络不仅仅需要理论基础，也就是公式，还要有相应的数学模型实现，并且这个实现过程是动态的，需要不断调整的。</p><p data-pid=\"K18qpbYg\">实际上算法的自我修正和学习是非常重要的，比如阿尔法狗就是不断的和人类对弈来优化自身模型来提高算法准确性。推荐算法也不例外，个性化推荐会随着用户的阅读轨迹、用户的行为记录进行反馈优化，逐步提高其准确性。公开资料显示，今日头条每个星期都会对算法模型进行一些优化和调整，近一年内今日头条的算法进行了 4 次比较大的模型迭代。亚马逊在过去二十年间也对推荐系统进行了无数次改进和优化，才有今天非常精准的推荐结果。</p><p data-pid=\"njDltgpb\">PC 时代的推荐非常原始，无非是拿浏览器里的 Cookie 数据进行关键词匹配。很多人会觉得，现在的算法不也这样么，无非是多了一些用户年龄属性，性别属性，偏好属性，然后套入公式，性别＊0.3 + 年龄＊0.5 + 偏好＊0.2，再加上一些地理位置等属性，就可以进行推荐了。</p><p data-pid=\"L0lApoIC\">实际上，这大概是二十年前推荐 1.0 时代的做法。如今推荐系统建立、使用和优化是一个非常复杂的过程。比如推荐系统的建立方式就包括基于用户、基于关联规则和基于模型的推荐。现在做的好的推荐系统都不会只采用某一种推荐的机制和策略，往往是结合多种推荐方法，以达到更好的推荐效果。</p><h2><b>误区五：推荐算法发展的很快，未来可以洞察人性，无所不能</b></h2><p data-pid=\"4kOSHvNP\">推荐算法的出现提高了信息分发效率，很好的解决了信息过载的问题。尽管个性化推荐需要用到一定的用户特征，但都是以公开特征和定向内容为主，很难全面的刻画出一个人，了解人性更是谈何容易。真正做到了解人性，就需要算法比你还了解你自己，以现在的科技水平，算法想要达到科幻小说里的洞悉人性是不可能的。</p><p data-pid=\"9eRbsRZJ\">更重要的是，任何算法都会有反例。简单说，如果一个分类算法单纯按照头发长短区分男女，有些男生头发比较长就会出现分类错误。作为新技术，机器推荐还有不完美的地方，仍然需优化和改进，这也是众多科学家努力的方向。当然，从比例上看优秀的算法肯定对绝大多数的案例进行正确分类，并有效的推荐给用户。</p><h2><b>误区六：算法都是公开的，竞争壁垒不高</b></h2><p data-pid=\"YuOHeZrQ\">首先，数据是非常重要的壁垒。真正应用到工业的推荐系统需要大量数据进行建模计算的。并非简单的少量的数据即可，一般情况下需要上亿的数据和上亿的属性特征进行推荐，没有数据只有理论基础都是纸上谈兵。</p><p data-pid=\"AJHVzv7f\">因此，如果想要做出一套好的推荐系统模型，需要在大数据的基础上建立非常庞大和成熟的工程师团队。Google、微软聘用了大量的高端人才进行推荐算法优化，无非是针对一些特定的知识点做专门的 Feature Engineering，国内的今日头条也有近半数的员工都是技术工程师。</p><p data-pid=\"66lEk6KT\">一些算法可能会在推荐算法的相关比赛中取得非常好的结果，但并不是说这就是一个最优的算法模型。很可能是机器把样本数据的所有特征都学习到了，获得了过多的局部特征和假特征，形成过拟合。当你用它识别新的数据样本的时就会发现，推荐准确率有可能非常低。</p><p data-pid=\"n5fnO-qb\">算法模型必须经过大量数据的学习和演化，没有任何一种机器模型可以被当做权威规则来使用。算法的学习和演化本身也是一种壁垒。换句话说，哪怕张一鸣自己离开今日头条，重新做一套推荐算法，也无法达到现在今日头条推荐算法的水平。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"de7hbxXw\"><b>卖桃者说：</b></p><p data-pid=\"CKCfa4v2\"><b>如果你是一位工程师，如果你读到了这里，还会觉得数据、算法和数学不重要嘛？不说了，我去学习算法去了。</b></p>", "excerpt": "前几天写过一篇「 <a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMjM5ODQ2MDIyMA%3D%3D%26mid%3D2650713845%26idx%3D1%26sn%3Da977810e1e076724fde295d7c5257b46%26chksm%3Dbec060a689b7e9b046df8c77775e10ef0510a61c8285baf396ee2476fc79edd6292d53d932fd%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">哪些职业容易被机器算法取代</a>」，很多人不以为然：我天天上网，怎么没感到机器算法呢？真那么智能，注册个账户又是密码又是安全问题两步验证，怎么不搞智能一点呢？现阶段的机器算法，并不是指具备高等智能的机器人，也不是有人类情感的仿生人，不过算法确实在我们的生活中发挥着各种各样的作用。比如你打开浏览器在网上闲逛的时候，你会发现某个网站的某个广告会出现一个你心仪品牌的羽毛球鞋的图片，点进去之…", "excerpt_new": "前几天写过一篇「 <a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMjM5ODQ2MDIyMA%3D%3D%26mid%3D2650713845%26idx%3D1%26sn%3Da977810e1e076724fde295d7c5257b46%26chksm%3Dbec060a689b7e9b046df8c77775e10ef0510a61c8285baf396ee2476fc79edd6292d53d932fd%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">哪些职业容易被机器算法取代</a>」，很多人不以为然：我天天上网，怎么没感到机器算法呢？真那么智能，注册个账户又是密码又是安全问题两步验证，怎么不搞智能一点呢？现阶段的机器算法，并不是指具备高等智能的机器人，也不是有人类情感的仿生人，不过算法确实在我们的生活中发挥着各种各样的作用。比如你打开浏览器在网上闲逛的时候，你会发现某个网站的某个广告会出现一个你心仪品牌的羽毛球鞋的图片，点进去之…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/32853683", "comment_permission": "all", "voteup_count": 240, "comment_count": 18, "image_url": "https://picx.zhimg.com/v2-99f2bd03cec306b358b39291b7ace0ac_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1515747396, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1515747085767", "type": "feed", "target": {"id": "265464354", "title": "今日头条首次公布算法原理，谁知道具体的算法是怎样的？", "url": "https://api.zhihu.com/questions/265464354", "type": "question", "question_type": "normal", "created": 1515721177, "answer_count": 4, "comment_count": 0, "follower_count": 73, "detail": "<p>近日头条刚公布了算法原理，我在网页上找到的都是新闻，而没有具体的算法原理，请问具体的算法原理是什么？</p>", "excerpt": "近日头条刚公布了算法原理，我在网页上找到的都是新闻，而没有具体的算法原理，请问…", "bound_topic_ids": [102177], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "8cb1aec8469ae63dffd430720729fdad", "name": "国服第一网名", "headline": "唐吉坷德大学 在读博士", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/tony-17-80", "url_token": "tony-17-80", "avatar_url": "https://picx.zhimg.com/3a23bc4fd_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1515747085, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1515566832462", "type": "feed", "target": {"id": "143145081", "type": "answer", "url": "https://api.zhihu.com/answers/143145081", "voteup_count": 73, "thanks_count": 7, "question": {"id": "40745462", "title": "\"爱可可-爱生活\"的微博内容是怎么产生的？", "url": "https://api.zhihu.com/questions/40745462", "type": "question", "question_type": "normal", "created": 1456385397, "answer_count": 7, "comment_count": 2, "follower_count": 161, "detail": "<p>每天分享这么多论文，技术贴，科技新闻等内容</p><p>博主陈光老师一个人做这事时间不够吧，是一个团队在维护吗？</p><p>不知道陈老师一天的时间是怎么安排的，除了科研，教务，带学生，写基金等工作，怎么做到每天发布这么多内容？</p><p>是不是用工具自动追踪arXiv、大牛博客、科技网站、twitter、G+，外加自动谷歌搜索一些关键词，然后把这些内容提取标题、图片发布到微博上？</p><p class=\"ztext-empty-paragraph\"><br/></p><p>---- 17.12.30 ----</p><p class=\"ztext-empty-paragraph\"><br/></p><p><a href=\"https://www.zhihu.com/people/qing-hua-da-xue-shu-ju-ke-xue-yan-jiu-yuan\" class=\"internal\">清华大学数据科学研究院</a>一篇分析：</p><ul><li><a href=\"https://zhuanlan.zhihu.com/p/32263213\" class=\"internal\">独家 | 数据分析@爱可可-爱生活是否在用机器学习算法运营微博</a></li></ul><p></p>", "excerpt": "每天分享这么多论文，技术贴，科技新闻等内容博主陈光老师一个人做这事时间不够吧，…", "bound_topic_ids": [3084, 13303, 89794], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "b46b480516492fe4125c73d218733b5f", "name": "qu<PERSON><PERSON>", "headline": "坐井观天", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/_nop", "url_token": "_nop", "avatar_url": "https://picx.zhimg.com/v2-067eb7e89acf9a5e24f5974617993bf8_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1485617340, "created_time": 1485581014, "author": {"id": "991331b4e2ef65f548450170a16ec4e8", "name": "无名小卒", "headline": "码农 | Kaggle Triple Master", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/jianjianshu", "url_token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar_url": "https://picx.zhimg.com/v2-c6854311dff3cf83ecd79978213e6a6b_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"H7YmxsSN\">专访 | 爱可可-爱生活：我是如何做到每天5点起床发微博的？</p><a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s/T4NwqjKRb1H4qlzjo2MZag\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">mp.weixin.qq.com/s/T4Nw</span><span class=\"invisible\">qjKRb1H4qlzjo2MZag</span><span class=\"ellipsis\"></span></a><p> </p><figure><noscript><img data-rawwidth=\"1242\" data-rawheight=\"2085\" src=\"https://pic1.zhimg.com/v2-be5c83bd3be853080590284d7009cd56_b.jpg\" data-original-token=\"v2-be5c83bd3be853080590284d7009cd56\" class=\"origin_image zh-lightbox-thumb\" width=\"1242\" data-original=\"https://pic1.zhimg.com/v2-be5c83bd3be853080590284d7009cd56_r.jpg\"/></noscript><img data-rawwidth=\"1242\" data-rawheight=\"2085\" src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1242&#39; height=&#39;2085&#39;&gt;&lt;/svg&gt;\" data-original-token=\"v2-be5c83bd3be853080590284d7009cd56\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1242\" data-original=\"https://pic1.zhimg.com/v2-be5c83bd3be853080590284d7009cd56_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-be5c83bd3be853080590284d7009cd56_b.jpg\"/></figure>", "excerpt": "专访 | 爱可可-爱生活：我是如何做到每天5点起床发微博的？ <a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s/T4NwqjKRb1H4qlzjo2MZag\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">mp.weixin.qq.com/s/T4Nw</span><span class=\"invisible\">qjKRb1H4qlzjo2MZag</span><span class=\"ellipsis\"></span></a> ", "excerpt_new": "专访 | 爱可可-爱生活：我是如何做到每天5点起床发微博的？ <a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s/T4NwqjKRb1H4qlzjo2MZag\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">mp.weixin.qq.com/s/T4Nw</span><span class=\"invisible\">qjKRb1H4qlzjo2MZag</span><span class=\"ellipsis\"></span></a> ", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1515566832, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1515536377795", "type": "feed", "target": {"id": "59357957", "title": "马云说「将来数据分析会没有工作」会成真吗？预计是多少年以后？", "url": "https://api.zhihu.com/questions/59357957", "type": "question", "question_type": "normal", "created": 1493826878, "answer_count": 69, "comment_count": 3, "follower_count": 956, "detail": "<figure><img src=\"https://picx.zhimg.com/v2-0af4c518f4e88fd7daf024317b76b76b_b.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1918\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-0af4c518f4e88fd7daf024317b76b76b_r.jpg\"/></figure>", "excerpt": "", "bound_topic_ids": [1761, 3074], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "e7e8a01578d434e1844c8dbc4f430733", "name": "勇闯天涯", "headline": "天道酬勤，人道酬信，商道酬善", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/cheng-ji-wei-24", "url_token": "cheng-ji-wei-24", "avatar_url": "https://picx.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1515536377, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1515461013108", "type": "feed", "target": {"id": "64970975", "title": "有哪些事是在你接近 30 岁的时候，才开始在意和担心的？", "url": "https://api.zhihu.com/questions/64970975", "type": "question", "question_type": "normal", "created": 1504674819, "answer_count": 1165, "comment_count": 22, "follower_count": 28466, "detail": "<p><b>本题已收录至知乎圆桌 <a href=\"https://www.zhihu.com/roundtable/middleage\" class=\"internal\">30 岁人生攻略</a></b> <b>，更多「<a href=\"https://www.zhihu.com/topic/19646617\" class=\"internal\">30 岁</a>」相关话题欢迎关注讨论。</b></p>", "excerpt": "<b>本题已收录至知乎圆桌 <a href=\"https://www.zhihu.com/roundtable/middleage\" class=\"internal\">30 岁人生攻略</a></b> <b>，更多「<a href=\"https://www.zhihu.com/topic/19646617\" class=\"internal\">30 岁</a>」相关话题欢迎关注讨论。</b>", "bound_topic_ids": [307, 404, 1309, 1546, 32192], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1515461013, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1515314303327", "type": "feed", "target": {"id": "50967184", "title": "看机器学习论文时，看不懂数学公式怎么办？", "url": "https://api.zhihu.com/questions/50967184", "type": "question", "question_type": "normal", "created": 1474716287, "answer_count": 30, "comment_count": 3, "follower_count": 1529, "detail": "普通本科，对机器学习感兴趣，老师丢给我几篇英文论文。内含很多数学公式，不太看得懂。接下去我该怎么做？看不懂硬看？<br/><br/>ps. 我的机器学习水平处于仅仅只看完ng和台大的公开课，写过一些机器学习代码，但量不大。<br/>数学水平：线性代数 处于看完mit公开课水平。  概率和微积分一般。<br/>————————————<br/><br/>谢谢各位。已找到了答案。@<a class=\"internal\" href=\"https://www.zhihu.com/people/li-hua-fan\">Takashi</a>  说的很对，无非基础不够，及不够了解。还要继续努力！", "excerpt": "普通本科，对机器学习感兴趣，老师丢给我几篇英文论文。内含很多数学公式，不太看得…", "bound_topic_ids": [707, 1354, 3084], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1515314303, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1515314303327&page_num=73"}}