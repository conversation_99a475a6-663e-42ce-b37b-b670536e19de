{"data": [{"id": "1514625102673", "type": "feed", "target": {"id": "39329428", "type": "answer", "url": "https://api.zhihu.com/answers/39329428", "voteup_count": 633, "thanks_count": 211, "question": {"id": "28086361", "title": "当面试官问你为什么离职，你怎么回答?", "url": "https://api.zhihu.com/questions/28086361", "type": "question", "question_type": "normal", "created": 1423475342, "answer_count": 264, "comment_count": 5, "follower_count": 2263, "detail": "与同事相处不愉快？薪资太少？ 公司太事儿妈？浪费生命？不能创造价值？ 面试官到底想听的是什么呢？面试官是想从这个问题中得到什么信息呢？", "excerpt": "与同事相处不愉快？薪资太少？ 公司太事儿妈？浪费生命？不能创造价值？ 面试官到底…", "bound_topic_ids": [523, 622, 1657, 13869, 121904], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "bc368beb1cecd322b0f72f0ea98e753c", "name": "马春锐", "headline": "刚刚入门的运营小虫", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/ma-chun-rui-65", "url_token": "ma-chun-rui-65", "avatar_url": "https://picx.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1423475942, "created_time": 1423475942, "author": {"id": "2ed6ff1214a4d45902cfa19a9a5e16a0", "name": "XMAN1861", "headline": "柔道！可加用户名同名微信骚扰！", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/feng-dong-46-34", "url_token": "feng-dong-46-34", "avatar_url": "https://picx.zhimg.com/v2-8a681d28751e574114b4f8863c2e648d_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 67, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"qoESxCSZ\">孩子，乃还太嫩啊！答案分三步，首先回顾在原单位工作的很开心，学到了很多，也贡献过努力，取得过成绩，同事关系处的也不错；然后—此处有掌声—感觉自己是个踏实稳重但不失锐意进取，追求进步的好少年，本来是不想辞职的，想在一个单位一直呆到萌嚎，但是原单位能够提供的平台有限，感觉自己能力已经达到新单位要求，希望能为新东家创造效益的同时，孜孜不倦提升、升华自己，以达到与新单位共成长的目的。最后—此处略有停顿—继续推销自己啊，傻瓜，要个好价钱啊。（打字好累）</p>", "excerpt": "孩子，乃还太嫩啊！答案分三步，首先回顾在原单位工作的很开心，学到了很多，也贡献过努力，取得过成绩，同事关系处的也不错；然后—此处有掌声—感觉自己是个踏实稳重但不失锐意进取，追求进步的好少年，本来是不想辞职的，想在一个单位一直呆到萌嚎，但是原单位能够提供的平台有限，感觉自己能力已经达到新单位要求，希望能为新东家创造效益的同时，孜孜不倦提升、升华自己，以达到与新单位共成长的目的。最后—此处略有停顿—…", "excerpt_new": "孩子，乃还太嫩啊！答案分三步，首先回顾在原单位工作的很开心，学到了很多，也贡献过努力，取得过成绩，同事关系处的也不错；然后—此处有掌声—感觉自己是个踏实稳重但不失锐意进取，追求进步的好少年，本来是不想辞职的，想在一个单位一直呆到萌嚎，但是原单位能够提供的平台有限，感觉自己能力已经达到新单位要求，希望能为新东家创造效益的同时，孜孜不倦提升、升华自己，以达到与新单位共成长的目的。最后—此处略有停顿—…", "preview_type": "expand", "preview_text": "<p data-pid=\"qoESxCSZ\">孩子，乃还太嫩啊！答案分三步，首先回顾在原单位工作的很开心，学到了很多，也贡献过努力，取得过成绩，同事关系处的也不错；然后—此处有掌声—感觉自己是个踏实稳重但不失锐意进取，追求进步的好少年，本来是不想辞职的，想在一个单位一直呆到萌嚎，但是原单位能够提供的平台有限，感觉自己能力已经达到新单位要求，希望能为新东家创造效益的同时，孜孜不倦提升、升华自己，以达到与新单位共成长的目的。最后—此处略有停顿—继续推销自己啊，傻瓜，要个好价钱啊。（打字好累）</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1514625102, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1514426354194", "type": "feed", "target": {"id": "282986829", "type": "answer", "url": "https://api.zhihu.com/answers/282986829", "voteup_count": 5399, "thanks_count": 1932, "question": {"id": "20054811", "title": "有何建议给即将步入职场的应届毕业生？", "url": "https://api.zhihu.com/questions/20054811", "type": "question", "question_type": "normal", "created": 1328687804, "answer_count": 1904, "comment_count": 19, "follower_count": 40667, "detail": "<p><b>本题已加入圆桌 » <a href=\"https://www.zhihu.com/roundtable/fenkai\" class=\"internal\">「分开」这件小事</a>，更多「分离」相关的话题欢迎关注，也欢迎参与想法 #<a href=\"https://www.zhihu.com/pin/special/991009756017901568\" class=\"internal\">分开这件小事</a># 讨论。</b></p><p>请各位给今年的应届毕业生一点建议。谢谢。<br/>请大家一起帮忙邀请各位职场达人。</p><p>相关问题：</p><p><a href=\"http://www.zhihu.com/question/28236217\" class=\"internal\">作为一个工作了很久的人你有哪些经验是想要告诫职场小菜鸟的？</a></p><p><a href=\"http://www.zhihu.com/question/21059020\" class=\"internal\">对于一个即将毕业参加工作的学生，你有什么忠告？</a></p>", "excerpt": "<b>本题已加入圆桌 » <a href=\"https://www.zhihu.com/roundtable/fenkai\" class=\"internal\">「分开」这件小事</a>，更多「分离」相关的话题欢迎关注，也欢迎参…</b>", "bound_topic_ids": [112, 2566, 3479, 3946, 7129], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "750bcd8beacc40d28f9568587535ed6a", "name": "江月何年", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/chuyu", "url_token": "chuyu", "avatar_url": "https://picx.zhimg.com/2770f00c9_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1519832617, "created_time": 1514215843, "author": {"id": "e82d209b1dddaeff3c5a986866d73b55", "name": "杜明远", "headline": "公众号：明远说，专注个人成长，思考致富，读书笔记。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xing-fu-de-shun-jian-68", "url_token": "xing-fu-de-shun-jian-68", "avatar_url": "https://pic1.zhimg.com/v2-5e0f2376a283d856c9f729e8f2929662_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "nobody", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "因作者设置，评论已关闭"}, "content": "<p data-pid=\"zXk_0UKs\">毕业8年的肺腑之言15条总结，让你少走5年弯路（已经码字到手抽筋）。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"QcM9RKM5\">1.<b>别以为自己刚毕业很年轻，就不努力</b>，等你工作2年后，部分重复性类的螺丝钉岗位，会被未来刚毕业的新生碾压，你没有核心竞争力，裁员的时候就容易被淘汰。</p><p data-pid=\"oFYiTUPE\">2.<b>方法比努力重要</b>，很多人没有方向的瞎努力，浪费了大量时间，要有目标，有规划的努力。</p><p data-pid=\"hHeRLptc\">3.<b>选择比努力重要</b>，要做天花板高的职位，否则很快就瓶颈了，如流水线的工人，即使做的再好，工资也是有上限的。</p><p data-pid=\"9v-595Hx\">4<b>.保持终身学习的好习惯，白天8小时决定生存，晚上2小时决定未来。（做到这点，必有所成）</b></p><p data-pid=\"hKNPRyUK\">5.<b>长板定理很重要</b>，当你有某一方面的特长，不发光都不行，因为这方面你做的是最好的，不可替代。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"HMbcGBdn\">6.<b>等待面试结果心情烦躁解惑</b>，大多数求职面试后，一般第二天晚上都不给答复，默认都是被淘汰了，如果你第二天等的比较着急，可以主动打电话问HR，很有效</p><p data-pid=\"FAfyfaTG\">7.<b>面试加分项</b>：面试前，如果你对该公司了解的特别清楚，都是加分项，（如行业分析，竞争对手，公司现状，盈利模式，岗位职责，其他加分项：可提供面试作品，朋友内推等）</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"38ZwcQG0\">8<b>.沟通能力很重要</b>，互联网创业公司经常开站会，大家在一起沟通一下进度，很多时候是站着开的，所以是站会，基本10-20分完事，很多人做了很多，但说不出来，或沟通费劲，更容易被大家认为能力不行，有淘汰的风险。</p><p data-pid=\"GtEhYU8B\">9.<b>法无禁止皆可为</b>，多做一些提升自己能力的事情，这样你会学会的更多（eg：小公司，一个人干N个人的活，大公司N个人干N个人活，但各有各的好处，对于你干的多，锻炼的多）</p><p data-pid=\"u78FnTVr\">10<b>.人生不要给自己设限</b>，不要活在别人的言语中。很多人不让做这个，但是你就是不喜欢这个工作，你愿意委曲求全吗？很多人突破了限制，转行成功，并有了新的发展。（eg：那些创业的人都是在别人的反对中，坚持下来的）（供参考：自己的人生，自己做决定，自己负责）</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"m8QxhqJK\"><b>11.谁的人生不迷茫</b>，工作几年，很多人迷茫，迷茫是正常现象，迷茫就得问，搜索答案，等待是不会有结果的。</p><p data-pid=\"0N_XLvY1\"><b>12.经常掉入时间黑洞</b>，很多人经常过度时间研究一个问题，却没有结果，你很努力，但老板认为你什么也没有干，没有出结果，认为你能力不行。这个时候你给自己定个时间点，超过了就要寻求帮助。</p><p data-pid=\"bIOQJfdt\"><b>13.经常要反思，这段期间有没有成长</b>。很多人经常忙些重复的工作，而没有成长，还经常加班。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"vCG2MEru\"><b>14.关于社保公积金，这几点你不知道就赔大了：</b></p><ul><li data-pid=\"PFzt8l-g\">部分公司没有公积金，你就赔大了，你交1000，公司给你交1000，相当于公司额外给你1000，公积金是可以取出来的。</li><li data-pid=\"XrX-8_1y\">公积金社保按最低基数交，几年前最低基数缴税后是交500元，那个时候公积金最低是120元，你至少挣1000元。（ps：工资太低，请忽略）</li><li data-pid=\"Oc7jV22l\">正常养老金是8%，公积金是工资的12%来缴费。（ps：更新的缴费方法见新规）你不知道吧，你上缴的越多越划算。因为公司给你交的更多，公司给你交的公积金可以取出来，社保也可以取出来的。</li></ul><p data-pid=\"UVBUYmRr\"><b>15.短时间如何火箭般 的进步：复盘是成功人士的必修</b>课。做事要有目标，目标可分解，量化，执行后有反馈，反馈后可以优化。如此反复，必有收获。</p><p data-pid=\"lznRjLnL\">……</p><p data-pid=\"TwIe59iW\"><b>总结：</b></p><p data-pid=\"Kv86Xcdb\"><b>终身学习习惯，有规划有目标，执行力强，总结复盘。</b></p><p data-pid=\"GjR4Tydk\"><b>5年内做到这4点，你就和周围的小伙伴拉开了距离。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"f8x93TmQ\">码字不易，感觉有用请收藏、点赞，后续有时间继续更新。</p><p class=\"ztext-empty-paragraph\"><br/></p><blockquote data-pid=\"pReanfjx\">关于作者：明远，一个爱学习de大表哥<br/>公众号【明远说】专注成长+职场进阶的干货分享<br/>公众号回复：【3G】获得3.39G简历模板大礼包</blockquote>", "excerpt": "毕业8年的肺腑之言15条总结，让你少走5年弯路（已经码字到手抽筋）。 1. <b>别以为自己刚毕业很年轻，就不努力</b>，等你工作2年后，部分重复性类的螺丝钉岗位，会被未来刚毕业的新生碾压，你没有核心竞争力，裁员的时候就容易被淘汰。2. <b>方法比努力重要</b>，很多人没有方向的瞎努力，浪费了大量时间，要有目标，有规划的努力。3. <b>选择比努力重要</b>，要做天花板高的职位，否则很快就瓶颈了，如流水线的工人，即使做的再好，工资也是有上限的…", "excerpt_new": "毕业8年的肺腑之言15条总结，让你少走5年弯路（已经码字到手抽筋）。 1. <b>别以为自己刚毕业很年轻，就不努力</b>，等你工作2年后，部分重复性类的螺丝钉岗位，会被未来刚毕业的新生碾压，你没有核心竞争力，裁员的时候就容易被淘汰。2. <b>方法比努力重要</b>，很多人没有方向的瞎努力，浪费了大量时间，要有目标，有规划的努力。3. <b>选择比努力重要</b>，要做天花板高的职位，否则很快就瓶颈了，如流水线的工人，即使做的再好，工资也是有上限的…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1514426354, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1514160874251", "type": "feed", "target": {"id": "19627832", "title": "豆瓣电影的分数和排序是怎么算出来的？", "url": "https://api.zhihu.com/questions/19627832", "type": "question", "question_type": "normal", "created": 1304586165, "answer_count": 7, "comment_count": 1, "follower_count": 1038, "detail": " 应该不是平均分吧？如果平均分同样是5分，会不会考虑打分的人数什么因素？比如1000个人打的和500个人打的，分数应该是不一样的吧。如果得分相同，谁排前面，谁排后面？ ", "excerpt": "应该不是平均分吧？如果平均分同样是5分，会不会考虑打分的人数什么因素？比如1000…", "bound_topic_ids": [1093, 4328, 8745, 9675, 17139], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "655746ec9c352df343eac4a5a67bf3c2", "name": "佳冬", "headline": "产品设计师", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/jia-dong-29", "url_token": "jia-dong-29", "avatar_url": "https://pica.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1514160874, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1504372394953", "type": "feed", "target": {"id": "102223677", "type": "answer", "url": "https://api.zhihu.com/answers/102223677", "voteup_count": 123, "thanks_count": 21, "question": {"id": "21251105", "title": "如何学习推荐系统？", "url": "https://api.zhihu.com/questions/21251105", "type": "question", "question_type": "normal", "created": 1372126960, "answer_count": 118, "comment_count": 0, "follower_count": 2647, "detail": "最近在学习推荐系统，了解了一些概念和方法，工作中也完成过职位协同的工作，但是对于推荐系统相关知识的了解依然很少，想系统的学习，却苦于不知从何入手，还望有这方面经验的同学帮帮忙，指点迷津，将不胜感激！", "excerpt": "最近在学习推荐系统，了解了一些概念和方法，工作中也完成过职位协同的工作，但是对…", "bound_topic_ids": [4259], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1463990573, "created_time": 1463990573, "author": {"id": "d4a7a9a5528adca81be3ab886107ad31", "name": "陈运文", "headline": "datagrand.com 达观数据创始人&amp;CEO ", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/chenyunwen", "url_token": "chen<PERSON><PERSON>", "avatar_url": "https://pic1.zhimg.com/v2-5be0a28d556e7c80dc77f7b1721efa08_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "identity", "description": "复旦大学 计算机应用技术博士", "topic_names": [], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "复旦大学 计算机应用技术博士", "detail_type": "identity", "icon": "", "night_icon": "", "sources": [], "title": "认证", "type": "identity", "url": "https://zhuanlan.zhihu.com/p/96956163"}], "title": "复旦大学 计算机应用技术博士"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": false, "comment_count": 2, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"F-4g1T8P\"><b>推荐系统</b>是个复杂的系统工程，依赖数据、架构、算法、人机交互等环节的有机结合，是数据挖掘技术、信息检索技术、计算统计学等悠久学科的智慧结晶，也关联到认知科学、预测理论、营销学等相关学科。所以，推荐系统的学习也是循序渐进的过程，由浅入深，层层递进。</p><p data-pid=\"H6Sr_awk\">首先，必须进行基础知识的储备，掌握相关的基本概念、推荐算法等理论知识，活学活用。这方面有很多书籍可以参考，如<b>《集体智慧编程》</b>、<b>《推荐系统实践》</b>、<b>《推荐系统》</b>。还有殿堂级大作<b>《Recommender systems handbook》</b>，里面不仅对推荐系统方方面面有详细介绍，还给出了引用的论文，值得投入更多的时间和精力不断钻研。</p><p data-pid=\"9M7uXsez\">其次，实践出真知。只有亲自动手实践才能深入体会推荐系统的各个环节，才能对各种推荐算法的优缺点有真切感受。一方面可以很熟练的完成简单的推荐算法，如content-based、item-based CF等。另一方面要掌握一些常见的推荐算法库，如SvdFeature、LibFM、Mahout、MLib等。</p><p data-pid=\"FBnJb96T\">再者，推荐系统的方方面面提现了很多很多学科的智慧，如信息检索、数据挖掘和机器学习等。掌握这些知识，对推荐效果提升、性能优化都有极大的帮助，也会不断的拓展推荐系统的业务场景。</p><p data-pid=\"6bK4JpTJ\">最后，技术的发展日新月异，要时刻保持对业界最新动态的关注。阅读相关的paper是免不了的，Recsys、KDD、SIGIR等都有推荐系统方面的论文在更新。还可以通过关注技术相关的微博账号、微信公众号及各大论坛的相关动态，了解推荐系统在各大公司的实践情况及最新进展。</p><p data-pid=\"nkD1_i2v\"><b>推荐系统</b>的构建和实际的业务场景是强相关的，效果提升之道和推荐算法的选择也是需要不断的尝试慢慢摸索。针对实际情况，分析bad case，不断迭代开发，才能打造一流的推荐系统。 </p>", "excerpt": "<b>推荐系统</b>是个复杂的系统工程，依赖数据、架构、算法、人机交互等环节的有机结合，是数据挖掘技术、信息检索技术、计算统计学等悠久学科的智慧结晶，也关联到认知科学、预测理论、营销学等相关学科。所以，推荐系统的学习也是循序渐进的过程，由浅入深，层层递进。首先，必须进行基础知识的储备，掌握相关的基本概念、推荐算法等理论知识，活学活用。这方面有很多书籍可以参考，如 <b>《集体智慧编程》</b>、<b>《推荐系统实践》</b>、<b>《推荐系…</b>", "excerpt_new": "<b>推荐系统</b>是个复杂的系统工程，依赖数据、架构、算法、人机交互等环节的有机结合，是数据挖掘技术、信息检索技术、计算统计学等悠久学科的智慧结晶，也关联到认知科学、预测理论、营销学等相关学科。所以，推荐系统的学习也是循序渐进的过程，由浅入深，层层递进。首先，必须进行基础知识的储备，掌握相关的基本概念、推荐算法等理论知识，活学活用。这方面有很多书籍可以参考，如 <b>《集体智慧编程》</b>、<b>《推荐系统实践》</b>、<b>《推荐系…</b>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "disallowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1504372394, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1504372392341", "type": "feed", "target": {"id": "21094996", "type": "answer", "url": "https://api.zhihu.com/answers/21094996", "voteup_count": 134, "thanks_count": 44, "question": {"id": "21251105", "title": "如何学习推荐系统？", "url": "https://api.zhihu.com/questions/21251105", "type": "question", "question_type": "normal", "created": 1372126960, "answer_count": 118, "comment_count": 0, "follower_count": 2647, "detail": "最近在学习推荐系统，了解了一些概念和方法，工作中也完成过职位协同的工作，但是对于推荐系统相关知识的了解依然很少，想系统的学习，却苦于不知从何入手，还望有这方面经验的同学帮帮忙，指点迷津，将不胜感激！", "excerpt": "最近在学习推荐系统，了解了一些概念和方法，工作中也完成过职位协同的工作，但是对…", "bound_topic_ids": [4259], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1387868415, "created_time": 1387868415, "author": {"id": "97c82488d8e76387e747498beed02984", "name": "李海波", "headline": "百度关键词搜索推荐引擎maker", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/li-hai-bo-9", "url_token": "li-hai-bo-9", "avatar_url": "https://pic1.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 7, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"OisfKD8Z\">入门的话， 可以看以下书籍：</p><ol><li data-pid=\"MOhoNLpi\">Segaran T. <strong>Programming collective intelligence: building smart web 2.0 applications</strong>[M]. O’Reilly Media, 2007.寓教于乐的一本入门教材，附有可以直接动手实践的toy级别代码</li><li data-pid=\"8PryFuKT\">Shapira B. <strong>Recommender systems handbook</strong>[M]. Springer, 2011. 推荐系统可做枕头，也应该放在枕边的书籍，看了半本多。如果将该书及其中的参考文献都看完并理解，那恭喜你，你已经对这个领域有深入理解了</li><li data-pid=\"tn39Otzd\">Jannach D, Zanker M, Felfernig A, et al.<strong> Recommender systems: an introduction</strong>[M]. Cambridge University Press, 2010. 可以认为是2010年前推荐系统论文的综述集合</li><li data-pid=\"1nUXpvdC\">Celma <PERSON>. <strong>Music recommendation and discovery</strong>[M]. Springer, 2010. 主要内容集中在音乐推荐，领域非常专注于音乐推荐，包括选取的特征，评测时如何考虑音乐因素</li><li data-pid=\"wnqwVtIq\"><strong>Word sense disambiguation: Algorithms and applications</strong>[M]. Springer Science+ Business Media, 2006. 如果涉及到关键词推荐，或是文本推荐， 则可以查阅该书</li></ol><p data-pid=\"LDz0wAt3\">需要深入研究的话，结合之前这方面的工作及交流整理了一些论文及业界应用， 可以参见：</p><a href=\"https://link.zhihu.com/?target=http%3A//semocean.com/%25E6%258E%25A8%25E8%258D%2590%25E7%25B3%25BB%25E7%25BB%259F%25E7%25BB%258F%25E5%2585%25B8%25E8%25AE%25BA%25E6%2596%2587%25E6%2596%2587%25E7%258C%25AE%25E5%258F%258A%25E8%25B5%2584%25E6%2596%2599/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">semocean.com/%E6%8E%A8%</span><span class=\"invisible\">E8%8D%90%E7%B3%BB%E7%BB%9F%E7%BB%8F%E5%85%B8%E8%AE%BA%E6%96%87%E6%96%87%E7%8C%AE%E5%8F%8A%E8%B5%84%E6%96%99/</span><span class=\"ellipsis\"></span></a>", "excerpt": "入门的话， 可以看以下书籍： Segaran T. <strong>Programming collective intelligence: building smart web 2.0 applications</strong>[M]. O’Reilly Media, 2007.寓教于乐的一本入门教材，附有可以直接动手实践的toy级别代码Shapira B. <strong>Recommender systems handbook</strong>[M]. Springer, 2011. 推荐系统可做枕头，也应该放在枕边的书籍，看了半本多。如果将该书及其中的参考文献都看完并理解，那恭喜你，你已经对这个领域有深入理解了Jannach D, Za…", "excerpt_new": "入门的话， 可以看以下书籍： Segaran T. <strong>Programming collective intelligence: building smart web 2.0 applications</strong>[M]. O’Reilly Media, 2007.寓教于乐的一本入门教材，附有可以直接动手实践的toy级别代码Shapira B. <strong>Recommender systems handbook</strong>[M]. Springer, 2011. 推荐系统可做枕头，也应该放在枕边的书籍，看了半本多。如果将该书及其中的参考文献都看完并理解，那恭喜你，你已经对这个领域有深入理解了Jannach D, Za…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1504372392, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1504290279489", "type": "feed", "target": {"id": "19619335", "title": "有哪些高质量的图片网站（社区）推荐？", "url": "https://api.zhihu.com/questions/19619335", "type": "question", "question_type": "normal", "created": 1304062122, "answer_count": 151, "comment_count": 8, "follower_count": 8624, "detail": " 1、高质量图片  2、免费，或者一点点。  3、支持主题搜索，不要下载大大的压缩包 ", "excerpt": "1、高质量图片 2、免费，或者一点点。 3、支持主题搜索，不要下载大大的压缩包 ", "bound_topic_ids": [2055, 4777, 5493, 7481, 8022], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "ca81403a33fad2943466ffafcc650ddf", "name": "王祥", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/wang-xiang-34", "url_token": "wang-xiang-34", "avatar_url": "https://pic1.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1504290279, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1503761803810", "type": "feed", "target": {"id": "26743347", "title": "网易云音乐的歌单推荐算法是怎样的？", "url": "https://api.zhihu.com/questions/26743347", "type": "question", "question_type": "normal", "created": 1416721706, "answer_count": 240, "comment_count": 56, "follower_count": 25864, "detail": "不是广告党，但我却成为网易云音乐的的重度患者，不管是黑红的用户界面，还是高质量音乐质量都用起来很舒服。我喜欢听歌，几乎每周不低于15小时，但其实听得不是特别多，并没有经常刻意地去搜歌名，所以曲目数量我并不是很在乎。但是比起其它，网音给我推荐的歌单几乎次次惊艳，而且大多都没听过，或者好久以前听过早就忘记了名字，或者之前不知道在哪听过 只是知道其中一部分旋律，根本不知道名字，等等，听起来整个人大有提升。<br/>——————————————————————————————————<br/>问题来了，我想知道网音的歌单推荐是网音项目团队精心挑选制作的，还是众多音乐达人的推荐？即：歌单是网音官方提供，还是UGC？才有如此对口味的歌单推荐？求研究过的大神给出详细解答。", "excerpt": "不是广告党，但我却成为网易云音乐的的重度患者，不管是黑红的用户界面，还是高质量…", "bound_topic_ids": [75, 368, 10096, 87983], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "ca18c03d322c001f819c8cedc95007ce", "name": "白胡子", "headline": "互联网 电子", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/lu-fei-7", "url_token": "lu-fei-7", "avatar_url": "https://pica.zhimg.com/e79c87446_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1503761803, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1503761803810&page_num=75"}}