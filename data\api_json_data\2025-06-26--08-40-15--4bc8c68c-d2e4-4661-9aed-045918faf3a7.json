{"data": [{"id": "48_1750898460.206", "type": "feed", "offset": 48, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898460, "updated_time": 1750898460, "target": {"id": "1920063645456528579", "type": "article", "url": "https://api.zhihu.com/articles/1920063645456528579", "author": {"id": "b182157671aa37ef92f213e6bf361145", "url": "https://api.zhihu.com/people/b182157671aa37ef92f213e6bf361145", "user_type": "people", "url_token": "dlimeng", "name": "李孟聊AI", "headline": "大数据 | 人工智能 | AI生成 | SolidUI发起人", "avatar_url": "https://pica.zhimg.com/50/v2-0d9546538ff05c899ef72e081095f75d_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 898, "is_following": false, "is_followed": false}, "title": "这9个MCP服务器改善AI上下文（减少99%的代码错误）", "image_url": "https://pic1.zhimg.com/v2-931cc1487b4de106f737d9f6996dadf1.jpg?source=7e7ef6e2&needBackground=1", "comment_permission": "all", "created": 1750559055, "updated": 1750650786, "voteup_count": 6, "voting": 0, "comment_count": 1, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "几乎所有AI编程助手都在持续产生过时API的幻觉，忘记项目上下文，并一遍又一遍地犯同样的错误。 如果你曾花费数小时调试为什么React组件无法渲染，最后却发现你的AI使用的是2022年已废弃的hooks，你就知道这种痛苦。 AI编程工具的上下文限制正在消耗开发者真正的时间和理智。 它们会忘记之前的对话，引用过时的文档，并且缺乏对你项目完整范围的认知。 但以下内容彻底改变了我的体验。 我发现MCP服务器可以将你的AI编程代理从上…", "excerpt_new": "几乎所有AI编程助手都在持续产生过时API的幻觉，忘记项目上下文，并一遍又一遍地犯同样的错误。 如果你曾花费数小时调试为什么React组件无法渲染，最后却发现你的AI使用的是2022年已废弃的hooks，你就知道这种痛苦。 AI编程工具的上下文限制正在消耗开发者真正的时间和理智。 它们会忘记之前的对话，引用过时的文档，并且缺乏对你项目完整范围的认知。 但以下内容彻底改变了我的体验。 我发现MCP服务器可以将你的AI编程代理从上…", "preview_type": "default", "preview_text": "", "column": {"id": "c_1915348458824508861", "type": "column", "url": "https://api.zhihu.com/columns/c_1915348458824508861", "author": {"id": "", "url": "", "user_type": "people", "url_token": "", "name": "匿名用户", "headline": "", "avatar_url": "https://picx.zhimg.com/v2-d41c2ceaed8f51999522f903672a521f_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "AI编程", "imageUrl": "https://picx.zhimg.com/v2-f111d7ee1c41944859e975a712c0883b_720w.jpg?source=d16d100b", "comment_permission": "private", "intro": "", "updated": 1749434658, "is_following": false}, "content": "<p></p><p data-pid=\"8GKD93pn\">几乎所有AI编程助手都在持续产生过时API的幻觉，忘记项目上下文，并一遍又一遍地犯同样的错误。</p><p data-pid=\"qfecOESz\">如果你曾花费数小时调试为什么React组件无法渲染，最后却发现你的AI使用的是2022年已废弃的hooks，你就知道这种痛苦。</p><p data-pid=\"cnJkS10Y\">AI编程工具的上下文限制正在消耗开发者真正的时间和理智。</p><p data-pid=\"-iXy2i1U\">它们会忘记之前的对话，引用过时的文档，并且缺乏对你项目完整范围的认知。</p><p data-pid=\"ORD-a7V_\">但以下内容彻底改变了我的体验。</p><p data-pid=\"0wpvKL3R\">我发现MCP服务器可以将你的AI编程代理从上下文混乱转变为项目感知的编程伙伴。</p><p data-pid=\"CsDpJSOg\">在专门测试了数十个用于上下文改进的MCP服务器后，我找到了9个能够消除最常见AI编程错误的服务器。</p><p data-pid=\"Wky-KaaC\">我已经运行这些MCP服务器数月，我的调试时间显著下降。</p><p data-pid=\"H74LFa2J\">以下是现在帮助改进上下文并以更少错误更快编程的MCP服务器：</p><h2>1. Context7 MCP</h2><p data-pid=\"JGrseGiv\">Context7是消除过时文档错误的游戏规则改变者。</p><p data-pid=\"osPWEnA_\">它将最新的库文档直接拉取到你的AI提示中，避免那些令人沮丧的时刻——你的助手引用已废弃的API或不存在的函数。</p><p data-pid=\"2VuA85BL\">我一直在使用Context7，它消除了我90%与文档相关的调试会话。</p><p data-pid=\"SwILgr5V\"><b>关键特性</b></p><ul><li data-pid=\"rEsqRq3F\">从源头获取当前的、版本特定的文档<br/> </li><li data-pid=\"WhNIn2Df\">检索现代框架的准确代码示例<br/> </li><li data-pid=\"14XgIz2C\">只需在提示中简单使用&#34;use context7&#34;即可工作<br/> </li><li data-pid=\"PcimZOte\">支持多种编程语言和框架<br/> </li><li data-pid=\"qdypHIX-\">随着库的演进自动更新<br/> </li></ul><p data-pid=\"hMTY8hq_\"><b>它预防的错误</b></p><ul><li data-pid=\"cKrqrZLl\">使用已废弃的API<br/> </li><li data-pid=\"N9IFysbG\">错误的函数签名<br/> </li><li data-pid=\"C4mAKW0V\">过时的语法建议<br/> </li><li data-pid=\"i8BLcXud\">缺少必需参数<br/> </li><li data-pid=\"nPpVPSgJ\">错误的导入语句<br/> </li></ul><p data-pid=\"sryECjwg\"><b>最佳使用场景</b></p><ul><li data-pid=\"MFbTkw3F\">构建使用快速演进框架的项目<br/> </li><li data-pid=\"EFgf-jhQ\">学习新库而无需不断切换标签页<br/> </li><li data-pid=\"_P5wWLfj\">确保代码示例与当前依赖项兼容<br/> </li><li data-pid=\"egNsX_i5\">获取特定包版本的准确语法<br/> </li></ul><p data-pid=\"obteC3nX\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/upstash/context7\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/upstash/cont</span><span class=\"invisible\">ext7</span><span class=\"ellipsis\"></span></a></p><p class=\"ztext-empty-paragraph\"><br/></p><h2>2. Memory Bank MCP服务器</h2><p data-pid=\"Sgb2sfe6\">Memory Bank MCP为你的AI助手在所有编程会话中创建持久内存。</p><p data-pid=\"xjtTPivE\">这消除了浪费大量开发时间的重复解释和上下文重建。</p><p data-pid=\"B6CMJ_WA\">你的AI会记住你的编程模式、项目决策以及之前会话中的架构选择。</p><p data-pid=\"N_qmgZLg\"><b>关键特性</b></p><ul><li data-pid=\"iqpFasny\">具有远程访问的集中式内存库服务<br/> </li><li data-pid=\"CiIm67kS\">完全隔离的多项目支持<br/> </li><li data-pid=\"OXCWZWyR\">路径验证和安全控制<br/> </li><li data-pid=\"8JaFZbpl\">跨会话和重启的持久存储<br/> </li><li data-pid=\"qFgToU38\">项目特定的上下文保留<br/> </li></ul><p data-pid=\"W5m-pfXL\"><b>它预防的错误</b></p><ul><li data-pid=\"qPYnLA5o\">重复创建函数<br/> </li><li data-pid=\"3cBKlrIa\">不一致的编程模式<br/> </li><li data-pid=\"ZhZ4JbxB\">重复的架构错误<br/> </li><li data-pid=\"qJkPGjZn\">会话间丢失项目上下文<br/> </li><li data-pid=\"XYW6fk8-\">忘记之前的决策和约束<br/> </li></ul><p data-pid=\"IBkJIr21\"><b>最佳使用场景</b></p><ul><li data-pid=\"rNiuPX5K\">长期项目开发<br/> </li><li data-pid=\"WHShnugO\">具有共享上下文的团队协作<br/> </li><li data-pid=\"Ng8uCLCe\">跨会话维护编程标准<br/> </li><li data-pid=\"s8cHLltI\">基于之前架构决策进行构建<br/> </li><li data-pid=\"QskoNtFF\">避免重复解释项目结构<br/> </li></ul><p data-pid=\"wmlu6a0_\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/alioshr/memory-bank-mcp\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/alioshr/memo</span><span class=\"invisible\">ry-bank-mcp</span><span class=\"ellipsis\"></span></a></p><h2>3. 知识图谱内存服务器</h2><p data-pid=\"fLdJI3Ap\">Knowledge Graph Memory通过理解项目不同部分之间的关系，将上下文感知提升到下一个层次。</p><p data-pid=\"KQwcArAd\">它不是将每个文件视为孤立的，而是映射你的组件、函数和模块如何连接和相互依赖。</p><p data-pid=\"B3_mcoWv\">这可以防止级联错误——改变一个部分会破坏看似无关的其他部分。</p><p data-pid=\"4aP03eNJ\">我在重构任务中经常使用它，因为理解组件关系至关重要。</p><p data-pid=\"WmzBk5sy\"><b>关键特性</b></p><ul><li data-pid=\"9G9bQwn0\">使用本地知识图谱的持久内存<br/> </li><li data-pid=\"fI6tGJgK\">代码组件间的关系映射<br/> </li><li data-pid=\"0rD3k-1e\">跨会话的轻量级上下文保留<br/> </li><li data-pid=\"faCOlWB7\">依赖跟踪和影响分析<br/> </li><li data-pid=\"IYkfi50X\">跨文件引用理解<br/> </li></ul><p data-pid=\"COLOHXgc\"><b>它预防的错误</b></p><ul><li data-pid=\"w84JN7sT\">依赖组件中的破坏性更改<br/> </li><li data-pid=\"E2ZUakyF\">重构期间遗漏导入更新<br/> </li><li data-pid=\"AfKpkDm2\">创建循环依赖<br/> </li><li data-pid=\"frN96CE-\">未使用代码积累<br/> </li><li data-pid=\"T6s3hBA4\">不完整的功能实现<br/> </li></ul><p data-pid=\"oEMHQ2sI\"><b>最佳使用场景</b></p><ul><li data-pid=\"Ea6DxrJt\">大型代码库重构<br/> </li><li data-pid=\"Yamj9aW1\">组件关系分析<br/> </li><li data-pid=\"D4iKw4bG\">依赖影响评估<br/> </li><li data-pid=\"A_NpNcpi\">架构决策跟踪<br/> </li><li data-pid=\"-svYFM9z\">跨模块功能开发<br/> </li></ul><p data-pid=\"9ZGY9_1W\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/shaneholloman/mcp-knowledge-graph\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/shaneholloma</span><span class=\"invisible\">n/mcp-knowledge-graph</span><span class=\"ellipsis\"></span></a></p><h2>4. 文件系统MCP服务器</h2><p data-pid=\"bVJNz1X_\">Filesystem MCP为你的AI提供对项目结构和文件的准确实时访问。</p><p data-pid=\"y8IQFatk\">这消除了对文件位置、目录结构和项目组织的猜测，这些猜测会导致破损的导入和丢失的文件。</p><p data-pid=\"S-BAQQMl\">你的AI现在可以在提出建议或生成代码之前准确看到项目中存在什么。</p><p data-pid=\"zVzXh8ZY\">这对于防止文件相关错误非常强大。</p><p data-pid=\"8o-vCpyZ\"><b>关键特性</b></p><ul><li data-pid=\"n3zT7Uw-\">使用简单命令读写文件<br/> </li><li data-pid=\"hR7cQF18\">准确创建、列出和删除目录<br/> </li><li data-pid=\"PjwYBGZV\">移动文件和目录而不破坏引用<br/> </li><li data-pid=\"OY0hNiRE\">使用模式匹配搜索文件<br/> </li><li data-pid=\"A5-8brvr\">获取详细的文件元数据和结构<br/> </li><li data-pid=\"Gi0EBlvy\">安全的受限目录访问<br/> </li></ul><p data-pid=\"_WpSs1Wb\"><b>它预防的错误</b></p><ul><li data-pid=\"m7mV2Pwo\">错误的文件路径引用<br/> </li><li data-pid=\"eO4Zsz0X\">缺少导入语句<br/> </li><li data-pid=\"-w6HSwps\">错误的目录结构<br/> </li><li data-pid=\"4UlkkKp8\">重复文件创建<br/> </li><li data-pid=\"0SaAVvJU\">破损的相对路径导入<br/> </li></ul><p data-pid=\"55vfOBAt\"><b>最佳使用场景</b></p><ul><li data-pid=\"fdO8G6VA\">开发期间管理项目文件<br/> </li><li data-pid=\"F85XyrP3\">批量文件操作和重组<br/> </li><li data-pid=\"4FcctnNa\">跨项目搜索特定代码模式<br/> </li><li data-pid=\"Acn9sXq5\">检索准确的文件详细信息用于调试<br/> </li><li data-pid=\"rMqar_Iw\">维护一致的项目结构<br/> </li></ul><p data-pid=\"BI4_1vpV\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/modelcontextprotocol/servers/tree/main/src/filesystem\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/modelcontext</span><span class=\"invisible\">protocol/servers/tree/main/src/filesystem</span><span class=\"ellipsis\"></span></a></p><h2>5. GitMCP</h2><p data-pid=\"52Lp9Esr\">GitMCP将你的AI助手转变为具有git感知的编程伙伴，它理解你的仓库历史、分支和版本控制上下文。</p><p data-pid=\"S9Az78Ey\">你的AI不再建议与最近提交冲突或忽略你的分支策略的更改，而是以完整的仓库感知进行工作。</p><p data-pid=\"Z4hsaO4Y\">这防止了那些令人沮丧的时刻——你的AI生成的代码破坏了现有功能或忽略了团队成员最近的更改。</p><p data-pid=\"Zg3-Xsze\">我发现GitMCP在处理功能分支时特别有价值，因为最近更改的上下文至关重要。</p><p data-pid=\"A3-u3U2z\"><b>关键特性</b></p><ul><li data-pid=\"2R5e5Wax\">具有完整git上下文的仓库和文件操作<br/> </li><li data-pid=\"Jz2ZeEMy\">问题跟踪和管理集成<br/> </li><li data-pid=\"BK-3sMQh\">用户和贡献者感知<br/> </li><li data-pid=\"0SMJ6V_3\">用于仓库、问题和用户的动态工具集<br/> </li><li data-pid=\"HnKFPUnT\">分支和提交历史理解<br/> </li><li data-pid=\"KmvTY3hQ\">通过上下文感知防止合并冲突<br/> </li></ul><p data-pid=\"3mA-LStv\"><b>它预防的错误</b></p><ul><li data-pid=\"dHBywDl_\">与最近提交的代码冲突<br/> </li><li data-pid=\"sQT-EvJx\">覆盖队友的更改<br/> </li><li data-pid=\"w74_UKwl\">破坏现有功能<br/> </li><li data-pid=\"fLP2HyBG\">忽略分支特定需求<br/> </li><li data-pid=\"0fYsIEdM\">建议中缺少仓库上下文<br/> </li></ul><p data-pid=\"AbtwIpcl\"><b>最佳使用场景</b></p><ul><li data-pid=\"YPmHYEVl\">多贡献者团队协作<br/> </li><li data-pid=\"jGqGLLJ3\">功能分支开发<br/> </li><li data-pid=\"5cJBA_Oz\">代码审查和冲突解决<br/> </li><li data-pid=\"oSqTAf2J\">问题驱动的开发工作流<br/> </li><li data-pid=\"B8XG43Yl\">仓库范围的重构项目<br/> </li></ul><p data-pid=\"CM9Dklur\"><a href=\"https://link.zhihu.com/?target=https%3A//gitmcp.io/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">gitmcp.io/</span><span class=\"invisible\"></span></a></p><p class=\"ztext-empty-paragraph\"><br/></p><h2>6. Obsidian-MCP</h2><p data-pid=\"v2lCA_sL\">Obsidian-MCP将你的AI助手连接到你的Obsidian知识库，将你的笔记、文档和项目见解直接带入编程会话。</p><p data-pid=\"9a1ytTHp\">这在你的思维过程和编程之间建立了桥梁，确保你的AI理解你正在构建什么以及它如何融入你更广泛的项目目标。</p><p data-pid=\"cdhbnvpq\">你的项目文档、架构决策和学习笔记成为你AI上下文的一部分。</p><p data-pid=\"Cqe5hfIZ\">这防止创建与你记录的需求或架构决策不一致的功能。</p><p data-pid=\"B9WUuNP7\"><b>关键特性</b></p><ul><li data-pid=\"oBNtmIz9\">直接访问Obsidian vault笔记<br/> </li><li data-pid=\"7Ia5L13a\">Markdown文档集成<br/> </li><li data-pid=\"wp2rT0HH\">链接笔记关系理解<br/> </li><li data-pid=\"HBt29Ck9\">标签和类别感知<br/> </li><li data-pid=\"-ecfwrPn\">项目文档上下文<br/> </li><li data-pid=\"x4ZnzRUV\">决策历史跟踪<br/> </li></ul><p data-pid=\"CzVCkzjF\"><b>它预防的错误</b></p><ul><li data-pid=\"n7uthGwo\">构建违背记录需求的功能<br/> </li><li data-pid=\"iM0IgtjQ\">忽略架构决策<br/> </li><li data-pid=\"0EUW_Qt2\">遗漏项目约束和目标<br/> </li><li data-pid=\"nANG-M7F\">不一致的实现模式<br/> </li><li data-pid=\"oP2lQjP4\">丢失之前规划会话的上下文<br/> </li></ul><p data-pid=\"D8_LFgF5\"><b>最佳使用场景</b></p><ul><li data-pid=\"bTgh4xgJ\">文档驱动开发<br/> </li><li data-pid=\"RFq_EIB7\">维护架构一致性<br/> </li><li data-pid=\"-AaMWdxb\">从之前项目笔记中学习<br/> </li><li data-pid=\"juUkGDAI\">需求对齐的功能开发<br/> </li><li data-pid=\"xszuuGKx\">基于知识的编程决策<br/> </li></ul><p data-pid=\"iVNW6rQd\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/MarkusPfundstein/mcp-obsidian\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/MarkusPfunds</span><span class=\"invisible\">tein/mcp-obsidian</span><span class=\"ellipsis\"></span></a></p><h2>7. Tavily MCP</h2><p data-pid=\"Aor64ERM\">Tavily MCP添加了AI驱动的搜索功能，超越基本网络搜索，找到开发者特定的、上下文相关的信息。</p><p data-pid=\"YKspL1sj\">当你的AI需要关于库、框架或特定编程问题解决方案的当前信息时，Tavily提供智能搜索结果。</p><p data-pid=\"ILFB7twl\">这防止过时的解决方案，并确保你的AI能够访问最新的最佳实践和问题解决方法。</p><p data-pid=\"vZr3xDa5\"><b>关键特性</b></p><ul><li data-pid=\"3mz_6P3p\">具有开发者上下文的AI驱动搜索<br/> </li><li data-pid=\"XOrOdxvp\">当前库和框架信息<br/> </li><li data-pid=\"CEVFDat1\">最佳实践和解决方案发现<br/> </li><li data-pid=\"89KX7QZH\">技术文档搜索<br/> </li><li data-pid=\"_tosfScS\">问题特定的结果过滤<br/> </li></ul><p data-pid=\"1Ydbtso_\"><b>它预防的错误</b></p><ul><li data-pid=\"qUcFq5WJ\">使用过时的解决方案和模式<br/> </li><li data-pid=\"bJskCsTE\">遗漏当前最佳实践<br/> </li><li data-pid=\"aK8jJn0K\">实现已知有问题的方法<br/> </li><li data-pid=\"pbNHppE_\">重新发明已存在的解决方案<br/> </li><li data-pid=\"7coWaJ_Y\">遵循已废弃的建议<br/> </li></ul><p data-pid=\"7TRUTpVd\"><b>最佳使用场景</b></p><ul><li data-pid=\"_oKbED65\">研究新库和框架<br/> </li><li data-pid=\"Zh2-S3o2\">找到特定编程问题的解决方案<br/> </li><li data-pid=\"7t8qEMXE\">跟上最佳实践<br/> </li><li data-pid=\"IezZrQiJ\">发现替代方法<br/> </li><li data-pid=\"hwEsIHhD\">技术问题解决研究<br/> </li></ul><p data-pid=\"3YqGVz4M\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/tavily-ai/tavily-mcp\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/tavily-ai/ta</span><span class=\"invisible\">vily-mcp</span><span class=\"ellipsis\"></span></a></p><h2>8. Sequential Thinking MCP</h2><p data-pid=\"v6YdJEaF\">Sequential Thinking MCP为你的AI助手提供结构化问题解决，将复杂的编程任务分解为逻辑的、可管理的步骤。</p><p data-pid=\"Es66ihUx\">AI现在系统性地思考问题，考虑依赖关系、边界情况和实现顺序。</p><p data-pid=\"_JEyifnh\">这大大减少了由于不完整分析或匆忙实现而产生的错误。</p><p data-pid=\"at6XFgaB\">我将其用于任何涉及多个组件或具有非显而易见实现挑战的复杂功能。</p><p data-pid=\"-lrWXBUR\"><b>关键特性</b></p><ul><li data-pid=\"C21hc__x\">将复杂任务分解为可管理的步骤<br/> </li><li data-pid=\"qwUQA2vy\">支持分支逻辑和决策树<br/> </li><li data-pid=\"ljDCi7uQ\">允许思路修订和完善<br/> </li><li data-pid=\"OBLdfWFC\">适合规划和分析<br/> </li><li data-pid=\"Ou48_ha9\">依赖识别和排序<br/> </li><li data-pid=\"pv9_9CPg\">风险评估和缓解规划<br/> </li></ul><p data-pid=\"EtmkUb1K\"><b>它预防的错误</b></p><ul><li data-pid=\"hlvVRVK5\">不完整的功能实现<br/> </li><li data-pid=\"_Jn1opPz\">遗漏边界情况处理<br/> </li><li data-pid=\"1JSUJStB\">糟糕的实现顺序导致冲突<br/> </li><li data-pid=\"Kns-AZVb\">忽视的依赖关系和需求<br/> </li><li data-pid=\"fG2UPUwM\">没有适当分析的匆忙解决方案<br/> </li></ul><p data-pid=\"gDTJovak\"><b>最佳使用场景</b></p><ul><li data-pid=\"5WyWwyr0\">复杂功能规划和实现<br/> </li><li data-pid=\"J6tZG6_W\">系统架构决策<br/> </li><li data-pid=\"ze5kC1mx\">调试复杂的多组件问题<br/> </li><li data-pid=\"0Rdu8pEw\">重构大型代码库<br/> </li><li data-pid=\"1s8oIKwj\">重大更改的风险评估<br/> </li></ul><p data-pid=\"Yy1Vs3I6\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/modelcontext</span><span class=\"invisible\">protocol/servers/tree/main/src/sequentialthinking</span><span class=\"ellipsis\"></span></a></p><h2>9. Fetch MCP服务器</h2><p data-pid=\"0axoxcZs\">Fetch MCP Server为你的AI助手提供直接检索和处理网络内容的能力，将HTML文档、教程和资源转换为可用的上下文。</p><p data-pid=\"LEDDGwDW\">这意味着你的AI可以实时从官方文档站点、GitHub仓库和技术资源中提取最新信息。</p><p data-pid=\"BD0Fgt4t\">不再有过时信息或缺少关于外部依赖和服务的上下文。</p><p data-pid=\"BW5WAWR7\"><b>关键特性</b></p><ul><li data-pid=\"YSHen0xB\">检索网络内容并将HTML转换为markdown<br/> </li><li data-pid=\"Cju_Tugo\">通过start_index参数支持分块读取<br/> </li><li data-pid=\"WBeWr8Wi\">使用可自定义max_length处理内容截断<br/> </li><li data-pid=\"xDzQaPyZ\">需要时提供原始内容选项<br/> </li><li data-pid=\"npM7-AeP\">实时文档访问<br/> </li><li data-pid=\"N41_OLHV\">外部资源集成<br/> </li></ul><p data-pid=\"V9juoOoG\"><b>它预防的错误</b></p><ul><li data-pid=\"RgUeZyCS\">过时的外部API信息<br/> </li><li data-pid=\"HVfm_jfK\">遗漏当前文档详细信息<br/> </li><li data-pid=\"I3NGkNwO\">错误的集成模式<br/> </li><li data-pid=\"mWyVEuV-\">陈旧的第三方服务信息<br/> </li><li data-pid=\"9q4njdsP\">不完整的外部依赖理解<br/> </li></ul><p data-pid=\"3XJuoU6o\"><b>最佳使用场景</b></p><ul><li data-pid=\"Pw0jcRrH\">研究外部API和服务<br/> </li><li data-pid=\"EMF9jfgL\">编程时访问当前文档<br/> </li><li data-pid=\"IL9eS53s\">与第三方服务集成<br/> </li><li data-pid=\"H0Hf0zgM\">遵循当前实现模式<br/> </li><li data-pid=\"0kYAQDwQ\">跟上框架变化<br/> </li></ul><p data-pid=\"D0MglRpc\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/modelcontextprotocol/servers/tree/main/src/fetch\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/modelcontext</span><span class=\"invisible\">protocol/servers/tree/main/src/fetch</span><span class=\"ellipsis\"></span></a></p><h2>结语</h2><p data-pid=\"_w5rtWJd\">这些上下文MCP服务器从根本上改变了我使用AI编程的方式。</p><p data-pid=\"Sn-wc0xL\">Context7消除了文档难题，Memory Bank防止重复解释，Knowledge Graph Memory理解你的项目关系。</p><p data-pid=\"IOlwDKs3\">Filesystem MCP提供准确的文件操作，而GitMCP增加了仓库感知，防止合并冲突。</p><p data-pid=\"QuoZResm\">Obsidian-MCP将你的文档带入编程会话，Tavily MCP提供智能搜索，Sequential Thinking MCP构建复杂问题解决，Fetch MCP保持一切最新。</p><p data-pid=\"q4tB4wl2\">它们共同创造了一个理解你项目、记住你决策并防止浪费数小时调试时间的常见错误的环境。</p><p data-pid=\"cakPIrcx\">我的最爱是Context7。你试过吗？请在下面评论中分享你的体验。</p><p data-pid=\"n04s64Au\">本文同步自知识星球《AI Disruption》</p><p data-pid=\"KgmZuNWG\">我是Substack编辑和独立开发。</p><p data-pid=\"P-mR9M86\">星球里面分享AI趋势，海外数字营销，美股。</p><p data-pid=\"ziqrDdEA\">星球非免费。定价99元/年，0.27元/天。</p><p data-pid=\"lC64Ij-H\">一是运行有成本，我希望它能自我闭环，这样才能长期稳定运转；</p><p data-pid=\"sUYOEGUg\">二是对人的挑选，鱼龙混杂不是我想要的，希望找到关注和热爱 AI 的人。</p><p data-pid=\"Zb47fuKs\">欢迎你的加入！</p><p data-pid=\"hnfhmtpr\"><a href=\"https://link.zhihu.com/?target=https%3A//t.zsxq.com/P0lXg\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">t.zsxq.com/P0lXg</span><span class=\"invisible\"></span></a> (二维码自动识别)</p><p></p>", "is_labeled": false, "visited_count": 380, "thumbnails": ["https://pic1.zhimg.com/v2-931cc1487b4de106f737d9f6996dadf1.jpg?source=7e7ef6e2&needBackground=1", "https://pic1.zhimg.com/50/v2-6a531fdc5008fb73d1e5a537097355e6_720w.jpg?source=b6762063"], "favorite_count": 25, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": 1920063645456528579}", "attached_info": "CswGCO7fqYPgk/SJuwEQBxoJMjU5Mzk0ODM1IM/S3cIGKAYwAUAwSjAKBkl0ZW1DRhIgZG9jX3R5cGU6IEFydGljbGUKaWQ6IDI1OTE0NjE1OQoYACAAOgBaCDEzNjkyMDQ3YiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3ITMTkyMDA2MzY0NTQ1NjUyODU3OYIBX2h0dHBzOi8vcGljMS56aGltZy5jb20vdjItOTMxY2MxNDg3YjRkZTEwNmY3MzdkOWY2OTk2ZGFkZjEuanBnP3NvdXJjZT03ZTdlZjZlMiZuZWVkQmFja2dyb3VuZD0xigEVY18xOTE1MzQ4NDU4ODI0NTA4ODYxqgEJcmVjb21tZW5kwgEgYjE4MjE1NzY3MWFhMzdlZjkyZjIxM2U2YmYzNjExNDXyAQoIDBIGTm9ybWFs8gEoCAoSJDVhZmU5YTUyLTNkMWEtNGMxYy05ZTg2LWVlZTQyZGIwYThjYfIBBQgLEgE5ggIAiALBkLnN+jKSAiBiMTgyMTU3NjcxYWEzN2VmOTJmMjEzZTZiZjM2MTE0NZoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhhQZXJpb2RJbnRlcmVzdFdlaWdodFJ1bGXKAhVVc2VyTGNuRXhpdFdlaWdodFJ1bGXaAgZJdGVtQ0boAgP6AgtOT1JNQUxfRkxPV4oDIGMzMDQzNGE0ZmFlMzQ4YWZiNDlhY2NkYjJkZjY1ZjU5mgMNCgJ2MhAAGgVvdGhlcqgD/ALYAwDqAxV0ZXh0QWxsU2l0ZU12SXRlbUNGVjL6A04SDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFOi0IAhCAChjQBSIjdjItOTMxY2MxNDg3YjRkZTEwNmY3MzdkOWY2OTk2ZGFkZjGABACIBACSBAZOb3JtYWyaBAEzoAQAqAQAsAQAugQCYWnCBAM0MDDIBADSBA/mjqjojZDlt7Lmm7TmlrDYBADwBAD5BAAAACD/r7A/gQUAAAAAAAAAAIkFkslO6rCP0j+SBQCaBQNkZnSiBQNkZnSyBQExuQUAAAAAAAAAANAFAOAFAOgFAPAFCZAGAKAGNKgGAZICLgoJMjU5Mzk0ODM1EhMxOTIwMDYzNjQ1NDU2NTI4NTc5GAciCklNQUdFX1RFWFQ=", "action_card": false}, {"id": "49_1750898460.685", "type": "feed", "offset": 49, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898460, "updated_time": 1750898460, "target": {"id": "3443242058", "type": "answer", "url": "https://api.zhihu.com/answers/3443242058", "author": {"id": "180505bb45e12860f2cfcf5d962341d6", "url": "https://api.zhihu.com/people/180505bb45e12860f2cfcf5d962341d6", "user_type": "people", "url_token": "mockingbird2046", "name": "剁椒鹿", "headline": "学兽医救不了牛马（私信都会看）", "avatar_url": "https://pic1.zhimg.com/50/v2-e25572a276c7d6eebf9387a76b1ca929_l.jpg?source=b6762063", "is_org": false, "gender": 0, "badge": [{"type": "zhihu_yearly_answerer", "description": "新知答主"}, {"type": "best_answerer", "description": "猫等 2 个话题下的优秀答主", "topic_names": ["猫", "动物学"], "topic_ids": [1570, 22767]}, {"type": "super_activity", "description": "优秀圈子主理人"}, {"type": "identity_people", "description": "宠物服务行业 兽医"}], "followers_count": 72174, "is_following": false, "is_followed": false}, "created_time": 1711374823, "updated_time": 1711375392, "voteup_count": 15431, "thanks_count": 1252, "comment_count": 692, "is_copyable": true, "question": {"id": "617767048", "type": "question", "url": "https://api.zhihu.com/questions/617767048", "author": {"id": "019191e5dd70b31e6d7ba596285a6280", "url": "https://api.zhihu.com/people/019191e5dd70b31e6d7ba596285a6280", "user_type": "people", "url_token": "bei-wei-qi-du-22", "name": "北纬七度", "headline": "22岁，秀发已秃", "avatar_url": "https://pic1.zhimg.com/50/v2-5a709cff5a563527b9c5ba0836f68ddf_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 310, "is_following": false, "is_followed": false}, "title": "为什么香港作为世界最发达的城市之一，思想却异常封建（风水，鬼神）？", "created": 1692261780, "answer_count": 0, "follower_count": 0, "comment_count": 59, "bound_topic_ids": [205, 4369, 21506, 22632, 174044], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "thumbnail": "https://picx.zhimg.com/50/v2-0cc8e44e7a83c8762149d9124dafe787_720w.jpg?source=b6762063", "excerpt": "这是一只普通鸽子：   当普通鸽子想要努力多多搞到一些食物的时候： 通常来说，它们会选择积极地早起捕猎，四处寻找掉落的果实等等手段。 但是很快，居住在城市里的鸽子发现了更便捷的方法，比如： 直接去人类手上抢一些现成的薯条:   搞错了，再来：   用抢的始终有被抓到驱赶的风险。 于是聪明的鸽子学会了工作换吃的，比如打乒乓球：   这是著名的行为学家斯金纳训练的鸽子，他通过食物奖励的方式训练两只鸽子打乒乓球，看得出来虽然…", "excerpt_new": "这是一只普通鸽子：   当普通鸽子想要努力多多搞到一些食物的时候： 通常来说，它们会选择积极地早起捕猎，四处寻找掉落的果实等等手段。 但是很快，居住在城市里的鸽子发现了更便捷的方法，比如： 直接去人类手上抢一些现成的薯条:   搞错了，再来：   用抢的始终有被抓到驱赶的风险。 于是聪明的鸽子学会了工作换吃的，比如打乒乓球：   这是著名的行为学家斯金纳训练的鸽子，他通过食物奖励的方式训练两只鸽子打乒乓球，看得出来虽然…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"_IN0qDa3\">这是一只普通鸽子：</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-f5d06acea35e46cf6b4e348572a9b364_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"625\" data-rawheight=\"408\" data-original-token=\"v2-f5d06acea35e46cf6b4e348572a9b364\" data-default-watermark-src=\"https://pic4.zhimg.com/v2-9302a9ef010f92362a0dcd52fd5836a9_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"625\" data-original=\"https://pic1.zhimg.com/v2-f5d06acea35e46cf6b4e348572a9b364_r.jpg\"/></figure><p data-pid=\"E_LM97hh\">当普通鸽子想要努力多多搞到一些食物的时候：</p><p data-pid=\"bmpv0eHs\">通常来说，它们会选择积极地早起捕猎，四处寻找掉落的果实等等手段。</p><p data-pid=\"w9w72rP-\">但是很快，居住在城市里的鸽子发现了更便捷的方法，比如：</p><p data-pid=\"TNF-EAe1\">直接去人类手上抢一些现成的薯条:</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-ecff784fc5f4023a69a6309c19d0c31e_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1060\" data-rawheight=\"1007\" data-original-token=\"v2-ecff784fc5f4023a69a6309c19d0c31e\" data-default-watermark-src=\"https://picx.zhimg.com/v2-fa755587da8634435b01eef5eeeb3255_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1060\" data-original=\"https://pic1.zhimg.com/v2-ecff784fc5f4023a69a6309c19d0c31e_r.jpg\"/></figure><p data-pid=\"0vOyQNKZ\">搞错了，再来：</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-fd01bcedba91a784495abf512d179444_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"950\" data-rawheight=\"642\" data-original-token=\"v2-fd01bcedba91a784495abf512d179444\" data-default-watermark-src=\"https://pica.zhimg.com/v2-cf8da7c5cd3b90c30d4b2ff7dfe4a932_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"950\" data-original=\"https://pica.zhimg.com/v2-fd01bcedba91a784495abf512d179444_r.jpg\"/></figure><p data-pid=\"uxJBLeZV\">用抢的始终有被抓到驱赶的风险。</p><p data-pid=\"Ut4cedPh\">于是聪明的鸽子学会了工作换吃的，比如打乒乓球：</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-277b499d7b380386f59b0c07f1e61212_1440w.gif\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"519\" data-rawheight=\"316\" data-original-token=\"v2-277b499d7b380386f59b0c07f1e61212\" data-thumbnail=\"https://pic1.zhimg.com/v2-277b499d7b380386f59b0c07f1e61212_b.jpg\" data-default-watermark-src=\"https://pic1.zhimg.com/v2-277b499d7b380386f59b0c07f1e61212_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"519\" data-original=\"https://pic1.zhimg.com/v2-277b499d7b380386f59b0c07f1e61212_r.jpg\"/></figure><p data-pid=\"d11urbtE\">这是著名的行为学家斯金纳训练的鸽子，他通过食物奖励的方式训练两只鸽子打乒乓球，看得出来虽然有打假球的行为，但是鸽子还是知道努力打球（工作）就会有食物奖励。</p><p data-pid=\"4pAxvDwe\">现在我们知道普通的环境会有普通的鸽子</p><p data-pid=\"a_S0z616\">现成的物质丰富而且惩罚轻微的环境会有容易抢夺的鸽子</p><p data-pid=\"ioz247ZX\">具有固定奖励系统的环境会造就努力工作的鸽子</p><p data-pid=\"JCqMdBTy\">那么我们怎么造就一只迷信的鸽子呢？</p><p data-pid=\"u6iZnkJI\">答案很简单：</p><h2>随机奖励</h2><p data-pid=\"hzL702sB\">随机奖励简直就是万恶之源</p><p data-pid=\"plbCNwqa\">如果你打游戏，宝箱的固定掉率是一万刀出一个传奇武器，你可能都懒得去玩这么费工费时的游戏。</p><p data-pid=\"XuKePt6X\">但是如果把概率改为万分之一，也就是你可能一万刀才出奖励，也可能第一刀砍下去就马上爆出胜利果实。</p><p data-pid=\"yebjDmke\">你就忽然沉迷了呢~</p><p data-pid=\"_icBk_QZ\">这两个机制一对比，你就会发现游戏厂商为了让你沉迷游戏简直用心险恶。</p><p data-pid=\"di-hODdy\">抽卡也是一样的道理，如果告诉你6480可以买一个角色，你可能都懒得抽，但是如果告诉你64.8就能抽到这个角色，你可能就会试一试，虽然这个概率调整为了百分之一，你要花的钱甚至比6480更多。</p><p data-pid=\"rwUvfh1z\">这就是随机奖励的迷人之处。</p><p data-pid=\"V0JFcK3x\">让我们把目光回到鸽子身上。</p><p data-pid=\"TW7bx64E\">同样是在斯金纳的实验室，这次他又折腾出了一个新点子：</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-7c09db45ca04c8c70c428c1c55fa8bba_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"467\" data-original-token=\"v2-7c09db45ca04c8c70c428c1c55fa8bba\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-25c9912784139993a7a7796cd823312e_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic3.zhimg.com/v2-7c09db45ca04c8c70c428c1c55fa8bba_r.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wo_UYK60\">这次的实验对象是8只鸽子，为了实验效果，还特意饿了它们几天。</p><p data-pid=\"eofYptGS\">通常来说，训练条件反射是根据动物的行为来给与奖励，也就是动物做出了实验者预期的行为，就会得到奖励。</p><p data-pid=\"CDuAZ6DQ\">但是这次情况不同，这次每隔15秒喂食器就会自动掉下来食物，然后放这8只连续几天对这些鸽子在箱子里随处溜达，惊人的结果出现了，8只鸽子里面有6只出现了迷信行为。</p><p class=\"ztext-empty-paragraph\"><br/></p><blockquote data-pid=\"uqLajAgP\">斯金纳在报告中写道：“8只鸽子中的<b>6只产生了非常明显的条件反射</b>，两名观察者得到了完全一致的记录。一只鸽子形成了<b>在箱子中逆时针转圈的条件反射</b>，在距离下一次投食时转2-3圈；另一只<b>反复将头撞向箱子上方的一个角落</b>；第三只鸽子只显现出一种上述反应，<b>把头放在一根看不见的杆下面并反复抬起</b>。还有两只鸽子的头和身体呈现出一种类似摇摆的动作，它们<b>头部前伸，并且从右向左大幅度摇摆，接着再慢慢地转过来，它们的身子也顺势移动，动作幅度过大时还会向前走几步</b>。还有一只鸽子形成了<b>不完整</b>地<b>啄击或轻触的条件反应</b>，动作直冲地面但并不接触。”</blockquote><p data-pid=\"TyLqN39f\">斯金纳把这些行为解释为迷信行为，因为鸽子试图尝试做这些无意义的行为来换取食物，当它们真的得到食物以后这种行为就会得到加强一直加强到它们笃信这些毫无意义的行为能够带来食物奖励。</p><p data-pid=\"JKmzjJhY\">现在有点头绪了吧？</p><p data-pid=\"OmZXwIrE\">发达城市才更容易诞生迷信行为，因为一些人因为顺应环境和时代获得了丰厚的奖励，而他们却选择把这一切归因于他们诚心拜XX的行为。</p><p data-pid=\"f7wJHauJ\">对此我是有深刻感受的，以前一直觉得上海这样的大都市是开放进步文明讲科学的，直到我发现住了好几个小区都有每月定时烧香拜佛的本地人的时候感到相当震惊。</p><p data-pid=\"FGm7fZSR\">就是那种直接每月两次在楼道里面烧纸，这在我们那个小城市都是很少见的行为，这边却非常常见。</p><h2>下面上点强度，供课后思考：</h2><p data-pid=\"somtNWCN\">假设自然界的某种动物，处于食物链中层的位置，现在它的世界出现树木和小草的扰动会有两种情况：</p><p data-pid=\"nJr_eugY\">1草木扰动，无事发生</p><p data-pid=\"jVwFDsD1\">2草木扰动，捕食者出现</p><p data-pid=\"3Uv5HE4v\">在一个时间单位内，草的扰动以<i>概率f</i>发生，树木的运动以<i>概率g</i>发生，捕食者跟随事件的概率分别为<i>p</i>和<i>q</i>。</p><p data-pid=\"e6XJnpLD\">此外，还有一个条件概率，即捕食者在没有任何草木扰动的情况下出现：概率为r。</p><p data-pid=\"3o3YtJSb\">现在我们大概知道草的扰动与捕食者出现的相关性要强于树木扰动与捕食者出现的相关性。</p><p data-pid=\"6oPcbnlM\">如果树木的运动与捕食者的存在没有因果关系，我们有<i>q</i>=<i>r</i>。</p><p data-pid=\"Qwa8rB1D\">现在该动物有逃跑来躲开捕食者的选择（例如，跑下洞穴），但这是以成本<i>c</i>为代价的。</p><p data-pid=\"woeyqiKU\">同时，捕食者在附近时不采取行动意味着死亡风险，<i>b</i>（或相当于在捕食者存在时逃跑的好处），<i>其中 b</i>&gt;<i>c</i>。</p><p data-pid=\"Om96dS0X\">最后，还要考虑听错的情况，比如把草的扰动听成树木的扰动，条件概率<i>为 P</i>（听到扰动2|捕食者出现1 发生）=<i>a</i>21，而概率<i>为 a</i>时，情况正好相反。</p><p data-pid=\"oPraikqW\">然后该动物有四种可能的策略：</p><p data-pid=\"aC6fK8yn\">（i）忽略草木扰动</p><p data-pid=\"wpkNAUQG\">（ii）对草做出反应  </p><p data-pid=\"gwSjwfy5\"> （iii）对树木做出反应</p><p data-pid=\"7pnJXBK3\">（iv）对两者都做出反应</p><p data-pid=\"4FJkNbAk\">建立一种数学模型，得出各种策略的生存概率如图：</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-a312b9c156f3f33e71db22c9b2eaaa5c_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1204\" data-rawheight=\"1280\" data-original-token=\"v2-a312b9c156f3f33e71db22c9b2eaaa5c\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-436d0124274eba257eb0be627648b37c_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1204\" data-original=\"https://pic3.zhimg.com/v2-a312b9c156f3f33e71db22c9b2eaaa5c_r.jpg\"/></figure><p data-pid=\"BEXz9C_L\">在最极端的情况，如果捕食者出现的概率与草木扰动100%相关，那么对两种情况100%不响应的动物死亡概率是100%。</p><p data-pid=\"xPp7y1qY\">由图可以看出，随着捕食者出现与草木扰动相关性的提高，该动物对草木扰动相应的策略就会提高生存概率。</p><p data-pid=\"mh7QUfeq\">尤其是当草木扰动与捕食者出现的相关性大于30%左右的时候，（ii）（iii）（iv）三种策略都会提高生存概率。</p><p data-pid=\"ipaSzdrD\">当风吹草动与捕食者出现的关联性是0的时候，该动物有任何风吹草动马上就跑的行为是迷信行为。</p><p data-pid=\"BT1iItSl\">而一旦这个关联性变成了30%以上，这种迷信行为就变成了一种行之有效的生存策略。</p><p data-pid=\"qpN1idxS\">但是我们学统计的时候可能会听过一个很重要的概念，关联性不等于因果性！！！</p><p data-pid=\"BrlQGnlv\">当一个因果的先前事件不能完全预测后一个事件时，通常可以利用更多的信息，将先前的事件细分为有时是因果事件和一些从来不是因果事件的事件。有了更多的信息，人们可能会更进一步，细分前一组，依此类推。然而，每当行为者无法完全剖析出具有完全因果关系的先前事件时，就会出现一种情况，他们被迫对因果和非因果事件的总和做出反应，或者根本不做出反应，即基于<i>P</i>（较高层次的概率）而不是<i>p</i>和<i>q</i>（较低层次的概率）做出反应。</p><p data-pid=\"35xhFRk7\">因此，在某种程度上，事物之间的关联性可以被视为因果关系和非因果关系的集合体。</p><p data-pid=\"OsX-xUpF\">由此，我们课后题最重要的结论得出来了：</p><h2>存在通过自然选择压力产生的迷信。</h2><p data-pid=\"CTHTCV7H\">我们可以套用“金银天然不是货币，但货币天然是金银”的说法：</p><h2>“迷信不产生自然选择，但是自然选择产生迷信”</h2><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Pytn3Val\">在人类中，由于推理和文化传播的潜力，对因果关系和相关反应的评估达到了最复杂的水平。沿着这些思路，<a href=\"https://link.zhihu.com/?target=https%3A//royalsocietypublishing.org/doi/full/10.1098/rspb.2008.0981%23bib3\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Beck&amp;Forstmeier（2007）</a>最近认为，先前的经验（个人的“世界观”）将在很大程度上影响当前关系是否被认为是真或假。理性与文化之间的这种潜在相互作用在人类农业的曙光与在珠宝中使用绿色珠子的趋势相吻合的观察中显而易见。这可能表明，当农作物变得重要时，人们开始认为绿色图标会带来好运（<a href=\"https://link.zhihu.com/?target=https%3A//royalsocietypublishing.org/doi/full/10.1098/rspb.2008.0981%23bib2\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Bar-Yosef Mayer &amp; Porat 2008</a>）。此外，替代医学具有很强的文化学习成分，似乎经常将无效药物与有效药物归为一类（f=&#34;<a href=\"https://link.zhihu.com/?target=https%3A//royalsocietypublishing.org/doi/full/10.1098/rspb.2008.0981%23bib1\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">royalsocietypublishing.org</span><span class=\"invisible\">/doi/full/10.1098/rspb.2008.0981#bib1</span><span class=\"ellipsis\"></span></a>&#34;&gt;Astin 等人，1998 年）。（这句话禁止联想）</p><p data-pid=\"NCTiyats\">由此我们就可以拼上整个拼图的最后一块：</p><p data-pid=\"H351NVP5\">受传统文化影响的香港社会，如果推定本来迷信的人群跟内地一样，但是随着历史的“进城”，在这批迷信的人之中出现了大量的富豪，甚至是富甲一方级别的人。</p><p data-pid=\"EcLRRfcg\">那么“求神拜佛”与“荣华富贵”的相关性就增强了（注意是相关性，非因果性）。</p><p data-pid=\"i2rp1qVE\">这种情况下，会极大地影响其他人对迷信的看法。</p><p data-pid=\"6HVzmrc_\">就像在刚才的举例当中，如果草木扰动和捕食者出现的相关性达到了100%，那么显然原先被认为迷信的行为就会看起来提高了生存概率。</p><p data-pid=\"cFZqNJi6\">原先看起来的迷信行为好像也确实提高了成为富豪的概率。</p><p data-pid=\"uc3ZfcmP\">以上~</p><p data-pid=\"7dmSBYns\">ref：<a href=\"https://link.zhihu.com/?target=https%3A//royalsocietypublishing.org/doi/full/10.1098/rspb.2008.0981%23fig4\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">迷信和迷信行为的演变 |英国皇家学会会刊B：生物科学 (royalsocietypublishing.org)</a></p><p></p><p></p><p></p><p></p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 907213, "thumbnails": ["https://picx.zhimg.com/50/v2-0cc8e44e7a83c8762149d9124dafe787_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-5188e77b84aefc88ae91c09afe379411_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-d9495cae6d3401cdf8ddfa724482293f_720w.jpg?source=b6762063"], "favorite_count": 11178, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 3443242058}", "attached_info": "CqcICO7fqYPgk/SJuwEQBBoJNjU2MTE3MjE0IOeDhrAGKMd4MLQFQDFKMAobVFNfU09VUkNFX0JBU0lDX0lORk9fUkVDQUxMEgEwGAAgADoKeyJyYXciOiIifVoIOTk1NjE3NThiIDU5MDE5OTM4OTdiMmI2NzE1MzRiNDFkZmUxNzAzOTQ3cgozNDQzMjQyMDU4igEJNjE3NzY3MDQ4qgEJcmVjb21tZW5kwgEgMTgwNTA1YmI0NWUxMjg2MGYyY2ZjZjVkOTYyMzQxZDbyAQoIDBIGTm9ybWFs8gEoCAoSJDhhOGRjZDgyLTg1OTMtNGJhNi05OWU5LTZkNTlkOTg4Njg0ZfIBBQgLEgE5ggIAiALBkLnN+jKSAiAxODA1MDViYjQ1ZTEyODYwZjJjZmNmNWQ5NjIzNDFkNpoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhZBY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIbSW50ZXJhY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIYUGVyaW9kSW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxlygIUQ29udGVudEFnZVdlaWdodFJ1bGXaAhtUU19TT1VSQ0VfQkFTSUNfSU5GT19SRUNBTEzoAgT6AgtOT1JNQUxfRkxPV4oDIGMzMDQzNGE0ZmFlMzQ4YWZiNDlhY2NkYjJkZjY1ZjU5mgMNCgJ2MhAAGgVvdGhlcqgDza832AMA6gMRYmFzaWNfaW5mb19yZWNhbGz6A7kCEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERTotCAMQ8QQYmAMiI3YyLWY1ZDA2YWNlYTM1ZTQ2Y2Y2YjRlMzQ4NTcyYTliMzY0Oi0IAhCkCBjvByIjdjItZWNmZjc4NGZjNWY0MDIzYTY5YTYzMDljMTlkMGMzMWU6LQgDELYHGIIFIiN2Mi1mZDAxYmNlZGJhOTFhNzg0NDk1YWJmNTEyZDE3OTQ0NDotCAAQhwQYvAIiI3YyLTI3N2I0OTlkN2IzODAzODZmNTliMGMwN2YxZTYxMjEyOi0IAxCABRjTAyIjdjItN2MwOWRiNDVjYTA0YzhjNzBjNDI4YzFjNTVmYThiYmE6LQgCELQJGIAKIiN2Mi1hMzEyYjljMTU2ZjNmMzNlNzFkYjIyYzliMmVhYWE1Y4AEAIgEAJIEBk5vcm1hbJoEATSgBACoBACwBAC6BAZtYW51YWzCBAMxNjDIBADSBA/mjqjojZDlt7Lmm7TmlrDYBADwBAD5BAAAAKDMZro/gQUAAAAAAAAAAIkFkslO6rCP0j+SBQCaBQNkZnSiBQNkZnSyBQExuQUAAAAAAAAAANAFAOAFAOgFAPAFCZAGAKAGNagGA5ICJQoJNjU2MTE3MjE0EgozNDQzMjQyMDU4GAQiCklNQUdFX1RFWFQ=", "action_card": false}, {"id": "50_1750898460.829", "type": "feed", "offset": 50, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898460, "updated_time": 1750898460, "target": {"id": "1921075471627563105", "type": "answer", "url": "https://api.zhihu.com/answers/1921075471627563105", "author": {"id": "770ac5d857436394529bed656ea1f7c8", "url": "https://api.zhihu.com/people/770ac5d857436394529bed656ea1f7c8", "user_type": "people", "url_token": "bai-zhan-gui-lai-zai-du-shu-23", "name": "阿凯财富论", "headline": "创业， 始于梦想， 死于常识。\n\n公号：【阿凯财富论】\n\n", "avatar_url": "https://pica.zhimg.com/50/v2-8adc0731233f4ab02311a283b214a61e_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 2266, "is_following": false, "is_followed": false}, "created_time": 1750800084, "updated_time": 1750800084, "voteup_count": 22, "thanks_count": 2, "comment_count": 3, "is_copyable": true, "question": {"id": "620696051", "type": "question", "url": "https://api.zhihu.com/questions/620696051", "author": {"id": "da9ac49cb09cc46c71716d8eeab80fb8", "url": "https://api.zhihu.com/people/da9ac49cb09cc46c71716d8eeab80fb8", "user_type": "people", "url_token": "zxb626", "name": "智乎心理", "headline": "《独处一个人时的思考》作者", "avatar_url": "https://pic1.zhimg.com/50/v2-5796c24051ad89bc128416133bf739a7_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 1305, "is_following": false, "is_followed": false}, "title": "创业最大的忌讳是啥？", "created": 1693954876, "answer_count": 0, "follower_count": 0, "comment_count": 2, "bound_topic_ids": [4597, 48214, 2244514], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "很多人会告诉你， 创业最大的忌讳是： 没钱， 没人脉， 没资源， 没经验。 这些， 都对， 但都只是“术”的层面。 是症状， 不是病根。 就像一个人发烧了， 你说他最大的问题是“体温高”， 废话， 但没用。 你得知道他为什么发烧， 是病毒感染， 还是细菌感染。创业， 也是一样。 那些让你死掉的， 往往不是这些看得见的“拦路虎”， 而是一些看不见的， 长在你脑子里的“肿瘤”。 这些“肿瘤”， 会在你最关键的时候， 给你下…", "excerpt_new": "很多人会告诉你， 创业最大的忌讳是： 没钱， 没人脉， 没资源， 没经验。 这些， 都对， 但都只是“术”的层面。 是症状， 不是病根。 就像一个人发烧了， 你说他最大的问题是“体温高”， 废话， 但没用。 你得知道他为什么发烧， 是病毒感染， 还是细菌感染。创业， 也是一样。 那些让你死掉的， 往往不是这些看得见的“拦路虎”， 而是一些看不见的， 长在你脑子里的“肿瘤”。 这些“肿瘤”， 会在你最关键的时候， 给你下…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"PGexNDWs\">很多人会告诉你，<br/>创业最大的忌讳是：<br/>没钱，<br/>没人脉，<br/>没资源，<br/>没经验。<br/>这些，<br/>都对，<br/>但都只是“术”的层面。<br/>是症状，<br/>不是病根。<br/>就像一个人发烧了，<br/>你说他最大的问题是“体温高”，<br/>废话，<br/>但没用。<br/>你得知道他为什么发烧，<br/>是病毒感染，<br/>还是细菌感染。</p><p data-pid=\"tPrXT2IZ\">创业，<br/>也是一样。<br/>那些让你死掉的，<br/>往往不是这些看得见的“拦路虎”，<br/>而是一些看不见的，<br/>长在你脑子里的“肿瘤”。<br/>这些“肿瘤”，<br/>会在你最关键的时候，<br/>给你下达一个错误的指令，<br/>然后，<br/>满盘皆输。</p><p data-pid=\"i-L2L9uO\">我见过太多倒下的创业者，<br/>他们不是死于市场的残酷，<br/>而是死于自己的“想当然”。<br/>如果非要说最大的忌讳，<br/>那只有一个，<br/>所有忌讳的根源，<br/>就是这一个：<br/><b>用“自我感动”的勤奋，</b><br/><b>去掩盖“战略思考”的懒惰。</b></p><p data-pid=\"-SkRTdyc\">这句话，<br/>你反复读三遍。<br/>读不懂，<br/>你创一次，<br/>死一次。<br/>读懂了，<br/>能让你少走十年弯路，<br/>少亏几百万。</p><p data-pid=\"ISqsxGiM\"><b><u>爱上你的“孩子”，而不是“用户”</u></b></p><p data-pid=\"aotEI3ie\">这是99%的初创者，<br/>尤其是技术出身或者产品出身的创业者，<br/>最容易犯的致命错误。<br/>他们把自己的“产品”或者“想法”，<br/>当成了自己的“亲生孩子”。</p><p data-pid=\"iQ3ghdbt\">他们会花上一年半载，<br/>躲在一个小黑屋里，<br/>呕心沥血，<br/>把这个“孩子”打磨得“尽善尽美”。<br/>他们会给它设计最漂亮的“衣服”（UI界面），<br/>给它加上各种酷炫的“玩具”（功能），<br/>他们觉得，<br/>自己的孩子，<br/>是全世界最聪明，<br/>最漂亮，<br/>最独一无二的。</p><p data-pid=\"I3fud99p\">然后，<br/>他们满怀期待地，<br/>把这个“完美的孩子”，<br/>推向市场。<br/>他们觉得，<br/>所有人都会像他们一样，<br/>爱上这个孩子。<br/>结果呢？<br/>市场冷冷地看了他一眼，<br/>说：<br/>“这谁家的丑孩子？<br/>我不需要。”</p><p data-pid=\"6q0TU4Wi\">这个时候，<br/>创业者就崩溃了。<br/>他无法接受这个事实。<br/>他会觉得：<br/>“是用户太傻逼，<br/>他们不懂得欣赏我的艺术。”<br/>“是市场太浮躁，<br/>他们只喜欢那些肤浅的东西。”<br/>他会继续花钱，<br/>去教育市场，<br/>去告诉用户，<br/>“你们应该爱我的孩子”。<br/>直到，<br/>把最后一分钱烧光，<br/>关门大吉。</p><p data-pid=\"dvbeS7h5\">你看，<br/>他从头到尾，<br/>都活在自己的世界里。<br/>他爱的是“创造”本身，<br/>是那个“从无到有”的成就感。<br/>他不是在做生意，<br/>他是在搞艺术创作。<br/>他是在“自我表达”，<br/>而不是在“解决问题”。</p><p data-pid=\"BI9Z3YPA\">一个真正懂生意的人，<br/>他从来不爱自己的“产品”。<br/><b>他只爱用户的“问题”。</b></p><p data-pid=\"5AnJwfwI\">他的思考路径，<br/>完全是反过来的。<br/>他会先去找到一群“嗷嗷待哺”的人，<br/>去听他们到底“饿”在哪里。<br/>是饭太难吃？<br/>是送得太慢？<br/>还是价格太贵？<br/>他会先用最简单，<br/>最粗糙，<br/>甚至最丑陋的方式，<br/>给他们端上一碗“能勉强填饱肚子”的饭。<br/>可能就是一个微信群，<br/>一份Excel表格，<br/>一个手写的菜单。</p><p data-pid=\"UqhGR1wj\">然后，<br/>他会蹲在旁边，<br/>看这群人是怎么吃饭的。<br/>他们是狼吞虎咽，<br/>还是吃两口就吐了？<br/>他们会抱怨饭太咸，<br/>还是会夸奖菜很新鲜？<br/>根据这些最真实的“反馈”，<br/>他再回去，<br/>一点一点地，<br/>改进他的“厨房”和“菜单”。</p><p data-pid=\"Ijjg2hsG\">记住，<br/>创业，<br/>不是“我有一个牛逼的想法，<br/>现在就差一个程序员了”。<br/>而是“<b>我发现了一个很痛苦的问题，</b><br/><b>现在我用最土的办法，</b><br/><b>先帮一个人解决看看</b>”。<br/>先有用户，<br/>再有产品。<br/>先有需求，<br/>再有功能。<br/>这个顺序，<br/>一旦搞反，<br/>神仙都救不了你。</p><p data-pid=\"Mg9iXyA-\"><b><u>追求“完美的准备”，错过“肮脏的开始”</u></b></p><p data-pid=\"fMax3xJJ\">这是第二种常见的死法。<br/>这种人，<br/>是典型的“好学生”和“计划控”。<br/>他们觉得，<br/>创业是一场大考。<br/>必须要把所有的知识点都复习到，<br/>把所有的公式都背下来，<br/>才能上考场。</p><p data-pid=\"FLqk7sxK\">他们会花半年时间，<br/>写一份长达100页的“商业计划书”。<br/>里面从市场分析，<br/>到竞品调研，<br/>到财务预测，<br/>应有尽有，<br/>看起来无懈可击。<br/>他们会花三个月时间，<br/>去考察办公室的选址，<br/>对比几十家供应商的报价。<br/>他们会花无数个夜晚，<br/>去纠结公司的logo，<br/>是用蓝色还是用绿色。</p><p data-pid=\"OO2hbKnx\">他们看起来，<br/>非常勤奋，<br/>非常努力，<br/>非常专业。<br/>但他们所有的这些“勤奋”，<br/>都只是在“准备区”里打转。<br/>他们迟迟不敢迈出那最关键的一步：<br/><b>去跟你的第一个真实用户，</b><br/><b>收第一分钱。</b></p><p data-pid=\"UkyjHfhV\">为什么不敢？<br/>因为他们害怕“失败”，<br/>害怕“不完美”，<br/>害怕“失控”。<br/>在他们的计划里，<br/>一切都是完美的，<br/>可控的。<br/>但真实的商业世界，<br/>是一个混乱的，<br/>肮脏的，<br/>充满意外的“菜市场”。<br/>那里没有PPT，<br/>只有赤裸裸的人性，<br/>和血淋淋的交易。<br/>他们对这种“混乱”，<br/>充满了恐惧。</p><p data-pid=\"XScBImwU\">所以，<br/>他们宁愿在“准备”这个动作里，<br/>获得一种“虚假”的掌控感和成就感。<br/>他们用战术上的勤奋（写PPT，<br/>做调研），<br/>来掩盖战略上的懒惰（不敢面对真实的市场）。</p><p data-pid=\"BlPw3IY1\">而当他们终于觉得“准备好了”的时候，<br/>市场早就变了。<br/>他们调研的竞品，<br/>可能已经死了。<br/>他们预测的趋势，<br/>可能已经被证伪了。<br/>他们就像一个在岸上，<br/>把所有游泳姿势都练得滚瓜烂熟的人，<br/>等他终于下水的时候，<br/>发现泳池里的水，<br/>早就抽干了。</p><p data-pid=\"V1U6ybWE\">创业最大的忌讳之一，<br/>就是“等”。<br/>等一个完美的时机，<br/>等一个完美的团队，<br/>等一个完美的产品。<br/>这个世界上，<br/>根本没有“完美”。<br/>所有牛逼的公司，<br/>所有成功的生意，<br/>都有一个“肮脏”的，<br/>“丑陋”的，<br/>甚至“可笑”的开始。</p><p data-pid=\"e5bDzKHA\">亚马逊一开始，<br/>只是贝索斯在他家车库里，<br/>卖几本旧书。<br/>Facebook一开始，<br/>只是扎克伯格为了给哈佛的女生打分，<br/>做的一个简陋网页。<br/><b>你的第一个版本，</b><br/><b>就应该是“丑”的。</b><br/>如果你对你的第一个版本感到骄傲，<br/>那说明你上线得太晚了。<br/>你需要用那个“丑陋”的版本，<br/>去换回市场最真实的，<br/>哪怕是骂你的“反馈”。<br/>这个反馈，<br/>比你关起门来写的任何商业计划书，<br/>都珍贵一百万倍。</p><p data-pid=\"cRlEQIwv\"><b><u>迷信“颠覆式创新”，无视“寄生式成长”</u></b></p><p data-pid=\"dqhpFuQv\">这是很多有野心，<br/>有情怀的创业者，<br/>最华丽的一种死法。<br/>他们一上来，<br/>就想搞个大新闻。<br/>“我要做一个平台，<br/>颠覆淘宝！”<br/>“我要做一个系统，<br/>取代微信！”<br/>“我要重新定义这个行业！”</p><p data-pid=\"noEBR_bl\">这种“颠覆”的情结，<br/>听起来很燃，<br/>很性感。<br/>但对于一个没有任何根基的初创公司来说，<br/>这不叫“梦想”，<br/>这叫“妄想”。</p><p data-pid=\"w2bZ9c7z\">你一个一无所有的小虾米，<br/>要去挑战一个武装到牙齿的巨无霸。<br/>你凭什么？<br/>凭你的激情？<br/>凭你那点可怜的本金？<br/>巨无霸只需要动动小指头，<br/>就能把你碾成粉末。<br/>它可以用钱砸死你，<br/>可以用流量淹死你，<br/>可以用它已有的用户网络，<br/>直接把你隔离在外。</p><p data-pid=\"UWPNdPe2\">对于一个初创者来说，<br/>最聪明的生存策略，<br/>不是去“对抗”巨头，<br/>而是去“<b>寄生</b>”在巨头的生态里。</p><p data-pid=\"NENakfb_\">把巨头，<br/>当成你的“基础设施”，<br/>你的“能量来源”。<br/>找到巨头生态里，<br/>那些它“看不上”，<br/>“做不好”，<br/>或者“没空做”的“缝隙”。<br/>然后，<br/>像一棵藤蔓一样，<br/>悄悄地，<br/>从这个缝隙里长出来。</p><p data-pid=\"46W83R_a\">举个例子。<br/>微信，<br/>是个巨大的生态吧？<br/>你想做一个比微信更好的社交软件，<br/>死路一条。<br/>但是，<br/>你可以做什么？</p><ul><li data-pid=\"llwjzxfw\"><b>你可以做“微信表情包”的设计和制作。</b><br/>这是一个微信自己不会去精细化运营，<br/>但用户又有巨大需求的市场。</li><li data-pid=\"bok1XWZg\"><b>你可以做“微信公众号排版”的工具。</b><br/>微信自带的编辑器很烂，<br/>这就是你的机会。</li><li data-pid=\"zohSH7TX\"><b>你可以做一个“微信小程序”的开发服务商。</b><br/>专门帮那些想做小程序但又不懂技术的实体店老板，<br/>提供解决方案。</li><li data-pid=\"h0G7pGBc\"><b>你可以做一个专门研究“微信视频号”玩法的社群。</b><br/>微信提供平台，<br/>你提供“攻略”。</li></ul><p data-pid=\"0zz-Y9yQ\">你看，<br/>你没有去挑战微信。<br/>你是在微信这棵大树底下，<br/>乘凉，<br/>捡果子。<br/>你利用它的流量，<br/>利用它的用户，<br/>利用它的支付体系，<br/>来完成你自己的商业闭环。<br/>你从它身上，<br/>吸取养分，<br/>慢慢长大。<br/>等你长得足够强壮了，<br/>你才有可能，<br/>去考虑，<br/>要不要自己也长成一棵树。</p><p data-pid=\"zxGCZ7zH\"><b>先当孙子，</b><br/><b>再当爷。</b><br/>这是所有白手起家的创业者，<br/>都必须遵守的铁律。<br/>一上来就想当爷的，<br/>最后都成了孤魂野鬼。</p><hr/><p data-pid=\"PrVQgMCF\"><b>所以，</b><br/><b>回到最初的问题。</b></p><p data-pid=\"06hGfl4l\">创业最大的忌讳是什么？<br/><b>是错把“情怀”当“能力”，</b><br/><b>错把“准备”当“行动”，</b><br/><b>错把“对抗”当“策略”。</b><br/>本质上，<br/>都是一种“想得太多，<br/>懂得太少”的自我麻痹。</p><p data-pid=\"PVixqwlq\">你不需要一个惊天动地的想法。<br/>你需要的，<br/>是一个能解决身边三五个人真实痛苦的，<br/>小小的，<br/>甚至有点笨拙的解决方案。<br/>然后，<br/>走出去，<br/>把它卖给他们，<br/>收回你的第一块钱。<br/>这个动作，<br/>比你看一万篇创业干货，<br/>都有用。</p><p data-pid=\"tKg3nYJH\">道理都说明白了，<br/>很多人可能还是会觉得，<br/>脑子清楚了，<br/>但手脚还是不知道该怎么动。</p><p data-pid=\"Y7XK0aPa\">知道要避免忌讳了，<br/>但那个正确的，<br/>能赚到第一块钱的“起手式”，<br/>到底该怎么做？</p><p data-pid=\"_Wmul9vB\">我懂。</p><p data-pid=\"Vwf4dilt\">所以我把我当年怎么从一个纯小白开始，<br/>怎么用最笨的方法赚到第一笔钱，<br/>整个从0到1（当时我比穷光蛋还穷），<br/>踩过哪些坑，<br/>用了哪些土办法，<br/>一五一十地复盘了一遍。</p><p data-pid=\"TkGUpXRA\">没有一句废话，<br/>全是能直接上手干的步骤。</p><p data-pid=\"PXYLXjKQ\">这篇文章叫，<br/><b>阿凯：《 互联网创业之：第一桶金&amp;0-1实操方法论》</b>。</p><p data-pid=\"tK-PsBZV\">放在了我的公号：<b>阿凯财富论</b>。</p><p data-pid=\"RX9qftV3\">过去之后，<br/>对话框里发“<b>第一桶金</b>”这三个字，<br/>就会自动发给你。</p><p data-pid=\"J4KKA4ok\">看完理论，<br/>再看这份真实的施工图纸，<br/>你才知道该怎么干。</p><p data-pid=\"X9f-a1cg\"><b>创业不是一场表演，</b><br/><b>而是一场交易。</b></p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 1391, "favorite_count": 61, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1921075471627563105}", "attached_info": "CqMGCO7fqYPgk/SJuwEQBBoJNzMzODY0NDU2INSt7MIGKBYwA0AySigKE1RTX1NPVVJDRV9GRUVEUkVfVjkSATAYACAAOgp7InJhdyI6IiJ9SkEKLFRTX1NPVVJDRV9UV09UT1dFUl9TSE9SVElOVEVSRVNUX1JFQ0FMTF9URVhUEgEwGAAgADoKeyJyYXciOiIifVoJMTAwMjEyMTM5YiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3ITMTkyMTA3NTQ3MTYyNzU2MzEwNYoBCTYyMDY5NjA1MaoBCXJlY29tbWVuZMIBIDc3MGFjNWQ4NTc0MzYzOTQ1MjliZWQ2NTZlYTFmN2M48gEKCAwSBk5vcm1hbPIBKAgKEiRkZDk5NzBkMS1jZmYyLTQyNGEtYWQzOS1kM2FmNzc3NWY3YjXyAQUICxIBOYICAIgCwZC5zfoykgIgNzcwYWM1ZDg1NzQzNjM5NDUyOWJlZDY1NmVhMWY3YziaAgDKAhZTaG9ySW50ZXJlc3RXZWlnaHRSdWxlygIWQWN0aW9uU2hvckludGVyZXN0UnVsZcoCG0ludGVyYWN0aW9uU2hvckludGVyZXN0UnVsZcoCGFBlcmlvZEludGVyZXN0V2VpZ2h0UnVsZcoCFVVzZXJMY25FeGl0V2VpZ2h0UnVsZdoCE1RTX1NPVVJDRV9GRUVEUkVfVjnoAgL6AgtOT1JNQUxfRkxPV4oDIGMzMDQzNGE0ZmFlMzQ4YWZiNDlhY2NkYjJkZjY1ZjU5mgMNCgJ2MhAAGgVvdGhlcqgD7wrYAwDqAwlmZWVkcmVfdjn6Ax8SDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFgAQAiAQAkgQGTm9ybWFsmgQBMqAEAKgEALAEALoEAmFpwgQDNDAwyAQA0gQP5o6o6I2Q5bey5pu05paw2AQA8AQA+QQAAAAg5OzBP4EFAAAAAAAAAACJBZLJTuqwj9I/kgUAmgUDZGZ0ogUDZGZ0sgUBMbkFAAAAAAAAAADQBQDgBQDoBQDwBQmQBgCgBjaoBgCSAi4KCTczMzg2NDQ1NhITMTkyMTA3NTQ3MTYyNzU2MzEwNRgEIgpJTUFHRV9URVhU", "action_card": false}, {"id": "51_1750898460.741", "type": "feed", "offset": 51, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898460, "updated_time": 1750898460, "target": {"id": "2828927833", "type": "answer", "url": "https://api.zhihu.com/answers/2828927833", "author": {"id": "cfe0097a6e9efb2537c9503761f72d7d", "url": "https://api.zhihu.com/people/cfe0097a6e9efb2537c9503761f72d7d", "user_type": "people", "url_token": "joss-67-60", "name": "对上班过敏的乔乔", "headline": "我脑袋里的怪东西……", "avatar_url": "https://pic1.zhimg.com/50/v2-5453a5290b3151e53d3263436a97fbf2_l.jpg?source=b6762063", "is_org": false, "gender": 0, "followers_count": 11623, "is_following": false, "is_followed": false}, "created_time": 1672803016, "updated_time": 1673435074, "voteup_count": 14195, "thanks_count": 2181, "comment_count": 542, "is_copyable": true, "question": {"id": "508264893", "type": "question", "url": "https://api.zhihu.com/questions/508264893", "author": {"id": "a7459d513fcf58ed9816f94875f811cf", "url": "https://api.zhihu.com/people/a7459d513fcf58ed9816f94875f811cf", "user_type": "people", "url_token": "zhong-hua-li-jian-55", "name": "中华利剑", "headline": "热爱音乐、军事、历史、影视等。", "avatar_url": "https://pica.zhimg.com/50/v2-50ff87b9d214e888809b7a624c48fdfd_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 40, "is_following": false, "is_followed": false}, "title": "如何评价埃隆·马斯克这个人？", "created": 1640414469, "answer_count": 0, "follower_count": 0, "comment_count": 76, "bound_topic_ids": [570, 922, 962, 2143, 70042], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "非常可怕。 去年上了个风投的课，讲师提到一句话： 近20年推出的最新的东西，才是近20年最赚钱的。（这句话听起来好像平平无奇，展开了说深意很多。） 想想上个二十年的暴利行业是啥？ ——房地产、互联网、手机、电商...... 其实这句话也隐含了以下的意思： 挣钱的行业也就蓬勃差不多二十来年，你看到什么东西搞的最如火如荼的时候，说明其寿数也将近，即将开始走下坡路。站着2023年的门槛前，还往互联网、房地产里跳，无论是个…", "excerpt_new": "非常可怕。 去年上了个风投的课，讲师提到一句话： 近20年推出的最新的东西，才是近20年最赚钱的。（这句话听起来好像平平无奇，展开了说深意很多。） 想想上个二十年的暴利行业是啥？ ——房地产、互联网、手机、电商...... 其实这句话也隐含了以下的意思： 挣钱的行业也就蓬勃差不多二十来年，你看到什么东西搞的最如火如荼的时候，说明其寿数也将近，即将开始走下坡路。站着2023年的门槛前，还往互联网、房地产里跳，无论是个…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"3medf3Or\"><b>非常可怕。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"DyDf6sVK\">去年上了个风投的课，讲师提到一句话：</p><p data-pid=\"Lbx4EN0k\"><b>近20年推出的最新的东西，才是近20年最赚钱的。</b></p><p data-pid=\"yAoYgdtJ\">（这句话听起来好像平平无奇，展开了说深意很多。）</p><p data-pid=\"9xzjBSAs\">想想上个二十年的暴利行业是啥？</p><p data-pid=\"PkI5zQ5A\">——房地产、互联网、手机、电商...... </p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"_Hn0-DwT\">其实这句话也隐含了以下的意思：</p><p data-pid=\"7-Ev1i6v\"><b>挣钱的行业也就蓬勃差不多二十来年，你看到什么东西搞的最如火如荼的时候，说明其寿数也将近，即将开始走下坡路。</b></p><p data-pid=\"iAnbh1XR\">站着2023年的门槛前，还往互联网、房地产里跳，无论是个人还是企业行为，都是懒惰又冒险的。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"2L1UVPxA\">我国的经济模式、商品模式、设备、家电、汽车、飞机、大航母，基本都是别人走过的路，跟别人学来的。可能玩的比别人更好，但是罕有全新的创新。</p><p data-pid=\"6uDgIPR4\">马斯克不管风评如何，现阶段投资成不成功，<b>他的可怕之处在于他一直在走全新的路，让别人玩他玩剩下的</b>——</p><p data-pid=\"owouZqyt\">1995年马斯克和哥哥一起创办Zip2（百度地图 + 大众点评），1999年康柏收购Zip2。</p><p data-pid=\"CbNCtvBf\">1999年马斯克与他人共同创立<a href=\"https://link.zhihu.com/?target=http%3A//X.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">X.com</span><span class=\"invisible\"></span></a>电子支付公司（在线银行），2000年<a href=\"https://link.zhihu.com/?target=http%3A//X.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">X.com</span><span class=\"invisible\"></span></a>收购Confinity，后来被合并成了PayPal，2022年由eBay公司收购。</p><p data-pid=\"oBr-m3ab\">2002年马斯克创立SpaceX（太空探索技术公司）。</p><p data-pid=\"JTU-6Zri\">2004年马斯克投资Tesla（纯电动车公司）并出任董事长。</p><p data-pid=\"mL7S-vZd\">2006年马斯克创立SolarCity（太阳能光伏发电公司）。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"CQ8kS7Kh\">想想以往的外资车企，一汽大众、上汽通用、广汽丰田、长安铃木、海南马自达，都是地方国企 + 外资的模式，市场换技术。</p><p data-pid=\"jx7yKw0J\">而特斯拉承诺没有核心保密技术，开放所有专利，使用中国员工，哪怕哪天离开中国了，也保证留下一条完整的汽车制造链。</p><p data-pid=\"5CV_ay4T\">他获得了政府的全力支持，独资，像一条巨型鲇鱼一样，进入这个池子就走上了一条前无古人、目前也没有来者的快速扩张鲸吞之路，冲击、挤压汽车原生态产业，并有摆脱监管的趋势。</p><p data-pid=\"KvZFToFB\">它用全力奔跑、独孤求败的方式来保持行业标杆地位，每天淘汰的是昨天的自己，而不是关注竞争者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"OOKPYyVs\">而SpaceX，2008年成功发射火箭。</p><p data-pid=\"x797zzSx\">以前卫星不是普通公司可以玩的，他不仅能玩，而且还玩的很好，还承包了政府的项目。</p><p data-pid=\"liEgDQx2\">如今太空正在布满“星链”网络，接下来的一两年将有多达40-50万颗卫星。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"7DZ1KHU_\">他根本不跟人竞争，永远在玩最新的东西。</p><p data-pid=\"czlLqorQ\"><b>当全新的、未知的、未有的战略资产，在你还没来得及意识到的某一天，突然之间变成社会中不可或缺的一块儿的时候，就能成功制造新刚需、新游戏规则，全盘通吃。</b></p><p data-pid=\"6Cfe3S1d\">你还拿它没有办法。</p><hr/><p data-pid=\"cjUDmAOm\">哎呀，大概是被哪个大佬点赞了。</p><p data-pid=\"Iq2QQBVF\">那天刷到这个问题，我确实有感，随手把这篇写了。</p><p data-pid=\"g7QvzjmV\">我的回答一般都是在坐地铁或者等待，还有带薪出恭入敬的时候写写，比较随意，很多地方确实不严谨（我承认他几几年玩了啥公司是我在知网上copy的，我不是他粉丝记不住）。</p><p data-pid=\"wKr_ffzf\">但是想法大致就是这么个意思，现在也不想修了，你们既然点赞了我就当你们都意会到了。</p><p data-pid=\"k1CcUWaZ\">谢谢给了个“专业”，受宠若惊，说明大佬穿过我随意又崎岖的文字读懂了我的意思，比我更专业。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"DJE3M9pJ\">房地产这玩意作为“生意”，确实几千年来就有（牙行），西周就有土地交易，战国就有房屋买卖。</p><p data-pid=\"47Q7_lAE\">但是小农经济里，规模上能形成“房地产行业”的，寥寥无几。</p><p data-pid=\"hdEYyBXu\">比如鸦片战争后出现过房地产行业，只在租界，定义行业规则和投资的，全是外资（洋行）。</p><p data-pid=\"0wFxeoVS\">建国以后的房地产行业真正开始，是要到1998年取消福利分房，中央发布《关于进一步深化城镇住房制度改革，加快住房建设的通知》——这时候才有高度的房地产市场化。</p><p data-pid=\"8neQXaum\">近代史的＂租界＂，和现在的＂商品房＂，你可以说都是房地产行业，但肯定不是一个东西。</p><p data-pid=\"XpZqFwlo\">现在的这个房地产行业，就是以前从来没有的。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"U21llN6x\">类似的道理，你说别提互联网，互联网好好的，你不懂。</p><p data-pid=\"bSIqUOgf\">我国94年正式接入互联网，每个阶段最挣钱的东西都不一样——浏览器、门户网站、搜索引擎、网游、社交软件、桌面应用、媒体平台、电子商务、微视频、直播、知识付费、内容生态......</p><p data-pid=\"XlNrURge\">＂互联网＂这个瓶子，本身就在不停的换新酒寻找出路。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"XOt-eWfh\">一个新行业的标志是＂别人都没有，我先有了＂。</p><p data-pid=\"vF0yeu2c\">一个新行业将爆发的标志是＂别人都没有，我先有了，嘿嘿，大家都想要＂。</p><p data-pid=\"eSpYOw6W\">一个行业饱和的标志是＂别人有，那我也要有＂。</p><p data-pid=\"nkor9MMu\">一个行业衰老的标志是＂大家都有，我要更快更好＂。</p><p data-pid=\"sKSe1T_g\">我跟你聊互联网聊的不是哪个具体技术、产品、公司，我跟你聊的是整体行业。</p><p data-pid=\"2fSziIoc\">你仔细看看从web 1.0的门户时代，到现在的互联网＋......创业是不是越来越艰难？工作是不是越来越饱和？你觉得互联网现在处于哪个状态？</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"jtDeS9Nj\">20年也是只是个抽象的虚指，就跟＂三千烦恼丝＂一样，没必要真的细抠到底多少根儿，细抠反而变味儿，才三千根这家伙就是个秃子。</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 1295250, "favorite_count": 11121, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 2828927833}", "attached_info": "CrcGCO7fqYPgk/SJuwEQBBoJNTQ0NDM1NjI1IMjl050GKPNuMJ4EQDNKQQosVFNfU09VUkNFX1RXT1RPV0VSX1NIT1JUSU5URVJFU1RfUkVDQUxMX1RFWFQSATAYACAAOgp7InJhdyI6IiJ9Wgg3NTIyOTM5NWIgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyCjI4Mjg5Mjc4MzOKAQk1MDgyNjQ4OTOqAQlyZWNvbW1lbmTCASBjZmUwMDk3YTZlOWVmYjI1MzdjOTUwMzc2MWY3MmQ3ZPIBCggMEgZOb3JtYWzyASgIChIkZTkxMmJlMjktZmQwZC00ZmViLTk4NGItNTQ4Mjc1ODQ1OTVi8gEFCAsSATmCAgCIAsGQuc36MpICIGNmZTAwOTdhNmU5ZWZiMjUzN2M5NTAzNzYxZjcyZDdkmgIAygIWU2hvckludGVyZXN0V2VpZ2h0UnVsZcoCFkFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhtJbnRlcmFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhhQZXJpb2RJbnRlcmVzdFdlaWdodFJ1bGXKAhVVc2VyTGNuRXhpdFdlaWdodFJ1bGXKAhRDb250ZW50QWdlV2VpZ2h0UnVsZdoCLFRTX1NPVVJDRV9UV09UT1dFUl9TSE9SVElOVEVSRVNUX1JFQ0FMTF9URVhU6AIC+gILTk9STUFMX0ZMT1eKAyBjMzA0MzRhNGZhZTM0OGFmYjQ5YWNjZGIyZGY2NWY1OZoDDQoCdjIQABoFb3RoZXKoA5KHT9gDAOoDGmZlZWRfYXR0bV90d290b3dlcl92Ml90ZXh0+gMfEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERYAEAIgEAJIEBk5vcm1hbJoEATKgBACoBACwBAC6BAZtYW51YWzCBAMxNjDIBADSBA/mjqjojZDlt7Lmm7TmlrDYBADwBAD5BAAAAKAnq84/gQUAAAAAAAAAAIkFkslO6rCP0j+SBQCaBQNkZnSiBQNkZnSyBQExuQUAAAAAAAAAANAFAOAFAOgFAPAFCZAGAKAGN6gGAJICJQoJNTQ0NDM1NjI1EgoyODI4OTI3ODMzGAQiCklNQUdFX1RFWFQ=", "action_card": false}, {"id": "52_1750898460.502", "type": "feed", "offset": 52, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898460, "updated_time": 1750898460, "target": {"id": "1921155901240349522", "type": "article", "url": "https://api.zhihu.com/articles/1921155901240349522", "author": {"id": "646e30948d93aa29e766eb4a6b11a1ba", "url": "https://api.zhihu.com/people/646e30948d93aa29e766eb4a6b11a1ba", "user_type": "people", "url_token": "kevin-71-87-23", "name": "数据与AI爱好者", "headline": "IT与骑行爱好者", "avatar_url": "https://pic1.zhimg.com/50/v2-a0aa0a20b8ad92c04f7878e96fbdb424_l.jpg?source=b6762063", "is_org": false, "gender": 1, "badge": [{"type": "identity_people", "description": "信息技术行业 首席技术官"}], "followers_count": 1563, "is_following": false, "is_followed": false}, "title": "强化学习扩展研究：环境、奖励欺骗、智能体与数据扩展", "image_url": "https://pica.zhimg.com/v2-01169577c3b428ed946601803255181b.jpg?source=7e7ef6e2&needBackground=1", "comment_permission": "all", "created": 1750836646, "updated": 1750836646, "voteup_count": 2, "voting": 0, "comment_count": 2, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "From Semianalysis 2025-06-08 Semianalysis 是一家专注于半导体与人工智能（AI）行业的独立研究与分析公司，其核心使命是通过深入的研究分析，建立半导体行业与商业需求之间的桥梁，消除双方在信息和理解上的鸿沟。尽管成立时间不长，但公司凭借其在线影响力及积极的行业参与，迅速在技术分析领域树立了重要地位，尤其是在高速发展的半导体与AI领域。 测试时扩展范式（test time scaling paradigm）正在蓬勃发展，推理模型持续…", "excerpt_new": "From Semianalysis 2025-06-08 Semianalysis 是一家专注于半导体与人工智能（AI）行业的独立研究与分析公司，其核心使命是通过深入的研究分析，建立半导体行业与商业需求之间的桥梁，消除双方在信息和理解上的鸿沟。尽管成立时间不长，但公司凭借其在线影响力及积极的行业参与，迅速在技术分析领域树立了重要地位，尤其是在高速发展的半导体与AI领域。 测试时扩展范式（test time scaling paradigm）正在蓬勃发展，推理模型持续…", "preview_type": "default", "preview_text": "", "column": {"id": "c_1851971684791357441", "type": "column", "url": "https://api.zhihu.com/columns/c_1851971684791357441", "author": {"id": "", "url": "", "user_type": "people", "url_token": "", "name": "匿名用户", "headline": "", "avatar_url": "https://pic1.zhimg.com/v2-d41c2ceaed8f51999522f903672a521f_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "与AI同行", "imageUrl": "https://picx.zhimg.com/v2-f111d7ee1c41944859e975a712c0883b_720w.jpg?source=d16d100b", "comment_permission": "public", "intro": "介绍国内外AI学术界与工业界的一些进展", "updated": 1735105548, "is_following": false}, "content": "<p data-pid=\"2NBtP-Az\">From Semianalysis  2025-06-08</p><p data-pid=\"xtddlFIy\">Semianalysis 是一家专注于半导体与人工智能（AI）行业的独立研究与分析公司，其核心使命是通过深入的研究分析，建立半导体行业与商业需求之间的桥梁，消除双方在信息和理解上的鸿沟。尽管成立时间不长，但公司凭借其在线影响力及积极的行业参与，迅速在技术分析领域树立了重要地位，尤其是在高速发展的半导体与AI领域。</p><hr/><p data-pid=\"s4mi9oIt\">测试时扩展范式（test time scaling paradigm）正在蓬勃发展，推理模型持续快速改进，变得更加高效和经济。以 SWE-Bench 为例，衡量真实软件工程任务的评测得分不断提高，而成本却逐步降低。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-daacf52c336feebfff317bc511c8bc96_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1604\" data-rawheight=\"866\" data-original-token=\"v2-2fd64251856b5d938a001c6f1cd0cf7d\" class=\"origin_image zh-lightbox-thumb\" width=\"1604\" data-original=\"https://pica.zhimg.com/v2-daacf52c336feebfff317bc511c8bc96_r.jpg\"/><figcaption>SWE-Bench性能和成本 来源：SemiAnalysis</figcaption></figure><p data-pid=\"zPhAF50N\">强化学习（RL）推动了这一进步。RL 使模型具备了生成思维链（CoT）的推理能力，这一趋势预计将持续发展。</p><p data-pid=\"zHxPtX3j\">除 CoT 创新外，模型的推理连贯性逐渐提升，释放了智能体能力。使用搜索、Python计算等工具的能力源于模型规划、推理和长时间操作能力的提升。推理能力提高让模型从简单的聊天机器人升级为具备规划能力的智能体，未来这些智能体甚至能完成完全自动化的远程办公和系统架构任务。</p><p data-pid=\"3ECIEaeg\">尽管取得了显著进展，RL 计算的规模扩展却面临基础设施瓶颈。RL 可能是通往通用人工智能（AGI）的关键范式之一，市场巨大且投资庞大，数十亿美元用于预训练，更多资金将用于扩展RL，但其基础设施要求不同。</p><h2>强化学习如何工作？ </h2><p data-pid=\"flOK9iWZ\">强化学习 (RL) 在概念上很简单。强化学习模型从任意环境中的当前状态中获取信息，生成一组选择行动的概率，然后采取该行动。模型的目标是实现一个目标，并由 “奖励函数” 定义。强化学习发生在改变模型权重时，使得生成的最大概率更有可能导致更高的奖励。</p><p data-pid=\"vbe2F6dP\">强化学习并非新事物。RL 是一种较为古老的技术，早于大型语言模型。例如，它是掌握围棋和国际象棋的系统背后的技术基础。然而，RL 最终在 LLM 等通用技术中发挥了作用，这对能力和技术传播都有重大影响。</p><h2>可验证奖励 </h2><p data-pid=\"8ikbAYsK\">RL 在 LLMs 中最适合有明确奖励定义的领域，如编码、数学等。OpenAI 对 GPT-4o 进行 RL 训练时发现，在明确的奖励定义领域中效果最佳。随着工具使用等新领域开放，即使是图片识别和定位任务也通过RL获得突破。然而，RL 投入远低于预训练，非可验证领域的奖励定义仍待解决。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-05e3041f918c8cc6a62d9d8b11962d61_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1536\" data-rawheight=\"957\" data-original-token=\"v2-d2bc06b5b48916c5b35f90d1e581354d\" class=\"origin_image zh-lightbox-thumb\" width=\"1536\" data-original=\"https://pic2.zhimg.com/v2-05e3041f918c8cc6a62d9d8b11962d61_r.jpg\"/><figcaption>OpenAI 对 GPT-4o 进行 RL 训练时发现，在明确的奖励定义领域中效果最佳 来源： SemiAnalysis</figcaption></figure><p data-pid=\"H5mD69fQ\">随着该领域的发展，一些新的方向开始涌现，比如工具的使用。OpenAI的o3模型可以对图片进行放大，推理出所观察到的内容，执行一些计算，再进一步推理，最终给出答案。这种能力使模型可以胜任一系列此前难以完成的任务，例如判断一张照片拍摄的位置。这类任务虽然在技术上是可以验证的，但模型并没有被专门训练过。然而，尽管取得了令人瞩目的进展，各实验室投入在强化学习上的资金仍然相对较少，尤其与预训练阶段的投入相比更是如此。那么，是什么因素阻碍了强化学习在计算资源上的投入达到甚至超越预训练阶段呢？那些无法直接验证的领域将来能被有效解决吗？</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-7d7d8b9413919201041f74a32446c88b_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"936\" data-rawheight=\"616\" data-original-token=\"v2-4ff392eca3fc4379a47e3f21f3c466aa\" class=\"origin_image zh-lightbox-thumb\" width=\"936\" data-original=\"https://pic4.zhimg.com/v2-7d7d8b9413919201041f74a32446c88b_r.jpg\"/><figcaption>RL的计算量远大于预训练，但是预训练的投入却远大于强化学习 来源: Dan Roberts, OpenAI</figcaption></figure><h2>强化学习的推理密集型特性</h2><p data-pid=\"YUZd2FKB\">通过研究当前最流行的强化学习算法之一，我们可以深入了解强化学习（RL）为何如此依赖推理（inference heavy）。群组相对策略优化（Group Relative Policy Optimization，简称GRPO）是一种常用的强化学习算法，例如DeepSeek公司训练其R1模型时使用的就是这种算法。</p><p data-pid=\"oqvCTR5W\">在GRPO算法中，模型被要求回答一个问题。接下来，模型会生成多个不同的答案，每个答案可以被视为一次“探索尝试”（rollout），本质上是模型试图找到正确答案或解决问题的单独尝试过程。针对每个问题所进行的“探索尝试”数量不固定，可能是几个，也可能多达数百个。虽然理论上没有技术上的上限，但每增加一次“探索尝试”，就会消耗更多的内存和计算资源。</p><p data-pid=\"RUOGo40x\">正因如此，强化学习算法对推理资源的需求极为巨大，每个问题都可能产生大量的答案。这一点具有非常重要的影响，在本报告的多个部分我们都会进一步探讨这一特性所带来的影响。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-90d86e2b11054973610d5f28e20a05ae_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1536\" data-rawheight=\"486\" data-original-token=\"v2-c6c8a8da0612a81ca439a77df1902be7\" class=\"origin_image zh-lightbox-thumb\" width=\"1536\" data-original=\"https://pic1.zhimg.com/v2-90d86e2b11054973610d5f28e20a05ae_r.jpg\"/><figcaption>来源：SemiAnalysis, Nvidia</figcaption></figure><p data-pid=\"H2m9YM3b\"><i>上图解释：灰色块（Sampler S）：模型采样模块（即推理/生成回答）；蓝色块（Reward R）：奖励模块，用于对每个采样结果评分；绿色块（Trainer T）：训练器模块，根据奖励更新模型权重；粉色块（Broadcast B）：将新的模型权重广播给所有采样节点（分布式同步）；横轴（Time）：时间轴，表示 RL 训练过程中不同组件的并行与顺序执行关系；整个过程的效率受 多阶段协调（Sampler→Reward→Trainer→Broadcast） 的限制，强化学习远比预训练更加工程复杂。</i></p><p data-pid=\"fZNTpCSF\">随后，这些模型生成的答案将会依据真实答案（Ground Truth）进行评分。在GRPO算法中，每个答案都会获得一个奖励分数（Reward Score）。决定奖励分数的不仅仅是答案的正确性，虽然正确性是重要因素，但奖励函数也可以调整为关注答案的格式、语言的一致性等其他方面。</p><p data-pid=\"M4e-Grl0\">在获得奖励分数后，模型将使用梯度下降（Gradient Descent）方法进行更新，以提高模型生成高分答案的概率。具体来说，就是提高模型生成获得正奖励答案的可能性。</p><p data-pid=\"nO965di_\">GRPO是近端策略优化（Proximal Policy Optimization，简称PPO）算法的一种变体。相比传统的PPO，GRPO无需使用一个额外的Critic模型（Critic模型用于预测未来奖励），因此具有更高的内存效率。无论是PPO还是GRPO，都可以选择使用基于学习的奖励模型，或基于规则的奖励系统来判断答案质量。</p><p data-pid=\"jbnJ0kqY\">GRPO算法在开源社区中的普及程度较高，这主要是因为它的内存需求较低。然而，我们预计各大实验室仍然会继续使用PPO算法及其变种。PPO算法最初由OpenAI开发，而实验室内部所用的PPO版本与公开版本（通常与GRPO相比较的版本）已有显著差异。此外，大型实验室通常拥有更充裕的计算资源，因此在计算方面的限制较少。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-3fa940a6e27f00a6157f4816b6fdeab1_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1536\" data-rawheight=\"678\" data-original-token=\"v2-a1c9f23c10c2915672751a11c59d2c6e\" class=\"origin_image zh-lightbox-thumb\" width=\"1536\" data-original=\"https://pic2.zhimg.com/v2-3fa940a6e27f00a6157f4816b6fdeab1_r.jpg\"/><figcaption>这张图清楚展示了 GRPO 作为 PPO 的一个简化、优化变体，通过取消 Value 模型、支持多样化生成和奖励评估，减少内存开销、提升并行训练效率。这也是为什么 GRPO 在开源社区日益流行，而大型实验室仍偏好用定制化的 PPO 变种。</figcaption></figure><p data-pid=\"p6SdyppR\">强化学习（RL）的核心思想通常包括以下三个要素：</p><ul><li data-pid=\"3eGbiEn_\"> 一个明确的问题（question）； </li><li data-pid=\"6_CGM0oY\"> 一个用于比对检查的参考答案；</li><li data-pid=\"ECBJEOQv\"> 一种方式，用以向模型反馈并指引其行为调整的方向。</li></ul><p data-pid=\"LzShWvLp\">模型探索答案的方式可以多样化，但通常都需要生成多个可能的答案（即多个“探索尝试”，rollouts），这使得RL在推理（inference）方面的需求极高。同时，模型也会根据奖励反馈进行更新，以提升生成正确答案的概率，因此也隐含着训练的特性。</p><h2>奖励函数定义的挑战</h2><p data-pid=\"pEdr5Yqy\">如前所述，在可验证的任务（例如数学问题）中，奖励函数的定义取得了显著进步，一个主要原因是这类任务的奖励函数易于明确界定——数学问题的答案只有正确和错误两种情况。然而从技术角度来看，奖励函数原则上可以是用户希望模型优化的任何内容。</p><p data-pid=\"WT_8QwM_\">从概念上讲，模型在强化学习下的首要目标是最大化整体的奖励。例如，当模型被训练下棋时，它的主要目标是在遵守规则的前提下赢得比赛。模型通过不断地实践棋局，识别出哪些走法能在不同情况下帮助它取胜，并因此逐步提高棋艺。模型通过其所处的环境获得反馈，例如在下棋场景中，这个环境就可以被理解为棋盘和棋子，模型通过与这些棋具互动获取反馈。这一点我们稍后会深入探讨。</p><p data-pid=\"nybSff6f\">然而，对于那些更加宽泛、抽象的任务，奖励函数的定义常常被描述为一种“黑暗艺术”（dark art），因为设定合理有效的奖励函数异常困难。即使在明确的任务环境下，设定正确的奖励函数也需要大量研究、测试和优化的工作。</p><p data-pid=\"-JWx5kz7\">以芯片设计为例，谷歌开发了一个名为AlphaChip的模型，专门用于辅助芯片设计并通过强化学习进行训练。该模型成功辅助谷歌设计了 TPUv6 芯片，将芯片的导线长度（wirelength）减少了6.2%。在这一具体情境下，奖励函数被明确定义如下：</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-e3acd2e1f83e60bb9c7d58115ede4025_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2389\" data-rawheight=\"253\" data-original-token=\"v2-e3acd2e1f83e60bb9c7d58115ede4025\" class=\"origin_image zh-lightbox-thumb\" width=\"2389\" data-original=\"https://pic2.zhimg.com/v2-e3acd2e1f83e60bb9c7d58115ede4025_r.jpg\"/><figcaption>奖励函数，在布局方案  、网格划分   下的奖励值，Wirelength(p,g)：布线总长度。希望越短越好，所以以负号表示——线越长，奖励越低，λ⋅Congestion(p,g)：局部拥堵程度，乘以系数  。拥堵越严重，奖励越低。γ⋅Density(p,g)：芯片布局密度不均衡情况，乘以系数  。布局过密也会降低奖励。</figcaption></figure><p data-pid=\"T_7i4VOS\">上述公式引导模型精确地优化关键因素：布线长度（wirelength）、布线拥堵（congestion）和布局密度（density）。值得注意的是，即使是这样一个相对简单的奖励函数，设置起来也绝非易事。拥堵和密度这两个指标都配备了标量系数（分别由λ和γ表示），用于调整各自的重要性。这些系数的取值经过工程师大量实验确定，最终的权衡考虑认定：布线长度才是最重要的因素。</p><h2>如何在非可验证领域中定义奖励函数？</h2><p data-pid=\"V3BVHhoy\">所谓非可验证领域，指的是像写作或策略制定这种不存在唯一标准答案的领域。一些人对强化学习是否能真正有效应用在这些领域一直持怀疑态度。但我们认为这是可行的，事实上，已经有成功的先例。</p><p data-pid=\"biai8B4z\">这种场景下需要改变奖励机制。与其依靠严格的形式化验证器（formal verifier）来判断答案正确与否，不如使用其他模型根据某个评分标准（rubric）来评估答案。</p><p data-pid=\"ZD8nhaYB\">OpenAI 就曾利用强化学习来调整模型行为，这种调整不如数学问题那么明确。比如 OpenAI 的审慎对齐（deliberative alignment）论文就采用了强化学习流程，以大型语言模型（LLM）作为裁判，基于明确的评分标准，确保模型更安全并减少错误拒绝（false rejection）。同时，这个过程只用了合成数据（synthetic data）。如前所述，这种方法使得模型能够在分布外（out-of-distribution）的安全场景中表现出强大的泛化能力。这种基于非可验证领域的强化学习方法，已经被用于 o1、o3-mini 和 o4-mini 等模型的训练，并将在未来的推理模型训练中继续使用。</p><p data-pid=\"0sfrKaXP\">能够进行推理不仅对数学任务有帮助，也对其他许多任务（包括非可验证任务）具有重要价值。例如，在许多情形中，推理能力可以更好地帮助模型区分哪些场景需要拒绝，哪些则不需要。然而必须承认，在非可验证领域，有一些因素比其他因素更为重要。举个例子来说，模型的个性（personality）对写作风格的影响就非常显著。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-dce9dfb5bfc9d252981cf0fe22699dba_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2560\" data-rawheight=\"1273\" data-original-token=\"v2-9d0f375733075e16058990ec3a4da38a\" class=\"origin_image zh-lightbox-thumb\" width=\"2560\" data-original=\"https://pica.zhimg.com/v2-dce9dfb5bfc9d252981cf0fe22699dba_r.jpg\"/><figcaption>模型推理时间越多，其在复杂、安全性相关、判断性任务中的表现越好。尤其在非可验证领域（如写作、策略、安全性判断）中，允许模型“思考得更久”可以带来显著性能提升。这也说明推理能力与模型对细节把控、风格一致性、安全性判断等高度相关</figcaption></figure><p data-pid=\"R0QX9XRX\">在非可验证领域中进行强化学习（RL）时，模型的表现也更为不稳定。例如，GPT-4o 模型之所以表现出“迎合”（sycophantic）的倾向，部分原因就在于 OpenAI 在进行强化学习时使用了用户偏好数据。这就是一个典型的例子：即便奖励函数设计的初衷是好的，但也可能导致一些不利或不受欢迎的模型行为。</p><h2>强化学习可以帮助更好地开展强化学习</h2><p data-pid=\"gBglvArP\">提升模型的强化学习能力本身就能直接增强强化学习过程，这形成了一个良性反馈回路。这种反馈回路的出现主要是因为人们通常会使用大语言模型（LLM）作为评判者，依据具体评分标准（rubric）来提供强化学习所需的奖励信号。如前文所述，当把具备推理能力的模型作为 LLM 评判者时，它能够更好地理解评分标准，也能更细致地辨别回应中的微妙之处。</p><p data-pid=\"3z-DuYIG\">OpenAI 的“深度研究”（Deep Research）项目也被经常引用，作为强化学习在非可验证领域实现突破的例子。实际上，OpenAI 在这一项目中不仅使用了带有明确答案的可验证任务，也使用了没有明确答案的非可验证任务。但值得注意的是，与之前的例子类似，这些非可验证任务都是由另一个 LLM 根据评分标准进行评判的。</p><p data-pid=\"X4cPukln\">此外，阿里巴巴的 Qwen-3 模型也利用了类似的方法，通过大量合成数据（synthetic data）和 LLM 评判者，在那些没有参考答案的任务中提供了明确的训练信号。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-bed8639dfd6844d4585fe8ac53ba57c2_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2560\" data-rawheight=\"1281\" data-original-token=\"v2-cd9186dcfe8580a6393cdbf1960dfff5\" class=\"origin_image zh-lightbox-thumb\" width=\"2560\" data-original=\"https://pic1.zhimg.com/v2-bed8639dfd6844d4585fe8ac53ba57c2_r.jpg\"/><figcaption>来源：OpenAI</figcaption></figure><p data-pid=\"3msOKchb\">HealthBench 是一个优秀的评估体系，OpenAI 将其公开是值得称赞的。</p><p data-pid=\"LS-cnv2y\">这一评估还反映出，大语言模型（LLM）作为评判者，在衡量非可验证领域的表现时非常有效。一旦表现可以被可靠地衡量，那么就可以通过强化学习（RL）进行改进。这突出体现了 RL 和评估（evals）之间一个经常被忽视的关系：评估可以及时反馈 RL 运行状况，指导下一步优化。</p><h2>环境（Environments）</h2><p data-pid=\"yumhsuQw\">强化学习需要对模型或智能体的行动或结果进行反馈，这种反馈通常需要一个“环境”（environment）来提供，从而指导模型下一步的行动。这促使了强化学习执行反馈（RLEF, Reinforcement Learning Execution Feedback）的出现，例如我们运行模型生成的代码，并使用运行的结果作为奖励信号。</p><p data-pid=\"0DSKgWI_\">环境是模型执行动作并接收反馈的场景或模拟。棋类游戏（如象棋、围棋）是经典的环境范例：目标明确，规则清晰。但随着环境复杂性和通用性的增加，我们也会涉及到更广泛的领域，例如电子游戏中智能体驾驶赛车，或模拟生物反应器中一系列参数的控制，再进一步，则包括数学推理、编程任务，甚至是网络浏览等任务。</p><p data-pid=\"a75KBs51\">环境的配置不同，也会导致智能体表现不同。如果环境配置不合理，可能导致模型对任务理解错误或无法有效泛化，甚至出现奖励作弊（reward hacking）的情况（后文详细讨论）。</p><p data-pid=\"y_iNdUJo\">因此，构建强大且精确反映所需奖励函数的环境是非常困难的。即使在简单的环境，如编程任务中，大量使用单元测试（unit tests）也可能使模型过于专注于通过测试，而非真正写出高质量的代码。因此，一个重要的工程挑战是如何建立一个真正忠实于目标（如“编写高质量代码”）的环境。</p><p data-pid=\"bFEYrRBw\">设定合理的奖励函数是一方面，另一方面是高效可靠地工程实现这些环境。创建可扩展、稳定的环境是一个关键的技术挑战。</p><p data-pid=\"2qyteZ5Y\">环境的工程实现涉及许多要求，例如：</p><ul><li data-pid=\"asQcGo2n\"> 低延迟（Latency）：智能体采取动作后，环境应迅速响应并给予反馈，否则智能体在等待中浪费大量推理时间。<br/> </li><li data-pid=\"G4w9ql1O\"> 高可靠性：持续稳定的连接，避免环境崩溃或中断，且具备容错和检查点机制（checkpointing），确保失败时能够平滑恢复。<br/> </li><li data-pid=\"7Dl0MZC8\"> 有效处理多个并发探索尝试（rollouts或trajectories）。<br/> </li><li data-pid=\"b_L9RJ7q\"> 完善的安全架构：防止模型受到外部攻击或尝试逃离环境。<br/> </li></ul><p data-pid=\"l_AETLjf\">此外，还有多种模型自身引发的失败模式，比如模型采取的动作可能超出机器可用资源的范围。环境工程还需要防止模型自身带来的问题，同时保障安全可靠的基础设施，并处理延迟、稳定性、真实仿真等一系列挑战。环境的设计还必须精确地反映真实或模拟场景，以便智能体清楚知道如何改进，同时又不能存在可被利用的漏洞。</p><p data-pid=\"gEM1En6i\">这些要求使得环境规模化（尤其是首次规模化）变得非常困难。正如我们之后会讨论的那样，随着模型推理连贯时间的延长，即便是相对简单的环境也可能难以维持，尤其是在电脑使用这类复杂场景中，我们稍后会做进一步深入探讨。</p><p data-pid=\"wcAhT7Lt\">基础设施工程或许看起来平凡无趣，但它对强化学习的成功是至关重要的。如果每个探索尝试（rollout）耗时过长，评判模型将会闲置，浪费大量资源。因此，如何有效利用这些评判模型（例如让它们同时评估其他探索尝试）就变得非常重要。</p><p data-pid=\"1FjYgl9_\">同时，这些软件限制还必须符合硬件的约束条件。绝大多数环境通常只能运行在 CPU 服务器上，而非 GPU，这意味着环境通常部署在专门的外部服务器上，这又带来了额外的工程挑战。</p><p data-pid=\"MrTvz12O\">需要注意的是，大部分公开的强化学习环境都聚焦在与单步问题相关的评估上，而 OpenAI 的 o3 等模型构建的环境通常涉及多个工具调用（tool calls）。我们稍后会详细介绍如何构建类似 o3 这样的模型，但需要指出的是，随着工具调用数量增加，环境的复杂性也随之提升。</p><h2>奖励作弊（Reward Hacking）</h2><p data-pid=\"Ek1H8hoJ\">如前所述，设定恰当的奖励函数并非易事，因为模型可能误解目标，并以不理想的方式优化。奖励作弊（Reward hacking）指的是模型利用环境或奖励机制中的漏洞，获取高分而未真正完成预期任务。</p><p data-pid=\"914Qsy6-\">早在 2016 年，这一问题便已得到广泛关注，当时的研究人员（如现任 Anthropic CEO 的 Dario Amodei）对此做了突出强调。例如，一个机械手臂被奖励将一个红色方块叠放在蓝色方块之上，但实际情况是，机械臂通过将红色方块翻转，使其底面高度超过蓝色方块，以此欺骗奖励机制获得高分。这是因为奖励函数只考察了方块底部的高度。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-370b4f6a028eb20952560018ac169740_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"430\" data-rawheight=\"360\" data-original-token=\"v2-7cbeb508b40715e7241f877aabc24c78\" class=\"origin_image zh-lightbox-thumb\" width=\"430\" data-original=\"https://pic3.zhimg.com/v2-370b4f6a028eb20952560018ac169740_r.jpg\"/><figcaption>奖励作弊案例1</figcaption></figure><p data-pid=\"-Ljf1IGo\">这展现了另一种失败模式：在设计用于训练机器人行走的物理仿真中，智能体发现了一个软件缺陷，利用这个缺陷可以在不真正迈步的情况下进行水平移动。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-08a39a7c6d837401353e7459ff17cc5c_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"616\" data-rawheight=\"342\" data-original-token=\"v2-3f9adda7697bd2896f22754b1a53154a\" class=\"origin_image zh-lightbox-thumb\" width=\"616\" data-original=\"https://pic1.zhimg.com/v2-08a39a7c6d837401353e7459ff17cc5c_r.jpg\"/><figcaption>奖励作弊案例2 Source: Code Bullet</figcaption></figure><p data-pid=\"YdfULvbH\">在大语言模型的案例中，Claude 3.7 Sonnet表现出了奖励作弊行为，它通过修改测试用例而不是改进代码来通过原始测试。举例来说，第三方评估人员发现Claude会直接编辑&#34;测试&#34;文件使所有测试通过，而不是编写代码来通过原始测试。Anthropic识别出了这个问题，虽然他们实施了部分缓解措施，但在Claude 3.7中这种模式仍然存在。</p><p data-pid=\"IETG12yV\">虽然这些案例很有趣，但问题在于工程师们始终无法准确描述奖励函数，或者只有在智能体发现环境中的漏洞之后才能识别出这些漏洞。许多奖励破解实例都是设计者从未考虑过的路径，虽然在训练过程中可以进行迭代，但对于大语言模型来说这很难做到。虽然机器人环境在目前的初级发展阶段相对容易调整，但大语言模型拥有庞大而复杂的动作空间，使得奖励破解更难预防。</p><p data-pid=\"n-F1odCy\">解决奖励作弊问题对所有实验室都至关重要，这将借鉴安全导向团队的许多理念。这是安全性和对齐性研究在推动企业和公司采用方面发挥关键作用的另一个例子。</p><p data-pid=\"h1pdY8ZA\">在Claude 4的发布中，Anthropic通过改进环境、明确奖励信号和实施主动监控，显著减少了奖励作弊现象。这并非易事，需要大量的专业知识和技术诀窍。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-612899217605e816ad4dcbaf811a5b60_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2560\" data-rawheight=\"1254\" data-original-token=\"v2-0046e6da9491df12638db50959620bbd\" class=\"origin_image zh-lightbox-thumb\" width=\"2560\" data-original=\"https://pic3.zhimg.com/v2-612899217605e816ad4dcbaf811a5b60_r.jpg\"/><figcaption>奖励作弊统计，来源: Anthropic Claude 4 system card</figcaption></figure><p data-pid=\"8Y3lzAgD\">但强化学习和奖励作弊并不是唯一的瓶颈，基础设施本身就是一个巨大的瓶颈。这始于强化学习所需的数据。</p><h2>数据和样本效率</h2><p data-pid=\"P_TV4gl-\">乍一看，强化学习似乎非常样本高效：在训练Qwen模型的&#34;推理强化学习&#34;阶段，仅使用了不到4千个问答对。这相比基础模型带来了显著的性能提升，并声称具有强大的样本效率。</p><p data-pid=\"9T2XUYAR\">然而，真实情况更加复杂。这4000个问答对中的每一个都有非常严格的要求：它们不应该在模型的冷启动阶段（训练中的前一个阶段）被使用过，必须尽可能具有挑战性，覆盖广泛的子领域范围，但同时也要在模型的能力范围内。</p><p data-pid=\"x5NCdRqr\">这些并非简单的要求。生成合适的合成数据涉及大量的过滤和重复的模型推理。此外，要求问题具有挑战性但对模型来说又不能过于困难，这需要实验和验证问题是否符合这个狭窄的范围。在某些情况下，当数据不是合成生成时，实验室正在招募STEM博士来帮助编写对模型来说足够有挑战性的问题和答案。这些博士也被招募来为大语言模型评判员编写评分标准。</p><p data-pid=\"L3xt4hyS\">像ScaleAI、Mercor和Handshake这样的公司现在从AI实验室获得了大量业务，来协助这一招募过程。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-047333f07f73a2e55dd089ee351181f3_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2560\" data-rawheight=\"1465\" data-original-token=\"v2-8373fce4d6c52630589feed81b7b239e\" class=\"origin_image zh-lightbox-thumb\" width=\"2560\" data-original=\"https://pic2.zhimg.com/v2-047333f07f73a2e55dd089ee351181f3_r.jpg\"/><figcaption>实验室正在招募STEM博士来帮助编写对模型来说足够有挑战性的问题和答案  来源: Mercor.</figcaption></figure><p data-pid=\"zBF7tYKJ\">Qwen模型在强化学习过程中还进行了另一个额外阶段的优化。出于体现效率优势的考虑，Qwen团队并未公开这一阶段使用的样本数量，因为实际数量远远超过先前所提及的4,000个。</p><p data-pid=\"G0r_Suun\">在这一阶段，Qwen在超过20个不同的领域内进行了强化学习，并使用了全部三种类型的奖励模型（基于规则的模型、带有标准答案的LLM裁判模型、以及没有标准答案的LLM裁判模型）。这样的做法需要复杂的工程支持和大量计算资源。</p><p data-pid=\"jvB0hrax\">从长期来看，我们预计各大实验室将会在数百个专业领域内开展强化学习，从而显著提高模型性能。在这一过程中，数据质量远比数量更重要——模型会精准优化以符合训练数据的特征，因此对训练数据的精挑细选和严格过滤至关重要。</p><p data-pid=\"1RhH1mBi\">虽然看似样本数量只有4,000个，但要达到这一规模却需要大量计算资源。从数据使用角度来看，强化学习可能是样本高效的（sample efficient），但从计算资源的角度看，它绝对是低效的（sample inefficient）。与预训练阶段相比，强化学习需要更庞大的工程团队才能有效开展。</p><h2>数据是护城河（Data is the Moat）</h2><p data-pid=\"oR8GD_sD\">Qwen模型的实践经验最终表明，高质量的数据是强化学习扩展过程中特别重要的资源。高质量的数据有助于给模型提供清晰有效的RL信号，使模型能够在特定任务中准确地提高表现。生成这些数据往往需要大量推理（inference）计算。</p><p data-pid=\"GsGyC7f1\">企业通常可以聚合自己的数据，借助类似OpenAI推出的“强化微调”（RFT，Reinforcement Fine Tuning）等服务，使用自定义评估器，并基于这些评估器或数据更新模型。我们认为，这一发布尚未获得足够的重视，即便不考虑模型本身未来的进展，这一技术也能产生巨大影响。</p><p data-pid=\"wKxt1C4a\">事实上，拥有能够聚合用户行为数据的产品，是极具价值的，因为用户数据通常才是最重要的数据集。这一洞察的一个有趣推论是：拥有用户数据的AI初创公司能够针对客户需求，用较低的计算预算通过RL定制模型，而不必再去高成本地合成数据。如果企业能恰当搭建强化学习环境，为企业定制模型的时代或许可以真正到来。但整体而言，企业对模型的微调（fine-tuning）往往难以抵挡基础模型不断进步带来的巨大冲击。</p><h2>智能体（Agent）任务的时间跨度不断增加</h2><p data-pid=\"Jw0MtPNd\">如今的模型可以在更长的时间跨度内保持连贯、有效的推理能力。这意味着更长时间尺度的任务，需要能在长时间稳定运行的环境与基础设施支持，这也加剧了环境搭建上的工程需求。</p><p data-pid=\"SPawNwzn\">下方的图表指出，独立编程任务（self-contained coding tasks）的表现，每7个月就会翻倍。但我们预计，除了编程以外的任务表现增长速度将更快。OpenAI的Deep Research是模型首次能连贯地工作数分钟以上的实例，我们预期，这种长期连贯工作的能力上限将在未来迅速大幅提高。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-2cdc7ef96a7c075619ac5bcf18c3b3f5_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2560\" data-rawheight=\"1392\" data-original-token=\"v2-9726b89296bb710e97e6b9d81e1b24a1\" class=\"origin_image zh-lightbox-thumb\" width=\"2560\" data-original=\"https://pic2.zhimg.com/v2-2cdc7ef96a7c075619ac5bcf18c3b3f5_r.jpg\"/><figcaption>AI Agent的任务时长不断增加，来源: METR</figcaption></figure><p data-pid=\"xDwBaUXJ\">智能体任务（Agentic tasks）通常拥有很高的经济价值，但由于其任务复杂性和资源密集程度，对强化学习（RL）也带来了显著挑战。</p><p data-pid=\"TO0jbZZc\">任务持续时间的延长意味着每一次RL迭代周期也随之延长，从而导致整个训练过程变慢。</p><p data-pid=\"Nrh_ylrs\">以计算机操作（Computer use）为例，它很好地体现了长时间跨度任务的诸多挑战：</p><p data-pid=\"62ugk4GK\">首先，计算机操作这样的任务更贴近现实世界问题，因此会面临新的困难。智能体会遇到大量防机器人的网页脚本、验证码（captcha）和复杂的Cloudflare保护机制。这些情况往往是随机出现的，使得环境的调试（debugging）复杂度提升了一个维度。计算机操作还需要稳定运行的虚拟机（VM）和浏览器连接，这些基础设施必须长时间稳定运行，并满足前文所述的环境工程高标准。</p><p data-pid=\"WU3qNqNG\">计算机使用任务往往持续数小时甚至更长。这意味着单次探索尝试（rollout）更长，但奖励却更稀疏——换句话说，智能体采取的步骤可能增加了十倍，但可能仅在最后一步获得奖励。这大幅减弱了强化学习信号。此外，计算机使用任务通常需要使用图像和视频向模型传达信息。虽然也有尝试使用网页的HTML流或文本表示，但当前的模型尚不能很好地理解这些上下文下的图像内容。如果能够有效利用文本表示，则可显著降低计算机使用任务的内存需求。</p><h2>环境计算（Environment Compute）的潜力</h2><p data-pid=\"nVQabxRd\">我们认为，与单纯增加强化学习计算（RL compute）相比，加大对环境计算（Environment Compute）的投入同样具有巨大潜力。例如，利用几十甚至上百个CPU并行运行，搭建一个非常真实且难以奖励作弊（reward hacking）的环境，这是一个值得开拓和扩展的新领域。这种高真实性的环境将提供更清晰的信号，从而实现显著的性能提升。</p><p data-pid=\"3ohusHAq\">未来，这些环境还可能在GPU上运行，以构建对真实世界高度仿真的“数字孪生”（digital twin）。值得注意的是，这类GPU（如RTX Pro GPU或客户端GPU）与目前专为AI设计的GPU或ASIC（如H100、B200、TPU、Trainium）不同，因为后者缺乏图形渲染能力。因此，目前也有大量资源投入到专门构建用于RL环境的AI世界模型（world model）上，而非传统的RL环境。这种做法可大幅简化环境扩展，否则环境复杂性会因软件与硬件的异构性而急剧上升。</p><p data-pid=\"fBKCaxgw\">我们预测，未来高可靠性、可扩展性且易于部署的环境将非常紧缺，并将成为初创公司争相进入的新领域。目前已有部分企业开始涉足。实际上，某些能力受限的原因并非模型本身能力不足（例如o3模型已足够智能以完成大多数任务），而是受限于环境和与世界交互获取上下文的能力。</p><p data-pid=\"T2jwkf-5\">这对于科学领域的AI应用尤其令人兴奋。例如，可以构建连接实验室各种测量设备的环境，让AI智能体直接控制物理世界，在收到环境反馈后操控和改变不同因素。例如，控制熔炉温度的反馈循环较快，模型可以快速迭代。</p><p data-pid=\"6-yw4N9p\">然而，对于一些实验周期较长的有价值任务，模型也需要具备相匹配的推理连贯时间。再考虑到每次迭代需要多个rollouts，这种场景的计算与物理资源需求都将非常高。</p><p data-pid=\"ROzajZGM\">在生物、半导体制造和材料科学等领域，尤其需要关注反馈循环的效率。生物实验、制造流程和工业过程通常都有物理的运行和验证速度限制，这决定了强化学习能产生影响的时间跨度。</p><p data-pid=\"eiVyF513\">有些领域强化学习计算所产生的影响可能会很缓慢，而其他领域（尤其反馈循环快的领域）则可能迅速发生变化。物理世界的AI反馈循环本质上比数字世界慢得多，这正是我们需要强大的数字孪生环境的原因。</p><h2>与评估（evals）的类比</h2><p data-pid=\"oJ-cuq2w\">作为类比，即使是概念上更简单的模型评估（evals），其运行过程也非常困难。Docker镜像可能经常失败，甚至简单的格式变化（例如选择题格式从(A)变为(1)）都可能导致模型在评估中的表现有高达5%的波动。在评估基础设施刚开始扩展时，Anthropic就曾公开讨论过这些工程挑战。</p><p data-pid=\"xB9B-jxz\">例如，GPQA是一个常用于测试模型在研究生级别物理、化学、生物问题表现的评估工具，但这个评估似乎有一个“噪音上限”（noise ceiling），尽管模型表现似乎趋于停滞，但实际上不可能达到100%的准确性，因为标注的答案本身就可能存在错误。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-a0848085ea5631125f8e277e0317e8b4_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2304\" data-rawheight=\"1357\" data-original-token=\"v2-89cfada20a252ccacab95ebdd4a0ff5d\" class=\"origin_image zh-lightbox-thumb\" width=\"2304\" data-original=\"https://pic3.zhimg.com/v2-a0848085ea5631125f8e277e0317e8b4_r.jpg\"/><figcaption>GPQA接近“噪音上限” Source: SemiAnalysis</figcaption></figure><p data-pid=\"e9xiWdRk\">从很多方面看，随着智能体任务（Agentic tasks）的时间跨度不断增加，这一问题实际上变得更加严重。模型可执行的动作空间显著增加，推理连贯时间持续延长，创建能够评估这些长时间跨度能力的评测（evals）也变得愈加困难，同时成本也大幅提升。</p><p data-pid=\"6vCZaiqz\">评估（eval）基础设施本身并不是新鲜事物，在概念上也较为简单，但实际运行中却经常遇到无数琐碎的麻烦（即所谓“death by a million paper cuts”）。而搭建大规模强化学习（RL）基础设施并实现规模化，则是更为艰难复杂的过程，可以称之为遭受“数百万次的琐碎折磨”。</p><p data-pid=\"l4pTiORR\">对于强化学习来说，这种增加的内存容量带来了多种新的能力：</p><ul><li data-pid=\"lkqAaItn\"> 首先，它允许针对特定问题进行更多次的探索尝试（rollouts）。<br/> </li><li data-pid=\"m_4Ei5NY\"> 其次，它能够更有效地处理长时间跨度的智能体任务。<br/> </li><li data-pid=\"w4caMC0l\"> 第三，它能更好地容纳更大规模或更具推理能力的模型作为评判者，这一点在非可验证领域尤其有帮助。<br/> </li><li data-pid=\"zVK2A-Dx\"> 第四，这种新范式高度依赖合成数据（synthetic data）的生成和过滤，而这些过程高度依赖推理（inference），NVL72系统在这方面表现卓越。<br/> </li></ul><p data-pid=\"ye_RBk2i\">然而，如何避免计算资源的低效利用（underutilization）也是这个过程中较为棘手的挑战之一。</p><h2>强化学习对硬件和数据中心建设的影响</h2><p data-pid=\"9XiXjtQV\">Nvidia基于GB200和GB300架构打造的 NVL72系统 在推理领域带来了关键的技术进步。显著增加的计算能力使得系统能够以更低延迟实现更高的吞吐量，而其共享内存设计则允许在更大规模的“世界大小（world size）”中分布KV缓存（KV Cache）。</p><p data-pid=\"QqrPBLdw\">虽然这些特性在推理阶段极大改善了推理模型的批量处理能力，但更为重要的是，这些硬件改进对强化学习（RL）也产生了深远影响。具体而言，强化学习对推理性能要求极高，这些硬件优化使强化学习的训练过程也变得更有效率，从而带来更广泛的应用前景。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-a73ee5866fc591a242824bd5019a3cfa_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1959\" data-rawheight=\"620\" data-original-token=\"v2-f6a28e60a19ea7f154f28293deb4955f\" class=\"origin_image zh-lightbox-thumb\" width=\"1959\" data-original=\"https://pic3.zhimg.com/v2-a73ee5866fc591a242824bd5019a3cfa_r.jpg\"/><figcaption>采样 + 奖励 + 训练 + 广播 是一个完整的 RL 闭环，广播阶段的同步延迟是当前系统性能的核心瓶颈； Source: SemiAnalysis, Nvidia</figcaption></figure><p data-pid=\"4mY3FV6y\">在在线强化学习的情况下，最后一次推演完成和第一次推演之间可能存在时间差异。很难对所有不同的采样副本进行负载均衡。权重的广播也可能导致显著的利用率不足，因为不同的采样器和训练器有不同的拓扑结构。</p><p data-pid=\"7ibiZesm\">在强化学习的所有阶段都需要推理，但推理不像预训练时代那样需要集中化。强化学习需要大量计算，但不需要在同一个位置。</p><p data-pid=\"tjLbcMOr\">举例来说，某一领域的合成数据可以在一个数据中心生成和验证，但训练过程可以在完全不同的数据中心进行。随着强化学习主导计算需求，我们可能会看到数据中心建设的转变。虽然预训练扩展仍需要最大的多吉瓦数据中心，但强化学习的去中心化程度如何还有待观察。</p><p data-pid=\"CLFndb9m\">与预训练不同（预训练可能一次性占用数万个GPU），用于强化学习的推理时间可以根据容量进行调整。这意味着实验室现在可以在非高峰时段利用GPU，例如在其强化学习流水线中进行合成数据生成。</p><p data-pid=\"axPxA05u\">事实上，我们知道至少有一个实验室正在利用未充分使用的推理集群运行这个过程，通过合成数据生成有效地为训练提供免费算力。推理和训练之间的界限在实验室中将继续模糊，使得比最大训练集群更多的算力能够交付给模型。这种未充分利用的算力实际上是免费交付给训练的，因为推理集群需要为峰值需求而配置。</p><p data-pid=\"L1heySVu\">Prime Intellect在其Intellect-2模型中展示了强化学习的去中心化特性，这是一个全球分布式的推理模型强化学习运行。</p><p data-pid=\"--3h0SzH\">在硬件设计方面，增加的推理和长期智能体任务使内存变得更加重要。强化学习使用的浮点运算比预训练少，但仍有繁重的内存负载。长期的硬件开发将改变以适应这种情况。这包括网络拓扑等其他因素。我们看到强化学习改变的不仅仅是硬件设计，它还在改变研究的组织方式。</p><h2>强化学习正在改变实验室的结构</h2><p data-pid=\"1cDZf_nI\">语言模型的强化学习是推理真正与训练过程交织在一起的首批案例之一。推理性能现在直接影响训练速度。这意味着生产级推理（快速、高效、便宜）现在是模型训练过程的组成部分。</p><p data-pid=\"SNTkYv29\">每个实验室以前都区分&#34;产品服务推理&#34;和&#34;内部推理&#34;（例如，用于评估）。但考虑到强化学习需要大量推理，构建直接&#34;集成&#34;到训练堆栈中的超优化推理堆栈至关重要。</p><p data-pid=\"cL2whPCj\">我们在公司结构中看到了这种变化。OpenAI合并了研究和应用研究推理团队。类似地，Anthropic和Google因此对其生产和内部团队进行了重大重组。</p><h2>强化学习是推理游戏，但中国缺乏芯片</h2><p data-pid=\"lfJ5dieQ\">这种范式转变的一个后果是推理需要大量算力。就中国而言，出口管制严重限制了可用的计算资源，减缓了他们的研究测试。对于强化学习，有限的算力意味着更少的推演、更慢的数据生成和过滤，以及延迟的实验和验证。短期内这并不重要，因为今年交付给强化学习的算力仍在数万GPU的范围内。</p><p data-pid=\"CSiK9dzd\">中期来看，中国生态系统将继续受到算力约束。H20和H20E（H20的一个变种，内存更大）的禁令严重削弱了推理能力，而这对强化学习至关重要。正如我们之前指出的，H20的推理性能比H100更好。</p><p data-pid=\"A6gx4jaG\">除了部署新模型更慢之外，中国公司在为客户提供服务时也会面临问题。DeepSeek应对约束的方式是以极慢的速度（每秒20个token）提供模型服务，损害用户体验，以尽可能批量处理更多响应。这为内部使用保留了尽可能多的算力。DeepSeek目前在生产中不使用华为昇腾芯片，只使用英伟达芯片，因为性能和体验更好，但他们将开始使用。</p><p data-pid=\"1PekvlM2\">这种影响怎么强调都不为过。由于这项禁令，中国错失了数百万颗芯片。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-bb681e035ab11d525f63ff22c42b5130_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1416\" data-rawheight=\"923\" data-original-token=\"v2-5000c7e4a7ec361d02c7bf7d7ddcf40a\" class=\"origin_image zh-lightbox-thumb\" width=\"1416\" data-original=\"https://pic3.zhimg.com/v2-bb681e035ab11d525f63ff22c42b5130_r.jpg\"/><figcaption>H20 与 H20E计划出货量，由于禁令，这些芯片并未交付中国  来源: SemiAnalysis Accelerator Model</figcaption></figure><p data-pid=\"GgEljimP\">华为正在积极推广昇腾910B和910C系列的采用。华为昇腾系列的主要客户是阿里巴巴和字节跳动，他们购买了华为芯片，并深度参与为下一版本的研发过程提供反馈。</p><p data-pid=\"V3p4tBjy\">除了华为通过规避出口管制从台积电获得的290万颗芯片外，我们还看到国内中芯国际的产能大幅提升，我们目前估计2025年国内将生产38万颗昇腾910C，随着良率改善以及中芯国际北京N+2晶圆厂除上海晶圆厂外也投产，明年将达到数百万颗。</p><p data-pid=\"DSCRFGLg\">字节跳动和阿里巴巴也都在开发自己的定制芯片，我们在加速器模型中密切跟踪这些进展。</p><h2>强化学习允许频繁的模型更新</h2><p data-pid=\"R-FupZpQ\">预训练体系与当前体系的一个明显区别是，强化学习可以在模型发布后进行。这意味着可以发布一个模型，继续进行强化学习以扩展能力，然后再次更新模型。这种迭代开发可以用来逐步增强现有模型。这正是新版本DeepSeek R1所发生的情况。</p><p data-pid=\"O6TCP1UD\">这对于后训练总体而言都是如此——当前的GPT-4o已经更新了很多次，不再是最初发布时的同一个GPT-4o模型。</p><p data-pid=\"q1KJ3l9u\">我们预期Anthropic将比以前更频繁地发布Claude模型更新，这是由于新范式所致。</p><h2>递归自我改进已经在发生</h2><p data-pid=\"a6lQTFFh\">我们提到了通过更好的模型在强化学习过程中成为更好的评判员来实现自我改进，但还有另一个重要维度需要考虑。这个想法是模型本身帮助训练和编码下一个模型。Claude 4系统卡提供了实验室思路的具体展示。Anthropic对编译器开发、内核工程，甚至四足机器人的强化学习进行了评估。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-745a99ce97a917147cc3f9b6e1c3d286_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2560\" data-rawheight=\"1258\" data-original-token=\"v2-163cefb84d59ef4599559e79f76a09b1\" class=\"origin_image zh-lightbox-thumb\" width=\"2560\" data-original=\"https://pic3.zhimg.com/v2-745a99ce97a917147cc3f9b6e1c3d286_r.jpg\"/><figcaption>比较了 Anthropic 的 Claude 系列模型（Claude Sonnet 3.7、Claude Sonnet 4、Claude Opus 4）在三个特定类别的 AI 研究任务（kernel）中的得分表，Claude Sonnet 4似乎表现最好 来源： Anthropic Claude 4 System Card</figcaption></figure><p data-pid=\"oEjTJaG4\">事实上，实验室正在做的大量工作都是艰难的工程工作，目的是从现有硬件中榨取每一寸性能。编译器、内核、内存管理优化、超参数调优等都是可以测量和改进的编码任务。它们每一个都对模型效率产生巨大影响。递归自我改进经常被称为这个诱人的术语，带有奇妙的含义，但现实是它在某种程度上已经在发生。实验室也可以通过在这些具体任务上进行强化学习来加倍努力，并拥有大量专门做这些工作的内部模型变种。</p><p data-pid=\"BO440V2O\">其中大部分最初将围绕着不起眼的繁重工作展开，然后逐渐发展到研究新架构。</p><p data-pid=\"EFbVFvzg\">当前的模型并不能大幅加速开发。但OpenAI的Codex工具已经在帮助员工构建下一个版本。思考自我改进的方式是，模型将让工程师花更少时间编码，更多时间思考与研究和数据相关的主题。在模型开发受到工程努力瓶颈制约的情况下，这些瓶颈将得到解决。然而现实中，模型开发受到各种其他因素的瓶颈制约，包括计算资源的获取。真正的递归自我改进也将大幅加速研究和数据处理。</p><h2>工具使用和o3</h2><p data-pid=\"Y93Aauew\">强化学习的有效性在o3模型中得到了清楚展示，特别是通过其对外部工具的高级使用。o3表明智能是有用的，但拥有工具访问权限并善于使用它们更加有用。OpenAI做了几件事来实现这种能力。首先是确保模型能够访问工具。这可以作为报告中涉及的更广泛基础设施的一部分来完成（例如访问环境）。在模型层面，访问可以通过模型可用于访问工具的特殊标记来触发。例如，模型使用像<code>&lt;search&gt;</code>这样的特殊标记来触发外部搜索，这些搜索返回可直接用于其推理过程的结构化结果。为模型提供多个不同特殊标记的访问权限，让它能够快速轻松地访问不同的环境。</p><div class=\"highlight\"><pre><code class=\"language-text\">USER\n“What’s the population density of Zürich in people /km²?”\n───────────────────────────────────────────────────────────────\nASSISTANT  (turn 1)\n&lt;think&gt;\nNeed population and area → then divide.\n&lt;/think&gt;\n&lt;answer&gt;\n&lt;tool&gt;search_web{&#34;query&#34;:&#34;Zürich population&#34;}&lt;/tool&gt;\n&lt;/answer&gt;\n────────────────────────────────────────────────────────────────\nRUNTIME  (executes the first &lt;tool&gt;)\n# observation[0]: &#34;Population of Zürich: 402 762&#34;\n────────────────────────────────────────────────────────────────\nASSISTANT  (turn 2 – sees observation[0])\n&lt;think&gt;\nPopulation = 402 762. Next: fetch area.\n&lt;/think&gt;\n&lt;answer&gt;\n&lt;tool&gt;search_web{&#34;query&#34;:&#34;Zürich area km²&#34;}&lt;/tool&gt;\n&lt;/answer&gt;\n────────────────────────────────────────────────────────────────\nRUNTIME\n# observation[1]: &#34;Area of Zürich: 87.88 km²&#34;\n────────────────────────────────────────────────────────────────\nASSISTANT  (turn 3)\n&lt;think&gt;\nNow compute density = 402 762 / 87.88.\n&lt;/think&gt;\n&lt;answer&gt;\n&lt;tool&gt;python{&#34;code&#34;:&#34;402762 / 87.88&#34;}&lt;/tool&gt;\n&lt;/answer&gt;\n────────────────────────────────────────────────────────────────\nRUNTIME\n# observation[2]: &#34;4586.4&#34;\n────────────────────────────────────────────────────────────────\nASSISTANT  (turn 4 – final reply)\n&lt;think&gt;\nCompose prose answer and cite both search results.\n&lt;/think&gt;\n&lt;answer&gt;\nZürich’s population density is **≈ 4 586 people /km²**  \n(using population = 402 762 and area = 87.88 km²).  \nSources: search result [0], search result [1].\n&lt;/answer&gt;</code></pre></div><p data-pid=\"J-ksiOQR\">另一个挑战是选择正确的问题集合进行训练。即使模型能够访问工具，如果不需要的话，它可能选择完全不使用这些工具。有效训练模型需要提出足够困难、需要使用工具的问题，确保模型学会自然地利用外部资源。这很难做到正确，需要大量测试来验证。然而，过度使用工具可能会降低性能，使奖励信号复杂化并降低整体效果。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-414b082ae0ab46bb7b7d8da17055d475_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2560\" data-rawheight=\"839\" data-original-token=\"v2-1de0abd88b81e1975bec8a77d843e0d5\" class=\"origin_image zh-lightbox-thumb\" width=\"2560\" data-original=\"https://pic2.zhimg.com/v2-414b082ae0ab46bb7b7d8da17055d475_r.jpg\"/><figcaption>动作数过多反而导致计划“走偏”或搜索空间稀释奖励信号。 来源：https://arxiv.org/pdf/2504.20073</figcaption></figure><p data-pid=\"wZeL2ajP\">其他因素包括确保推演具有多个初始状态，每个起始点有多个响应以帮助稳定性和学习效率，为格式错误的输出添加惩罚，以及为正确使用标签添加奖励。</p><p data-pid=\"0deEfenU\">制作o3需要为模型提供多个工具的访问权限（例如通过特殊标记），并在强制模型使用这些工具的问题上进行训练。</p><h2>为什么o3会产生幻觉</h2><p data-pid=\"w_vX8kih\">o3尽管在查找和研究事物方面能力出众，但却因产生幻觉而臭名昭著。该模型经常会编造内容。这个问题随着强化学习计算规模的扩大而加剧。为什么会发生这种情况？</p><p data-pid=\"FTjyimQi\">我们认为这要追溯到这些模型是如何训练的。模型通常只因正确结果而获得奖励，不会因错误推理而受到惩罚，这使它们能够通过有缺陷的逻辑实现准确性。</p><p data-pid=\"_0hU8EUn\">例如，一个模型可能在简单的棋盘游戏中获胜，尽管它误解了游戏规则，错误地学到了它的缺陷推理是可接受的。这不仅不会因模型错误思考而惩罚它们，反而积极奖励这种行为。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-484161fe4d593fbf86005bf36125c7d9_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1682\" data-rawheight=\"1772\" data-original-token=\"v2-323b773fae2781ef1b8799105927f13e\" class=\"origin_image zh-lightbox-thumb\" width=\"1682\" data-original=\"https://pic2.zhimg.com/v2-484161fe4d593fbf86005bf36125c7d9_r.jpg\"/><figcaption>在 Sokoban 环境中，AI 模型在 3 步推理中表现出“错误推理（Spurious Reasoning）”的一个案例</figcaption></figure><p data-pid=\"sbU5mapA\">我们预期这种行为不仅仅适用于棋盘游戏。这无意中教会了模型在新的、未训练的场景中产生幻觉，将有缺陷的推理扩展到更广泛的语境中。使用推理模型作为评判员将在一定程度上帮助解决这个问题，因为它们可以纠正整个推理轨迹。其他想法包括更具体的奖励信号，对每个标记进行不同的奖励，在给出正确答案的同时惩罚错误的逻辑。</p><p data-pid=\"4nRwTMTn\">需要明确的是，这种错误的奖励行为可能会对代码等方面产生影响。一个模型可能写出糟糕的代码但仍然通过单元测试。这强化了拥有正确奖励函数的必要性。</p><p data-pid=\"qK91zfdm\">接下来，我们探讨强化学习的不同训练方法，以及它如何迫使实验室做出在预训练时代不必做出的权衡。我们还探讨如何需要扩展来获得更好的小模型。最后，我们深入研究OpenAI推理模型（如o4和o5）的未来，包括它们将如何以不同于以前模型的方式进行训练和开发。</p>", "is_labeled": false, "visited_count": 18, "thumbnails": ["https://pica.zhimg.com/v2-01169577c3b428ed946601803255181b.jpg?source=7e7ef6e2&needBackground=1", "https://picx.zhimg.com/50/v2-2f3bf114f9b609784246c153f7607ad7_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-207a820a491954759330bc45fc80a619_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-16b9d785339d3c2c8b355c19154dc512_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-c19bce7b9777be9a9de41bc38ae1ef91_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-7985f811302d4b6c247b281754d4f88b_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-7623c1b25480ca86cb64be1d8e5e1193_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-19ded12a4a1456df5fdc6dc432b91c62_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-03d3d2488ee5b8db348621c48f932893_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-f64a5c60d7cbb3380b84afd6cd8ee864_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-8a358ff339cece5749ebbb3d354dc801_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-77e8b229a560fc8a3fcb39249148ec75_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-4ec658bad2416bd33a0aa1d282558360_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-80a85576234bfa63c5342e6cdecb4c3b_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-91ee76820afa601ad2f78f0e23aebec9_720w.jpg?source=b6762063"], "favorite_count": 4, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": 1921155901240349522}", "attached_info": "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", "action_card": false}, {"id": "53_1750898460.139", "type": "feed", "offset": 53, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898460, "updated_time": 1750898460, "target": {"id": "1920467968246145437", "type": "answer", "url": "https://api.zhihu.com/answers/1920467968246145437", "author": {"id": "132d5aa1a2bae80fc7dabd354698743d", "url": "https://api.zhihu.com/people/132d5aa1a2bae80fc7dabd354698743d", "user_type": "people", "url_token": "70-70-43-88", "name": "欲观其妙", "headline": "所谓入世，无非苦乐。世间之乐，究竟为苦。性德虽同，修德各别。", "avatar_url": "https://picx.zhimg.com/50/v2-7ed69c9d207799407eb1c0eca6d8b79b_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 6397, "is_following": false, "is_followed": false}, "created_time": 1750655244, "updated_time": 1750731040, "voteup_count": 935, "thanks_count": 48, "comment_count": 51, "is_copyable": false, "question": {"id": "601884939", "type": "question", "url": "https://api.zhihu.com/questions/601884939", "author": {"id": "bba1cba165032b1317a45e1d463440e8", "url": "https://api.zhihu.com/people/bba1cba165032b1317a45e1d463440e8", "user_type": "people", "url_token": "loner-80-74", "name": "loner", "headline": "", "avatar_url": "https://pic1.zhimg.com/50/v2-d36ea3dff47f8ba2f1f7b99bf9512154_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 10, "is_following": false, "is_followed": false}, "title": "为什么穷人的孩子很难成功？", "created": 1684482855, "answer_count": 0, "follower_count": 0, "comment_count": 19, "bound_topic_ids": [15948, 76263, 185050], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "因为被算计了。 哄、吓、骗是社会老油条通用三板斧，别觉得这个总、那个总、这个经理、那个经理的，他们就真的是人上人了。 接收到的信息一旦经过多层切割之后，便陷入自我怀疑，自我贬低，见人就觉得高人一等。 实际上你见多了，交流多了之后，会发现也不过如此，凑近了看也就那么回事。 有些是运气好坐到了这个位置，有些是靠坑蒙拐骗坐到了这个位置，真正厉害的角色就那么一小撮。 大家都是出来干活的，不存在谁真的惹不起谁…", "excerpt_new": "因为被算计了。 哄、吓、骗是社会老油条通用三板斧，别觉得这个总、那个总、这个经理、那个经理的，他们就真的是人上人了。 接收到的信息一旦经过多层切割之后，便陷入自我怀疑，自我贬低，见人就觉得高人一等。 实际上你见多了，交流多了之后，会发现也不过如此，凑近了看也就那么回事。 有些是运气好坐到了这个位置，有些是靠坑蒙拐骗坐到了这个位置，真正厉害的角色就那么一小撮。 大家都是出来干活的，不存在谁真的惹不起谁…", "preview_type": "default", "preview_text": "", "reshipment_settings": "disallowed", "content": "<p data-pid=\"o7Qoru3d\">因为被算计了。</p><p data-pid=\"60fJAat_\">哄、吓、骗是社会老油条通用三板斧，别觉得这个总、那个总、这个经理、那个经理的，他们就真的是人上人了。</p><p data-pid=\"SJ1w1vwP\">接收到的信息一旦经过多层切割之后，便陷入自我怀疑，自我贬低，见人就觉得高人一等。</p><p data-pid=\"970fuo3z\">实际上你见多了，交流多了之后，会发现也不过如此，凑近了看也就那么回事。</p><p data-pid=\"sJ3eQtVA\">有些是运气好坐到了这个位置，有些是靠坑蒙拐骗坐到了这个位置，真正厉害的角色就那么一小撮。</p><p data-pid=\"50NEosmA\">大家都是出来干活的，不存在谁真的惹不起谁，且你真正惹不起的人，压根就不会出现在你的视野里面。</p><p data-pid=\"HV-c9mrS\">学会对身份属性祛魅，得意识到什么高人一等、低人一等这些偏激的想法，都是别人有意无意给你输送的。</p><p data-pid=\"5FO1TaaF\">你要真信了这一套，去自我矮化、设限，如果真被吓唬到变得自卑，除非外部有极其强大的机缘来让你扭转这种心态，否则大概率要被人踩在脚下。</p><p data-pid=\"X7tOrd3q\">世人总是喜欢无限地去神化那些所谓的强者，其实那些人的成就不过就是特定环境的特定产物罢了。</p><p data-pid=\"01dmmL6L\">成功需要的运气是大于能力的，而平台和机遇的作用，也是远远大于个人能力的作用的。</p><p data-pid=\"EtPYrsKO\">因此，<b>不要过于看重自己的努力及能力，但也不要过于轻视自己的努力和能力，承认客观差距的同时也不要增加主观滤镜。</b></p><p data-pid=\"CQOwoPmR\">强者身上固然有闪光点，能取得成功固然有能力、特质方面的原因。而所谓的眼界、格局、思维等，不过是在相应的位置上自然而然匹配得上的东西，并不全是因为他们的能力。</p><p data-pid=\"hHuPiNlG\">换个普通人坐上去，时间长了自然而然也会拥有这些。</p><p data-pid=\"IIpSnTtQ\">除去少部分正儿八经靠自己杀出一条路的大佬强者，其余的所谓“成功人士”都习惯过分强调自己的能力，而对天时地利等其它因素避而不谈。</p><p data-pid=\"RtTQQHtQ\">普通人眼中的所谓成功者，他们的成功并不是有多牛，而是他们在某个特殊时候的本能反应，加上他们自身某个阶段的时运刚好匹配上了这个时期的某个机会罢，某种意义上来说，这就是属于狗屎运而已。</p><p data-pid=\"3yCyIKBf\">说得轻佻一点，成功就是多重因素的叠加，命好、运好就是最大的本事，这玩意儿学不来的。</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": true, "visited_count": 45351, "favorite_count": 914, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1920467968246145437}", "attached_info": "Cu4HCO7fqYPgk/SJuwEQBBoJNzMzNTc4MzY2IIzC48IGKKcHMDNANUowChtUU19TT1VSQ0VfQkFTSUNfSU5GT19SRUNBTEwSATAYACAAOgp7InJhdyI6IiJ9SigKE1RTX1NPVVJDRV9GRUVEUkVfVjkSATAYACAAOgp7InJhdyI6IiJ9SkEKLFRTX1NPVVJDRV9UV09UT1dFUl9TSE9SVElOVEVSRVNUX1JFQ0FMTF9URVhUEgEwGAAgADoKeyJyYXciOiIifUooCh1UU19TT1VSQ0VfTkVBUkxJTkVfQ09OVEVOVF9WMhIBMBgAIAA6AEo/CipUU19TT1VSQ0VfWlJFQ0FMTF9GRUVEUkVfTkVXQklFX0hPVVJMWV9SVU0SATAYACAAOgp7InJhdyI6IiJ9Wgg5NjAzMjY2NGIgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyEzE5MjA0Njc5NjgyNDYxNDU0MzeKAQk2MDE4ODQ5MzmqAQlyZWNvbW1lbmTCASAxMzJkNWFhMWEyYmFlODBmYzdkYWJkMzU0Njk4NzQzZPIBCggMEgZOb3JtYWzyASgIChIkYjliMDEzNGYtOTgyMC00YTNjLThlYzYtMjEyMjI0NGQzYzUx8gEFCAsSATmCAgCIAsGQuc36MpICIDEzMmQ1YWExYTJiYWU4MGZjN2RhYmQzNTQ2OTg3NDNkmgIAygIWU2hvckludGVyZXN0V2VpZ2h0UnVsZcoCFkFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhtJbnRlcmFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhZSZXZpc2l0VmFsdWVXZWlnaHRSdWxlygIYUGVyaW9kSW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxl2gIbVFNfU09VUkNFX0JBU0lDX0lORk9fUkVDQUxM6AIC+gILTk9STUFMX0ZMT1eKAyBjMzA0MzRhNGZhZTM0OGFmYjQ5YWNjZGIyZGY2NWY1OZoDDQoCdjIQABoFb3RoZXKoA6fiAtgDAOoDEWJhc2ljX2luZm9fcmVjYWxs+gMfEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERYAEAIgEAJIEBk5vcm1hbJoEATKgBACoBACwBAC6BAZtYW51YWzCBAMxNjDIBADSBA/mjqjojZDlt7Lmm7TmlrDYBADwBAD5BAAAAKCeB7w/gQUAAAAAAAAAAIkFkslO6rCP0j+SBQCaBQNkZnSiBQNkZnSyBQExuQUAAAAAAAAAANAFAOAFAOgFAPAFCZAGAKAGOagGAJICLgoJNzMzNTc4MzY2EhMxOTIwNDY3OTY4MjQ2MTQ1NDM3GAQiCklNQUdFX1RFWFQ=", "action_card": false}], "paging": {"is_end": false, "is_start": false, "next": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=down&ad_interval=-10&after_id=53&desktop=true&end_offset=57&page_number=10&session_token=5901993897b2b671534b41dfe1703947", "previous": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=pull&ad_interval=-10&before_id=53&desktop=true&end_offset=57&page_number=10&session_token=5901993897b2b671534b41dfe1703947", "totals": 0}, "fresh_text": "推荐已更新"}