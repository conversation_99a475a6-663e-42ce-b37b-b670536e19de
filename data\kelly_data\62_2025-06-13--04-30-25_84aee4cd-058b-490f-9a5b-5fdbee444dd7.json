{"data": [{"id": "1529987814269", "type": "feed", "target": {"id": "49818432", "type": "answer", "url": "https://api.zhihu.com/answers/49818432", "voteup_count": 2344, "thanks_count": 1129, "question": {"id": "30690300", "title": "嘴唇总是干燥起死皮怎么办？", "url": "https://api.zhihu.com/questions/30690300", "type": "question", "question_type": "normal", "created": 1432629310, "answer_count": 264, "comment_count": 12, "follower_count": 3044, "detail": "嘴唇一年四季起死皮。用润唇膏（小蜜缇）用的时候还好等润唇膏没了我的嘴就又很干。在知乎上好像没有专门解答唇部护理的问题。渴望得到改善的答案～", "excerpt": "嘴唇一年四季起死皮。用润唇膏（小蜜缇）用的时候还好等润唇膏没了我的嘴就又很干。…", "bound_topic_ids": [9403, 20414, 31071], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "3b9438ae773d5b8fd03cdce369b72c51", "name": "夭瑶瑶瑶", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/zhang-si-yao-17", "url_token": "zhang-si-yao-17", "avatar_url": "https://pica.zhimg.com/5b20489b47cc1755c736652b7866ee2e_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1433233185, "created_time": 1433231753, "author": {"id": "66258bee35cc43b919ace0b206da264d", "name": "Pan Fan", "headline": "我有一只超棒的变美公众号panfan007", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/pan-fan-65", "url_token": "pan-fan-65", "avatar_url": "https://pica.zhimg.com/v2-606ca0c3ffb638b165fa3e982a7993c5_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["时尚", "服饰搭配"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "时尚等 2 个话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "v2-35cfe0bc5ec63010304f32959e668454", "avatar_url": "https://picx.zhimg.com/v2-35cfe0bc5ec63010304f32959e668454_720w.jpg?source=32738c0c", "description": "", "id": "19551052", "name": "时尚", "priority": 0, "token": "19551052", "type": "topic", "url": "https://www.zhihu.com/topic/19551052"}, {"avatar_path": "v2-35c084f0443b0b724ddaf9ac32c00e01", "avatar_url": "https://picx.zhimg.com/v2-35c084f0443b0b724ddaf9ac32c00e01_720w.jpg?source=32738c0c", "description": "", "id": "19641262", "name": "服饰搭配", "priority": 0, "token": "19641262", "type": "topic", "url": "https://www.zhihu.com/topic/19641262"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}], "title": "时尚等 2 个话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 134, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"sJJw4wQB\">谢邀！常年口红党必须了解的基础问题就是“怎样解决嘴唇干燥起死皮”，因为我们都深知两片有死皮的嘴唇抹上口红是什么可怕模样，以及卡在那些死皮和唇纹中间的口红碎屑……算了，不再描述，简直让人心悸。</p><br/><p data-pid=\"G-ILhICU\">两步走，先说说怎么救急，再说怎么保养。</p><br/><p data-pid=\"JIe-gSeU\"><strong><u>第一，紧要关头的救急。</u></strong>相信很多姑娘应该知道，我再推广下。</p><br/><p data-pid=\"2J8i_Au1\">就一招，非常简单非常无技术含量，零成本，但效果却好到极致。不管你身处何时何地，只要两分钟，立刻解决问题，还你婴儿般新生嫩唇（欧莱雅广告腔）。</p><br/><p data-pid=\"bVnwrGxa\"><strong>首先，你需要一块柔软的手帕或毛巾</strong>，关键是要柔软，且必须吸水能力一流，最好是棉麻质地的。好吧，你没有，那<strong>面纸你总有吧？</strong>面纸，拿个两张，叠几叠，叠成比嘴唇稍微大一点点的方形。</p><p data-pid=\"E1eWpLac\"><strong>然后，将叠好的手帕（面纸）吸饱水，最好是温热的水，然后将手帕（面纸）覆盖在嘴唇上</strong>，保持这个姿势不动，半分钟到一分钟，死皮就软化了。</p><p data-pid=\"CP1HnsNK\"><strong>其次，将手帕（面纸）拿下，用手轻轻在上下嘴唇各搓几下</strong>，死皮全部会搓下来。会有点恶心，但是你会感觉爆。爽。爽完了就去把死皮冲洗或擦干净吧。</p><p data-pid=\"36ur1UAT\"><strong>最后，涂上润唇膏</strong>。</p><br/><p data-pid=\"16pCFuU9\">很简单吧？简单得不能再简单，而且几分钟后再涂口红真的特别好上色，如果没试过的建议你看答案的这分钟就试试，超灵……而且百分之八九十的人会发现，嘴唇的死皮比自己以为的和能看见的多多了。</p><p data-pid=\"XcgbFY2t\">这个法子是真的救急，不建议经常用，平时涂上厚厚的润唇膏过个半天擦掉效果也是一样的。这个法子适用于立刻要涂口红以及死皮多到一定程度的情况，切记。</p><p data-pid=\"CDOSmUbC\"><strong><u>第二，怎么保养。</u></strong></p><p data-pid=\"2RwpyJLm\"><strong>1. 唇部卸妆一定要做到位，要单独卸。</strong></p><p data-pid=\"lbV5cKtB\">再说一次，不要用脸部卸妆的东西卸口红，一定要用专门的唇部卸妆产品，一定一定一定。打个比方，用万能FANCL纳米油或者经典EVE LOM洁颜霜一把抹完过后，再拿个唇部卸妆液抹抹，也还是会有淡淡的红色。口红卸不干净长此以往不止是色素沉淀嘴唇颜色变丑的问题，也会让嘴唇变得容易过敏，增加干裂和炎症的发作可能性。所以不要偷懒，认真做唇部卸妆是保养的基础。</p><br/><p data-pid=\"wnyGoaY1\"><strong>2.养成一年四季都涂润唇膏的习惯，尽量不要用开架货。</strong></p><p data-pid=\"npHxbhBA\">秋季冬季是最容易出现嘴唇干裂的，所以大家秋冬季节用是不消说，但夏季因为怕黏糊糊以及比较湿润，就会偷懒不涂，但夏季也一定要涂，第一是要做好唇部防晒，第二就是夏季做好预防工作到换季时嘴唇就不太会出状况。</p><p data-pid=\"VbUbYjuJ\">虽说唇膏的成分无外乎就是什么甘油羊毛脂凡士林精油维生素E椰子油这些乱七八糟的，但是好用的成分配比还是有出入的，我个人认为能不用开架货还是不要用的好。</p><p data-pid=\"btG3bLj-\">我推荐几款我用过的觉得性价比高又好用的唇膏给大家作为参考，作为一个鱼唇星人，不知道试过多少唇膏吃了多少，用到现在基本上就固定这几款，不会再用其他的了。由于润唇膏消耗量太大，不会买过分贵的款，都还算是平价；而且我不用罐装的，觉得手很脏，不喜欢每次伸手，烦死。</p><br/><p data-pid=\"6508i1__\">首先，王牌零缺点产品，伊丽莎白雅顿8小时润唇膏。</p><figure><noscript><img src=\"https://pic2.zhimg.com/b3b7bbc7a99c80095bd543cc0e24da13_b.jpg\" data-rawheight=\"350\" data-rawwidth=\"350\" data-original-token=\"b3b7bbc7a99c80095bd543cc0e24da13\" class=\"content_image\" width=\"350\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;350&#39; height=&#39;350&#39;&gt;&lt;/svg&gt;\" data-rawheight=\"350\" data-rawwidth=\"350\" data-original-token=\"b3b7bbc7a99c80095bd543cc0e24da13\" class=\"content_image lazy\" width=\"350\" data-actualsrc=\"https://pic2.zhimg.com/b3b7bbc7a99c80095bd543cc0e24da13_b.jpg\"/></figure><br/><p data-pid=\"QkGlahL2\">其实整个8小时系列都是经典，比如8小时眼霜，又不贵又好用，不长脂肪粒；比如8小时润泽霜，粘是粘巴了点，但我是用来涂指甲边缘的……那灵光简直无法用言语形容。这支唇膏绝对是良心产品，不粘，淡淡的柑橘香味，极其治愈，滋润效果持久；有防晒效果，SPF15，算是唇膏里还不错的。</p><p data-pid=\"Xj_DHtAb\">而且人家外表很具备专业精神，并不因为自己是支润唇膏就降低要求，硬生生长成一支口红，很棒</p><br/><p data-pid=\"BsHRUcxx\">其次，碧欧泉活泉护唇蜜</p><figure><noscript><img src=\"https://pica.zhimg.com/6d0cf4e6affcc95b6c192f6a892e7b14_b.jpg\" data-rawheight=\"342\" data-rawwidth=\"384\" data-original-token=\"6d0cf4e6affcc95b6c192f6a892e7b14\" class=\"content_image\" width=\"384\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;384&#39; height=&#39;342&#39;&gt;&lt;/svg&gt;\" data-rawheight=\"342\" data-rawwidth=\"384\" data-original-token=\"6d0cf4e6affcc95b6c192f6a892e7b14\" class=\"content_image lazy\" width=\"384\" data-actualsrc=\"https://pica.zhimg.com/6d0cf4e6affcc95b6c192f6a892e7b14_b.jpg\"/></figure><br/><p data-pid=\"yyZTYkK9\">这个一开始就是冲着外在去买的，粉红色，我拒绝不了，水蜜桃味，我更拒绝不了！买买买！用了之后发现，意外的好用，不像别的唇蜜那么油腻糊在外面一层似的，属于很滋润很容易吸收的那种，而且流动性没有别的唇蜜那么强，就不会看起来像吃了猪油膏。而且……它是甜的……真的，甜甜的……带一点点粉色，也是防晒，不过是SPF8.</p><br/><p data-pid=\"LpvGaw-i\">最后，大葡萄的润唇膏</p><figure><noscript><img src=\"https://pic1.zhimg.com/931d5ff76f4812e44bd84cfe940eb7ee_b.jpg\" data-rawheight=\"350\" data-rawwidth=\"350\" data-original-token=\"931d5ff76f4812e44bd84cfe940eb7ee\" class=\"content_image\" width=\"350\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;350&#39; height=&#39;350&#39;&gt;&lt;/svg&gt;\" data-rawheight=\"350\" data-rawwidth=\"350\" data-original-token=\"931d5ff76f4812e44bd84cfe940eb7ee\" class=\"content_image lazy\" width=\"350\" data-actualsrc=\"https://pic1.zhimg.com/931d5ff76f4812e44bd84cfe940eb7ee_b.jpg\"/></figure><br/><p data-pid=\"KJjjNt9g\">这个是真实在，便宜好用，三十几块用个够。涂上去我觉得几乎没有太多光泽，特别适合拿来做口红前面的打底，完全不影响画口红的效果。虽然滋润度比前面两种稍微差点，但是它的膏体非常棒，涂在嘴唇上的时候十分细腻，有种被悉心呵护的感觉……多数膏体软的唇膏都腻哒哒的，大葡萄就不会，这很难得。SPF5，聊胜于无吧。</p><br/><p data-pid=\"CH1IMzzz\"><strong>3. 补充B族维生素，如果唇部出现严重状况时可以直接涂抹胶囊维E。</strong></p><p data-pid=\"73M0pJqB\">唇部干裂的很大一个原因是维生素的缺乏，多吃蔬菜水果，必要时可以口服B族维生素，但要注意摄入量。</p><p data-pid=\"YHiM1jXp\">另外有时候会出现唇部发炎的情况，比如出现水泡裂口等等，去药店买那种维生素E胶囊，大概就几块钱一盒，剪破或刺破，将里面的液体涂抹在嘴唇上，睡一觉起来会好很多，如果不管用坚持多几次，对加速愈合很有好处。注意不要涂抹范围，不要抹到周边的皮肤上，容易长脂肪粒，血泪教训。</p><br/><p data-pid=\"XF1k3dgi\"><strong>4. 多喝水，改掉舔嘴唇和撕死皮的坏习惯。</strong></p><p data-pid=\"1kwpco0_\">这个不用解释了嘛？唾液里的淀粉酶会引起结缔组织收缩，然后嘴唇就会变得皱巴巴的，更加干燥，有这个习惯的话一定要改啊，不然嘴唇只有干到老死。</p><br/><p data-pid=\"StcNC2pw\">暂时就这些吧，回答完毕。</p>", "excerpt": "谢邀！常年口红党必须了解的基础问题就是“怎样解决嘴唇干燥起死皮”，因为我们都深知两片有死皮的嘴唇抹上口红是什么可怕模样，以及卡在那些死皮和唇纹中间的口红碎屑……算了，不再描述，简直让人心悸。 两步走，先说说怎么救急，再说怎么保养。 <strong><u>第一，紧要关头的救急。</u></strong>相信很多姑娘应该知道，我再推广下。 就一招，非常简单非常无技术含量，零成本，但效果却好到极致。不管你身处何时何地，只要两分钟，立刻解决问题，还你婴…", "excerpt_new": "谢邀！常年口红党必须了解的基础问题就是“怎样解决嘴唇干燥起死皮”，因为我们都深知两片有死皮的嘴唇抹上口红是什么可怕模样，以及卡在那些死皮和唇纹中间的口红碎屑……算了，不再描述，简直让人心悸。 两步走，先说说怎么救急，再说怎么保养。 <strong><u>第一，紧要关头的救急。</u></strong>相信很多姑娘应该知道，我再推广下。 就一招，非常简单非常无技术含量，零成本，但效果却好到极致。不管你身处何时何地，只要两分钟，立刻解决问题，还你婴…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1529987814, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "**********847", "type": "feed", "target": {"id": "207388004", "type": "answer", "url": "https://api.zhihu.com/answers/207388004", "voteup_count": 24, "thanks_count": 8, "question": {"id": "20250506", "title": "职场新人，入职第一天，要做些什么？面对不认识的同事，该怎么交流？", "url": "https://api.zhihu.com/questions/20250506", "type": "question", "question_type": "normal", "created": 1337648222, "answer_count": 111, "comment_count": 3, "follower_count": 2664, "detail": "<p><b>本题已收录至知乎圆桌：「<a href=\"https://www.zhihu.com/roundtable/zhichangxinren\" class=\"internal\">职场新人须知</a>」</b>，<b>更多「职场」「职场新人」相关话题欢迎关注讨论。</b></p>", "excerpt": "<b>本题已收录至知乎圆桌：「<a href=\"https://www.zhihu.com/roundtable/zhichangxinren\" class=\"internal\">职场新人须知</a>」</b>，<b>更多「职场」「职场新人」相关话题欢迎关…</b>", "bound_topic_ids": [1657, 2566, 7583, 27919, 33261, 37593], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "99446aae84a7a345ddecedf7d2d962f6", "name": "科技老宅", "headline": "公众号：【科技老宅】，获取软件！", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/fectuer-32", "url_token": "fectuer-32", "avatar_url": "https://picx.zhimg.com/v2-43cd6377aa90fd5adc7e5abef21780b4_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1501640315, "created_time": 1501640315, "author": {"id": "5c3e07a02f2681182abd2cea264af0d9", "name": "陆纪尧-卓尔礼仪", "headline": "法国卓尔礼仪CEO", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/lu-ji-yao-24", "url_token": "lu-ji-yao-24", "avatar_url": "https://picx.zhimg.com/v2-51f5f2c4f5c25fd2564be414cd293353_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"fDOfL-St\">初入职场都会存在或多或少的恐惧。此时最重要的是给同事留下好的印象，并尽快找到自己在公司里的定位。</p><p data-pid=\"lRApzZj3\">第一条建议<b>：不要僭越职责范围，做好自己的事（不要勉强，做自己。）</b>。如果第一天上班有点紧张，没关系的，没人会因此而责备你。</p><p data-pid=\"hjJoWE2V\">第二条<b>：保持礼貌开朗</b>，<b>主动和同事们说说话。可以趁</b>午餐时间找合适的话题聊聊：工作多久，有什么爱好等等。工作的第一周一定<b>少看手机</b>，因为手机会让人觉得你沉浸在自己的世界里，没人会主动来接近你。过了工作刚开始的这段时间，你会发现要想在公司里找到自我定位、与同事建立良好的关系会更加难。</p><p data-pid=\"eYNhsoNp\">对于服装，<b>选择正式套装</b>，但一定要<b>穿最合适自己的</b>，会让你觉得自信和漂亮的！如果你自己都感觉不自在，又怎么会轻松地认识新同事？</p><p data-pid=\"vygazIuL\">记住这两点：保持本性<b>和学会感激。主动认识新同事，不要自己一个人苦着脸</b>。</p>", "excerpt": "初入职场都会存在或多或少的恐惧。此时最重要的是给同事留下好的印象，并尽快找到自己在公司里的定位。 第一条建议 <b>：不要僭越职责范围，做好自己的事（不要勉强，做自己。）</b>。如果第一天上班有点紧张，没关系的，没人会因此而责备你。第二条 <b>：保持礼貌开朗</b>，<b>主动和同事们说说话。可以趁</b>午餐时间找合适的话题聊聊：工作多久，有什么爱好等等。工作的第一周一定<b>少看手机</b>，因为手机会让人觉得你沉浸在自己的世界里，没人会主动来…", "excerpt_new": "初入职场都会存在或多或少的恐惧。此时最重要的是给同事留下好的印象，并尽快找到自己在公司里的定位。 第一条建议 <b>：不要僭越职责范围，做好自己的事（不要勉强，做自己。）</b>。如果第一天上班有点紧张，没关系的，没人会因此而责备你。第二条 <b>：保持礼貌开朗</b>，<b>主动和同事们说说话。可以趁</b>午餐时间找合适的话题聊聊：工作多久，有什么爱好等等。工作的第一周一定<b>少看手机</b>，因为手机会让人觉得你沉浸在自己的世界里，没人会主动来…", "preview_type": "expand", "preview_text": "<p data-pid=\"fDOfL-St\">初入职场都会存在或多或少的恐惧。此时最重要的是给同事留下好的印象，并尽快找到自己在公司里的定位。</p><p data-pid=\"lRApzZj3\">第一条建议<b>：不要僭越职责范围，做好自己的事（不要勉强，做自己。）</b>。如果第一天上班有点紧张，没关系的，没人会因此而责备你。</p><p data-pid=\"hjJoWE2V\">第二条<b>：保持礼貌开朗</b>，<b>主动和同事们说说话。可以趁</b>午餐时间找合适的话题聊聊：工作多久，有什么爱好等等。工作的第一周一定<b>少看手机</b>，因为手机会让人觉得你沉浸在自己的世界里，没人会主动来接近你。过了工作刚开始的这段时间，你会发现要想在公司里找到自我定位、与同事建立良好的关系会更加难。</p><p data-pid=\"eYNhsoNp\">对于服装，<b>选择正式套装</b>，但一定要<b>穿最合适自己的</b>，会让你觉得自信和漂亮的！如果你自己都感觉不自在，又怎么会轻松地认识新同事？</p><p data-pid=\"vygazIuL\">记住这两点：保持本性<b>和学会感激。主动认识新同事，不要自己一个人苦着脸</b>。</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": **********, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1527946096520", "type": "feed", "target": {"id": "251420431", "type": "answer", "url": "https://api.zhihu.com/answers/251420431", "voteup_count": 620, "thanks_count": 352, "question": {"id": "20250506", "title": "职场新人，入职第一天，要做些什么？面对不认识的同事，该怎么交流？", "url": "https://api.zhihu.com/questions/20250506", "type": "question", "question_type": "normal", "created": 1337648222, "answer_count": 111, "comment_count": 3, "follower_count": 2664, "detail": "<p><b>本题已收录至知乎圆桌：「<a href=\"https://www.zhihu.com/roundtable/zhichangxinren\" class=\"internal\">职场新人须知</a>」</b>，<b>更多「职场」「职场新人」相关话题欢迎关注讨论。</b></p>", "excerpt": "<b>本题已收录至知乎圆桌：「<a href=\"https://www.zhihu.com/roundtable/zhichangxinren\" class=\"internal\">职场新人须知</a>」</b>，<b>更多「职场」「职场新人」相关话题欢迎关…</b>", "bound_topic_ids": [1657, 2566, 7583, 27919, 33261, 37593], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "99446aae84a7a345ddecedf7d2d962f6", "name": "科技老宅", "headline": "公众号：【科技老宅】，获取软件！", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/fectuer-32", "url_token": "fectuer-32", "avatar_url": "https://pic1.zhimg.com/v2-43cd6377aa90fd5adc7e5abef21780b4_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1509187800, "created_time": 1509187002, "author": {"id": "ac30f33d004e0f5aaaf8212595796554", "name": "NoCat", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/wang-mao-mao-65", "url_token": "wang-mao-mao-65", "avatar_url": "https://picx.zhimg.com/v2-0087bc64a24dfee535eb57fc9ba0cbac_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 25, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"d4re8RiH\">做过新人，带过新人。就入职第一天，给后辈们写点干货：</p><p data-pid=\"kDFfCPIm\"><b>一、“三多”</b></p><p data-pid=\"EKHPTfEv\"><b>多笑</b></p><p data-pid=\"I3dI9LwS\">职场之道，无招胜有招。爱笑的姑娘，运气都不会差，爱笑的帅哥，桃花运都不会少。HR也是人，无法免俗，谁不爱看笑脸不是？多笑，多微笑，甚至是多傻笑，给人留下开朗亲切的第一印象很重要。</p><p data-pid=\"iW4SLIFV\"><b>多问</b></p><p data-pid=\"0FP_ex8p\">职场新人，不懂装懂是大忌，碰到问题，多问多请教，还能增加与老员工的互动，刷刷存在感。不用太担心别人对你的评价，大多数人都会觉得新人不懂是正常。</p><p data-pid=\"qRja3Lgb\"><b>多礼貌</b></p><p data-pid=\"r_09sllF\">初来乍到，多用“您”，多称“老师”。新人摆正心态很重要，不管之前曾有多辉煌的过去，新起点，从零开始。谦虚的低姿态，给人好感。</p><p data-pid=\"cOJ_5Nlg\"><i>称呼</i></p><p data-pid=\"ZLU6NZcZ\">新人第一天，适用得体的称呼也会是加分项目，这里就要分外资背景还是国资背景。</p><ul><li data-pid=\"A-ruqg2f\">外企员工之间直接称呼英文名（First Name），不用称呼Mr. XX等，甚至对老板也可以直呼其名。</li><li data-pid=\"M3pPc6em\">国企、机关单位，知道官衔的，请称呼“X局”、“X处”、“X科”、“X总”、“X主任”，如果不明白，统一称呼“老师”总不会错。如果是校友，年龄差距不大，称呼“师兄”、“师姐”也能很快拉近距离，但这仅仅适用于入职一个月以内。</li></ul><p data-pid=\"e593Ezg8\"><b>二、“三记”</b></p><p data-pid=\"vAI8dx56\"><b>记住“这一天”</b></p><p data-pid=\"N7yLpP59\">入职日期，（<b>敲黑板</b>），很重要。关系日后续签合同、办入职手续、工龄起算等各种事项，新人务必请记得“这一天”。</p><p data-pid=\"GkXi2weX\"><b>记住“这个人”</b></p><p data-pid=\"NXiixSC2\">HR的姓名、称谓、联系方式，并对他/她好一点。至少在一年内，除了直属领导，这是最常打交道的人。</p><p data-pid=\"LCUnCGX9\"><b>记住“这件事”</b></p><p data-pid=\"12JlgaCO\">作息规定: 几点上班，几点午休，几点下班，中午能否在办公室午休/用餐，上下班是否要打卡。是不是超简单？</p><p data-pid=\"BnEsVrEA\">不同单位给新人安排的流程各不相同，有上手第一天就开工干活儿的，也有安排长达几个月的入职培训的。带过很多新人，竟然也有弄不清上下班时间的，也是没话说了。</p><p data-pid=\"cc9eC0v8\"><b>三、“三备”</b></p><p data-pid=\"FVjG4HzE\"><b>备好“职业装”</b></p><p data-pid=\"lZEMd4S2\">在没有摸清单位的dress code前，business casual 是比较安全的做法。如果business casual的说法对你而言还是太抽象，那么素色衬衫，长裤（不要牛仔裤）/及膝裙，皮鞋的组合一般比较安全，宁可overdressed, 也不要让人觉得太过随意。女生画个淡妆是加分项（别和我谈烈焰红唇姨妈色）。如果化妆技术不过关，那就还是素颜吧。</p><p data-pid=\"3fzcxq8S\"><b>备好“自我介绍”</b></p><p data-pid=\"Vf1TON14\">自我介绍的要素包括：</p><ul><li data-pid=\"myVnqF9_\">籍贯</li><li data-pid=\"-0SWpGmU\">毕业院校</li><li data-pid=\"NcWGNwKW\">专业</li><li data-pid=\"rrQgCUD3\">爱好和特长</li></ul><p data-pid=\"6Snn8v-E\">自我介绍的要点是：</p><ul><li data-pid=\"aKCUK87X\">准确（政法背景，招过一新人，自称“西政”毕业。按照通识，都以为是西南政法。搞了半天弄明白，竟是“西北政法”，闹了个笑话。）</li><li data-pid=\"xX_jKzR4\">简洁（10句话以内，1-2分钟）</li><li data-pid=\"sk1zTRGD\">不黑不吹（都是职场过来人，水分干货一目了然）</li></ul><p data-pid=\"avjFHHWw\">总之，自我介绍请面带微笑，语速适中，吐字清晰，略带青涩和害羞无伤大雅，但总体上更爱开朗大方。</p><p data-pid=\"WmY-xdgH\"><b>备好“茶杯”</b></p><p data-pid=\"nUioY8XI\">自带杯具绝对是良心建议。</p><p data-pid=\"vH0jbNI0\"><b>四、“三不”</b></p><p data-pid=\"dNCJ014C\"><b>不要带饭</b></p><p data-pid=\"yws-EqDu\">第一天机会难得，和HR/师兄师姐/team member出去聚个餐，拉近距离、聊点八卦，不能更完美。新人入职的第一要务就是增进了结，缩短沟通距离，吃吃饭，聊聊美食，吃货之间的友谊是迅速而稳固的。</p><p data-pid=\"xdIRBJ_b\"><b>不要套近乎</b></p><p data-pid=\"fiX0zlZq\">新人切记用力过猛，套近乎、问隐私都是大忌。刚进入团队，正确的做法还是多观察、多学习，少说多做。职场水深，走得稳比走得快更重要，来日方长。</p><p data-pid=\"CeYMRH1U\"><b>不要玩手机</b></p><p data-pid=\"v7TwPsPd\">第一天再无所事事，也不要光玩手机。可以的话，手机尽量调静音，有些办公环境极其安静，上级在布置任务的时候，猛然“最炫民族风”响起，何其尴尬。</p><p data-pid=\"vL1C1pxg\">如果觉得无聊，可以带本书，不要带职场通关或专业类书籍，让人觉得特别aggressive。推荐文艺或文学类，一来拉拉逼格，二来也能跟同事增加个话题。</p><p data-pid=\"FgRHADQH\"><b>总结</b></p><p data-pid=\"5keLMTwz\"><i>简而言之，傻笑就可以了</i></p>", "excerpt": "做过新人，带过新人。就入职第一天，给后辈们写点干货： <b>一、“三多”</b> <b>多笑</b>职场之道，无招胜有招。爱笑的姑娘，运气都不会差，爱笑的帅哥，桃花运都不会少。HR也是人，无法免俗，谁不爱看笑脸不是？多笑，多微笑，甚至是多傻笑，给人留下开朗亲切的第一印象很重要。 <b>多问</b>职场新人，不懂装懂是大忌，碰到问题，多问多请教，还能增加与老员工的互动，刷刷存在感。不用太担心别人对你的评价，大多数人都会觉得新人不懂是正常。 <b>多礼貌</b>…", "excerpt_new": "做过新人，带过新人。就入职第一天，给后辈们写点干货： <b>一、“三多”</b> <b>多笑</b>职场之道，无招胜有招。爱笑的姑娘，运气都不会差，爱笑的帅哥，桃花运都不会少。HR也是人，无法免俗，谁不爱看笑脸不是？多笑，多微笑，甚至是多傻笑，给人留下开朗亲切的第一印象很重要。 <b>多问</b>职场新人，不懂装懂是大忌，碰到问题，多问多请教，还能增加与老员工的互动，刷刷存在感。不用太担心别人对你的评价，大多数人都会觉得新人不懂是正常。 <b>多礼貌</b>…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1527946096, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1527945893807", "type": "feed", "target": {"id": "27931790", "type": "answer", "url": "https://api.zhihu.com/answers/27931790", "voteup_count": 1408, "thanks_count": 851, "question": {"id": "20250506", "title": "职场新人，入职第一天，要做些什么？面对不认识的同事，该怎么交流？", "url": "https://api.zhihu.com/questions/20250506", "type": "question", "question_type": "normal", "created": 1337648222, "answer_count": 111, "comment_count": 3, "follower_count": 2664, "detail": "<p><b>本题已收录至知乎圆桌：「<a href=\"https://www.zhihu.com/roundtable/zhichangxinren\" class=\"internal\">职场新人须知</a>」</b>，<b>更多「职场」「职场新人」相关话题欢迎关注讨论。</b></p>", "excerpt": "<b>本题已收录至知乎圆桌：「<a href=\"https://www.zhihu.com/roundtable/zhichangxinren\" class=\"internal\">职场新人须知</a>」</b>，<b>更多「职场」「职场新人」相关话题欢迎关…</b>", "bound_topic_ids": [1657, 2566, 7583, 27919, 33261, 37593], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "99446aae84a7a345ddecedf7d2d962f6", "name": "科技老宅", "headline": "公众号：【科技老宅】，获取软件！", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/fectuer-32", "url_token": "fectuer-32", "avatar_url": "https://pica.zhimg.com/v2-43cd6377aa90fd5adc7e5abef21780b4_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1405305565, "created_time": 1405305565, "author": {"id": "d54a57aba8aa5a9bf3ecba32faef6ecc", "name": "温暖的理智", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/hong-ye2", "url_token": "hong-ye2", "avatar_url": "https://pica.zhimg.com/v2-ac296246a55f8ae8179c277d550af997_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 119, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"bmCxIinx\"><b>关于第一天入职：大体原则是低调得体，积极融入。</b></p><p data-pid=\"ecDLPdsn\">1、尽量穿的正式一点，起码要干净整洁不要很多褶皱，如果是女性就化淡妆，不要穿太高端的品牌。一个新人穿着谨慎点比较安全，但要是穿的过于高档，容易有显摆的嫌疑。</p><p data-pid=\"bu8nehnQ\">2、不要带饭。带饭的话你就失去了和同事吃饭的机会，头几天和同事共进午餐非常必要，便于你迅速融入团队。</p><p data-pid=\"QQ1E3IV4\">3、遇到不认识的同事，可以微笑说你好。若是对方想继续聊，你们可以交流。若无意接茬，就安静离开。不要招人烦。</p><p data-pid=\"m-M3U8cL\">4、头一天不要问同事太多问题，尤其是关于单位福利待遇和单位领导等敏感话题。</p><p data-pid=\"Oxbe_YZN\">5、若头一天没有太多工作，可以趁直属领导不忙的时候，问问是否以往的资料可供你学习以便尽快进入工作状态。</p><p data-pid=\"bosuV6s3\">6、头几天的工作可能是都是打杂等不重要的工作，不要有怨言，安排什么就干什么。</p><p data-pid=\"IEGKYjyP\"><b>关于与陌生同事的交流：大体原则是不要急于建立关系，有序开展，稳步推进。</b></p><p data-pid=\"YmcPWuq9\">陌生同事分为两种，一种团队内的，一种团队外的。要首先接触团队内的，刚来的时候不要急着表现自己或是急于与陌生人交流。如果比较忙的话，更加不适合你过多交流，大家都忙着呢，和你说话还得浪费时间，你就看看能帮大家干点什么。在头几天可以安静的观察，看看团队内的人都大致是什么性格。观察几天后，先接触比较能说的、好接触的，问问团队情况，熟悉一些之后，再开始接触比较沉闷的、不好接触的。然后逐步的与大家多交流。</p><p data-pid=\"sBZac_GG\">团队外的话，等和团内成员熟悉一些再开始不迟。从你认识的行政人员开始，如果是女性可以从服装服饰、商场打折等信息切入；如果是男性，可以观察一下单位男同事的吸烟地点，然后进入抽烟，很快就会熟络起来。但是在你未摸清楚团队内部情况时，不要向外透露团队内部的项目、人员情况等信息，就说刚来不知道。即使都是单位同事，也有内外之别，你不清楚情况，不该说的说出去，就很麻烦。</p><p data-pid=\"vB2WCnFl\">总而言之，新人到一个新环境，不要着急表现，要沉下来，给大家一个安稳的印象，在熟悉工作的同时熟悉人员，然后一点一点的去建立人际关系。</p>", "excerpt": "<b>关于第一天入职：大体原则是低调得体，积极融入。</b>1、尽量穿的正式一点，起码要干净整洁不要很多褶皱，如果是女性就化淡妆，不要穿太高端的品牌。一个新人穿着谨慎点比较安全，但要是穿的过于高档，容易有显摆的嫌疑。 2、不要带饭。带饭的话你就失去了和同事吃饭的机会，头几天和同事共进午餐非常必要，便于你迅速融入团队。 3、遇到不认识的同事，可以微笑说你好。若是对方想继续聊，你们可以交流。若无意接茬，就安静离开。不…", "excerpt_new": "<b>关于第一天入职：大体原则是低调得体，积极融入。</b>1、尽量穿的正式一点，起码要干净整洁不要很多褶皱，如果是女性就化淡妆，不要穿太高端的品牌。一个新人穿着谨慎点比较安全，但要是穿的过于高档，容易有显摆的嫌疑。 2、不要带饭。带饭的话你就失去了和同事吃饭的机会，头几天和同事共进午餐非常必要，便于你迅速融入团队。 3、遇到不认识的同事，可以微笑说你好。若是对方想继续聊，你们可以交流。若无意接茬，就安静离开。不…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {"REACTION_CONTENT_SEGMENT_LIKE": "HIDE"}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1527945893, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1527696027007", "type": "feed", "target": {"id": "403408146", "type": "answer", "url": "https://api.zhihu.com/answers/403408146", "voteup_count": 857, "thanks_count": 418, "question": {"id": "23386750", "title": "对刚入职场几年的新人有哪些建议和忠告？", "url": "https://api.zhihu.com/questions/23386750", "type": "question", "question_type": "normal", "created": 1397315146, "answer_count": 4574, "comment_count": 17, "follower_count": 52483, "detail": "<p><b>本题已收录至知乎圆桌：<a href=\"https://www.zhihu.com/roundtable/longlife\" class=\"internal\">漫长人生告慰书</a>，欢迎关注我们，一同分享探讨面对困境的经验与解决办法：）</b></p>", "excerpt": "<b>本题已收录至知乎圆桌：<a href=\"https://www.zhihu.com/roundtable/longlife\" class=\"internal\">漫长人生告慰书</a>，欢迎关注我们，一同分享探讨面对困境的经验…</b>", "bound_topic_ids": [1537, 2566, 3479, 7583], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "1e6f8c8327fbaca7d74bbb88dfe72066", "name": "张帆", "headline": "懒", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/zhang-fan-11-60", "url_token": "zhang-fan-11-60", "avatar_url": "https://picx.zhimg.com/2617f8fdc_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1549236992, "created_time": 1527487321, "author": {"id": "75da285a6201ce1ec1bd327090fe694f", "name": "路在脚下", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/lifejiazhi", "url_token": "<PERSON><PERSON><PERSON><PERSON>", "avatar_url": "https://pica.zhimg.com/v2-fe7aaac370b13987cda2d3a52dc03ae9_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": false, "comment_count": 18, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"fJbudSls\"><b>看我的回答，能节省浏览本答案80%的时间，把百赞以上的回答都过了一遍！</b></p><p data-pid=\"KvIkL_7l\"><b>在此特别感谢各位答主的回答，让我受益匪浅。参考高赞回答，再加上我的个人经历，经过思考总结，提炼出了职场新人应该了解的七大模块：职场社交、工作误区、工作建议、向上沟通、个人认知、公司认知、工作认知。在此先放上总体的思维导图，方便大家阅读。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-75e8bcc23dc0f26615cbaf9f9fb5891f_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"2422\" data-rawheight=\"2751\" data-original-token=\"v2-1731ac58588a03d1bf7baaaf5921f6d0\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-c2cb2ba64e22f1857e8bedafa5534b86_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"2422\" data-original=\"https://picx.zhimg.com/v2-75e8bcc23dc0f26615cbaf9f9fb5891f_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;2422&#39; height=&#39;2751&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"2422\" data-rawheight=\"2751\" data-original-token=\"v2-1731ac58588a03d1bf7baaaf5921f6d0\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-c2cb2ba64e22f1857e8bedafa5534b86_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"2422\" data-original=\"https://picx.zhimg.com/v2-75e8bcc23dc0f26615cbaf9f9fb5891f_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-75e8bcc23dc0f26615cbaf9f9fb5891f_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><h2><b>【职场社交】</b></h2><p data-pid=\"PBBrcf0T\"><b>1、说话简单易懂</b></p><p data-pid=\"xUrEXUDU\">与人沟通多用简单、清楚、常识性语句，少用专业术语和大量数据。语言的目的在于沟通。</p><p data-pid=\"qVvBMHkA\"><b>2、不要说同事或领导的坏话</b></p><p data-pid=\"26Z9Dx6y\">背后说人坏话，容易被怀疑人品，且坏话总会被你说的人听到，不利于你的职业发展。</p><p data-pid=\"Y6QJ0C7e\"><b>3、谨慎使用性别红利</b></p><p data-pid=\"oKqf0_eE\">高颜值女性容易受到男士优待，为了不造成麻烦，要谨慎把握异性好感间的分寸。</p><p data-pid=\"m9EchgT2\"><b>4、不要传递负能量</b></p><p data-pid=\"WX5FlO24\">职场上，你累、难受，大家也不容易，不要每天传播负能量，容易被大家排斥。</p><p data-pid=\"LAk3agNc\"><b>5、不要急于反驳</b></p><p data-pid=\"dqaX8YYx\">进入职场，你要学会接受别人的不同观点，在反驳前想清楚你的谈话目的，考虑好反驳有意义吗。</p><p data-pid=\"ttqQZx0K\"><b>6、做好基本的社交礼仪</b></p><p data-pid=\"MJfMUxmm\">要懂得职场穿着、仪态、说话，认识到职场人的多样性，对于看不惯的人，也要保持微笑。</p><p data-pid=\"ckwi1x2M\"><b>7、减少无效社交</b></p><p data-pid=\"D9P7dz6u\">不要忙于认识很多人，职场上的社交凭证是你能给别人提供帮助。</p><p data-pid=\"1xOgQMic\"><b>8、正确认识职场送礼</b></p><p data-pid=\"JDlmI4HH\">送礼，是为了表明一个态度，如表达关心、拉近感情。要认识到送礼是有讲究的，不能随便送。如果不喜欢送礼，就不要勉强。</p><h2>【工作误区】</h2><p data-pid=\"KeSqj9Ge\"><b>1、小事不想做</b></p><p data-pid=\"My0_d0oY\">小事都做不好，更何况大事呢？你不仅要做好小事，还要思考引申出的问题、方法、技能，考虑的周密透彻，才能成长更快。</p><p data-pid=\"tvUMUDhZ\"><b>2、工作偷懒</b></p><p data-pid=\"15Wo8I_1\">要认识到工作是为了自己，多做事情才能获得成长。</p><p data-pid=\"clHQglDl\"><b>3、工作被动</b></p><p data-pid=\"jIKhjB_m\">没有老板喜欢被动做事的人，你要转变学生思维，主动去做事。</p><p data-pid=\"n3XHkF36\"><b>4、工作太忙或太闲</b></p><p data-pid=\"tFGnwd03\">要经常停下来审查自己的工作，太忙可能是工作效率低，太闲可能是你对工作质量的要求低。</p><p data-pid=\"eQnj0WII\"><b>5、截止日前给老板方案</b></p><p data-pid=\"i2uxHCRZ\">经常这样做会让老板对你的感觉变差，你要提前几天给老板方案，得到反馈，再进行修改，将方案做得更好。</p><p data-pid=\"OK39vPj5\"><b>6、拒绝做不喜欢的事</b></p><p data-pid=\"3ehBghlz\">工作内容不喜欢可能是因为你还不熟悉，但工作不像学习，不能由着自己性子来。职业化的人是会将不喜欢的事一样做好。</p><p data-pid=\"TLZmb7d3\"><b>7、习惯于舒适区</b></p><p data-pid=\"Xs_OcWVS\">不要沉浸于做习惯的事情，要多去尝试做不熟悉的事，才能成长更快。</p><h2>【工作建议】</h2><p data-pid=\"_nmBPBg4\"><b>1、遇到问题先思考</b></p><p data-pid=\"UV3S4vaT\">不要遇到问题就去问同事和领导，先自己尝试解决，且不要重复问同样的问题。</p><p data-pid=\"DKRYCqIl\"><b>2、拒绝“差不多”</b></p><p data-pid=\"UuWM-YuO\">不要总是把工作做得差不多就行，做事之前要多问“5W2H”，争取把事情做到完善。</p><p data-pid=\"kjCzLyOP\"><b>3、增强自己的不可替代性</b></p><p data-pid=\"qI9eiphl\">认识到能否升职加薪在于自己的价值，要加强自己的核心竞争力，培养独有价值。</p><p data-pid=\"YtNtlKi_\"><b>4、学会计划和总结</b></p><p data-pid=\"MHwgpy2e\">每天早晨计划好当日工作，晚上总结当天工作疏漏，以后工作避免同样的错误。</p><p data-pid=\"tNddTcFE\"><b>5、敢于做有挑战性的工作</b></p><p data-pid=\"9CrlK5OF\">要敢于承担更大责任和风险，愿意去做有难度的工作，才能更快成长。</p><p data-pid=\"ZAps0_7y\"><b>6、学会对工作说“不”</b></p><p data-pid=\"znoLtM4I\">工作不是无条件的服从，做好自己该做的，对于领导安排的一些工作，如果的确没时间或者有困难，要学会拒绝</p><p data-pid=\"uiuqgN8C\"><b>7、工作细节要做好</b></p><p data-pid=\"MUBCOEKW\">工作细节体现了人的职业水平，在职场上不要失信、迟到、劝酒、打扰别人，要会正确地发微信，写邮件</p><p data-pid=\"8FxjLg8e\"><b>8、多与同事讨论交流</b></p><p data-pid=\"OkAv0-7v\">工作中不要闭门造车，多与同事交流，很多工作任务需要团队合作完成，且他人的经验有时会节省你瞎琢磨的时间</p><h2>【向上沟通】</h2><p data-pid=\"KWJLuALQ\"><b>1、学会展示价值</b></p><p data-pid=\"ORDmBKtp\">领导有时不一定清楚你在做什么，在做好自己工作的同时，要学会向领导展示工作成果</p><p data-pid=\"N3ODP2XP\"><b>2、要经常主动沟通</b></p><p data-pid=\"gBRB1qhd\">不要只等着领导找你谈话，平时主动找领导交流，强化印象</p><p data-pid=\"m5Sdx4Bm\"><b>3、不能盲从</b></p><p data-pid=\"Qts0SpKQ\">领导和你的利益有时并不统一，且你比领导要更了解自己</p><p data-pid=\"tmGcWT_5\"><b>4、要提选择题</b></p><p data-pid=\"j9_F0nD8\">你遇到问题就去问领导，要你就没用了。要思考好，针对问题拿出至少2个解决方案，让领导选择</p><p data-pid=\"QHr8nXdm\"><b>5、交心要有分寸</b></p><p data-pid=\"xTwF4Q_l\">与领导要保持良好关系，但要清楚什么话不能跟领导说</p><p data-pid=\"1WRrZgmX\"><b>6、工作任务要及时反馈</b></p><p data-pid=\"gG9qma2v\">工作任务耗时长，要经常汇报进度；工作中遇到困难，要及时反应。</p><h2>【个人认知】</h2><p data-pid=\"sF_6AMWT\"><b>1、要摒弃“玻璃心”</b></p><p data-pid=\"eCUcEqsT\">认识到自己是个职场新人，被批评和工作压力大是正常的，即使感觉受了委屈，哭完后振作精神继续工作</p><p data-pid=\"feEpkT1j\"><b>2、外在形象要融入职场环境</b></p><p data-pid=\"KLQ0OLkC\">职场精英并不都是穿一线品牌，你的穿着打扮要符合工作环境</p><p data-pid=\"pwQyPED5\"><b>3、加强情绪控制</b></p><p data-pid=\"m-seHmwW\">职场不会容忍你的失态，焦躁、愤怒时要保持冷静，情绪不好也要完成工作</p><p data-pid=\"qDhFzQ1X\"><b>4、要保持身体健康</b></p><p data-pid=\"6w01hcgV\">少抽烟、喝酒，保持充足的睡眠，经常锻炼身体，饮食要营养均衡</p><p data-pid=\"vTF-4T4c\"><b>5、要进行职业规划</b></p><p data-pid=\"PUleH9Rc\">对3年后的工作、生活有个预期，依据预期去选择工作，经常审视工作是否有收获</p><p data-pid=\"oafPy1w_\"><b>6、不要攀附“大牛”</b></p><p data-pid=\"Wo-NO39X\">不要花时间攀附层次比你高很多的人，要记得不能给别人提供价值，成不了朋友</p><p data-pid=\"jt97cA-r\"><b>7、利用好下班时间</b></p><p data-pid=\"zE2VVT1Q\">提高职业技能，发展业余爱好，多学习知识，如金融、法律、互联网</p><p data-pid=\"MHD05B1j\"><b>8、学会正确咨询建议</b></p><p data-pid=\"P8zJZD1w\">遇到问题要去问有经验的小前辈，因为即便是大权威，如果没有与你问题相关的经验，也很难给出高质量的建议</p><h2>【公司认知】</h2><p data-pid=\"v2mTWXeg\"><b>1、公司不是家</b></p><p data-pid=\"8vP_Quds\">员工和公司之间本质是利益关系</p><p data-pid=\"8UrWum8W\"><b>2、公司不是学校</b></p><p data-pid=\"aNCGoCKa\">公司录用你是让你来创造价值的，而不是学习</p><p data-pid=\"Y6oXt1kR\"><b>3、遵守公司的规则</b></p><p data-pid=\"ovxjZwJ3\">进入职场，首先要了解公司规则，然后遵守规则，违反规则的人很难发展</p><p data-pid=\"CG18ZCoH\"><b>4、少提建议</b></p><p data-pid=\"hxRZvgK8\">公司的运作体系等一般是多方博弈后的最优状态，新人要少说、多观察、多做，逐步融入公司</p><p data-pid=\"JWJk08w-\"><b>5、下一家公司不一定更好</b></p><p data-pid=\"zfzCAlpG\">每个公司都会有些问题，不存在你想象中的完美公司，选择标准在于是否助力你的职业发展</p><h2>【工作认知】</h2><p data-pid=\"qK7Rr6G5\"><b>1、工作结果只要更好</b></p><p data-pid=\"dO_eoBH_\">不要想一次把工作做到最好，也不要忙着寻求完成任务的好建议，去做然后总结，下次做得更好</p><p data-pid=\"bABvMvZy\"><b>2、你的目标决定薪水</b></p><p data-pid=\"klNgcOQS\">职位薪水是有浮动区间的。你在乎短期利益，可以用离职要求加薪；反之你在乎长期发展，就不要纠结于比别人工资少</p><p data-pid=\"QSIX3Nd2\"><b>3、要求加薪前要懂的</b></p><p data-pid=\"Pi7I3xzj\">你给企业创造的价值要大于你的薪水，薪水高低决定于你对公司的重要性</p><p data-pid=\"3AGHcRIq\"><b>4、不要追求轰轰烈烈</b></p><p data-pid=\"Ycg4rNr7\">正常人的工作生涯是很平淡的，不要被励志故事洗脑，保持平和、乐观的心态，认真地做好当前工作。</p><p data-pid=\"RKvm1AzW\"><b>5、辞职应该懂的</b></p><p data-pid=\"LJQRz8hC\">不要冲动辞职，想好再做决定。辞职前不要让公司人知道，离职面谈收敛点、不要一吐真实心声。</p><p data-pid=\"bfYrf-qB\"><b>6、不要热衷于与人比较</b></p><p data-pid=\"Az8URsZx\">每人的情况是不一样的，比较工作任务多少是没有意义的，应把精力用于提高工作能力。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"AB48CnKr\"><b>整理码字，总结思考好辛苦，请点个赞支持下！</b></p>", "excerpt": "<b>看我的回答，能节省浏览本答案80%的时间，把百赞以上的回答都过了一遍！</b> <b>在此特别感谢各位答主的回答，让我受益匪浅。参考高赞回答，再加上我的个人经历，经过思考总结，提炼出了职场新人应该了解的七大模块：职场社交、工作误区、工作建议、向上沟通、个人认知、公司认知、工作认知。在此先放上总体的思维导图，方便大家阅读。</b> <b>【职场社交】</b> <b>1、说话简单易懂</b>与人沟通多用简单、清楚、常识性语句，少用专业术语和大量数据。语言…", "excerpt_new": "<b>看我的回答，能节省浏览本答案80%的时间，把百赞以上的回答都过了一遍！</b> <b>在此特别感谢各位答主的回答，让我受益匪浅。参考高赞回答，再加上我的个人经历，经过思考总结，提炼出了职场新人应该了解的七大模块：职场社交、工作误区、工作建议、向上沟通、个人认知、公司认知、工作认知。在此先放上总体的思维导图，方便大家阅读。</b> <b>【职场社交】</b> <b>1、说话简单易懂</b>与人沟通多用简单、清楚、常识性语句，少用专业术语和大量数据。语言…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "disallowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1527696027, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1527562434025", "type": "feed", "target": {"id": "153640089", "type": "answer", "url": "https://api.zhihu.com/answers/153640089", "voteup_count": 26, "thanks_count": 15, "question": {"id": "26582470", "title": "想要了解 Google、亚马逊等公司最前沿的技术可以去哪些网站？", "url": "https://api.zhihu.com/questions/26582470", "type": "question", "question_type": "normal", "created": 1415769260, "answer_count": 9, "comment_count": 1, "follower_count": 1074, "detail": "关于Google,亚马逊等公司最前沿的技术(大数据)可以去哪里获取学习资料", "excerpt": "关于Google,亚马逊等公司最前沿的技术(大数据)可以去哪里获取学习资料", "bound_topic_ids": [45, 1103, 5231, 63708], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "81461b12c6f2693784ed52e72fe8b461", "name": "九思", "headline": "怅寥廓问苍茫大地谁主沉浮", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/ling-shan-94", "url_token": "ling-shan-94", "avatar_url": "https://picx.zhimg.com/efa9a24b5a22a2daa11a8057b279a152_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1491801155, "created_time": 1490498722, "author": {"id": "ca543017f72b86792a84c8fbcfe22b49", "name": "九章算法", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/reseted1668736536000", "url_token": "reseted1668736536000", "avatar_url": "https://pic1.zhimg.com/26bd9bde846f0777ecef6f095f6dc1a1_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": false, "comment_count": 1, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"bCq-vPDA\">如果你想了解IT企业的技术背景等知识，我觉得可以去这些IT公司的官方Tech Blog去找答案，下面我就给你总结一下这些IT公司的官方Tech Blog。</p><br/><p data-pid=\"woNMhHhj\">Amazon Seattle, WA </p><p data-pid=\"mfD-KDwF\"><a href=\"https://link.zhihu.com/?target=http%3A//aws.typepad.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">aws.typepad.com</span><span class=\"invisible\"></span></a></p><br/><p data-pid=\"_KAFb4lq\">Cloudera Palo Alto, CA </p><p data-pid=\"21wg7kC9\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cloudera.com/blog/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">cloudera.com/blog/</span><span class=\"invisible\"></span></a></p><br/><p data-pid=\"rOI9EC8e\">Dropbox San Francisco, CA </p><p data-pid=\"TALR9RXc\"><a href=\"https://link.zhihu.com/?target=https%3A//tech.dropbox.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">tech.dropbox.com/</span><span class=\"invisible\"></span></a></p><br/><p data-pid=\"XWPBP4CP\">Facebook Menlo Park, CA </p><p data-pid=\"18dJskT3\"><a href=\"https://link.zhihu.com/?target=https%3A//developers.facebook.com/blog/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">developers.facebook.com</span><span class=\"invisible\">/blog/</span><span class=\"ellipsis\"></span></a></p><br/><p data-pid=\"f0WbedKm\">FourSquare NewYork  </p><p data-pid=\"JHfipibF\"><a href=\"https://link.zhihu.com/?target=http%3A//engineering.foursquare.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">engineering.foursquare.com</span><span class=\"invisible\">/</span><span class=\"ellipsis\"></span></a></p><br/><p data-pid=\"TG2ZoCFJ\">Google CA </p><p data-pid=\"zAqYgXkh\"><a href=\"https://link.zhihu.com/?target=https%3A//developers.googleblog.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">developers.googleblog.com</span><span class=\"invisible\">/</span><span class=\"ellipsis\"></span></a></p><br/><p data-pid=\"xYFBB4Jw\">Github San Francisco, CA </p><p data-pid=\"cltVdYiB\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/blog/category/engineering\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/blog/categor</span><span class=\"invisible\">y/engineering</span><span class=\"ellipsis\"></span></a></p><br/><p data-pid=\"ZukFDXur\">Groupon Chicago, IL </p><p data-pid=\"ZMCfiFwW\"><a href=\"https://link.zhihu.com/?target=https%3A//engineering.groupon.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">engineering.groupon.com</span><span class=\"invisible\">/</span><span class=\"ellipsis\"></span></a></p><br/><p data-pid=\"HwMbETT9\">Linkedin Mountain View, CA </p><p data-pid=\"VV7YPO5f\"><a href=\"https://link.zhihu.com/?target=http%3A//engineering.linkedin.com/blog\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">engineering.linkedin.com</span><span class=\"invisible\">/blog</span><span class=\"ellipsis\"></span></a></p><br/><p data-pid=\"hxn87rv9\">Netflix Los Gatos, CA </p><p data-pid=\"mv9pQYht\"><a href=\"https://link.zhihu.com/?target=http%3A//techblog.netflix.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">techblog.netflix.com/</span><span class=\"invisible\"></span></a></p><br/><p data-pid=\"oadc6hGG\">Quora Mountain View, CA </p><p data-pid=\"os-0FHq5\"><a href=\"https://link.zhihu.com/?target=http%3A//engineering.quora.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">engineering.quora.com/</span><span class=\"invisible\"></span></a></p><br/><p data-pid=\"X3i_WEZV\">Square San Francisco, CA </p><p data-pid=\"CeEkU9MN\"><a href=\"https://link.zhihu.com/?target=http%3A//corner.squareup.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">corner.squareup.com/</span><span class=\"invisible\"></span></a></p><br/><p data-pid=\"xnJZGIkR\">Twitter San Francisco, CA </p><p data-pid=\"d2uLiNs0\"><a href=\"https://link.zhihu.com/?target=http%3A//engineering.twitter.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">engineering.twitter.com</span><span class=\"invisible\">/</span><span class=\"ellipsis\"></span></a></p><br/><p data-pid=\"JWohkZaf\">Uber San Francisco, CA </p><p data-pid=\"I0MIssXT\"><a href=\"https://link.zhihu.com/?target=https%3A//eng.uber.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">eng.uber.com/</span><span class=\"invisible\"></span></a></p><br/><p data-pid=\"XQ89JwT1\">Yelp San Francisco, CA </p><p data-pid=\"MYyiQhwp\"><a href=\"https://link.zhihu.com/?target=http%3A//engineeringblog.yelp.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">engineeringblog.yelp.com</span><span class=\"invisible\">/</span><span class=\"ellipsis\"></span></a></p><br/><p data-pid=\"XYNPQdic\">我的微信公众号：ninechapter中有更多的IT公司的技术和面试介绍，有兴趣的可以关注一下</p>", "excerpt": "如果你想了解IT企业的技术背景等知识，我觉得可以去这些IT公司的官方Tech Blog去找答案，下面我就给你总结一下这些IT公司的官方Tech Blog。 Amazon Seattle, WA <a href=\"https://link.zhihu.com/?target=http%3A//aws.typepad.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">aws.typepad.com</span><span class=\"invisible\"></span></a> Cloudera Palo Alto, CA <a href=\"https://link.zhihu.com/?target=http%3A//www.cloudera.com/blog/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">cloudera.com/blog/</span><span class=\"invisible\"></span></a> Dropbox San Francisco, CA <a href=\"https://link.zhihu.com/?target=https%3A//tech.dropbox.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">tech.dropbox.com/</span><span class=\"invisible\"></span></a> Facebook Menlo Park, CA <a href=\"https://link.zhihu.com/?target=https%3A//developers.facebook.com/blog/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">developers.facebook.com</span><span class=\"invisible\">/blog/</span><span class=\"ellipsis\"></span></a> FourSquare NewYork <a href=\"https://link.zhihu.com/?target=http%3A//engineering.foursquare.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">engineering.foursquare.com</span><span class=\"invisible\">/</span><span class=\"ellipsis\"></span></a> Goog…", "excerpt_new": "如果你想了解IT企业的技术背景等知识，我觉得可以去这些IT公司的官方Tech Blog去找答案，下面我就给你总结一下这些IT公司的官方Tech Blog。 Amazon Seattle, WA <a href=\"https://link.zhihu.com/?target=http%3A//aws.typepad.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">aws.typepad.com</span><span class=\"invisible\"></span></a> Cloudera Palo Alto, CA <a href=\"https://link.zhihu.com/?target=http%3A//www.cloudera.com/blog/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">cloudera.com/blog/</span><span class=\"invisible\"></span></a> Dropbox San Francisco, CA <a href=\"https://link.zhihu.com/?target=https%3A//tech.dropbox.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">tech.dropbox.com/</span><span class=\"invisible\"></span></a> Facebook Menlo Park, CA <a href=\"https://link.zhihu.com/?target=https%3A//developers.facebook.com/blog/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">developers.facebook.com</span><span class=\"invisible\">/blog/</span><span class=\"ellipsis\"></span></a> FourSquare NewYork <a href=\"https://link.zhihu.com/?target=http%3A//engineering.foursquare.com/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">engineering.foursquare.com</span><span class=\"invisible\">/</span><span class=\"ellipsis\"></span></a> Goog…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "disallowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1527562434, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1527486364975", "type": "feed", "target": {"id": "155029601", "type": "answer", "url": "https://api.zhihu.com/answers/155029601", "voteup_count": 46, "thanks_count": 13, "question": {"id": "57486725", "title": "在美国硅谷（旧金山湾区）买房应该怎样选择购房中介？", "url": "https://api.zhihu.com/questions/57486725", "type": "question", "question_type": "normal", "created": 1490210287, "answer_count": 16, "comment_count": 0, "follower_count": 302, "detail": "", "excerpt": "", "bound_topic_ids": [307, 94701], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "d6529af436bb4995918fb418c55acbc3", "name": "xinbenlv", "headline": "谷歌软件工程师", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xinbenlv", "url_token": "xinbenlv", "avatar_url": "https://pic1.zhimg.com/6786644d3c795f48ce6f8161e929af94_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1491172828, "created_time": 1491172828, "author": {"id": "", "name": "", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/", "url_token": "", "avatar_url": "https://pic3.zhimg.com/da8e974dc.jpg", "gender": -1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 3, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"t4NGSSJR\">回答这种问题比较敏感，有砸人饭碗的嫌疑，还是匿名了。认真讲，在zillow、redfin、trulia这些网站这么发达的今天，agent的“价值”除了帮你准备document以外几乎为0，不必在意他们的吹嘘。</p><br/><p data-pid=\"yBBpxrr5\">买方市场的话，可以找厉害的买房代理去帮你挑房子，一项一项看条件然后压卖房心理底线进行砍价，该有的各种contingency都加上。说白了就是，这房子你不卖我的话，一两个月内你是卖不出去的，而挂的时间长了更掉价（别人会怀疑你这房子有某些隐患）。</p><p data-pid=\"hMtIbiwT\">卖方市场的话，一个房子上市都是一周内pending的，offer数量少则三五个，多的50个也不稀奇。这种情况下大家都没contingency，甚至cash offer也不稀奇，收完offer后再来两轮竞价都是常态，那好，<b>人家卖方代理凭什么把房子卖给你，而不是卖给别人？</b>换你做卖方代理的话，你有2.5%的佣金，你会选择把另外2.5%的佣金给一个不认识的不知名的agent，还是会选择做双重代理让自己拿到4％的佣金（如果卖房agent不向卖方disclose的话也可以拿5%，也很常见，但这属于灰色地带，可能会惹官司），或者说，选择一个跟你关系不错的agent让他的客户的offer被卖方接受呢？这样一来卖个人情，下次等他当卖方agent的时候也能还回来。</p><br/><p data-pid=\"OnvlZ5RW\">说句遭人恨的实话吧：<b>卖方市场的话，有房源的agent是大爷，没房源的agent饿死活该</b>。至于怎么找agent？在open house的时候直接去找卖房agent，问是否可以当dual agent或者有没有其他agent可以推荐，就行了。卖方agent会非常乐意做这样的事情的，不信的话题主可以去open house走动走动看，基本上看门的卖方agent上来第一句话问你就是“你是不是已经有agent了”，你可以对比一下不同回复得到的服务态度的区别。</p><p data-pid=\"Whhacb3I\">买房切忌守着一个agent到底，下一个offer用一个agent就行了（但这个agent一定是要跟这个卖方agent有关系的人）。站在买方的角度讲，<b>你那2.5%的买房代理佣金给谁不是给</b>，你愿意给一个没法帮你抢到房子的agent，<b>每一次都是花一堆时间精力去勾心斗角，猜到底有多少人投了offer最高价是多少自己应该加价多少</b>，还是愿意给一个一过offer deadline就能告诉你总共有几个offer、最高价多少、是否要bid等信息，如果你愿意的话一下就能帮你抢到房子的agent？</p>", "excerpt": "回答这种问题比较敏感，有砸人饭碗的嫌疑，还是匿名了。认真讲，在zillow、redfin、trulia这些网站这么发达的今天，agent的“价值”除了帮你准备document以外几乎为0，不必在意他们的吹嘘。 买方市场的话，可以找厉害的买房代理去帮你挑房子，一项一项看条件然后压卖房心理底线进行砍价，该有的各种contingency都加上。说白了就是，这房子你不卖我的话，一两个月内你是卖不出去的，而挂的时间长了更掉价（别人会怀疑你这房子有某…", "excerpt_new": "回答这种问题比较敏感，有砸人饭碗的嫌疑，还是匿名了。认真讲，在zillow、redfin、trulia这些网站这么发达的今天，agent的“价值”除了帮你准备document以外几乎为0，不必在意他们的吹嘘。 买方市场的话，可以找厉害的买房代理去帮你挑房子，一项一项看条件然后压卖房心理底线进行砍价，该有的各种contingency都加上。说白了就是，这房子你不卖我的话，一两个月内你是卖不出去的，而挂的时间长了更掉价（别人会怀疑你这房子有某…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1527486364, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1527486364975&page_num=62"}}