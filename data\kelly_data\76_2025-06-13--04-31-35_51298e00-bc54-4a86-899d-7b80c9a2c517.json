{"data": [{"id": "1502208128371", "type": "feed", "target": {"id": "19843390", "title": "有哪些解决推荐系统中冷启动的思路和方法？", "url": "https://api.zhihu.com/questions/19843390", "type": "question", "question_type": "normal", "created": 1315384809, "answer_count": 37, "comment_count": 1, "follower_count": 1416, "detail": "比如在注册阶段，对于一个完全陌生的新用户，如何应付冷启动？", "excerpt": "比如在注册阶段，对于一个完全陌生的新用户，如何应付冷启动？", "bound_topic_ids": [4259], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "d4ee19666cd6c71befe34174160bfafa", "name": "SeaBitcoin", "headline": "独立韭菜 BTC ◦ 访谈播客 Sea Talk", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/oxygen", "url_token": "oxygen", "avatar_url": "https://picx.zhimg.com/v2-d249904cf935290c53f4cc47c41e05b0_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "reaction_instruction": {}}, "verb": "", "created_time": 1502208128, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1501563857639", "type": "feed", "target": {"id": "16596589", "type": "answer", "url": "https://api.zhihu.com/answers/16596589", "voteup_count": 5, "thanks_count": 0, "question": {"id": "19660417", "title": "为什么那么多牛人成天在研究讨论算法，系统自动推荐的东西还是不能令人满意呢？", "url": "https://api.zhihu.com/questions/19660417", "type": "question", "question_type": "normal", "created": 1305903160, "answer_count": 101, "comment_count": 11, "follower_count": 2145, "detail": " 比如豆瓣推荐的图书、电影，知乎推荐的可能感兴趣的人或话题，据说要关注一个方面，推荐就会比较准确，难道每个方面都要专门注册一个账号？为什么朋友推荐的书总是让我满意，难道朋友在推荐书的时候作了比计算机所能进行的运算还要复杂的大脑活动了吗？<br/>2011-8-23补充：好像没有看到结合google广告推荐来谈的。google的广告链接虽然从来不点，但是推荐的内容相关度还是相当高的。 ", "excerpt": "比如豆瓣推荐的图书、电影，知乎推荐的可能感兴趣的人或话题，据说要关注一个方面，…", "bound_topic_ids": [3074, 4259, 5783, 6349, 8598, 10096], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "210e2adb77c257604fe491a343f8b9a9", "name": "王鑫", "headline": "数码爱好者，kindle 3 ipad iphone", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kaixindel<PERSON>jia", "url_token": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar_url": "https://pic1.zhimg.com/c40a550c1_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1365108866, "created_time": 1365108866, "author": {"id": "ff6ebbafb06285aedd994ed32eec6d0d", "name": "郭立帆", "headline": "machine learning/problem solver", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/guo-li-fan-93", "url_token": "guo-li-fan-93", "avatar_url": "https://picx.zhimg.com/011ac077e_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 1, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"nUq-Q66f\">首先, 图书电影推荐, 问题推荐, 以及商品化的推荐 是有极大的区别的. 前两者都是希望能够增加用户对网站的粘度，后者是为了卖出更多的商品。 目的不一样，推荐的效果也当然不一样。</p><p data-pid=\"ywz9fjU5\">其次，需要你来选择一个“方面”, 那是因为要让计算机来了解你, 如果计算机不了解你, 那怎么能给你推荐一个好的东西呢，你也知道朋友推荐会比计算机推荐靠谱。那是因为朋友是了解你的。计算机了解你需要一些监督型的学习成本。</p><p data-pid=\"bjTRyzsA\">再次，推荐也存在一个信任问题，现有的推荐没有能够做到个性化，至少有两个方面，1，没有对你的人的profile个性化分析，2，没有对你的人的行为进行个性化分析。 这个也是这个领域的一些方向。比如说，通过社交网络来获取你的profile，或者是通过你的网上的行为来分析你。</p><p data-pid=\"jkYtyZwI\">最后，Google 能够给你推荐广告，那是因为，你用了google 或者chrome进行了检索，让google学习到了你的profile 和行为,然后他给你做了推荐。所以它不是自动而是比较不干扰你的时候学习到了你，而没有让你发现而已。</p>", "excerpt": "首先, 图书电影推荐, 问题推荐, 以及商品化的推荐 是有极大的区别的. 前两者都是希望能够增加用户对网站的粘度，后者是为了卖出更多的商品。 目的不一样，推荐的效果也当然不一样。 其次，需要你来选择一个“方面”, 那是因为要让计算机来了解你, 如果计算机不了解你, 那怎么能给你推荐一个好的东西呢，你也知道朋友推荐会比计算机推荐靠谱。那是因为朋友是了解你的。计算机了解你需要一些监督型的学习成本。 再次，推荐也存在一…", "excerpt_new": "首先, 图书电影推荐, 问题推荐, 以及商品化的推荐 是有极大的区别的. 前两者都是希望能够增加用户对网站的粘度，后者是为了卖出更多的商品。 目的不一样，推荐的效果也当然不一样。 其次，需要你来选择一个“方面”, 那是因为要让计算机来了解你, 如果计算机不了解你, 那怎么能给你推荐一个好的东西呢，你也知道朋友推荐会比计算机推荐靠谱。那是因为朋友是了解你的。计算机了解你需要一些监督型的学习成本。 再次，推荐也存在一…", "preview_type": "expand", "preview_text": "<p data-pid=\"nUq-Q66f\">首先, 图书电影推荐, 问题推荐, 以及商品化的推荐 是有极大的区别的. 前两者都是希望能够增加用户对网站的粘度，后者是为了卖出更多的商品。 目的不一样，推荐的效果也当然不一样。</p><p data-pid=\"ywz9fjU5\">其次，需要你来选择一个“方面”, 那是因为要让计算机来了解你, 如果计算机不了解你, 那怎么能给你推荐一个好的东西呢，你也知道朋友推荐会比计算机推荐靠谱。那是因为朋友是了解你的。计算机了解你需要一些监督型的学习成本。</p><p data-pid=\"bjTRyzsA\">再次，推荐也存在一个信任问题，现有的推荐没有能够做到个性化，至少有两个方面，1，没有对你的人的profile个性化分析，2，没有对你的人的行为进行个性化分析。 这个也是这个领域的一些方向。比如说，通过社交网络来获取你的profile，或者是通过你的网上的行为来分析你。</p><p data-pid=\"jkYtyZwI\">最后，Google 能够给你推荐广告，那是因为，你用了google 或者chrome进行了检索，让google学习到了你的profile 和行为,然后他给你做了推荐。所以它不是自动而是比较不干扰你的时候学习到了你，而没有让你发现而已。</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1501563857, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1501563711632", "type": "feed", "target": {"id": "24207660", "type": "answer", "url": "https://api.zhihu.com/answers/24207660", "voteup_count": 57, "thanks_count": 6, "question": {"id": "19660417", "title": "为什么那么多牛人成天在研究讨论算法，系统自动推荐的东西还是不能令人满意呢？", "url": "https://api.zhihu.com/questions/19660417", "type": "question", "question_type": "normal", "created": 1305903160, "answer_count": 101, "comment_count": 11, "follower_count": 2145, "detail": " 比如豆瓣推荐的图书、电影，知乎推荐的可能感兴趣的人或话题，据说要关注一个方面，推荐就会比较准确，难道每个方面都要专门注册一个账号？为什么朋友推荐的书总是让我满意，难道朋友在推荐书的时候作了比计算机所能进行的运算还要复杂的大脑活动了吗？<br/>2011-8-23补充：好像没有看到结合google广告推荐来谈的。google的广告链接虽然从来不点，但是推荐的内容相关度还是相当高的。 ", "excerpt": "比如豆瓣推荐的图书、电影，知乎推荐的可能感兴趣的人或话题，据说要关注一个方面，…", "bound_topic_ids": [3074, 4259, 5783, 6349, 8598, 10096], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "210e2adb77c257604fe491a343f8b9a9", "name": "王鑫", "headline": "数码爱好者，kindle 3 ipad iphone", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kaixindel<PERSON>jia", "url_token": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar_url": "https://pic1.zhimg.com/c40a550c1_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1481187898, "created_time": 1396877560, "author": {"id": "5c269bd4c5fed237fd22c47b2a004e21", "name": "这个杀手不太准", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/zhou-kai-tuo", "url_token": "zhou-kai-tuo", "avatar_url": "https://pica.zhimg.com/67998136a_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["机器学习"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "机器学习话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "v2-59cdc75217c5bed5676901f99bbf9db4", "avatar_url": "https://picx.zhimg.com/v2-59cdc75217c5bed5676901f99bbf9db4_720w.jpg?source=32738c0c", "description": "", "id": "19559450", "name": "机器学习", "priority": 0, "token": "19559450", "type": "topic", "url": "https://www.zhihu.com/topic/19559450"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}], "title": "机器学习话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": false, "comment_count": 9, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"WJPpLMdC\">在淘宝做过几个实际上线的推荐系统，从一个idea到最终能被千百万用户看到的产品。\n\n写一写我们的思考，不涉及商业机密。\n\n首先推荐系统的本质是什么?\n\n我认为站的角度不同会有完全不同的答案。\n\n从商业角度来看其实更接近本质：就是把你认为适合用户的东西推销给他。\n\n上面这句话就意味着推荐系统要解决两个问题：\n\n1. 找到适合用户的东西。\n2. 推销给他，或者说推荐给他。\n\n\n\n第一点是算法的范畴\n一般在商品推荐领域有两种方法，一种是基于商品关联的推荐：比如你买了手机，那很可能还要买手机壳移动电源。比如你喜欢听爱情买卖，那很可能你还喜欢听最炫民族风。基于商品的推荐算法实现简单粗暴，工业界应用极为广泛。\n一种是基于用户属性的推荐，其实基于商品的推荐广义上是基于用户属性推荐的子集。因为用户属性代表一切你可能掌握的用户数据，有静态的用户档案如年龄性别职业，也有动态的行为信息记录如购买历史，浏览历史，搜索词集等。简单的如协同过滤，复杂的如现在流行的机器学习方法。本质都是一样的-试图拟合一个性能良好的从用户属性空间到购买行为的函数。\n\n不幸的是据我所知即使最先进的算法，对上述函数的拟合程度，用精确率和召回率衡量，都不怎么样。\n\n更复杂的算法还会考虑时间与地理的维度，由于这个方向涉及我实际项目，就不具体展开了。\n\n而第二点是我要探讨的核心，也就是推荐系统的商业本质。太阳底下没有新鲜事，在传统商业时代做这件事情的叫做销售或者导购员。需求既是发现的，有算法来发现，也是创造的。我们知道超市里往往有导购员，其实超市的商品摆放非常讲究，大多数请况下很合理，但是有和没有导购却对销量有巨大影响。卖同样的商品，销售之间可能有几十倍业绩的差别。\n\n同样据我所知，同样的算法，不同的交互设计，甚至不同的文案，都带来巨大的结果差异。\n\n这些无不提示我们，推荐系统不是一个简单的算法问题，是一个商业问题。\n\n在合适的时间，合适的场景下，以合适的方式推荐合适的商品，才能得到最好的效果。在这个问题上我们也在探索。\n\n机器学习的模型难以解释，如何告诉用户我为什么给你做这样的推荐？\n\n推荐系统和机器广告铺天盖地，如何让用户有新鲜感，觉得你的推荐有意思?\n\n这些问题都在算法之外，希望能给同仁们带来一些思考。</p>", "excerpt": "在淘宝做过几个实际上线的推荐系统，从一个idea到最终能被千百万用户看到的产品。 写一写我们的思考，不涉及商业机密。 首先推荐系统的本质是什么? 我认为站的角度不同会有完全不同的答案。 从商业角度来看其实更接近本质：就是把你认为适合用户的东西推销给他。 上面这句话就意味着推荐系统要解决两个问题： 1. 找到适合用户的东西。 2. 推销给他，或者说推荐给他。 第一点是算法的范畴 一般在商品推荐领域有两种方法，一种是基…", "excerpt_new": "在淘宝做过几个实际上线的推荐系统，从一个idea到最终能被千百万用户看到的产品。 写一写我们的思考，不涉及商业机密。 首先推荐系统的本质是什么? 我认为站的角度不同会有完全不同的答案。 从商业角度来看其实更接近本质：就是把你认为适合用户的东西推销给他。 上面这句话就意味着推荐系统要解决两个问题： 1. 找到适合用户的东西。 2. 推销给他，或者说推荐给他。 第一点是算法的范畴 一般在商品推荐领域有两种方法，一种是基…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "need_payment", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1501563711, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1495348838315", "type": "feed", "target": {"id": "140507794", "type": "answer", "url": "https://api.zhihu.com/answers/140507794", "voteup_count": 850, "thanks_count": 314, "question": {"id": "20394372", "title": "工作后的成长速度是如何产生差异的？", "url": "https://api.zhihu.com/questions/20394372", "type": "question", "question_type": "normal", "created": 1343875898, "answer_count": 647, "comment_count": 11, "follower_count": 55839, "detail": "<p></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-0e0513473ac99c262696495ce1526ccf_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"600\" data-rawheight=\"344\" class=\"origin_image zh-lightbox-thumb\" width=\"600\" data-original=\"https://picx.zhimg.com/v2-0e0513473ac99c262696495ce1526ccf_r.jpg\"/></figure><p><b>09.19 起上知乎搜索「求职」，免费领取毕业规划全景图，get 全方位求职指南。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p>本题已收录至<b><a href=\"https://www.zhihu.com/org-campaign/recruitment?utm_campaign=org_recruitment&amp;utm_source=question\" class=\"internal\">知乎首次招聘活动</a></b>：知乎联合 10 家知名互联网企业发布岗位，发送「微简历」即可申请。详情可见：<b><a href=\"https://www.zhihu.com/org-campaign/recruitment?utm_campaign=org_recruitment&amp;utm_source=question\" class=\"internal\">在知乎找工作是一种怎样的体验？</a></b>（仅客户端可见）</p><p><b>本题已加入圆桌 »<a href=\"https://www.zhihu.com/roundtable/burnout\" class=\"internal\">职业倦怠与跳槽须知 </a>更多「职业倦怠」「跳槽」等相关的话题欢迎关注讨论</b></p>", "excerpt": "<b>09.19 起上知乎搜索「求职」，免费领取毕业规划全景图，get 全方位求职指南。</b> 本题…", "bound_topic_ids": [1537, 2566, 3479, 7129, 12585], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "updated_time": 1484215040, "created_time": 1484168691, "author": {"id": "f00777df16e82c87e05965b5c859f72d", "name": "阿羊", "headline": "职场就和谈恋爱一样，总是需要智慧的。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/ayangdianshang", "url_token": "a<PERSON><PERSON>shan<PERSON>", "avatar_url": "https://pic1.zhimg.com/v2-a04c65d81c2c5899d035211960a81efd_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": false, "comment_count": 45, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"LrY081lh\">这个问题就像我生活里经常会问自己的一个问题：</p><p data-pid=\"wtPl2xX9\"><b>为什么毕业一年，有的朋友已经买了路虎极光，或者特斯拉，而我连驾照都还没考？<br/></b></p><p data-pid=\"BXsCPd99\">上学时候，老师经常会说，大家的起跑线都是一样的。</p><figure><noscript><img src=\"https://pic3.zhimg.com/v2-451e192663d473799d73e4f3f0d5445e_b.jpg\" data-rawwidth=\"580\" data-rawheight=\"349\" data-original-token=\"v2-451e192663d473799d73e4f3f0d5445e\" class=\"origin_image zh-lightbox-thumb\" width=\"580\" data-original=\"https://pic3.zhimg.com/v2-451e192663d473799d73e4f3f0d5445e_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;580&#39; height=&#39;349&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"580\" data-rawheight=\"349\" data-original-token=\"v2-451e192663d473799d73e4f3f0d5445e\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"580\" data-original=\"https://pic3.zhimg.com/v2-451e192663d473799d73e4f3f0d5445e_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-451e192663d473799d73e4f3f0d5445e_b.jpg\"/></figure><br/><p data-pid=\"w0zhUwTD\">是的，老师说得对，王思聪说了，他爸也就帮了他一下，赞助了一点钱。好家伙，他爸的一个小目标就是一个亿，全中国有几个人能这么说话的？</p><p data-pid=\"bgfQJVJM\">家庭背景的不一样，本身就存在着一定的不公平性，但是我们要突破的就是这不公平。因为每一个牛逼货都是从不牛逼里面来的。</p><p data-pid=\"RK7YkyRq\">老王家也一个样。</p><p data-pid=\"3EDYGcIy\">我们的职业生涯本身就有多因素的环境组成。</p><br/><p data-pid=\"pMELMZQf\"><b>自身资源与技能模块：</b></p><p data-pid=\"G9hTAvZz\"><b><br/>1、<br/>既有的资源优势，自身的能力所在也是决定的一部分。</b></p><p data-pid=\"61tYxKyW\">要不然你爸给你一个亿，你也就知道吃喝玩乐，还能干点什么幺蛾子？</p><p data-pid=\"AiL2wDOR\">《心经》中，我很喜欢一句：「是故空中无色，无受想行识，无眼耳鼻舌身意，无色声香味触法，无眼界，乃至无意识界。」</p><p data-pid=\"ao4LC39a\">有些人爱用外在的物事来给你贴标签，可那真的不是你？</p><p data-pid=\"D54O1FXI\">你的生活就是你自己的，不是别人的。</p><p data-pid=\"FLxTY0Qn\">不要让外在的东西把你局限住，当你真正认可了你自己，就没有什么能把你困住了。</p><p data-pid=\"DlffUCmK\"><b><br/>认清自我，了解自己，下一步才是整合资源，提升自我价值！</b></p><p data-pid=\"c1CbkWxh\"><b><br/><br/>2、<br/>师傅领进门，修行就靠个人。</b>这比摸石头过河的好多了，最起码你有长者带路。</p><p data-pid=\"6Y8PNXtr\">学习师傅的毕生绝学，并且学会举一反三，创建新的招式，构建自己的知识体系。</p><p data-pid=\"Q2QI14vA\">刚入行那会，什么都不会，老板说要做一个项目，策划一场产品活动。不知如何下手？</p><p data-pid=\"1OBHDKCC\">师傅带着做市场分析，人群定位，人群画像，数据分析，测试方案，再到执行方案，再最后的方案总结，整一条龙下来，到了一遍。</p><p data-pid=\"dGI16y8A\">当老板第二次说要做另外的活动的时候，我主动和师傅说要去承接活动，策划，执行，分析总结，全部自己做完它，过程顺利，不懂的就去摸索和请教，一个流程下来，整个的思路和体系都非常的清晰。</p><p data-pid=\"lGg7WGuh\"><b>实践，能够让你清晰理论知识，并且修正其中不属于你职业习惯的部分。</b></p><br/><p data-pid=\"JdJQZC8l\"><b>3、</b></p><p data-pid=\"MWkXwyo9\">行业的基础知识别人可以帮你<b>，举一反三才是你真正成长的部分。</b></p><p data-pid=\"aWKy5jSs\">现在我带团队新员工，特别喜欢举一反三，一点就通的人。这一类人必须是要有内在也即是内核的存在。</p><p data-pid=\"rD2tDpBN\">如果你的内核没有这一模块的东西，并且不去深入研究的话，说再多都是对牛弹琴，说的就是这个理！</p><p data-pid=\"cYgHiLTI\"><b>要和别人请教任何问题，自己都要内心有一份方案。</b></p><br/><p data-pid=\"XstIrAwz\"><b>4、</b></p><p data-pid=\"rUsT3bUN\"><b>学会主动去承接问题，帮公司解决问题。</b></p><p data-pid=\"mwF0WJ3X\">任何一个牛逼的人都是从不牛逼中提升上来的。解决问题就是提升自己的契机，也是老板正面看待你的机会。</p><p data-pid=\"IjfJAooi\">去了解所在公司的发展规划，根据公司的发展规划在制定自身的发展规划，两者要结合好，然后去为之奋斗，这样子做，你想不被提升都难。</p><p data-pid=\"m5qGZ5nu\">之前在公司的时候，老板和我说要去整合一个部门，将部门抽离出来，建立一个新的部门。</p><p data-pid=\"4m6N_k5l\">这对于我来说，挺有意思的，这不单能够锻炼我的团队管理能力，也能学会去创造更多的价值，给自己技能模块加分。</p><p data-pid=\"sPNkM77a\"><b>什么都没说，就是做！</b></p><p data-pid=\"IWgaBq1K\">用了2个月的时间，我将我负责的模块重新洗牌，重建，并做了基础的根基打造。</p><p data-pid=\"mP4_scpy\">这里面包括部门的规章制度，团队氛围，人员培训等等都进行了完善！</p><p data-pid=\"SlLiczKC\">就因为这个事情，我在公司的地位也稳固了，薪资也升了好几个级，创造了公司开创以来最快当上部门主管的历史记录。</p><p data-pid=\"gAMtpF-b\"><b>主动去承担问题，你做不好了，并不会怎么样，</b>但是如果不去承担，永远都和你没什么关系。</p><br/><p data-pid=\"7osWtYeH\"><b>5、</b></p><p data-pid=\"PTxX8CFA\"><b>前瞻性的去学习，增加个人技能。</b></p><p data-pid=\"AMmuE3Iy\">活到老学到老，老板要的东西或者社会要求你的东西永远都要是新的，假如你停滞不前的话，那你迟早是要被淘汰的。</p><p data-pid=\"jeUUZP0k\"><b>每天的读书，以及每周末的同行见面会，或者沙龙</b>，如果没有，那就自己做这一个的开创者，这绝对是一个可以疯狂学习的平台。</p><p data-pid=\"NRrbVwYf\">我基本每周都会参与一次这种活动，因为都是喜欢头脑风暴的人，迸发出来的火花，能让你的思维不断地清晰。</p><br/><p data-pid=\"JL_TyQWX\"><b>6、</b></p><p data-pid=\"RGiFeMIs\"><b>单兵作战那是不能够的，要的是团队协助能力。</b></p><p data-pid=\"Oj2rsy7T\">当你还只是一个人的时候，你思考的只是我如何让其他部门的人舒服的去配合我的工作。</p><p data-pid=\"XrrI-LgP\">因为舒服能引导开心的工作，这对你的工作开展只有利没有弊。</p><p data-pid=\"huiuRVdQ\">分析每一个你需要对接的版块的工作内容，和对接人聊天，了解对方的脾性。</p><p data-pid=\"lKaF2TUo\">在工作交接上，按着性子和习惯来操作，正常是很顺畅的。</p><p data-pid=\"A_XdDabu\">就像策划师和设计师的关系一样。左改改，右改改，弄回来算了。这种做法不被怼死就算不错了。不过你要是设计师关系好的话，先了解清楚，一步到位，最多修改个3次以内，一般问题不大的。</p><p data-pid=\"4oZVOW0D\"><b>学会怎么与团队相处，与其他部门的相处。</b></p><br/><br/><br/><p data-pid=\"UemPbPnN\"><b>7、</b></p><p data-pid=\"cvcae1Se\"><b>时间管理能力。</b></p><br/><p data-pid=\"aV5yVbRj\">每一个人的时间都是24小时，就看你怎么用而已。</p><p data-pid=\"keXTMkHn\">举个例子：最近比较忙，没时间看书，我就会调整为听书。地铁里面信号比较差，没法上网，那就提前下载。这样子你坐车的时间也可以利用起来，不会说用来发呆，玩游戏等等。</p><p data-pid=\"aK3pupKR\">大学我们宿舍经常说一句话：<b>生前何必久睡，死后自会长眠。</b></p><p data-pid=\"iLBe2g8l\">睡觉挺浪费时间的，这句话是针对赖床的。</p><br/><br/><p data-pid=\"b7pIqxC7\"><b>个人心理模块：</b></p><br/><p data-pid=\"AT4ACwJq\"><b>1、</b></p><p data-pid=\"UUAwbEw2\"><b>不能容许自己拥有一个玻璃心</b></p><br/><p data-pid=\"deIg0W0S\">玻璃心，小孩子可以有，因为他受委屈了，可以直接放声哭，但是我们作为成年人，这是不能够的，我们要思考的应该是怎么会造成这样子的局面。</p><p data-pid=\"Zl99ACom\">之前带过一个新人，刚毕业，工作能力还可以，就是在和别的部门对接的时候没处理好，导致对接人产生了不愉快的心情。他就觉得对方是故意给小鞋子穿啊什么的，然后就有小情绪啊，委屈，抱怨就都来了。</p><p data-pid=\"nm90huLK\">我就只和他说了一句话：你已经成年了，这种事情要去分析解决，而不是怀着玻璃心在抱怨。</p><p data-pid=\"iFZu8C1W\"><b>在这个社会上，自尊只能是自己去争取的，不能靠别人施舍。</b></p><br/><p data-pid=\"xx1Q3WdD\"><b>2、</b></p><p data-pid=\"KkBcV-gu\"><b>拥有一颗坚信自己的心。</b></p><p data-pid=\"SlUdhqVc\">相信自己能够去承接任何一个问题，并且解决他。很简单，如果你连你自己都不敢相信你自己，凭什么要让企业来投资你。</p><p data-pid=\"MmVWW-mB\">假如我不信自己，我就不会帮之前的企业解决那么多的问题。换句话说，如果我不相信我自己，我就不会自己创立团队，走上创业的道路。毕竟我还得对我团队里的人负责。</p><br/><p data-pid=\"II2ryfSu\"><b>一件事情，85%是看做事态度，15%看智力。</b></p><br/><br/><br/><p data-pid=\"yuMwVwix\"><b>3、</b></p><p data-pid=\"zwnbdM2K\"><b>换位思考能力。</b></p><br/><p data-pid=\"uXZXSoqR\">学会去换位思考每一个和你对接的人，或者你的上司。将自己的格局放宽，不要局限于当下自己负责的那一小模块上。思想上的巨人，也要是行动上的巨人。</p><p data-pid=\"YPzACVmB\">当你预想能够比别人快，并且能够在合适的时机将对公司有用的建议传达给你的上司，让发现你是属于战略型人才。</p><p data-pid=\"qNxN6Qsj\">那发展为管理层，就只是时间的问题了。</p><br/><p data-pid=\"yF-x20LS\"><b>4、</b></p><p data-pid=\"-HBc0Uy6\"><b>学会耐心去分享，去工作。</b></p><p data-pid=\"PO9s1M-l\">很多人都曾梦想做一番大事业，但是哪有那么多的大事可以做，有的只是小事。一件一件小事累积起来就形成了大事。</p><p data-pid=\"93HF7N0a\"><b>任何大成就或者大灾难都是累积的结果。</b></p><br/><br/><br/><p data-pid=\"JmnTTBbD\"><b>5、</b></p><p data-pid=\"eCvYxYaw\"><b>用一颗感怀的心去待人。</b></p><p data-pid=\"KGhAMYK3\">每一个人都是需要被理解的，都有脆弱的一面，我们不是要去攻击人家的脆弱面，而是要帮助对方去治愈或者减少脆弱面。</p><p data-pid=\"cgi_Sr9K\"><b>对方烦躁时递给他一支烟或一杯水，气氛就会缓解。</b></p><br/><p data-pid=\"5ZcMC2ku\">给予就会被给予，剥夺就会被剥夺。信任就会被信任，怀疑就会被怀疑。爱就会被爱，恨就会被恨。</p><p data-pid=\"DkTLTAoC\">你播种什么就收获什么；给予什么就得到什么。你怎样对待人们，取决于你怎样看待他们，这是普遍的真理，爱别人就是爱自己。</p><p data-pid=\"PTwu2wkQ\">格拉威尔在《异类》一书中指出：“经过一万小时锻炼，任何人都能从平凡变成卓越。”这就是“一万小时定律”。也有观点称，初步掌握一项技能，至少需要学习800小时。10000小时折算每天投入四小时，一周五天，相当于十年。</p><br/><p data-pid=\"q8LR8MYE\">还有一个点，就是学会点赞，而不是收藏，我发现收藏比点赞还高很多。</p><p data-pid=\"KrsnVcA3\">收藏能够看到的只有你自己，点赞能够帮忙顶上去，看到的人就会多得多。</p><p data-pid=\"UKXcmJix\">予人玫瑰，手留余香，请点赞。</p><br/><p data-pid=\"3k0lHdBl\">感谢阅读。</p>", "excerpt": "这个问题就像我生活里经常会问自己的一个问题： <b>为什么毕业一年，有的朋友已经买了路虎极光，或者特斯拉，而我连驾照都还没考？ </b>上学时候，老师经常会说，大家的起跑线都是一样的。 是的，老师说得对，王思聪说了，他爸也就帮了他一下，赞助了一点钱。好家伙，他爸的一个小目标就是一个亿，全中国有几个人能这么说话的？ 家庭背景的不一样，本身就存在着一定的不公平性，但是我们要突破的就是这不公平。因为每一个牛逼货都是从不…", "excerpt_new": "这个问题就像我生活里经常会问自己的一个问题： <b>为什么毕业一年，有的朋友已经买了路虎极光，或者特斯拉，而我连驾照都还没考？ </b>上学时候，老师经常会说，大家的起跑线都是一样的。 是的，老师说得对，王思聪说了，他爸也就帮了他一下，赞助了一点钱。好家伙，他爸的一个小目标就是一个亿，全中国有几个人能这么说话的？ 家庭背景的不一样，本身就存在着一定的不公平性，但是我们要突破的就是这不公平。因为每一个牛逼货都是从不…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "need_payment", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1495348838, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1495348757603", "type": "feed", "target": {"id": "137975383", "type": "answer", "url": "https://api.zhihu.com/answers/137975383", "voteup_count": 1694, "thanks_count": 594, "question": {"id": "20394372", "title": "工作后的成长速度是如何产生差异的？", "url": "https://api.zhihu.com/questions/20394372", "type": "question", "question_type": "normal", "created": 1343875898, "answer_count": 647, "comment_count": 11, "follower_count": 55839, "detail": "<p></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-0e0513473ac99c262696495ce1526ccf_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"600\" data-rawheight=\"344\" class=\"origin_image zh-lightbox-thumb\" width=\"600\" data-original=\"https://picx.zhimg.com/v2-0e0513473ac99c262696495ce1526ccf_r.jpg\"/></figure><p><b>09.19 起上知乎搜索「求职」，免费领取毕业规划全景图，get 全方位求职指南。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p>本题已收录至<b><a href=\"https://www.zhihu.com/org-campaign/recruitment?utm_campaign=org_recruitment&amp;utm_source=question\" class=\"internal\">知乎首次招聘活动</a></b>：知乎联合 10 家知名互联网企业发布岗位，发送「微简历」即可申请。详情可见：<b><a href=\"https://www.zhihu.com/org-campaign/recruitment?utm_campaign=org_recruitment&amp;utm_source=question\" class=\"internal\">在知乎找工作是一种怎样的体验？</a></b>（仅客户端可见）</p><p><b>本题已加入圆桌 »<a href=\"https://www.zhihu.com/roundtable/burnout\" class=\"internal\">职业倦怠与跳槽须知 </a>更多「职业倦怠」「跳槽」等相关的话题欢迎关注讨论</b></p>", "excerpt": "<b>09.19 起上知乎搜索「求职」，免费领取毕业规划全景图，get 全方位求职指南。</b> 本题…", "bound_topic_ids": [1537, 2566, 3479, 7129, 12585], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "updated_time": 1490781553, "created_time": 1482855222, "author": {"id": "8fdbf1eef176843b767a741ea737e472", "name": "李石", "headline": "量化私募基金合伙人", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/li-shi-87-4", "url_token": "li-shi-87-4", "avatar_url": "https://pic1.zhimg.com/v2-4f3c3aa0e6015219e4ac352d8e35a125_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["创业", "人力资源（HR）", "职场", "职业规划"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "创业等 4 个话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "v2-af063bec99e4decd9aa90c38d709e46f", "avatar_url": "https://picx.zhimg.com/v2-af063bec99e4decd9aa90c38d709e46f_720w.jpg?source=32738c0c", "description": "", "id": "19550560", "name": "创业", "priority": 0, "token": "19550560", "type": "topic", "url": "https://www.zhihu.com/topic/19550560"}, {"avatar_path": "v2-55d5d586064836cfe95fe3393deae1e6", "avatar_url": "https://picx.zhimg.com/v2-55d5d586064836cfe95fe3393deae1e6_720w.jpg?source=32738c0c", "description": "", "id": "19555189", "name": "人力资源（HR）", "priority": 0, "token": "19555189", "type": "topic", "url": "https://www.zhihu.com/topic/19555189"}, {"avatar_path": "v2-15c9885c2487fb2333d49b0005fa6ae6", "avatar_url": "https://pic1.zhimg.com/v2-15c9885c2487fb2333d49b0005fa6ae6_720w.jpg?source=32738c0c", "description": "", "id": "19557876", "name": "职场", "priority": 0, "token": "19557876", "type": "topic", "url": "https://www.zhihu.com/topic/19557876"}, {"avatar_path": "v2-98ba63611671c9911d17432016c70a4e.jpg", "avatar_url": "https://picx.zhimg.com/v2-98ba63611671c9911d17432016c70a4e_720w.jpg?source=32738c0c", "description": "", "id": "19560641", "name": "职业规划", "priority": 0, "token": "19560641", "type": "topic", "url": "https://www.zhihu.com/topic/19560641"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}], "title": "创业等 4 个话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": false, "comment_count": 98, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<blockquote data-pid=\"79gBz9WD\">毕业后工作的成长速度差异是如何造成的？</blockquote><p data-pid=\"pYlcCfae\">回答这个问题首要去除两个关键因素，既<b>既往资源差距</b>和<b>运气</b>。</p><p data-pid=\"tSB8-BOd\">排除以上两个<b>重要因素</b>，假设初始资源一样，大家运气差不多，中长期来看影响成长速度差异的只有两个因素。</p><ol><li data-pid=\"HsoP1Y_1\">能否突破舒适区，进入正反馈的学习区。</li><li data-pid=\"HnXCJeIB\">能否不断突破舒适区，进入正反馈的学习区。</li></ol><br/><p data-pid=\"QFnxcz3n\">这个两个因素衡量起来十分简单，随时可以反观自照。</p><p data-pid=\"orxSN0v3\">如果你现在的学习和工作很轻松，你就在舒适区。</p><p data-pid=\"MJnnqftV\">如果你现在的学习和工作困难、痛苦、抓狂、崩溃，你还能坚持下来，在纷繁中找到线索，在压力下找到路径，你就是在学习区。</p><p data-pid=\"g-eAgIoM\">学习和工作都不难。难得是：</p><ol><li data-pid=\"akK4X-ME\"><b>拿出勇气走出舒适区；<br/></b></li><li data-pid=\"HJUHDKH5\"><b>运用智慧建立反馈点；</b></li><li data-pid=\"sKiZXMCo\"><b>坚守信念挺过难关，不放弃。</b></li></ol><p data-pid=\"8C-Sgedv\">真正的成长是没办法规划、没办法设计的，只有走到悬崖边上，迎风一跃，你才知道自己是否能够飞翔。不磕个头破血流，就想领悟真知灼见，哪有这么美的事。没有经过血汗浇灌，随便翻翻就能学到的干货，在生死关头你真的敢信么？</p><p data-pid=\"p__z9Wyu\"><b>被动走到学习区是大运气，主动走到学习区是大勇气。</b></p><p data-pid=\"N4N11stA\">想成长的快，如果你没有足够的运气，最好就拿出勇气。</p><p data-pid=\"8Hkqj0WF\"><b>下一次，人生的岔路上，难易自选，云泥随缘。</b></p><br/><br/><br/><br/><p data-pid=\"CHNFc78i\">————————广告——————————</p><p data-pid=\"aPg0d7wC\"><b>1月8日的 <a href=\"https://www.zhihu.com/lives/798177377090961408\" class=\"internal\">知乎 Live </a>管理常识课，欢迎捧场。</b></p>", "excerpt": "毕业后工作的成长速度差异是如何造成的？回答这个问题首要去除两个关键因素，既 <b>既往资源差距</b>和<b>运气</b>。排除以上两个 <b>重要因素</b>，假设初始资源一样，大家运气差不多，中长期来看影响成长速度差异的只有两个因素。能否突破舒适区，进入正反馈的学习区。能否不断突破舒适区，进入正反馈的学习区。 这个两个因素衡量起来十分简单，随时可以反观自照。 如果你现在的学习和工作很轻松，你就在舒适区。 如果你现在的学习和工作困难、痛苦…", "excerpt_new": "毕业后工作的成长速度差异是如何造成的？回答这个问题首要去除两个关键因素，既 <b>既往资源差距</b>和<b>运气</b>。排除以上两个 <b>重要因素</b>，假设初始资源一样，大家运气差不多，中长期来看影响成长速度差异的只有两个因素。能否突破舒适区，进入正反馈的学习区。能否不断突破舒适区，进入正反馈的学习区。 这个两个因素衡量起来十分简单，随时可以反观自照。 如果你现在的学习和工作很轻松，你就在舒适区。 如果你现在的学习和工作困难、痛苦…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "need_payment", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1495348757, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1495348672812", "type": "feed", "target": {"id": "20394372", "title": "工作后的成长速度是如何产生差异的？", "url": "https://api.zhihu.com/questions/20394372", "type": "question", "question_type": "normal", "created": 1343875898, "answer_count": 647, "comment_count": 11, "follower_count": 55839, "detail": "<p></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-0e0513473ac99c262696495ce1526ccf_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"600\" data-rawheight=\"344\" class=\"origin_image zh-lightbox-thumb\" width=\"600\" data-original=\"https://picx.zhimg.com/v2-0e0513473ac99c262696495ce1526ccf_r.jpg\"/></figure><p><b>09.19 起上知乎搜索「求职」，免费领取毕业规划全景图，get 全方位求职指南。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p>本题已收录至<b><a href=\"https://www.zhihu.com/org-campaign/recruitment?utm_campaign=org_recruitment&amp;utm_source=question\" class=\"internal\">知乎首次招聘活动</a></b>：知乎联合 10 家知名互联网企业发布岗位，发送「微简历」即可申请。详情可见：<b><a href=\"https://www.zhihu.com/org-campaign/recruitment?utm_campaign=org_recruitment&amp;utm_source=question\" class=\"internal\">在知乎找工作是一种怎样的体验？</a></b>（仅客户端可见）</p><p><b>本题已加入圆桌 »<a href=\"https://www.zhihu.com/roundtable/burnout\" class=\"internal\">职业倦怠与跳槽须知 </a>更多「职业倦怠」「跳槽」等相关的话题欢迎关注讨论</b></p>", "excerpt": "<b>09.19 起上知乎搜索「求职」，免费领取毕业规划全景图，get 全方位求职指南。</b> 本题…", "bound_topic_ids": [1537, 2566, 3479, 7129, 12585], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1495348672, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1494257285885", "type": "feed", "target": {"id": "51489241", "type": "answer", "url": "https://api.zhihu.com/answers/51489241", "voteup_count": 7923, "thanks_count": 3032, "question": {"id": "31154592", "title": "李叫兽是如何搜集和整理信息的？", "url": "https://api.zhihu.com/questions/31154592", "type": "question", "question_type": "normal", "created": 1433917557, "answer_count": 28, "comment_count": 4, "follower_count": 5106, "detail": "<p>相关问题：<a href=\"https://www.zhihu.com/question/59981279/answer/170940332\" class=\"internal\">“李叫兽”是如何推广个人微信公众号的？</a></p><p>李叫兽是如何搜集和整理信息的？如何建立的知识体系？</p>", "excerpt": "相关问题：<a href=\"https://www.zhihu.com/question/59981279/answer/170940332\" class=\"internal\">“李叫兽”是如何推广个人微信公众号的？</a>李叫兽是如何搜集和整理信息的？…", "bound_topic_ids": [196, 3356, 10272, 74067, 170979], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "16cbdfa8a9c2e80eff8011f31241679a", "name": "毕加索的夜曲", "headline": "an idiot", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/james-zheng-72", "url_token": "james-zheng-72", "avatar_url": "https://picx.zhimg.com/087fd0e15a20951c0898a84846c0efb6_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1434440463, "created_time": 1434391979, "author": {"id": "6a422fb2ea7dc5ede2686ecf53d3930e", "name": "李靖", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/li-jing-20-69", "url_token": "li-jing-20-69", "avatar_url": "https://picx.zhimg.com/0638f512c334aece83a0562d8dfd93e5_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 326, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"HX4zzeDC\">谢邀，我如何打造知识体系？</p><p data-pid=\"yLcxrSbg\">我想很多人看到这个问题，期待的答案是一个书单，可是我要告诉你这并没有什么卵用。</p><p data-pid=\"cOpEkY55\"><b>我想大部分人都经历过高中，你会发现高考650分的人和450分的人书单基本上是一样的。书单本身并不能造成知识体系的差异，甚至接触信息的数量本身也不能。</b></p><p data-pid=\"isaASV3R\">可是为什么有的人总是让人感觉“充满洞见”，“具有启发性”，“能谈笑风生”，而有的人却不能？</p><p data-pid=\"_7Np9EN-\">这往往并不是因为他们接触了更多的信息，找到了更适合搜信息的网站，或者偶然获得了绝密的书单，<b>而是因为他们处理信息的方式、看书的方式与众不同。</b></p><p data-pid=\"9V5K7z1-\">而我就讲讲我是如何整理信息，并且获得“系统化知识体系”的。(前方鸡汤预警)</p><p data-pid=\"5FTwQMxQ\"><b><u>1，建立知识之间的联系</u></b></p><br/><p data-pid=\"7xG6rGLz\">我在看书的时候，每看到一个有用的知识，都停下来去寻找联系——<b>看看有什么其他的现象能够被这个理论解释。</b></p><p data-pid=\"ypAH_xhS\">比如我在一本讲神经心理学的书籍中偶然看到这样一句话：</p><figure><noscript><img src=\"https://pic1.zhimg.com/4cee02903773fa6a05950e663db9e2aa_b.jpg\" data-rawwidth=\"587\" data-rawheight=\"433\" data-original-token=\"4cee02903773fa6a05950e663db9e2aa\" class=\"origin_image zh-lightbox-thumb\" width=\"587\" data-original=\"https://pic1.zhimg.com/4cee02903773fa6a05950e663db9e2aa_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;587&#39; height=&#39;433&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"587\" data-rawheight=\"433\" data-original-token=\"4cee02903773fa6a05950e663db9e2aa\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"587\" data-original=\"https://pic1.zhimg.com/4cee02903773fa6a05950e663db9e2aa_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/4cee02903773fa6a05950e663db9e2aa_b.jpg\"/></figure><blockquote data-pid=\"_74T-FHx\"><b>人的爬行脑（控制人的欲望的那部分大脑）更加喜欢视觉化的信息，而不是抽象的信息。</b></blockquote><br/><p data-pid=\"YO3t22S1\">这句话如此之简单，任何人看完、学习完甚至把它背得滚瓜烂熟都不会超过5秒钟。</p><p data-pid=\"Xp4lbtCB\">但是我却当场就学了1个小时以上。</p><p data-pid=\"4tCshrr0\">我问自己：<b>我遇到的那些现象可以被这个理论所解释呢？</b>如果不找出至少5个现象我是不会罢休的。</p><p data-pid=\"TPIl8ubB\">首先我想到了iPod的文案“把1000首歌放到口袋里”，这句话显然比“小体积大容量的mp3”要好的多，因为上面说了“人的大脑更加容易理解和记忆视觉化的信息”，而“1000首歌放在口袋里”能够就是更加视觉化的信息。</p><figure><noscript><img src=\"https://pic4.zhimg.com/fdc54cf7238d8655af91f2f3fcf6f9b1_b.jpg\" data-rawwidth=\"1212\" data-rawheight=\"827\" data-original-token=\"fdc54cf7238d8655af91f2f3fcf6f9b1\" class=\"origin_image zh-lightbox-thumb\" width=\"1212\" data-original=\"https://pic4.zhimg.com/fdc54cf7238d8655af91f2f3fcf6f9b1_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1212&#39; height=&#39;827&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1212\" data-rawheight=\"827\" data-original-token=\"fdc54cf7238d8655af91f2f3fcf6f9b1\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1212\" data-original=\"https://pic4.zhimg.com/fdc54cf7238d8655af91f2f3fcf6f9b1_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/fdc54cf7238d8655af91f2f3fcf6f9b1_b.jpg\"/></figure><p data-pid=\"Ld4JSaGf\">既然这样，还有哪些视觉化的文案呢？比如“能拍星星的手机”？“只融在口，不融在手？”</p><p data-pid=\"K9P5yya_\">除了广告文案，还有什么现象可以被这个理论解释？</p><p data-pid=\"gYHI4Uw5\"><b>对了，“理论的传播”本身就可以被这个理论解释啊！</b></p><p data-pid=\"OsXpT6jS\">“只要抓住机会，即使你能力不高，也更有可能成功”，这句话早就有了，但是并没有被大家挂在嘴边。</p><p data-pid=\"DUNXtu3f\">直到雷军说了句“台风口上，猪也会飞。”比起前面抽象的理论，这是更加视觉化的表达，自然容易朗朗上口，从而被记住和传播。</p><figure><noscript><img src=\"https://pic4.zhimg.com/11d07b2a99a28785d2a5046ef30f4941_b.jpg\" data-rawwidth=\"437\" data-rawheight=\"287\" data-original-token=\"11d07b2a99a28785d2a5046ef30f4941\" class=\"origin_image zh-lightbox-thumb\" width=\"437\" data-original=\"https://pic4.zhimg.com/11d07b2a99a28785d2a5046ef30f4941_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;437&#39; height=&#39;287&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"437\" data-rawheight=\"287\" data-original-token=\"11d07b2a99a28785d2a5046ef30f4941\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"437\" data-original=\"https://pic4.zhimg.com/11d07b2a99a28785d2a5046ef30f4941_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/11d07b2a99a28785d2a5046ef30f4941_b.jpg\"/></figure><p data-pid=\"HXfzLB9H\">哦对，说到“朗朗上口”，众多的谚语也是流传多年、朗朗上口啊，那么它们是不是“视觉化”的呢？</p><p data-pid=\"8ul_x5fC\"><b>的确是的，谚语并没有说：</b></p><p data-pid=\"F2x7ZoJJ\">“同时实现多个目标”</p><p data-pid=\"BnJphLQH\">“拿在手里的机会才是最重要的”</p><p data-pid=\"DbVV1F5n\">“敌人现在很害怕”</p><p data-pid=\"JAfOjhLs\">“不要第一个出风头”</p><p data-pid=\"D-9T-aYD\">“早点行动更加有机会”</p><p data-pid=\"25Z29zSe\"><strong>而是说：</strong></p><p data-pid=\"9W9jb9j9\">“一石二鸟”</p><p data-pid=\"7cR-AIsE\">“双鸟在林，不如一鸟在手”</p><p data-pid=\"y-ydVS3j\">“敌人如惊弓之鸟”</p><p data-pid=\"F5nIV0HG\">“枪打出头鸟”</p><p data-pid=\"kK9JxCa1\">“早起的鸟有虫吃”？</p><br/><p data-pid=\"HV_lubnD\"><b>人们口口流传的，除了谚语，还有什么？</b></p><br/><p data-pid=\"2ZvnmVb7\">自然是谣言了，实际上，谣言也是视觉化的信息：</p><p data-pid=\"UVajgZ6P\">“wifi会杀精”，而不是说更加抽象的“wifi对健康不好”；</p><br/><p data-pid=\"FhEKun1v\">还有呢？</p><br/><p data-pid=\"ara3Rvdp\"><b>古人对抽象现象的解释也是视觉化的</b>（这样更加容易理解），为了解释成雨的原因，他们塑造了雷公电母和龙王，它们都有明确的视觉化形象。他们更加容易相信龙王这样的视觉化信息，而不是抽象的自然原因。</p><br/><p data-pid=\"RkSACZmo\"><b>甚至很多伟大的演讲也是视觉化的。比如马丁路德金并不是说：</b></p><blockquote data-pid=\"P2RC5Cko\">“我们追求人人生而平等，我们要减少种族歧视！”</blockquote><br/><p data-pid=\"gkAvUdIi\"><b>而是说：</b></p><blockquote data-pid=\"o42fa70q\">“我梦想有一天，在佐治亚的红山上，昔日奴隶的儿子将能够和昔日奴隶主的儿子坐在一起，共叙兄弟情谊。”</blockquote><p data-pid=\"1FhApOVg\"><b>还有呢？</b>我喜欢足球，突然发现球员的绰号也是视觉化的，比如“独狼罗马里奥”、“小坦克鲁尼”、“小跳蚤梅西”，而不是抽象的“一意孤行罗马里奥”、“勇猛鲁尼”和“灵活梅西”。</p><p data-pid=\"NjsVO20i\">……</p><p data-pid=\"ubQuwYru\">“爬行脑更喜欢视觉化的信息”，这句话如此简单，1秒就能看完，但是用这种方法，即使看再多的书又有什么用？</p><p data-pid=\"I7OajMoD\">书上可能会对理论举例说明，但是<b>那仅仅是别人使用理论解释现象，并不代表你也可以用同样的理论来解释现象。</b></p><p data-pid=\"JM_tbqHI\">所以我在看书或者接触任意一种信息的时候，一旦发现有价值的理论，总是问自己：<b>还有哪些现象可以被这个理论解释？还有呢？还有呢？（一般要找到5个以上）</b></p><p data-pid=\"fZ8naiLi\">之所以这样，是因为<b>真正导致人与人之间知识水平差异的，往往并不是知识数量，而是知识之间的联系。</b>而且随着知识的增多，建立联系的收获也会越来越大。</p><p data-pid=\"PTqRe9sb\">当你只有一个知识的时候，增长1个知识可能就是增长一个知识。但是当你有10000个知识，接触1个新知识可能意味着增长了5000个知识——因为你跟其中一半的知识建立了联系。</p><p data-pid=\"juTeW7QO\"><b><u>2，构思知识的多种用法</u></b></p><p data-pid=\"4qrfrerH\">当我接触了一个新知识的时候，不光要想“过去的哪些现象可以被这个知识解释？”，还要想“<b>我的哪些行为可以被这个知识所改进？</b>”</p><p data-pid=\"ZSmG3pfH\">比如仍然是上面的知识点，“人的大脑喜欢视觉化”，我的哪些行为或者工作可以被这个知识所改进呢？</p><p data-pid=\"jzwkJcV5\">首先就是我自己的公众号，过几周可能要写写“产品的象征性价值”，如果视觉化的话，不如改成“给用户带上合适的帽子：你的产品会想帽子一样，影响用户的形象。”</p><p data-pid=\"xzxhnYgj\">还有我的演讲，比如前段时间的《你为什么会写自嗨型文案》中，我把两种文案写法（追求语言的华丽VS追求影响用户的感受）人格化成了两种人——X型文案人和Y型文案人，从而让人一下子记住。</p><p data-pid=\"tqeqX-g8\">还有，我甚至想到了将来求婚的时候，不应该说<b>“我想和你永远在一起</b>”，而是“<b>我希望有一天我们都70岁了，仍然能手牵手走在XX的沙滩上。</b>”（更加视觉化）</p><figure><noscript><img src=\"https://pic4.zhimg.com/144865b9754689d8f181795cafd29a5d_b.jpg\" data-rawwidth=\"674\" data-rawheight=\"452\" data-original-token=\"144865b9754689d8f181795cafd29a5d\" class=\"origin_image zh-lightbox-thumb\" width=\"674\" data-original=\"https://pic4.zhimg.com/144865b9754689d8f181795cafd29a5d_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;674&#39; height=&#39;452&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"674\" data-rawheight=\"452\" data-original-token=\"144865b9754689d8f181795cafd29a5d\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"674\" data-original=\"https://pic4.zhimg.com/144865b9754689d8f181795cafd29a5d_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/144865b9754689d8f181795cafd29a5d_b.jpg\"/></figure><p data-pid=\"AfjfQ2FG\">总之，当你学习了一个新的知识，一定要提前构思一下——<b>这个知识会如何改变我的行为？将来我可能会怎么用它？</b></p><p data-pid=\"H8j5FDK8\">为什么呢？</p><p data-pid=\"MNmVPN0m\"><b>因为很多人的问题并不是缺乏知识，而是到了某个时刻想不起来应该用什么知识。</b>而这种提前的设想（将来我可能会用这个知识来解决什么问题），就可以让你形成一种“自动触发”，到了某个问题，就可以立马想到相应的知识。</p><p data-pid=\"8xvBACs4\">就好像熟练的司机开车遇到突然状况的时候，不用思考也知道松油门踩刹车踩离合——这些知识已经形成了“自动触发”，可以很容易被应用。</p><p data-pid=\"OL57LNso\"><b><u>3，尝试去探索事物背后的原因</u></b></p><p data-pid=\"tCAXMEn3\">大部分人满足于既定的任务和日常的生活，不想去深入探索事物背后的原因。<b>而有一部分人却在别人停止思考、对周围发生的一切习以为常的时候，仍然积极地探索答案。</b></p><p data-pid=\"64d7v1ct\">比如在有一次出去吃饭的时候，我走进一个很廉价的盖浇饭餐厅，见到了这种菜单：</p><figure><noscript><img src=\"https://picx.zhimg.com/4731b7d1026ccc6f3952d3b962444d95_b.jpg\" data-rawwidth=\"700\" data-rawheight=\"525\" data-original-token=\"4731b7d1026ccc6f3952d3b962444d95\" class=\"origin_image zh-lightbox-thumb\" width=\"700\" data-original=\"https://picx.zhimg.com/4731b7d1026ccc6f3952d3b962444d95_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;700&#39; height=&#39;525&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"700\" data-rawheight=\"525\" data-original-token=\"4731b7d1026ccc6f3952d3b962444d95\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"700\" data-original=\"https://picx.zhimg.com/4731b7d1026ccc6f3952d3b962444d95_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/4731b7d1026ccc6f3952d3b962444d95_b.jpg\"/></figure><br/><br/><p data-pid=\"t_zglkqe\">同行的朋友都觉得这样的菜单隐隐让自己很不舒服，不知道该选什么。但他们都让老板推荐了几个菜之后开始吃起来，忘记了刚刚“不舒服”的选择过程。</p><p data-pid=\"lWdoRDkq\"><b>而我却无法容忍这种模糊的感觉，希望探究一下背后的道理。</b>然后想着想着，突然想到了很久之前看过的一个心理学实验：</p><p data-pid=\"37PZF87v\">研究人员随机找了2组消费者，在两组面前都呈现了一系列昂贵的果酱，并且给他们提供试吃机会和优惠券。</p><br/><p data-pid=\"sReH-e9z\">不同的是A组消费者面前摆着6款不同的果酱，B组消费者面前摆着24种不同的果酱。</p><br/><figure><noscript><img src=\"https://pic4.zhimg.com/d40ab8f74083cc3d27066734494b2293_b.jpg\" data-rawwidth=\"701\" data-rawheight=\"353\" data-original-token=\"d40ab8f74083cc3d27066734494b2293\" class=\"origin_image zh-lightbox-thumb\" width=\"701\" data-original=\"https://pic4.zhimg.com/d40ab8f74083cc3d27066734494b2293_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;701&#39; height=&#39;353&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"701\" data-rawheight=\"353\" data-original-token=\"d40ab8f74083cc3d27066734494b2293\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"701\" data-original=\"https://pic4.zhimg.com/d40ab8f74083cc3d27066734494b2293_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/d40ab8f74083cc3d27066734494b2293_b.jpg\"/></figure><br/><p data-pid=\"QRh1Nxby\"><b>结果发现，A组中有30%的人最终购买了果酱，而B组中只有3%的人购买</b>——都说选择多是好事，但过多的选择让B组消费者在比较的过程中心疲力竭，最终放弃了购买。</p><br/><p data-pid=\"p8Aky6Ni\">在心理学上，这种现象叫做“决策瘫痪”，指选择过多的情况下，人会因为对比选项耗费的过多精力而直接放弃做决策。</p><br/><p data-pid=\"yLOvWeEF\"><b>这不就是家餐厅菜单的问题吗？</b>主人自己觉得要为消费者提供更多的选择，但是这么多选择反而过多耗费了消费者的脑力，最终降低了所有选项的吸引力。</p><br/><p data-pid=\"GLk5wYuk\"><b>然后我再继续想，还有什么事件和这个是类似的？</b></p><p data-pid=\"HUJX_p4x\">比如肯德基为了方便选择，菜单只有20多道菜，甚至还推出了套餐来简化选择。</p><br/><p data-pid=\"IbFOCLW8\">再比如很多互联网公司越来越喜欢“单品爆款”而不是“机海战术”。</p><br/><p data-pid=\"QyGH1fLA\"><b>还要再想，将来我有什么行为可以因为这个知识而改进？</b></p><p data-pid=\"o11R2Rff\">结果还真的有。</p><br/><p data-pid=\"WcPw1QTV\">有一次我做募捐的时候就使用了这个技巧，一开始队友都是说：“有一个XXX公益项目，请支持一下，捐一些钱吧，多少都行！”</p><br/><p data-pid=\"EQ_RQGe9\">而我就想到了自己的这次经历，觉得这样说会提高捐款人做决定的成本（他还需要想一下到底捐多少合适），因此建议队友改为：“帮帮这些孩子，捐10块钱吧！”</p><br/><p data-pid=\"staNFkPM\">结果募捐的数量大幅度上涨。</p><br/><p data-pid=\"3h98yt6G\"><b>很多人可以容忍生活中的奇怪现象和模糊性。在他们眼里，世界已经变得如此熟悉，所有的事情都可以见怪不怪了。</b></p><br/><p data-pid=\"xKaTnpcV\"><b>但是我却无法容忍这种模糊性和一知半解。</b></p><br/><p data-pid=\"gvZ8FXtb\">再比如我本科在武大时，曾在快递点看到一个牌子:</p><figure><noscript><img src=\"https://pic2.zhimg.com/4338679a69662b35f9536e190f3215e3_b.jpg\" data-rawwidth=\"577\" data-rawheight=\"497\" data-original-token=\"4338679a69662b35f9536e190f3215e3\" class=\"origin_image zh-lightbox-thumb\" width=\"577\" data-original=\"https://pic2.zhimg.com/4338679a69662b35f9536e190f3215e3_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;577&#39; height=&#39;497&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"577\" data-rawheight=\"497\" data-original-token=\"4338679a69662b35f9536e190f3215e3\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"577\" data-original=\"https://pic2.zhimg.com/4338679a69662b35f9536e190f3215e3_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/4338679a69662b35f9536e190f3215e3_b.jpg\"/></figure><p data-pid=\"8yzfRFlT\">看到后我觉得有点怪怪的，然后就停下脚步去想，还拍了下来。</p><br/><p data-pid=\"C2KTCM3p\">最终发现哪里奇怪了：<b>快递公司把“武大分部”这样的信息放在显著位置，却把“申通快递”这样的信息给边缘化。</b></p><br/><p data-pid=\"Ccj5s28T\">从设计角度来讲，这个牌子的核心表达的信息是“武大分部”，可是对于一个放在武大的牌子来说，“武大分部”有什么意义？难道武大会出现“华科分部”这样的牌子？</p><br/><p data-pid=\"5fcVTKZ7\"><b>但是对申通快递来说，这样做却又是合理的</b>——对他们的快递员来说，所有的牌子都是“申通快递”，因此“申通快递”这几个字是没有用的，他们更加关系的是“华科分部”还是“武大分部”这样的信息。</p><br/><p data-pid=\"hAZrAB0Z\">所以，用互联网时髦词汇来说，这个牌子并不是“用户导向”的。</p><br/><p data-pid=\"jfnG6nGi\"><b>然后再想，有没有其他人研究过这个呢？</b></p><br/><p data-pid=\"DxuWDM9a\">后来我学了心理学才知道perspective taking的理论，人考虑问题习惯性先从自己出发，而不是对方出发。</p><br/><p data-pid=\"MkK1m2gJ\"><b>然后还有哪些现象可以被这个解释呢？</b>比如我过去发邮件简历的时候，命名直接是“简历.pdf”而不是“李靖.pdf”。</p><br/><p data-pid=\"PPnuEhMw\">再比如早上看到太阳，所有人都会说“日出了”（自我视角），而不是更加客观的“地转了”。</p><figure><noscript><img src=\"https://pic4.zhimg.com/ca99f01d0f1ee4ea470e4f21dc8138c3_b.jpg\" data-rawwidth=\"602\" data-rawheight=\"314\" data-original-token=\"ca99f01d0f1ee4ea470e4f21dc8138c3\" class=\"origin_image zh-lightbox-thumb\" width=\"602\" data-original=\"https://pic4.zhimg.com/ca99f01d0f1ee4ea470e4f21dc8138c3_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;602&#39; height=&#39;314&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"602\" data-rawheight=\"314\" data-original-token=\"ca99f01d0f1ee4ea470e4f21dc8138c3\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"602\" data-original=\"https://pic4.zhimg.com/ca99f01d0f1ee4ea470e4f21dc8138c3_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/ca99f01d0f1ee4ea470e4f21dc8138c3_b.jpg\"/></figure><br/><p data-pid=\"ZAhKyq44\">实际上，如果仔细观察历史上的“洞察力”事件，路径经常出人意料地相似——在大部分人对反常的现象视而不见、放弃思考的时候，总有一部分人选择去解释它、探究它。<b>因为他们无法容忍自相矛盾、不明不白和模糊性。</b></p><br/><p data-pid=\"byZ6n6De\"><b>比如X射线的发明：</b></p><p data-pid=\"lUIe4rEB\">伦琴在做研究时，发现及时用纸板盖住了阴极射线发射器，房间另一头的氰亚铂酸盐钡屏幕还是会发光。</p><br/><p data-pid=\"OXqIX92e\">其他很多研究者都发现了这种现象，但是他们都把这种现象解释为“设备疏漏”等原因。而伦琴却无法接受这种牵强的解释，于是花费精力去探究，最终发现了X射线。</p><br/><p data-pid=\"vI59FI3m\"><b><u>结语：</u></b></p><p data-pid=\"ODxoWruV\">经常有人问我“你是如何构建的知识体系”，写出来后，我却发现原来如此之简单：</p><ul><li data-pid=\"W91NkK9f\"><b>建立知识之间的联系</b>——看到知识后问自己：还有什么现象可以被这个知识解释？还有呢？还有呢……<br/></li><li data-pid=\"K7UEZ8Q_\"><b>构思知识的多种用法</b>——问自己：这个知识可以用于做哪些事？还有呢？还有呢……<br/></li><li data-pid=\"6ltHT6EI\"><b>探索事物背后的原因</b>——遇到反常或者有趣的事情，问自己：为什么会这样？有什么理论或者知识可以解释？有哪些相似的事件？<br/></li></ul><br/><p data-pid=\"YghfCNAS\">我知道很多人会不喜欢这个答案，他们更加期待的是具体的一套工具，<b>比如一个思维的模板、一个整理知识的软件、一个搜索知识的网站或者一个优秀的书单</b>。</p><br/><p data-pid=\"fJnDTf4I\">但是我提供的却是一种“无法看了就会”的方法。这种方法就像“俯卧撑训练技巧”一样，无法让你看到之后就能增长胸肌，非得自己付出大量的努力来训练才行。</p><figure><noscript><img src=\"https://picx.zhimg.com/e5e3897c511a39049f322e30b7ff1f13_b.jpg\" data-rawwidth=\"545\" data-rawheight=\"372\" data-original-token=\"e5e3897c511a39049f322e30b7ff1f13\" class=\"origin_image zh-lightbox-thumb\" width=\"545\" data-original=\"https://picx.zhimg.com/e5e3897c511a39049f322e30b7ff1f13_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;545&#39; height=&#39;372&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"545\" data-rawheight=\"372\" data-original-token=\"e5e3897c511a39049f322e30b7ff1f13\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"545\" data-original=\"https://picx.zhimg.com/e5e3897c511a39049f322e30b7ff1f13_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/e5e3897c511a39049f322e30b7ff1f13_b.jpg\"/></figure><br/><p data-pid=\"3Cvh6ePn\">实际上，洞察力、知识网络化等能力增长的确跟肌肉增长原理差不多。<b>肌肉增长需要大量的刻意训练来刺激肌纤维的生长，而洞察力等能力也需要大量的思考、练习和探究，来刺激新的大脑神经突触的增长。</b></p><br/><p data-pid=\"_lrHR5sb\">所以，如何构建庞大的知识网络？并不是要单纯多看书多接触世界就行了，而是有“好奇心”，总是想建立联系，想用知识提升现在的工作，想问“为什么”。</p><br/><br/><p data-pid=\"sDJ_qy6b\"><b>小时候你看到天空是蓝的，你会问：妈妈，天空为什么是蓝的啊？</b>所以你一直在进步、一直在理解这个世界。</p><br/><p data-pid=\"LQn3qgVA\">直到后来，不知道什么时候，你对所有的事情都习以为常，失去了对世界的好奇心，也就渐渐失去了洞察力。<b>当你开始觉得，天是蓝的是因为它一直是蓝的，公司有这个规定是因为它一直是这样做的，某个偏方有效是因为一千多年来一直是这样，那么你就失去了洞察力。</b></p><p data-pid=\"3PiuM5bv\">John Carter教授对哈佛大学商学院几十年的跟踪研究发现，取得伟大成就的人和取得一般成就的人最大的区别就是2点：</p><br/><p data-pid=\"f2zB8p1f\"><b>1，他们相信自己，他们认为自己可以改变些什么。</b></p><p data-pid=\"5pgA0BFq\"><b>2，他们喜欢问为什么。</b>他们无法容忍自相矛盾、模糊性和不清不明，他们保持着对世界的好奇心。</p><p data-pid=\"VRA4D2dP\">最后，借用乔布斯的一句话：</p><figure><noscript><img src=\"https://pic2.zhimg.com/d93cd16d043043b2589ade0390c9067f_b.jpg\" data-rawwidth=\"1434\" data-rawheight=\"778\" data-original-token=\"d93cd16d043043b2589ade0390c9067f\" class=\"origin_image zh-lightbox-thumb\" width=\"1434\" data-original=\"https://pic2.zhimg.com/d93cd16d043043b2589ade0390c9067f_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1434&#39; height=&#39;778&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1434\" data-rawheight=\"778\" data-original-token=\"d93cd16d043043b2589ade0390c9067f\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1434\" data-original=\"https://pic2.zhimg.com/d93cd16d043043b2589ade0390c9067f_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/d93cd16d043043b2589ade0390c9067f_b.jpg\"/></figure><br/><br/><p data-pid=\"1KjaLava\">喜欢更多的启发式分析，搜索并关注我的微信公众号“李叫兽”（ID: Professor-Li）</p>", "excerpt": "谢邀，我如何打造知识体系？ 我想很多人看到这个问题，期待的答案是一个书单，可是我要告诉你这并没有什么卵用。 <b>我想大部分人都经历过高中，你会发现高考650分的人和450分的人书单基本上是一样的。书单本身并不能造成知识体系的差异，甚至接触信息的数量本身也不能。</b>可是为什么有的人总是让人感觉“充满洞见”，“具有启发性”，“能谈笑风生”，而有的人却不能？ 这往往并不是因为他们接触了更多的信息，找到了更适合搜信息的…", "excerpt_new": "谢邀，我如何打造知识体系？ 我想很多人看到这个问题，期待的答案是一个书单，可是我要告诉你这并没有什么卵用。 <b>我想大部分人都经历过高中，你会发现高考650分的人和450分的人书单基本上是一样的。书单本身并不能造成知识体系的差异，甚至接触信息的数量本身也不能。</b>可是为什么有的人总是让人感觉“充满洞见”，“具有启发性”，“能谈笑风生”，而有的人却不能？ 这往往并不是因为他们接触了更多的信息，找到了更适合搜信息的…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1494257285, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1494257285885&page_num=76"}}