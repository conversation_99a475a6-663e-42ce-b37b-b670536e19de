{"data": [{"id": "1437611198593", "type": "feed", "target": {"id": "24900201", "type": "answer", "url": "https://api.zhihu.com/answers/24900201", "voteup_count": 145, "thanks_count": 72, "question": {"id": "19593179", "title": "关于 Python 的经典入门书籍有哪些？", "url": "https://api.zhihu.com/questions/19593179", "type": "question", "question_type": "normal", "created": 1302271340, "answer_count": 153, "comment_count": 6, "follower_count": 6398, "detail": " 适合完全没有编程基础的新手使用。 ", "excerpt": "适合完全没有编程基础的新手使用。 ", "bound_topic_ids": [870, 872, 1354, 35436], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "360fe533b2ee53181234cac7f5d843c0", "name": "timwong", "headline": "PM", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/timwong", "url_token": "timwong", "avatar_url": "https://picx.zhimg.com/v2-0312df6b08fa48beee3bab2e78bc8fd5_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1453884837, "created_time": 1398405930, "author": {"id": "3049d74682543a3f8f73b57964bf5db3", "name": "<PERSON><PERSON>", "headline": "零基础编程入门，提问请搜索关注：Crossin的编程教室", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/crossin", "url_token": "crossin", "avatar_url": "https://picx.zhimg.com/85b47091c_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "identity", "description": "上海交通大学 计算机应用技术硕士", "topic_names": [], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "上海交通大学 计算机应用技术硕士", "detail_type": "identity", "icon": "", "night_icon": "", "sources": [], "title": "认证", "type": "identity", "url": "https://zhuanlan.zhihu.com/p/96956163"}], "title": "上海交通大学 计算机应用技术硕士"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 15, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"xmJ90l5g\">不少人推荐《<i>Learn Python The Hard Way（笨办法学 Python）</i>》，但也有很多人吐槽过此书只讲其然，不讲其所以然。感觉有点类似学古诗，先背上个几百首，完了之后自然就会了。因此对于此书，真的是仁者见仁智者见智。如果你是个好问为什么的人，我不建议此书。但如果你是个能闷头坚持啃书做题的，借此书可能会上手很快。</p><p data-pid=\"bu3SL0oE\">我推荐完全没有编程基础的新手先看《<i>与孩子一起学编程</i>》，这类书其实是借 python 来讲编程入门。</p><p data-pid=\"Dp1crhwI\">如果有一点点编程基础，而想学 python，建议《简明Python教程》或《Dive into Python》，这两个都有在线中文版本：</p><a href=\"https://link.zhihu.com/?target=http%3A//woodpecker.org.cn/abyteofpython_cn/chinese/index.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">简明 Python 教程</a><a href=\"https://link.zhihu.com/?target=http%3A//woodpecker.org.cn/diveintopython/toc/index.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Dive Into Python</a><br/><p data-pid=\"OLDLhfqN\"><i>另外推荐《head first python》，但最好也需要一点点基础。</i></p><p data-pid=\"ySLyyTl2\">最后是私货：</p><p data-pid=\"jdJJX0NP\">因为我觉得完全面向零基础初学者的编程书实在不多，且不太可能单靠一本书解决所有问题。所以我自己自己写了专门面向零基础新手的 python 教程，并在此基础上做了个小社区。欢迎所有编程新手：</p><a href=\"https://link.zhihu.com/?target=http%3A//www.crossincode.com/home/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Crossin的编程教室 - 编程世界的新手村</a><p data-pid=\"OC524vpo\">或者加微信公众号：crossincode （Crossin的编程教室）</p><figure><noscript><img src=\"https://picx.zhimg.com/4dc741fda074ca085b2a9a68abb67c31_b.png\" data-rawwidth=\"430\" data-rawheight=\"430\" data-tag=\"qrcode\" data-qrvalue=\"http://weixin.qq.com/r/wXWGnqzEDfFyrej99yDT\" data-original-token=\"4dc741fda074ca085b2a9a68abb67c31\" class=\"origin_image zh-lightbox-thumb\" width=\"430\" data-original=\"https://picx.zhimg.com/4dc741fda074ca085b2a9a68abb67c31_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;430&#39; height=&#39;430&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"430\" data-rawheight=\"430\" data-tag=\"qrcode\" data-qrvalue=\"http://weixin.qq.com/r/wXWGnqzEDfFyrej99yDT\" data-original-token=\"4dc741fda074ca085b2a9a68abb67c31\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"430\" data-original=\"https://picx.zhimg.com/4dc741fda074ca085b2a9a68abb67c31_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/4dc741fda074ca085b2a9a68abb67c31_b.png\"/></figure>", "excerpt": "不少人推荐《 <i>Learn Python The Hard Way（笨办法学 Python）</i>》，但也有很多人吐槽过此书只讲其然，不讲其所以然。感觉有点类似学古诗，先背上个几百首，完了之后自然就会了。因此对于此书，真的是仁者见仁智者见智。如果你是个好问为什么的人，我不建议此书。但如果你是个能闷头坚持啃书做题的，借此书可能会上手很快。我推荐完全没有编程基础的新手先看《 <i>与孩子一起学编程</i>》，这类书其实是借 python 来讲编程入门。如果有一点…", "excerpt_new": "不少人推荐《 <i>Learn Python The Hard Way（笨办法学 Python）</i>》，但也有很多人吐槽过此书只讲其然，不讲其所以然。感觉有点类似学古诗，先背上个几百首，完了之后自然就会了。因此对于此书，真的是仁者见仁智者见智。如果你是个好问为什么的人，我不建议此书。但如果你是个能闷头坚持啃书做题的，借此书可能会上手很快。我推荐完全没有编程基础的新手先看《 <i>与孩子一起学编程</i>》，这类书其实是借 python 来讲编程入门。如果有一点…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1437611198, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1437611170068", "type": "feed", "target": {"id": "12316147", "type": "answer", "url": "https://api.zhihu.com/answers/12316147", "voteup_count": 624, "thanks_count": 232, "question": {"id": "19593179", "title": "关于 Python 的经典入门书籍有哪些？", "url": "https://api.zhihu.com/questions/19593179", "type": "question", "question_type": "normal", "created": 1302271340, "answer_count": 153, "comment_count": 6, "follower_count": 6398, "detail": " 适合完全没有编程基础的新手使用。 ", "excerpt": "适合完全没有编程基础的新手使用。 ", "bound_topic_ids": [870, 872, 1354, 35436], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "360fe533b2ee53181234cac7f5d843c0", "name": "timwong", "headline": "PM", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/timwong", "url_token": "timwong", "avatar_url": "https://pica.zhimg.com/v2-0312df6b08fa48beee3bab2e78bc8fd5_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1395993577, "created_time": 1302284849, "author": {"id": "e5fb0aab23f1ebca897594c74d178c79", "name": "涛吴", "headline": "樂 人 無 術", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/Metaphox", "url_token": "Metaphox", "avatar_url": "https://picx.zhimg.com/v2-4339c799599be3058a4527cbce2bf137_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["英语", "编程", "字体", "旅行", "语言学", "德语"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "新知答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": null, "title": "新知答主", "type": "best", "url": "https://zhuanlan.zhihu.com/p/344234033"}], "title": "新知答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 43, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"wWMA-EaE\">适合完全没有编程基础的新手使用？</p><p data-pid=\"rlvDGKxN\"><i>Learn Python The Hard Way </i>是你唯一的、最终的、史诗级的选择。它也许不能和 _why 的 <i>why&#39;s (poignant) Guide to Ruby</i> 重口味相提并论，但是作为不说废话、以练习为导向的教材，它是少林长拳一般的存在。</p><a href=\"https://link.zhihu.com/?target=http%3A//learnpythonthehardway.org/book/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Learn Python The Hard Way, 3rd Edition</a><br/><br/><p data-pid=\"tH4-iDRf\"><b>Update:</b></p><p data-pid=\"MvnggHmZ\">此外，作为做练习间隙的调剂，请下载</p><ul><li data-pid=\"E506HeIH\"><i>Monty Python and the Holy Grail</i> ( <a href=\"https://link.zhihu.com/?target=http%3A//www.imdb.com/title/tt0071853/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Monty Python and the Holy Grail (1975)</a> )</li><li data-pid=\"zVlAM1B5\"><i>Monty Python&#39;s The Meaning of Life</i> ( <a href=\"https://link.zhihu.com/?target=http%3A//www.imdb.com/title/tt0085959/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">The Meaning of Life (1983)</a> )</li><li data-pid=\"VaXkpJRc\"><i>Life of Brian</i> ( <a href=\"https://link.zhihu.com/?target=http%3A//www.imdb.com/title/tt0079470/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Life of Brian (1979)</a> )</li></ul><br/><p data-pid=\"bwwPycSI\">并正襟危坐，反复观看。若不如此，则效果同窥看少林门径而不读《法华经》，将来的下场，就是被扫地僧说一个笑话，你听不懂。</p>", "excerpt": "适合完全没有编程基础的新手使用？ <i>Learn Python The Hard Way </i>是你唯一的、最终的、史诗级的选择。它也许不能和 _why 的 <i>why&#39;s (poignant) Guide to Ruby</i> 重口味相提并论，但是作为不说废话、以练习为导向的教材，它是少林长拳一般的存在。<a href=\"https://link.zhihu.com/?target=http%3A//learnpythonthehardway.org/book/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Learn Python The Hard Way, 3rd Edition</a> <b>Update:</b>此外，作为做练习间隙的调剂，请下载 <i>Monty Python and the Holy Grail</i> ( <a href=\"https://link.zhihu.com/?target=http%3A//www.imdb.com/title/tt0071853/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Monty Python and the Holy Grail (1975)</a> )<i>Monty Python&#39;s The M…</i>", "excerpt_new": "适合完全没有编程基础的新手使用？ <i>Learn Python The Hard Way </i>是你唯一的、最终的、史诗级的选择。它也许不能和 _why 的 <i>why&#39;s (poignant) Guide to Ruby</i> 重口味相提并论，但是作为不说废话、以练习为导向的教材，它是少林长拳一般的存在。<a href=\"https://link.zhihu.com/?target=http%3A//learnpythonthehardway.org/book/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Learn Python The Hard Way, 3rd Edition</a> <b>Update:</b>此外，作为做练习间隙的调剂，请下载 <i>Monty Python and the Holy Grail</i> ( <a href=\"https://link.zhihu.com/?target=http%3A//www.imdb.com/title/tt0071853/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Monty Python and the Holy Grail (1975)</a> )<i>Monty Python&#39;s The M…</i>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1437611170, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1433089201301", "type": "feed", "target": {"id": "31667666", "type": "answer", "url": "https://api.zhihu.com/answers/31667666", "voteup_count": 226, "thanks_count": 80, "question": {"id": "22601442", "title": "想从事数据分析工作，学什么软件或语言最好？", "url": "https://api.zhihu.com/questions/22601442", "type": "question", "question_type": "normal", "created": 1390644396, "answer_count": 46, "comment_count": 4, "follower_count": 1428, "detail": "", "excerpt": "", "bound_topic_ids": [99, 1103, 2470, 3074, 7138], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "9a75e45bff5c9008abae87d078151966", "name": "Crown", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/crown-93", "url_token": "crown-93", "avatar_url": "https://picx.zhimg.com/3a51394da89931b30c22c5fbc523a909_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1503557383, "created_time": 1412953922, "author": {"id": "8b93dbdbc9e5ceaccde60ce2fd238b58", "name": "马六一", "headline": "不想写剧本的数据科学家不是好摄影师", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/datascience", "url_token": "datascience", "avatar_url": "https://picx.zhimg.com/v2-7a8ccd0a04c30dc6f2cbb4f9f405fbe9_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": false, "comment_count": 12, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"OalpZSr9\">贴一篇仿照着之前玩摄影的时候看过的</p><a href=\"https://link.zhihu.com/?target=http%3A//forum.xitek.com/thread-552714-1-1-1.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《镜头七武器》</a><p data-pid=\"ScnCIicU\">写的《数据分析之七武器》</p><p data-pid=\"ygF5c-9w\">***注意这里写的偏重于对商业分析师的要求，而非数据挖掘工程师。这二者基本上是数据分析这一职业类型的两个高级转职分支（类似于法师和术士的关系。。。？）。对数据挖掘工程师的技术要求不太一样（他们肯定会有JAVA和Hadoop）。</p><ul><li data-pid=\"gck-NGvc\">   长生剑——Excel<br/></li></ul><br/><p data-pid=\"h3Swfb_h\">   剑是优雅的代名词，是风度翩翩的君子所爱之物，佩带一把长生剑，练得一套好剑谱，可以以最优雅的方式击倒对手。这正如微软出品的Excel。据我所知，玩Excel的高手们主要集中在金融领域，带高富帅光环的金融男配上长生剑，引来的不只是目光和尖叫，更让你的加班费噌噌上涨。当然，剑也是难用的，会vlookup和pivot table的是初级剑客，会规划求解的是中级剑客，会VBA的是高级剑客，而本人见过的绝顶剑客玩起Excel是从来不用鼠标的。Excel几乎是每个人接触数据开始所用的第一款软件，拥有最美观的界面，最完善的生态，同时又能用macro编程，各种插件已经足以解决大多数传统企业需要解决的问题，把玩价值不亚于任何一款新型软件。Excel是比尔盖茨最引以为傲的产品，是每一个数据分析师心中的上等之剑，同时也是长生之剑（至今依然被广泛使用）</p><br/><br/><ul><li data-pid=\"9Fuh7vC1\">   碧玉刀——WEKA<br/></li></ul><br/><p data-pid=\"KBRzkcT8\">   刀是绿林好汉的最爱，是最容易上手也是最爽快的武器，一刀在手，神挡杀神佛挡杀佛，乃以一当百之不二选择，要问什么软件用起来最爽快？我的回答当属WEKA，各种model通杀，锐不可当，同时却又不失灵活性，模型的各种参数一个不少，他的图形界面虽然不算美观却是非常简洁易用，然而当你知道这款软件是免费的时候，就算你现在用不到，相信你会毫不犹豫去下载下来放在电脑里。它的另一个好处是，它是用JAVA实现的，所以运算非常快，激爽。</p><br/><br/><ul><li data-pid=\"huT7mhbM\">   孔雀翎——SPSS/SAS<br/></li></ul><br/><p data-pid=\"oegHKWK9\">   孔雀翎是极其小巧的，却又是杀伤力巨大的暗器，一旦用起来得心应手，那将杀人于无形，这正如SPSS/SAS，只有图形界面的SPSS，看上去毫不起眼，而且缺陷巨大；SAS能写一些语句，但是在技术达人看来那些其实都是伪装成代码的参数，这么容易就出结果的软件似乎不符合那些技术控们的口味。然而，一旦你掌握了统计方法的精髓，你会发现这两款软件的威力是巨大的。有时候简单的跑个回归或者聚类，实在是没必要写什么代码，click-click省时省力，将你从繁琐的劳动中解放出来，它..实在是最了不起的暗器！</p><br/><br/><ul><li data-pid=\"N51PnXb0\">    多情环——R<br/></li></ul><br/><p data-pid=\"724eq7c5\">    环是浑圆的，是综合能力最平均的兵器，R可能灵活性不如Python，简洁性不及SAS，速度没有JAVA快，然而R的综合能力是最平均的。你可以说R是一种语言，但是它又是交互式的。R的软件界面看上去平淡无奇，然而各种独一无二的package，让他可machine learning，可Bayes statistics，可network science，同时它的ggplot2做出的漂亮的visualization也是令人难忘的，更为神奇的是，它还能移植部分其他软件的功能，去找找package吧，你会发现RJava，RWeka，RSQL等奇葩的存在。。。如此迷离而多情的开源软件，你一定要试试！</p><br/><br/><ul><li data-pid=\"M2nyTSbE\">    离别钩——Python<br/></li></ul><br/><p data-pid=\"NWfB-2tP\">    钩是高手的玩物，虽然能力全面，用起来却不容易，Python，饱受争议，却依然是高手的梦寐之物，它的钩子，就在于它的代码太简洁，以至于你总是怀疑自己是不是在写伪代码。实际上，它使你能够专注于解决问题而不是去搞明白语言本身，正是这个特性把它抬上了神坛，成为数据挖掘领域最不朽的传奇。文本挖掘是Python远超其他几种武器的一大法宝，现行的的库能帮你做到你想到的几乎任何事。不管别人怎么看，至少所有会写Python的数据分析师都觉得自己很酷。</p><br/><br/><ul><li data-pid=\"vQhGB7PX\">    霸王枪——SQL<br/></li></ul><br/><p data-pid=\"MkTtxzK8\">    枪是效率最高的，既可以英雄单挑，也能在乱军中杀出一条血路，不管是Microsoft出的，还是Oracle出的，本质上都是一样的。SQL是处理大型关系型数据库的霸主，其地位至今无人可以代替。不想每次在不同的Excel表格里做同样的工作？一条简单的SQL查询语句就可以帮你搞定。如果你见识过excel里vlookup的强大，那么当你用一句接近于英语的SQL语句实现同样功能的时候，此时的心情，可想而知。SQL，它的能力..实在是霸王级别的。然而在这个大数据时代下，SQL的生存空间开始受到挤压...MongoDB等新生力量似乎已经成为了市场最火热的NoSQL，是geek玩家捧上天的神器。让人不禁感叹，廉颇老矣，尚能饭否？</p><br/><br/><ul><li data-pid=\"DJYAcp_2\">    拳头——Powerpoint<br/></li></ul><br/><p data-pid=\"1BLdDJll\">    武林宗师根本不需要武器，拳头就可以横扫一切，称霸天下。而Powerpoint，毫无疑问就是分析高手的拳头。最好的Powerpoint通常会出现在咨询公司和苹果的产品发布会。请不要鄙视PPT达人，要向他们学习。当你作为在一个国际团队里的中国人，做了绝大部分dirty work而外国人只需要光彩照人地上台present的时候，就会明白我说这句话的用意。PPT不是仅仅让你看起来懂得很多、做得很好这么简单，它能帮你理清思路，逼你重新审视你的整个逻辑。对于有些分析师来说，presentation是天下最难的事情，然而对于宗师乔布斯来说，这不是问题，一次精彩的presentation，足以让后人铭记一生。</p><p data-pid=\"QgnBGc24\">--------</p><p data-pid=\"hTHJFsdE\">欢迎关注微信公众号：大数据留学申请（dsjlxsq），我会分享一些最新的大数据留学项目的信息和行业信息。</p>", "excerpt": "贴一篇仿照着之前玩摄影的时候看过的 <a href=\"https://link.zhihu.com/?target=http%3A//forum.xitek.com/thread-552714-1-1-1.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《镜头七武器》</a>写的《数据分析之七武器》 ***注意这里写的偏重于对商业分析师的要求，而非数据挖掘工程师。这二者基本上是数据分析这一职业类型的两个高级转职分支（类似于法师和术士的关系。。。？）。对数据挖掘工程师的技术要求不太一样（他们肯定会有JAVA和Hadoop）。 长生剑——Excel 剑是优雅的代名词，是风度翩翩的君子所爱之物，佩带一把长生剑，练得一套好剑谱，可以以最优雅的方式…", "excerpt_new": "贴一篇仿照着之前玩摄影的时候看过的 <a href=\"https://link.zhihu.com/?target=http%3A//forum.xitek.com/thread-552714-1-1-1.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《镜头七武器》</a>写的《数据分析之七武器》 ***注意这里写的偏重于对商业分析师的要求，而非数据挖掘工程师。这二者基本上是数据分析这一职业类型的两个高级转职分支（类似于法师和术士的关系。。。？）。对数据挖掘工程师的技术要求不太一样（他们肯定会有JAVA和Hadoop）。 长生剑——Excel 剑是优雅的代名词，是风度翩翩的君子所爱之物，佩带一把长生剑，练得一套好剑谱，可以以最优雅的方式…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "need_payment", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1433089201, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1423426761573", "type": "feed", "target": {"id": "19847699", "title": "对初学者而言，如何开始学习冥想？有哪些经验和好的书籍可以分享？", "url": "https://api.zhihu.com/questions/19847699", "type": "question", "question_type": "normal", "created": 1315792196, "answer_count": 132, "comment_count": 6, "follower_count": 6782, "detail": "积极心理学和哈佛幸福课的Tal老师都推崇冥想，有很多好处。我自己最近每日做深呼吸，感觉也不错。想认真地开始学习一下冥想。", "excerpt": "积极心理学和哈佛幸福课的Tal老师都推崇冥想，有很多好处。我自己最近每日做深呼吸…", "bound_topic_ids": [404, 2307, 26424, 27157, 155497], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "3e69d3b502e6c0de80bd1894e926a801", "name": "带来意义", "headline": "自我救赎之旅", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/search4meaning", "url_token": "search4meaning", "avatar_url": "https://picx.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1423426761, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1416364815097", "type": "feed", "target": {"id": "20544832", "title": "咨询行业的面试里面，案例分析如何下手？", "url": "https://api.zhihu.com/questions/20544832", "type": "question", "question_type": "normal", "created": 1350789496, "answer_count": 95, "comment_count": 3, "follower_count": 19107, "detail": "<p>本题已加入圆桌「<b><a href=\"https://www.zhihu.com/roundtable/zixun\" class=\"internal\">知招职场 | 咨询求职</a></b>」，更多咨询行业讨论，欢迎关注圆桌&gt;&gt;&gt;</p>", "excerpt": "本题已加入圆桌「<b><a href=\"https://www.zhihu.com/roundtable/zixun\" class=\"internal\">知招职场 | 咨询求职</a></b>」，更多咨询行业讨论，欢迎关注圆桌&gt;&gt;&gt;", "bound_topic_ids": [622, 1761, 6609, 18116, 38109], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "3fb635a250e382435e0fe4629c283dbe", "name": "Cici Dora", "headline": "教育", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/czzpsych06", "url_token": "czzpsych06", "avatar_url": "https://picx.zhimg.com/1f3856e3e73c21bc548dbbce6b6d83e4_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1416364815, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1400145323471", "type": "feed", "target": {"id": "19902088", "title": "如何判断一个人的人品？", "url": "https://api.zhihu.com/questions/19902088", "type": "question", "question_type": "normal", "created": 1320296284, "answer_count": 2623, "comment_count": 16, "follower_count": 23051, "detail": "", "excerpt": "", "bound_topic_ids": [732, 16208], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "c8e25f1a20dfb222a1196d8354ad7727", "name": "joan", "headline": "我", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xiao-xiao-yue", "url_token": "xiao-xiao-yue", "avatar_url": "https://picx.zhimg.com/v2-8573ee3ef79812f2c40248d109544859_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1400145323, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1393555144139", "type": "feed", "target": {"id": "20543245", "title": "如何判断人的情商高低？如何提升情商？", "url": "https://api.zhihu.com/questions/20543245", "type": "question", "question_type": "normal", "created": 1350701597, "answer_count": 292, "comment_count": 16, "follower_count": 37060, "detail": "", "excerpt": "", "bound_topic_ids": [404, 3946, 4467, 6831, 141404], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "413cac83ac9bb62b8c55c85f7f80722b", "name": "徐强", "headline": "教育赋能人类", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/johnxu", "url_token": "joh<PERSON><PERSON>", "avatar_url": "https://picx.zhimg.com/v2-004623660829745f0a845b22b7edabe3_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "", "night_mode_url": ""}, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "reaction_instruction": {}}, "verb": "", "created_time": 1393555144, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1393555144139&page_num=89"}}