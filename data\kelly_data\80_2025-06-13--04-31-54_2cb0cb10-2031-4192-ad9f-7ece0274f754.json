{"data": [{"id": "1481958342065", "type": "feed", "target": {"id": "131117973", "type": "answer", "url": "https://api.zhihu.com/answers/131117973", "voteup_count": 7683, "thanks_count": 2757, "question": {"id": "52178718", "title": "25 岁做什么，可在 5 年后受益匪浅？", "url": "https://api.zhihu.com/questions/52178718", "type": "question", "question_type": "normal", "created": 1477974166, "answer_count": 7079, "comment_count": 181, "follower_count": 313936, "detail": "类似问题：<br/><a href=\"https://www.zhihu.com/question/27032155\" class=\"internal\">20 岁做什么，可以在 5 年后受益匪浅？ - 生活</a><br/><a href=\"https://www.zhihu.com/question/28569572\" class=\"internal\">30 岁做什么，可以在 5 年后受益匪浅？ - 生活</a>", "excerpt": "类似问题： <a href=\"https://www.zhihu.com/question/27032155\" class=\"internal\">20 岁做什么，可以在 5 年后受益匪浅？ - 生活</a> <a href=\"https://www.zhihu.com/question/28569572\" class=\"internal\">30 岁做什么，可以在 5 …</a>", "bound_topic_ids": [307, 404, 988, 1537, 4761], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "c57cf5563468a78bf4a7d3a639365795", "name": "尾宿", "headline": "你所看到的都是别人想让你看到的!", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/liu-min-62-80-11", "url_token": "liu-min-62-80-11", "avatar_url": "https://pica.zhimg.com/v2-b644065fe20b6d8d17eae7e4ac04ab85_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1520753721, "created_time": 1479035218, "author": {"id": "1585720f0b84d1bce317b2eaac9cd109", "name": "鬼木知", "headline": "你我皆凡人", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/gui-mu-zhi", "url_token": "gui-mu-zhi", "avatar_url": "https://picx.zhimg.com/ec797b2e3_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "censor", "is_copyable": true, "comment_count": 430, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"MfASwCty\">拒绝重复，拒绝无聊，拒绝拖延。</p><p data-pid=\"UGXIMQoK\">不让重复的工作和生活消耗掉自己的青春，不让自己再屈服于这种无聊的生活，不给自己明天再做也不迟的借口。</p><p data-pid=\"at6ZeSs_\">工作后，会发现自己其实每天都在重复着昨天的生活，凭着已有的知识和技巧处理日常的工作，工作内容繁琐而重复，这类工作就算做得再好，也不过是提高自己的熟练度罢了。下班后，回宿舍看一部电影，看几集综艺节目，刷一会儿知乎，一天就过去了；周末里，睡两次懒觉，再出门逛个街，找朋友去吃个饭，一周就过去了；发工资后，上淘宝消费一番，出门去挥霍几次，然后再紧巴巴地等着下月的工资，一个月也就过去了；</p><p data-pid=\"uPyksHtY\">站在二十四、五岁的年龄，我开始感到恐惧，自己已经过了有大把时光可以挥霍的年纪，过了身体和头脑的巅峰时期，面对未来，自己到底还剩下多少机会？五年时间，六十个月，也就是说我只要再领六十次工资就到了三十岁，而在这期间，自己又剩下多少时间去改变自己。</p><p data-pid=\"Skaz3J6H\">站在二十四、五岁的年龄，我开始反感那些动辄给自己定五年、十年目标的励志鸡汤，因为这种长期性目标容易给人一种自己还有很多时间的错觉，然后会觉得把事情拖到“明天”再做也没关系；</p><p data-pid=\"mEcvR9pO\">站在二十四、五岁的年龄，我们有太多想法，想改变，想成功，还想毫不费力就能变成自己想成为的那种人。总是期望有人来告诉自己该怎么做，不想浪费精力，不想做无用功，在没找到答案之前干脆保持现状，得过且过。等到三十岁的时候，又该提问此时如何做才能及时补救，才能在五年后改变人生。</p><p data-pid=\"VX3CH-SO\">其实答案大家不是早知道了么：现在，立刻，马上开始，马上改变，去跑步健身，去背英语单词，去看专业论文，去学做PPT，到了时间马上关电脑关手机睡觉。将五年的目标细化为每天具体的努力，每天都努力改变一点，也许不用五年，便能看到一个不一样的自己。</p><p data-pid=\"pFUITve3\">共勉～</p>", "excerpt": "拒绝重复，拒绝无聊，拒绝拖延。 不让重复的工作和生活消耗掉自己的青春，不让自己再屈服于这种无聊的生活，不给自己明天再做也不迟的借口。 工作后，会发现自己其实每天都在重复着昨天的生活，凭着已有的知识和技巧处理日常的工作，工作内容繁琐而重复，这类工作就算做得再好，也不过是提高自己的熟练度罢了。下班后，回宿舍看一部电影，看几集综艺节目，刷一会儿知乎，一天就过去了；周末里，睡两次懒觉，再出门逛个街，找朋友…", "excerpt_new": "拒绝重复，拒绝无聊，拒绝拖延。 不让重复的工作和生活消耗掉自己的青春，不让自己再屈服于这种无聊的生活，不给自己明天再做也不迟的借口。 工作后，会发现自己其实每天都在重复着昨天的生活，凭着已有的知识和技巧处理日常的工作，工作内容繁琐而重复，这类工作就算做得再好，也不过是提高自己的熟练度罢了。下班后，回宿舍看一部电影，看几集综艺节目，刷一会儿知乎，一天就过去了；周末里，睡两次懒觉，再出门逛个街，找朋友…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1481958342, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1481958231260", "type": "feed", "target": {"id": "135219904", "type": "answer", "url": "https://api.zhihu.com/answers/135219904", "voteup_count": 161469, "thanks_count": 105871, "question": {"id": "52178718", "title": "25 岁做什么，可在 5 年后受益匪浅？", "url": "https://api.zhihu.com/questions/52178718", "type": "question", "question_type": "normal", "created": 1477974166, "answer_count": 7079, "comment_count": 181, "follower_count": 313936, "detail": "类似问题：<br/><a href=\"https://www.zhihu.com/question/27032155\" class=\"internal\">20 岁做什么，可以在 5 年后受益匪浅？ - 生活</a><br/><a href=\"https://www.zhihu.com/question/28569572\" class=\"internal\">30 岁做什么，可以在 5 年后受益匪浅？ - 生活</a>", "excerpt": "类似问题： <a href=\"https://www.zhihu.com/question/27032155\" class=\"internal\">20 岁做什么，可以在 5 年后受益匪浅？ - 生活</a> <a href=\"https://www.zhihu.com/question/28569572\" class=\"internal\">30 岁做什么，可以在 5 …</a>", "bound_topic_ids": [307, 404, 988, 1537, 4761], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "c57cf5563468a78bf4a7d3a639365795", "name": "尾宿", "headline": "你所看到的都是别人想让你看到的!", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/liu-min-62-80-11", "url_token": "liu-min-62-80-11", "avatar_url": "https://picx.zhimg.com/v2-b644065fe20b6d8d17eae7e4ac04ab85_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1586229917, "created_time": 1481367384, "author": {"id": "dab4832b139393ed3d5f9f1143d05a78", "name": "刘帅餐饮变革", "headline": "公众号“刘帅餐饮变革”", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/isteven", "url_token": "<PERSON><PERSON><PERSON>", "avatar_url": "https://pic1.zhimg.com/v2-766ad045af19c139a39286d50f867e04_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 5173, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"ImvKcofp\">今年正好30岁，从反面回答一下吧，后悔自己25岁时没人告诉我的。</p><p data-pid=\"nJEJHZC9\">1、25岁时，没人跟我说，<b>知识或者技能这种东西，学到了就跟你一辈子</b>，不管工作或时空跨度多大，新知和技能都能排上用场</p><p data-pid=\"8E1DBXY7\">2、25岁时，有人跟我说，习惯很重要，但<b>没人跟我说重要到深度影响自己生活、人生选择和生命质量的程度。因为几乎大多数人，每时每刻都生活在形形色色的思维习惯、生活习惯中</b>，比如健身、跑步、阅读、表达、写作、沟通、学习等等，所有影响我们生活工作结果的全是这些习惯。<b>我们养成习惯，然后习惯养成我们。</b></p><p data-pid=\"V3m30D99\">3、25岁时，没人跟我说，围剿式学习，就是吸星大法，<b>通过观察对方的沟通、演讲、写作方式，从而学习他们的思维方式、学习他们身上最优秀的思维习惯。</b></p><p data-pid=\"vl3lcIwJ\">4、25岁时，没人跟我说，<b>真诚和诚信有多么重要，真诚是领导力的真谛，诚信是商业的真谛，每个人的年薪、领导能力、商业成就全部都可以从这两个词汇里诠释。</b></p><p data-pid=\"UDiOYlaA\">5、25岁时，没人跟我说，<b>父母正在逼近死亡</b>。年轻人可能心里想得更多的是幸福生活，但父母的年纪，他们年龄越大，越要去面临离开这一主题，他们担心被世界抛弃，担心自己在儿女生活中扮演不了什么重要价值，特别渴望回馈、关注和尊重，不管多忙，能多打一个电话就多打一个电话，能多在一起吃顿饭，就多在一起吃顿饭，能满足他们想要的，不管大小，尽量去满足。</p><p data-pid=\"1KFFwSV-\">6、25岁时，没人跟我说，<b>睡眠其实真正决定了生命的效率。</b>因为睡眠决定着第二天的心情、状态、专注度等，而心情、状态、专注度直接影响结果，我们的现在就是由大大小小的这些结果构成的。<b>所以睡个好觉可能是让生活变得更好的最大的捷径。</b></p><p data-pid=\"G5PcxThh\">7、25岁时，没人跟我说，保持自我，并不断变得更好，其实在一段感情里，远比委曲求全，一味地宠溺对方，更重要。<b>保持自我可以测试出和对方是否合拍，不断变得更好，可以让感情更深刻而持久。</b>千万不要因为孤独、无助、父母逼迫、个人面子、焦虑等原因仓促地选择一个人。这个人是自己生命的一部分，对自己施加着无以复加地影响，所以谨慎点，即便单身都比错误好很多。</p><p data-pid=\"umb4r-Se\">8、25岁时，没人跟我说，利用所有的感官去体验周遭的世界是那么重要，<b>刻意发现生活的美，发现细微处的不同，会给自己带来很多小惊喜。</b><br/><br/>&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;</p><p data-pid=\"iYih_5VV\"><b>谢谢各位的赞，再补充几条</b></p><p data-pid=\"LaN8z0N1\"><b>除了真诚，诚信之外，如果想更有作为，还要有更多的善念、利他心、成全心，本质上每个人都是自私的，所以利他心、善念等才会帮助你构建一个价值网络，让更多人从你这里都能持续取得利益，你也自然能有所收获，从商业模式的定义来讲，即利益相关者的交易结构，这一点也是成立的，本质上创业的从0到1，从1到1000再到无穷大，就是构建出一个人得利到一群人共同得利的价值网，能席卷整个产业的价值网就会成就一个产业的垄断龙头。</b></p><p data-pid=\"MXXKnlxX\"><b>除了好习惯爱学习之外，还要懂得资本的力量，拿更多钱去改善自身的认知环境、生存环境，让自己生活在一个高度竞争高认知的生存环境中，会重新塑造你，能去华为阿里就去华为阿里，能去北上广深就去北上广深（东京柏林巴黎纽约伦敦更好），一定要在人类或者行业之巅寻找机会，才更有可能通过时间的积累让自己的人生拥有更多选择的自由</b></p><p data-pid=\"TmqNw771\">》》》》》》》》》》》》</p><p data-pid=\"e_jo2-OE\">我一直以为，30岁这篇写得更好，</p><a data-draft-node=\"block\" data-draft-type=\"link-card\" href=\"https://www.zhihu.com/question/28569572/answer/427665809\" class=\"internal\">30岁做什么，可在5年后受益匪浅？</a><p data-pid=\"K_dvBg7k\">公众号“迎春巷18号院”，主要是个人精进相关内容。欢迎关注</p>", "excerpt": "今年正好30岁，从反面回答一下吧，后悔自己25岁时没人告诉我的。 1、25岁时，没人跟我说， <b>知识或者技能这种东西，学到了就跟你一辈子</b>，不管工作或时空跨度多大，新知和技能都能排上用场2、25岁时，有人跟我说，习惯很重要，但 <b>没人跟我说重要到深度影响自己生活、人生选择和生命质量的程度。因为几乎大多数人，每时每刻都生活在形形色色的思维习惯、生活习惯中</b>，比如健身、跑步、阅读、表达、写作、沟通、学习等等，所有影响我…", "excerpt_new": "今年正好30岁，从反面回答一下吧，后悔自己25岁时没人告诉我的。 1、25岁时，没人跟我说， <b>知识或者技能这种东西，学到了就跟你一辈子</b>，不管工作或时空跨度多大，新知和技能都能排上用场2、25岁时，有人跟我说，习惯很重要，但 <b>没人跟我说重要到深度影响自己生活、人生选择和生命质量的程度。因为几乎大多数人，每时每刻都生活在形形色色的思维习惯、生活习惯中</b>，比如健身、跑步、阅读、表达、写作、沟通、学习等等，所有影响我…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {"REACTION_CONTENT_SEGMENT_LIKE": "HIDE"}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1481958231, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1481775933999", "type": "feed", "target": {"id": "134611409", "type": "answer", "url": "https://api.zhihu.com/answers/134611409", "voteup_count": 17233, "thanks_count": 10076, "question": {"id": "21274334", "title": "健身房都有哪些健身器材？分别有什么作用？", "url": "https://api.zhihu.com/questions/21274334", "type": "question", "question_type": "normal", "created": 1372603941, "answer_count": 118, "comment_count": 6, "follower_count": 36034, "detail": "<p><b>本题已收录圆桌»运动在冬季，更多「冬季运动」相关话题欢迎关注讨论。</b></p>", "excerpt": "<b>本题已收录圆桌»运动在冬季，更多「冬季运动」相关话题欢迎关注讨论。</b>", "bound_topic_ids": [658, 833, 972, 10158, 35707], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "1b3f49f065a6574b295576e091abe3f4", "name": "Honor", "headline": "岁在甲午。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/honor-14", "url_token": "honor-14", "avatar_url": "https://picx.zhimg.com/ccbf0372e_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1481014534, "created_time": 1481014067, "author": {"id": "b8376da6d812da2d81c8e4a4dba04335", "name": "Keep", "headline": "哪里有运动，哪里就是你的自由运动场，自律给我自由。", "type": "people", "user_type": "organization", "url": "https://www.zhihu.com/people/keep-13-17", "url_token": "keep-13-17", "avatar_url": "https://picx.zhimg.com/v2-25d515d41076fdc87dd4dd4721251eaf_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": true, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 382, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"yBldErYk\">没有邀请，不请自来，以后这种问题记得喊 Keeeeeeeeeeeeeeeeeeeeeep 哈哈哈哈！</p><p data-pid=\"Vr5AbC6A\">高票回答们其实已经说得比较全面了，但是由于对小白用户的不放心，还是决定来详细说说。</p><p data-pid=\"wl88p5A2\">很多人第一次进健身房都是一脸懵逼的状态，环顾四周，却不知该从那个器械下手，最后只能去跑步机上溜达几下，冲个澡然后回家...（捂脸</p><p data-pid=\"6IY_Ov4b\">为了防止世界被破坏，为了守护世界的和平，今天来给大家讲讲健身房那些<b>脸熟</b>的器械怎么用。</p><figure><noscript><img src=\"https://pic4.zhimg.com/v2-c3147ff61dd3d5b7013ad96cccf15333_b.jpg\" data-rawwidth=\"797\" data-rawheight=\"827\" data-original-token=\"v2-c3147ff61dd3d5b7013ad96cccf15333\" class=\"origin_image zh-lightbox-thumb\" width=\"797\" data-original=\"https://pic4.zhimg.com/v2-c3147ff61dd3d5b7013ad96cccf15333_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;797&#39; height=&#39;827&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"797\" data-rawheight=\"827\" data-original-token=\"v2-c3147ff61dd3d5b7013ad96cccf15333\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"797\" data-original=\"https://pic4.zhimg.com/v2-c3147ff61dd3d5b7013ad96cccf15333_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-c3147ff61dd3d5b7013ad96cccf15333_b.jpg\"/></figure><br/><p data-pid=\"lqNAAZWc\">这个器械能<strong>单独锻炼到胸肌</strong>，如果无法掌握杠铃卧推等复杂动作，可以先从它开始练习，寻找胸肌发力的感觉；女生也可以用它来为俯卧撑打基础。</p><br/><p data-pid=\"KsaM0ayz\"><strong>使用方法：</strong></p><p data-pid=\"wgT2SHNs\">1.调节座椅高度，让两侧握把的高度与胸部下沿齐平</p><p data-pid=\"JAdyK7sI\">2.挺胸，双肩向后夹紧，贴紧靠背，保持这个姿势推出握把</p><p data-pid=\"wgdojSEJ\">3.呼气推出，吸气还原</p><br/><p data-pid=\"wMEiKFw-\">4.使用示意图：</p><figure><noscript><img src=\"https://pic3.zhimg.com/v2-d20894c6e3b6a32785f3b3f0520ea49a_b.png\" data-rawwidth=\"601\" data-rawheight=\"339\" data-original-token=\"v2-d20894c6e3b6a32785f3b3f0520ea49a\" class=\"origin_image zh-lightbox-thumb\" width=\"601\" data-original=\"https://pic3.zhimg.com/v2-d20894c6e3b6a32785f3b3f0520ea49a_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;601&#39; height=&#39;339&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"601\" data-rawheight=\"339\" data-original-token=\"v2-d20894c6e3b6a32785f3b3f0520ea49a\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"601\" data-original=\"https://pic3.zhimg.com/v2-d20894c6e3b6a32785f3b3f0520ea49a_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-d20894c6e3b6a32785f3b3f0520ea49a_b.png\"/></figure><figure><noscript><img src=\"https://pica.zhimg.com/v2-a384931afe01f04dcbe16230cf3c530e_b.png\" data-rawwidth=\"598\" data-rawheight=\"338\" data-original-token=\"v2-a384931afe01f04dcbe16230cf3c530e\" class=\"origin_image zh-lightbox-thumb\" width=\"598\" data-original=\"https://pica.zhimg.com/v2-a384931afe01f04dcbe16230cf3c530e_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;598&#39; height=&#39;338&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"598\" data-rawheight=\"338\" data-original-token=\"v2-a384931afe01f04dcbe16230cf3c530e\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"598\" data-original=\"https://pica.zhimg.com/v2-a384931afe01f04dcbe16230cf3c530e_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-a384931afe01f04dcbe16230cf3c530e_b.png\"/></figure><br/><figure><noscript><img src=\"https://pic2.zhimg.com/v2-de77ac7ac0809637f34f123e79f3f62b_b.jpg\" data-rawwidth=\"797\" data-rawheight=\"827\" data-original-token=\"v2-de77ac7ac0809637f34f123e79f3f62b\" class=\"origin_image zh-lightbox-thumb\" width=\"797\" data-original=\"https://pic2.zhimg.com/v2-de77ac7ac0809637f34f123e79f3f62b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;797&#39; height=&#39;827&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"797\" data-rawheight=\"827\" data-original-token=\"v2-de77ac7ac0809637f34f123e79f3f62b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"797\" data-original=\"https://pic2.zhimg.com/v2-de77ac7ac0809637f34f123e79f3f62b_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-de77ac7ac0809637f34f123e79f3f62b_b.jpg\"/></figure><p data-pid=\"Gk1onBgc\">高位下拉器械是健身房最热门的器械之一，经常看到有很多人排着队练。它能模拟引体向上的发力，<strong>锻炼到整个背部的肌肉</strong>。如果你还做不了引体向上，不妨先从它开始。</p><br/><p data-pid=\"MEBnAQuH\"><strong>使用方法：</strong></p><p data-pid=\"seFCjRYI\">1.调整坐姿，让握把位于头顶正上方</p><p data-pid=\"SM3uMaAg\">2.调节座椅前挡板高度，让它牢牢固定住双腿</p><p data-pid=\"kwTC71cW\">3.抓紧握把，挺胸，先用肩部下沉的力量启动，再向下拉动握把</p><p data-pid=\"jaIJJZlv\">4.呼气下拉，吸气还原</p><p data-pid=\"oLvK0wkT\">5.使用示意图：</p><figure><noscript><img src=\"https://pic1.zhimg.com/v2-433a5758a1bb702ad7979ac510953cd0_b.png\" data-rawwidth=\"597\" data-rawheight=\"337\" data-original-token=\"v2-433a5758a1bb702ad7979ac510953cd0\" class=\"origin_image zh-lightbox-thumb\" width=\"597\" data-original=\"https://pic1.zhimg.com/v2-433a5758a1bb702ad7979ac510953cd0_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;597&#39; height=&#39;337&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"597\" data-rawheight=\"337\" data-original-token=\"v2-433a5758a1bb702ad7979ac510953cd0\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"597\" data-original=\"https://pic1.zhimg.com/v2-433a5758a1bb702ad7979ac510953cd0_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-433a5758a1bb702ad7979ac510953cd0_b.png\"/></figure><figure><noscript><img src=\"https://pic4.zhimg.com/v2-4c02ad3df1bbe97e194e6856b558abe5_b.png\" data-rawwidth=\"599\" data-rawheight=\"338\" data-original-token=\"v2-4c02ad3df1bbe97e194e6856b558abe5\" class=\"origin_image zh-lightbox-thumb\" width=\"599\" data-original=\"https://pic4.zhimg.com/v2-4c02ad3df1bbe97e194e6856b558abe5_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;599&#39; height=&#39;338&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"599\" data-rawheight=\"338\" data-original-token=\"v2-4c02ad3df1bbe97e194e6856b558abe5\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"599\" data-original=\"https://pic4.zhimg.com/v2-4c02ad3df1bbe97e194e6856b558abe5_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-4c02ad3df1bbe97e194e6856b558abe5_b.png\"/></figure><p data-pid=\"8Vsl98xh\">ps：高位下拉不同的握法、握的宽度，会产生不一样的训练效果，建议自己多尝试几种握法，找到背部发力感最好的姿势。<br/></p><br/><figure><noscript><img src=\"https://pic1.zhimg.com/v2-4f5a8d99d18abf4bcb60c2e26977c5c0_b.jpg\" data-rawwidth=\"797\" data-rawheight=\"827\" data-original-token=\"v2-4f5a8d99d18abf4bcb60c2e26977c5c0\" class=\"origin_image zh-lightbox-thumb\" width=\"797\" data-original=\"https://pic1.zhimg.com/v2-4f5a8d99d18abf4bcb60c2e26977c5c0_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;797&#39; height=&#39;827&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"797\" data-rawheight=\"827\" data-original-token=\"v2-4f5a8d99d18abf4bcb60c2e26977c5c0\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"797\" data-original=\"https://pic1.zhimg.com/v2-4f5a8d99d18abf4bcb60c2e26977c5c0_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-4f5a8d99d18abf4bcb60c2e26977c5c0_b.jpg\"/></figure><p data-pid=\"Ry7m-Iuv\">坐姿划船与高位下拉一样都是练习背部的器械，但它的动作轨迹和高位下拉是两个不同的平面，这使得它<strong>更能锻炼到背部靠中间部分的肌</strong><strong>肉</strong>，<strong>能让背沟更加深</strong>，背部肌肉更加立体，是男女生都应该练习的器械。</p><br/><p data-pid=\"MhyWMSMW\"><strong> 使用方法：</strong></p><p data-pid=\"Q1-EWQgg\">1.吸气挺胸，用胸部抵住前侧挡板</p><p data-pid=\"mZGDmDzA\">2.呼气拉动握把，呼气时应用腹部呼气，保持挺胸姿势</p><p data-pid=\"6XLvprwd\">3.把握把拉到最大幅度时，保持 1-2 秒，感受背部肌肉的挤压感</p><p data-pid=\"G4N32X9h\">4.使用示意图：</p><figure><noscript><img src=\"https://pica.zhimg.com/v2-13dc3399a178a97be98499a4ed36fc52_b.png\" data-rawwidth=\"596\" data-rawheight=\"339\" data-original-token=\"v2-13dc3399a178a97be98499a4ed36fc52\" class=\"origin_image zh-lightbox-thumb\" width=\"596\" data-original=\"https://pica.zhimg.com/v2-13dc3399a178a97be98499a4ed36fc52_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;596&#39; height=&#39;339&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"596\" data-rawheight=\"339\" data-original-token=\"v2-13dc3399a178a97be98499a4ed36fc52\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"596\" data-original=\"https://pica.zhimg.com/v2-13dc3399a178a97be98499a4ed36fc52_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-13dc3399a178a97be98499a4ed36fc52_b.png\"/></figure><figure><noscript><img src=\"https://pic2.zhimg.com/v2-98987c565bdcf1e1bf13a6c8fcfe2c1b_b.png\" data-rawwidth=\"598\" data-rawheight=\"340\" data-original-token=\"v2-98987c565bdcf1e1bf13a6c8fcfe2c1b\" class=\"origin_image zh-lightbox-thumb\" width=\"598\" data-original=\"https://pic2.zhimg.com/v2-98987c565bdcf1e1bf13a6c8fcfe2c1b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;598&#39; height=&#39;340&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"598\" data-rawheight=\"340\" data-original-token=\"v2-98987c565bdcf1e1bf13a6c8fcfe2c1b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"598\" data-original=\"https://pic2.zhimg.com/v2-98987c565bdcf1e1bf13a6c8fcfe2c1b_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-98987c565bdcf1e1bf13a6c8fcfe2c1b_b.png\"/></figure><figure><noscript><img src=\"https://picx.zhimg.com/v2-261d7cdfb5b6bc9227941f470e80cbff_b.jpg\" data-rawwidth=\"797\" data-rawheight=\"738\" data-original-token=\"v2-261d7cdfb5b6bc9227941f470e80cbff\" class=\"origin_image zh-lightbox-thumb\" width=\"797\" data-original=\"https://picx.zhimg.com/v2-261d7cdfb5b6bc9227941f470e80cbff_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;797&#39; height=&#39;738&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"797\" data-rawheight=\"738\" data-original-token=\"v2-261d7cdfb5b6bc9227941f470e80cbff\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"797\" data-original=\"https://picx.zhimg.com/v2-261d7cdfb5b6bc9227941f470e80cbff_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-261d7cdfb5b6bc9227941f470e80cbff_b.jpg\"/></figure><p data-pid=\"QlLu_SXr\"><strong>多数人大腿前侧和后侧的力量都是不平衡的</strong>，这导致我们在做深蹲类的动作时会优先使用力量较强的肌肉，力量弱的肌肉就得不到锻炼。这两个器械能单独锻炼大腿的前后侧肌肉，<strong>让你的大腿更加匀称</strong>，力量更加均衡。</p><br/><p data-pid=\"Li3N0k3f\"><strong>使用方法：</strong></p><p data-pid=\"zIYC2cYu\">1.调整坐姿，让器械转动轴对准膝盖位置</p><p data-pid=\"WreoEeoh\">2.双手抓住握把，勾起脚尖，用力伸直大腿</p><p data-pid=\"cnH1BsNt\">3.双腿缓慢还原，不能直接放松下落</p><p data-pid=\"1V0D6Tr4\">4.使用示意图：</p><p data-pid=\"aHeQZVHa\">*腿屈伸</p><p data-pid=\"Te_fKK1s\"><figure><noscript><img src=\"https://pica.zhimg.com/v2-23cd8e6c26fe94aa1cb5004f75e35d22_b.png\" data-rawwidth=\"598\" data-rawheight=\"340\" data-original-token=\"v2-23cd8e6c26fe94aa1cb5004f75e35d22\" class=\"origin_image zh-lightbox-thumb\" width=\"598\" data-original=\"https://pica.zhimg.com/v2-23cd8e6c26fe94aa1cb5004f75e35d22_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;598&#39; height=&#39;340&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"598\" data-rawheight=\"340\" data-original-token=\"v2-23cd8e6c26fe94aa1cb5004f75e35d22\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"598\" data-original=\"https://pica.zhimg.com/v2-23cd8e6c26fe94aa1cb5004f75e35d22_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-23cd8e6c26fe94aa1cb5004f75e35d22_b.png\"/></figure><figure><noscript><img src=\"https://pic1.zhimg.com/v2-6c385d04ac78105608b196735e9a03ec_b.png\" data-rawwidth=\"595\" data-rawheight=\"336\" data-original-token=\"v2-6c385d04ac78105608b196735e9a03ec\" class=\"origin_image zh-lightbox-thumb\" width=\"595\" data-original=\"https://pic1.zhimg.com/v2-6c385d04ac78105608b196735e9a03ec_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;595&#39; height=&#39;336&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"595\" data-rawheight=\"336\" data-original-token=\"v2-6c385d04ac78105608b196735e9a03ec\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"595\" data-original=\"https://pic1.zhimg.com/v2-6c385d04ac78105608b196735e9a03ec_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-6c385d04ac78105608b196735e9a03ec_b.png\"/></figure>*腿弯举</p><p data-pid=\"GvVkM_jp\"><figure><noscript><img src=\"https://picx.zhimg.com/v2-930100a643211bcecffd246179a85d21_b.png\" data-rawwidth=\"598\" data-rawheight=\"336\" data-original-token=\"v2-930100a643211bcecffd246179a85d21\" class=\"origin_image zh-lightbox-thumb\" width=\"598\" data-original=\"https://picx.zhimg.com/v2-930100a643211bcecffd246179a85d21_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;598&#39; height=&#39;336&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"598\" data-rawheight=\"336\" data-original-token=\"v2-930100a643211bcecffd246179a85d21\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"598\" data-original=\"https://picx.zhimg.com/v2-930100a643211bcecffd246179a85d21_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-930100a643211bcecffd246179a85d21_b.png\"/></figure><figure><noscript><img src=\"https://pic3.zhimg.com/v2-e9b4127fc08210612dcc5e08a1e21bf6_b.png\" data-rawwidth=\"598\" data-rawheight=\"338\" data-original-token=\"v2-e9b4127fc08210612dcc5e08a1e21bf6\" class=\"origin_image zh-lightbox-thumb\" width=\"598\" data-original=\"https://pic3.zhimg.com/v2-e9b4127fc08210612dcc5e08a1e21bf6_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;598&#39; height=&#39;338&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"598\" data-rawheight=\"338\" data-original-token=\"v2-e9b4127fc08210612dcc5e08a1e21bf6\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"598\" data-original=\"https://pic3.zhimg.com/v2-e9b4127fc08210612dcc5e08a1e21bf6_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-e9b4127fc08210612dcc5e08a1e21bf6_b.png\"/></figure>ps：这两个器械使用方法很相似，所以在一些健身房里，这它们是一体的，通过调节插销就可以切换功能。<br/></p><br/><figure><noscript><img src=\"https://picx.zhimg.com/v2-62d3c95da7036eb60eb3eedf5d61e107_b.jpg\" data-rawwidth=\"797\" data-rawheight=\"797\" data-original-token=\"v2-62d3c95da7036eb60eb3eedf5d61e107\" class=\"origin_image zh-lightbox-thumb\" width=\"797\" data-original=\"https://picx.zhimg.com/v2-62d3c95da7036eb60eb3eedf5d61e107_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;797&#39; height=&#39;797&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"797\" data-rawheight=\"797\" data-original-token=\"v2-62d3c95da7036eb60eb3eedf5d61e107\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"797\" data-original=\"https://picx.zhimg.com/v2-62d3c95da7036eb60eb3eedf5d61e107_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-62d3c95da7036eb60eb3eedf5d61e107_b.jpg\"/></figure><p data-pid=\"RcemwxN9\">在徒手训练和杠铃哑铃的训练中，很难专门练到<strong>大腿内侧和臀部外侧</strong>的肌肉，但这两个区域又是女生必练的部位。所以需要用到腿内收&amp;腿外展器械，它的固定轨道能帮助你更好地找到这两个区域的发力感觉。</p><br/><p data-pid=\"O6w-W1jT\"><strong>使用方法：</strong></p><p data-pid=\"KkcKGJMZ\">1.调整插销位置，让双腿的开合幅度尽可能大</p><p data-pid=\"c8Bbtcwv\">2.用膝盖向内或者向外发力，而不是用脚来发力</p><p data-pid=\"jixF4uRE\">3.双腿缓慢还原，不能直接放松</p><p data-pid=\"qR3Lsb0y\">4.使用示意图：</p><p data-pid=\"F22ifUUo\">*腿外展<br/></p><p data-pid=\"47WLn67m\"><figure><noscript><img src=\"https://pic2.zhimg.com/v2-d718cb1a1d8fc30c8809f72dd096d82b_b.png\" data-rawwidth=\"597\" data-rawheight=\"335\" data-original-token=\"v2-d718cb1a1d8fc30c8809f72dd096d82b\" class=\"origin_image zh-lightbox-thumb\" width=\"597\" data-original=\"https://pic2.zhimg.com/v2-d718cb1a1d8fc30c8809f72dd096d82b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;597&#39; height=&#39;335&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"597\" data-rawheight=\"335\" data-original-token=\"v2-d718cb1a1d8fc30c8809f72dd096d82b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"597\" data-original=\"https://pic2.zhimg.com/v2-d718cb1a1d8fc30c8809f72dd096d82b_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-d718cb1a1d8fc30c8809f72dd096d82b_b.png\"/></figure>*腿内收</p><p data-pid=\"Ea3cEApo\"><figure><noscript><img src=\"https://pica.zhimg.com/v2-6bd0cc9761800820bdb6f50705114146_b.png\" data-rawwidth=\"597\" data-rawheight=\"337\" data-original-token=\"v2-6bd0cc9761800820bdb6f50705114146\" class=\"origin_image zh-lightbox-thumb\" width=\"597\" data-original=\"https://pica.zhimg.com/v2-6bd0cc9761800820bdb6f50705114146_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;597&#39; height=&#39;337&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"597\" data-rawheight=\"337\" data-original-token=\"v2-6bd0cc9761800820bdb6f50705114146\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"597\" data-original=\"https://pica.zhimg.com/v2-6bd0cc9761800820bdb6f50705114146_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-6bd0cc9761800820bdb6f50705114146_b.png\"/></figure>ps：健身房里这两个器械通常也是一体的，通过转动挡板的位置就可以改变它的功能。<br/></p><br/><figure><noscript><img src=\"https://pic2.zhimg.com/v2-a7a1cd3c449e74002f7c4ae3f7472571_b.jpg\" data-rawwidth=\"797\" data-rawheight=\"738\" data-original-token=\"v2-a7a1cd3c449e74002f7c4ae3f7472571\" class=\"origin_image zh-lightbox-thumb\" width=\"797\" data-original=\"https://pic2.zhimg.com/v2-a7a1cd3c449e74002f7c4ae3f7472571_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;797&#39; height=&#39;738&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"797\" data-rawheight=\"738\" data-original-token=\"v2-a7a1cd3c449e74002f7c4ae3f7472571\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"797\" data-original=\"https://pic2.zhimg.com/v2-a7a1cd3c449e74002f7c4ae3f7472571_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-a7a1cd3c449e74002f7c4ae3f7472571_b.jpg\"/></figure><p data-pid=\"Z45Ffmwm\">史密斯机是一个<strong>综合器械</strong>，它可不是只用来锻炼某一块肌肉的，通过改变动作腿部、臀部、胸肌、背部全都可以练到。它其实是在一根杠铃的基础上增加了一个固定的运动轨道，使得我们在练习时身体更不容易晃动。这样一方面可以<strong>更加专注于肌肉的发力</strong>，另一方面也更为安全。</p><br/><p data-pid=\"PIK74yvE\">这个器械没有统一的使用方法，具体根据你想要练习的部位而定。最常见的是下面两种形式：</p><br/><p data-pid=\"nlZNtQ6o\">*史密斯深蹲</p><figure><noscript><img src=\"https://pica.zhimg.com/v2-48c0eaa7745f77de720bee0cbc67a4b8_b.png\" data-rawwidth=\"596\" data-rawheight=\"335\" data-original-token=\"v2-48c0eaa7745f77de720bee0cbc67a4b8\" class=\"origin_image zh-lightbox-thumb\" width=\"596\" data-original=\"https://pica.zhimg.com/v2-48c0eaa7745f77de720bee0cbc67a4b8_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;596&#39; height=&#39;335&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"596\" data-rawheight=\"335\" data-original-token=\"v2-48c0eaa7745f77de720bee0cbc67a4b8\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"596\" data-original=\"https://pica.zhimg.com/v2-48c0eaa7745f77de720bee0cbc67a4b8_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-48c0eaa7745f77de720bee0cbc67a4b8_b.png\"/></figure><p data-pid=\"fX32etRw\">*史密斯卧推</p><figure><noscript><img src=\"https://picx.zhimg.com/v2-313729b0b664075ef58c9534af19f0a7_b.png\" data-rawwidth=\"596\" data-rawheight=\"338\" data-original-token=\"v2-313729b0b664075ef58c9534af19f0a7\" class=\"origin_image zh-lightbox-thumb\" width=\"596\" data-original=\"https://picx.zhimg.com/v2-313729b0b664075ef58c9534af19f0a7_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;596&#39; height=&#39;338&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"596\" data-rawheight=\"338\" data-original-token=\"v2-313729b0b664075ef58c9534af19f0a7\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"596\" data-original=\"https://picx.zhimg.com/v2-313729b0b664075ef58c9534af19f0a7_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-313729b0b664075ef58c9534af19f0a7_b.png\"/></figure><p data-pid=\"mXooGxsf\">ps：史密斯机有垂直、倾斜、三维三种轨道，垂直轨道是最传统的史密斯，倾斜轨道在推起杠铃时有一个侧向的阻力，肌肉发力感会更好，但实际使用上倾斜和垂直都各有好处。三维的史密斯是在垂直轨道外增加了两条水平的滑轨，杠铃可以在一个框架内移动，它只不过限制了杠铃左右倾斜的运动，已经很接近自由杠铃的感觉，难度也相对高一些。</p><p data-pid=\"1SL2Umii\">以上就是健身房常见的几类器械。如果你是第一次到健身房，建议<strong><u>务！必！先从这些固定器械开始练习</u></strong>。因为它们的<strong><u>轨道是固定的</u></strong>，非常安全，能更好地帮助你找到肌肉的发力感觉，是健身房初学者最适合的训练方式。<br/></p><br/><p data-pid=\"iDVgnuZV\">也可以尝试 Keep 针对新手制定的新课<strong>「健身房入门·男生版」</strong>和<strong>「健身房入门·女生版」，</strong>在视频的指导下进行系统地入门练习，一定能尽快摆脱菜鸟状态！</p><figure><noscript><img src=\"https://pic4.zhimg.com/v2-bd6fb05fa70c3f165a26e96e07c0103b_b.png\" data-rawwidth=\"570\" data-rawheight=\"400\" data-original-token=\"v2-bd6fb05fa70c3f165a26e96e07c0103b\" class=\"origin_image zh-lightbox-thumb\" width=\"570\" data-original=\"https://pic4.zhimg.com/v2-bd6fb05fa70c3f165a26e96e07c0103b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;570&#39; height=&#39;400&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"570\" data-rawheight=\"400\" data-original-token=\"v2-bd6fb05fa70c3f165a26e96e07c0103b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"570\" data-original=\"https://pic4.zhimg.com/v2-bd6fb05fa70c3f165a26e96e07c0103b_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-bd6fb05fa70c3f165a26e96e07c0103b_b.png\"/></figure><figure><noscript><img src=\"https://pic3.zhimg.com/v2-adcec2c1b89c441267ef6851e54540b6_b.png\" data-rawwidth=\"570\" data-rawheight=\"408\" data-original-token=\"v2-adcec2c1b89c441267ef6851e54540b6\" class=\"origin_image zh-lightbox-thumb\" width=\"570\" data-original=\"https://pic3.zhimg.com/v2-adcec2c1b89c441267ef6851e54540b6_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;570&#39; height=&#39;408&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"570\" data-rawheight=\"408\" data-original-token=\"v2-adcec2c1b89c441267ef6851e54540b6\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"570\" data-original=\"https://pic3.zhimg.com/v2-adcec2c1b89c441267ef6851e54540b6_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-adcec2c1b89c441267ef6851e54540b6_b.png\"/></figure><br/><p data-pid=\"TkEEASL3\">好了，下课！今晚去健身房就试试看吧~</p>", "excerpt": "没有邀请，不请自来，以后这种问题记得喊 Keeeeeeeeeeeeeeeeeeeeeep 哈哈哈哈！ 高票回答们其实已经说得比较全面了，但是由于对小白用户的不放心，还是决定来详细说说。 很多人第一次进健身房都是一脸懵逼的状态，环顾四周，却不知该从那个器械下手，最后只能去跑步机上溜达几下，冲个澡然后回家...（捂脸 为了防止世界被破坏，为了守护世界的和平，今天来给大家讲讲健身房那些 <b>脸熟</b>的器械怎么用。 这个器械能 <strong>单独锻炼到胸肌</strong>，…", "excerpt_new": "没有邀请，不请自来，以后这种问题记得喊 Keeeeeeeeeeeeeeeeeeeeeep 哈哈哈哈！ 高票回答们其实已经说得比较全面了，但是由于对小白用户的不放心，还是决定来详细说说。 很多人第一次进健身房都是一脸懵逼的状态，环顾四周，却不知该从那个器械下手，最后只能去跑步机上溜达几下，冲个澡然后回家...（捂脸 为了防止世界被破坏，为了守护世界的和平，今天来给大家讲讲健身房那些 <b>脸熟</b>的器械怎么用。 这个器械能 <strong>单独锻炼到胸肌</strong>，…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1481775933, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1481775214364", "type": "feed", "target": {"id": "37676922", "type": "answer", "url": "https://api.zhihu.com/answers/37676922", "voteup_count": 10728, "thanks_count": 4783, "question": {"id": "27583640", "title": "你在出差过程中有总结了哪些经验值得借鉴？", "url": "https://api.zhihu.com/questions/27583640", "type": "question", "question_type": "normal", "created": 1421211077, "answer_count": 522, "comment_count": 19, "follower_count": 31649, "detail": "工作原因近期开始频繁出差，中短途为主，大家在工作中都有什么经验技巧吗？", "excerpt": "工作原因近期开始频繁出差，中短途为主，大家在工作中都有什么经验技巧吗？", "bound_topic_ids": [309, 2072, 11662, 12585, 39539], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "99b1ddd8d5057f95bd7a8efd1dd7ded8", "name": "郭小斌", "headline": "电商从业者/郭老师/宝爸", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/bingoloveyou", "url_token": "bingoloveyou", "avatar_url": "https://picx.zhimg.com/8b3727b26d908c63a762a985be8edbd6_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1421991867, "created_time": 1421768525, "author": {"id": "4fb012252bdf5ee276bd506cd332f215", "name": "FantasticCathy", "headline": "简单粗暴不文艺|知乎专栏：陈凯西", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/FantasticCathy", "url_token": "FantasticCathy", "avatar_url": "https://picx.zhimg.com/386d9aa9abacc2689545a15dd9268c7c_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["首次公开募股（IPO）"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "首次公开募股（IPO）话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "4daba2dd7", "avatar_url": "https://picx.zhimg.com/4daba2dd7_720w.jpg?source=32738c0c", "description": "", "id": "19559516", "name": "首次公开募股（IPO）", "priority": 0, "token": "19559516", "type": "topic", "url": "https://www.zhihu.com/topic/19559516"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}], "title": "首次公开募股（IPO）话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 518, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"cZeDCiIm\">这个问题我太有感受了。每年飞20+万公里，国内出差主要上海深圳广州香港，国外新加坡美国</p><p data-pid=\"85BwGPYR\"><b><u>出行准备</u></b></p><p data-pid=\"2eh_L84K\"><b>航空公司的选择:</b></p><p data-pid=\"Qy5lICcf\">首先你要选一个默认航空公司，比较容易积累里程并快速的升到贵宾会员。就看你所在城市是哪个航空公司的hub。以前住香港必定入马可波罗会，还可以申领联名信用卡里程积分互换；后来搬来北京后就坐国航多了，国航和国泰港龙互相持股，里程可以累积在知音卡，也可以在马可波罗卡（当然是累积在自己航空公司的卡里面更合算一点），所以能保持一定程度的灵活度。现在每年能保持国航金卡及以上，国泰港龙至少银卡。达到VIP待遇的好处是，一旦发生晚点，可以舒服很多，至少休息室里面有吃有喝有沙发有Wi-Fi呀，特别在雷雨季节以及冬季下雪天，五六个小时的晚点，有口热汤喝，有个沙发睡是很重要      </p><p data-pid=\"lNeYpnNO\">选定你的主要航空公司以后，海外航班可以尽量选同一个联盟的，比如国航是星空联盟，那么去美国可以多飞美联航，去新加坡可以飞新航。另外在海外机场可以考察一下同一个联盟的航空公司的休息室，国内航空公司在国外机场的休息室还是比较差一点。。。比如在香港机场，如果飞国航，其实可以去新航休息室，软硬件设备好很多。如果航班有晚点去本航空公司的休息室比较好，有利于及时获得航班最新信息，在新航休息室人家也不知道国航飞机到底有没有到达本场，香港到北京航路上有没有交通管制或者特殊天气</p><p data-pid=\"4B7YHBXc\"><b>行李准备:</b></p><p data-pid=\"JM8qLhA6\">一周以内的旅行我通常不托运行李，节约时间，也减少不必要的风险。洲际旅行可以考虑托运一个箱子，万一在美国买买买了呢，主要是看在美国是飞商业航班还是有定私人飞机，如果有私人飞机，那就随便带啦，喜欢用的毛巾都可以打包带上，反正就在美国的第一站等一次行李嘛，本来外国人过关的队伍都超级长，多一个行李的额外时间成本不那么大。Remowa的登机箱轻巧又塞得进东西，非常推荐。托运行李推荐Tumi，经造。我喜欢用这样的收纳袋收拾衣服，箱子没有拉杆的一边正好放两个大号的收纳袋，上面可以再压一个笔记本和一本书上去。小号的收纳袋我会有来收拾运动衣和游泳衣，有的套装里有两层设计的袋子，一边是网眼的，一边是通常的全封盖子，有湿的衣物可以很方便的和干净衣服隔离开来。图片来自淘宝，到处都有卖，很便宜</p><figure><noscript><img src=\"https://pic1.zhimg.com/606f0203ac8215dc4c0aa6c8a08efc88_b.jpg\" data-rawwidth=\"800\" data-rawheight=\"800\" data-original-token=\"606f0203ac8215dc4c0aa6c8a08efc88\" class=\"origin_image zh-lightbox-thumb\" width=\"800\" data-original=\"https://pic1.zhimg.com/606f0203ac8215dc4c0aa6c8a08efc88_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;800&#39; height=&#39;800&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"800\" data-rawheight=\"800\" data-original-token=\"606f0203ac8215dc4c0aa6c8a08efc88\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"800\" data-original=\"https://pic1.zhimg.com/606f0203ac8215dc4c0aa6c8a08efc88_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/606f0203ac8215dc4c0aa6c8a08efc88_b.jpg\"/></figure><br/><p data-pid=\"6ZMZTNWq\">推荐这样的内衣收纳袋给女生，盖子上放小内内，下面放bra，可以备一个保鲜袋把干净的和换洗的分开来，好处是bra不会变形啊，这太重要了！图片来自淘宝，随便搜的 </p><figure><noscript><img src=\"https://picx.zhimg.com/aabe1653b2a417f1df8bdbb614ef8b4f_b.jpg\" data-rawwidth=\"400\" data-rawheight=\"380\" data-original-token=\"aabe1653b2a417f1df8bdbb614ef8b4f\" class=\"content_image\" width=\"400\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;400&#39; height=&#39;380&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"400\" data-rawheight=\"380\" data-original-token=\"aabe1653b2a417f1df8bdbb614ef8b4f\" class=\"content_image lazy\" width=\"400\" data-actualsrc=\"https://picx.zhimg.com/aabe1653b2a417f1df8bdbb614ef8b4f_b.jpg\"/></figure><br/><p data-pid=\"JruNYn-Q\">一直没有买到合意的充电器收纳袋，现在用一个化妆包。自从淘汰了我的老iPad以后感觉生活水准上了一个大台阶：再也不用带两种水果充电线了！插头转换器我是走到哪里带到哪里的，路演时候一路下来与其每次想着下一个国家用什么头，不如一劳永逸带个万能的。另外我的很多电器是香港头，所以在国内也总是带着转换器以防万一（感觉中国的酒店一般都有万用插座，这一点美国就比较废一点，好在中国插头在美国也能用）</p><p data-pid=\"UuE4AkA0\">除了行李箱，Longchamp的折叠大号袋子也很好用，尤其是去的时候没啥东西，一路上买买买，回来时候就要用上了；更常见的是陪客户去路演，去的时候带着很多会议材料，一路开会一路用掉，回来时候就能把包包折起来塞进箱子里。陪我南征北战的大号Longchamp在此：</p><figure><noscript><img src=\"https://pic1.zhimg.com/0dbbdb6eb41d7bc0b67c933b56c2cf46_b.jpg\" data-rawwidth=\"960\" data-rawheight=\"1280\" data-original-token=\"0dbbdb6eb41d7bc0b67c933b56c2cf46\" class=\"origin_image zh-lightbox-thumb\" width=\"960\" data-original=\"https://pic1.zhimg.com/0dbbdb6eb41d7bc0b67c933b56c2cf46_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;960&#39; height=&#39;1280&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"960\" data-rawheight=\"1280\" data-original-token=\"0dbbdb6eb41d7bc0b67c933b56c2cf46\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"960\" data-original=\"https://pic1.zhimg.com/0dbbdb6eb41d7bc0b67c933b56c2cf46_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/0dbbdb6eb41d7bc0b67c933b56c2cf46_b.jpg\"/></figure><p data-pid=\"-rDnIjku\">Longchamp这样的软包还有一个作用：装鞋。女生的鞋形状不那么规则，放箱子里很浪费空间，高跟鞋尤甚。一般出门我会带三双鞋：运动鞋，平底鞋和高跟鞋，高跟鞋就扔软袋里啦。如果箱子里面空间紧张，把运动鞋挪出来也能省出很多地方</p><p data-pid=\"gw9_IIgg\">这就引出下一个话题：<b>如何规划出差的衣服和化妆品</b>？我的体会是，每次出门前想好今后几天的主打颜色，衣服、丝巾、包包、鞋子和彩妆都配合这个色系，能最经济的用好行李空间，每天早晨也不用花太多时间想怎么穿。比如我这次带的都是黑白灰的衣服，拎一个红色的手袋，那就带一双黑色高跟鞋，红色平跟鞋，偏红色丝巾，暖色眼影，偏橘色腮红，红色、珊瑚色口红；如果我打算穿蓝色衣服，那就带蓝色平跟，电蓝色丝巾，深蓝色手袋，紫色／粉色／大地色眼影盘，粉色腮红，玫红色系口红</p><p data-pid=\"NNmLIiq4\">----刚才想了一下，其实我是先选包，再配衣服，鞋，配饰和彩妆，因为出差，即使出门两周，也不会带好几个手袋，一般就一大一小，顶多了，所以我会围绕包包来选可以搭的衣服。包包可以带一个素色，一个艳色，或者一个冷色，一个暖色，基本可以应付所有场合了。嗯</p><p data-pid=\"YL9IXNcp\"><u><b>好，终于能出门了</b>！</u></p><p data-pid=\"B5gJCfyH\"><b>在机场搭小火车</b>，一定要去最前方的车厢，停车后不要回头，快步往前走，过关的时候5米的优势可能就是少排队10分钟啊！排队要挑外国人少的，警察叔叔处理外国人的护照和签证时间显著高于处理中国公民的。</p><p data-pid=\"GUqmG8_l\"><b>安检</b>这里就又显示出两舱的优势了，贵宾通道一般会快一点。安检通道的选择也很重要，比如首都机场，有的通道的安检门靠外，有的靠里，所以有的队伍看起来很短，其实都排在里头呢，前面还有10个人才到安检门，有的队伍看起来很长，都堵到外面来了，其实再过5个人就到安检门了。我的心得，安检队伍要尽量找商务旅客多的，要是有公公婆婆旅行团，那就麻烦了，这个包里有瓶水，那个袋子里个大水壶，等吧</p><p data-pid=\"v3M6O6Ay\">休息室的选择前文已经提过，不再赘述。</p><p data-pid=\"ejzxLAAV\">至于<b>飞机上位子的选择</b>，我个人喜欢过道位子，原因是走动方便。如果坐在靠窗位子然后旁边是一个一路上睡得昏天黑地的乘客，腰高腿长的男生还能抬腿跨过去，女生万一穿了裙子可怎么办^_^ 其实我挺喜欢国泰港龙的两舱，一个个位子是斜的，这样就能开开心心坐靠窗位子而不用怕被堵在里头啦</p><figure><noscript><img src=\"https://pic4.zhimg.com/bcaac8126f39418b46d93b61c02968f7_b.jpg\" data-rawwidth=\"720\" data-rawheight=\"960\" data-original-token=\"bcaac8126f39418b46d93b61c02968f7\" class=\"origin_image zh-lightbox-thumb\" width=\"720\" data-original=\"https://pic4.zhimg.com/bcaac8126f39418b46d93b61c02968f7_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;720&#39; height=&#39;960&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"720\" data-rawheight=\"960\" data-original-token=\"bcaac8126f39418b46d93b61c02968f7\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"720\" data-original=\"https://pic4.zhimg.com/bcaac8126f39418b46d93b61c02968f7_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/bcaac8126f39418b46d93b61c02968f7_b.jpg\"/></figure><p data-pid=\"7OdR3waJ\"><b>地面交通</b>：</p><p data-pid=\"zJa8EMBp\">经过长途飞行，现在你到了一个陌生的城市。备好当地零钱付出租车费很重要，你想你要是在北京掏出美元给出租车司机，人家也不收呀，谁知道真钞假钞呢？虽然越来越多的国家可以用信用卡付出租车钱，有点零钱还是更方便啦。如果去国内二三线城市或者县城，就算公司不可以报销订车的钱我也宁愿自己掏钱包车，安全，其实也不贵啦。</p><p data-pid=\"xiAuk2Ld\">另外平日出去开会也要考虑好车的问题，不是每个城市每个地方都好搭出租车的。公司能包车最好，没有包车的话提前问好酒店，对第二天的交通安排有个预计，哪里到哪里可以步行，哪里最好地铁，哪里必须搭车。现在有了Uber和各类专车，车的解决方案比以前多很多了</p><p data-pid=\"XFf5bzND\"><b>酒店：</b></p><p data-pid=\"xAaSvCXB\">几大酒店集团，感觉starwood的酒店最多吧？没有查具体数据，反正到哪里都有St Regis, Westin, Sheraton。 另一个选择就是Hyatt系列的酒店，但是亚洲以外Grand Hyatt其实没那么好，上回在纽约，会议室居然没有Wi-Fi！！！四季酒店业很好，就是没有会员积分制度，香格里拉仅限香格里拉品牌，洲际倒是有很多品牌可以积分，但是平时住洲际不那么多。再往下一等就是Sofitel之类了。不管哪个集团哪个品牌的酒店，最关键关键的指标是：离开会的地方近！北上广的堵车大家都知道，今天在杭州也堵得我心焦，差点没赶上火车。其实国外也一样，曼哈顿有点施工就堵得很，十条街以内的距离我宁愿走。所以近很重要，少浪费一小时在路上，就是多一小时的睡眠哟！</p><p data-pid=\"UX4l-wVo\">睡觉的时候锁好门，窗帘拉好，出门在外，保证睡眠质量很重要。女生一定要带一些面膜出门，长途飞行皮肤容易缺水，晚上睡觉前敷一个面膜，或者摸好睡眠面膜睡觉，对第二天早晨能够像模像样出门很重要。</p><p data-pid=\"zi6RzvpF\"><b>吃东西</b>：</p><p data-pid=\"LLbpc1bG\">如果在我熟悉的城市，且有充分的时间，我会喜欢出门吃早饭，譬如在上海我就喜欢到弄堂里面寻找生煎包，大饼油条和小馄饨。没时间的话room service或者让酒店打包一些东西拿到车上吃。如果一个人吃饭，可以去看看当地特色美食，但是前提是干净，吃坏肚子就得不偿失了。问一下酒店礼宾部，再cross check美食APP ，基本靠谱。出门前装好目的地的餐馆评论APP会很有用，到香港要Open Rice, 到台湾用食在方便，到美国查Yelp，国内的软件在海外地图不准，数据量也不够多。要是没时间或者太累了，就在酒店吃饭，最不费脑子了，通常不会太好吃，但是也不会太糟糕，至少填饱肚子没问题。</p><br/><p data-pid=\"WlquKonu\">高铁上网络不太给力，小小修改了一下，没想到这么多人看呢 哎呀呀</p>", "excerpt": "这个问题我太有感受了。每年飞20+万公里，国内出差主要上海深圳广州香港，国外新加坡美国 <b><u>出行准备</u></b> <b>航空公司的选择:</b>首先你要选一个默认航空公司，比较容易积累里程并快速的升到贵宾会员。就看你所在城市是哪个航空公司的hub。以前住香港必定入马可波罗会，还可以申领联名信用卡里程积分互换；后来搬来北京后就坐国航多了，国航和国泰港龙互相持股，里程可以累积在知音卡，也可以在马可波罗卡（当然是累积在自己航空公司的卡里面…", "excerpt_new": "这个问题我太有感受了。每年飞20+万公里，国内出差主要上海深圳广州香港，国外新加坡美国 <b><u>出行准备</u></b> <b>航空公司的选择:</b>首先你要选一个默认航空公司，比较容易积累里程并快速的升到贵宾会员。就看你所在城市是哪个航空公司的hub。以前住香港必定入马可波罗会，还可以申领联名信用卡里程积分互换；后来搬来北京后就坐国航多了，国航和国泰港龙互相持股，里程可以累积在知音卡，也可以在马可波罗卡（当然是累积在自己航空公司的卡里面…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1481775214, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1481574403348", "type": "feed", "target": {"id": "71077682", "type": "answer", "url": "https://api.zhihu.com/answers/71077682", "voteup_count": 1903, "thanks_count": 924, "question": {"id": "20039623", "title": "编程零基础应当如何开始学习 Python？", "url": "https://api.zhihu.com/questions/20039623", "type": "question", "question_type": "normal", "created": 1327915953, "answer_count": 566, "comment_count": 17, "follower_count": 44312, "detail": "", "excerpt": "", "bound_topic_ids": [307, 870, 872, 932], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1633668987, "created_time": 1446798506, "author": {"id": "661451715265ecd7de8ca438506a1575", "name": "<PERSON>", "headline": "公众号「Coder魔法院」☑工具控 ☑编程 ☑读书☑电影", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/michaelXoX", "url_token": "michaelXoX", "avatar_url": "https://pica.zhimg.com/ec0ab91f22237dd8910f80b5a1f93d49_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": false, "comment_count": 52, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"ZUs6IFl2\">一、说明</p><p data-pid=\"9Wsi4Lxl\">面对网络上纷繁复杂的资料，自己真是眼花缭乱，学的毫无章法，东一榔头西一棒子，这样不仅知识不能成为体系，自己的学习进度也不容易掌握，收效甚微。突然有个想法，就是把自己这几天收藏的资料整理出文章出来，方便自己有章可依，逐步走上python小牛的境界……</p><p data-pid=\"Js86far7\">PS:附上一些python相关的好文：</p><p data-pid=\"JHMYRYNd\">1.<a href=\"http://zhuanlan.zhihu.com/xiao-jing-mo/19959253\" class=\"internal\">萧大的编程入门指南</a></p><p data-pid=\"I7aGOsHp\">知乎获赞无数的编程指南，介绍的不光是一门语言的入门，也是关于编程的入门，谈到了作为一名程序员，应该掌握的一些计算机知识。</p><p data-pid=\"WpBLA96U\">2. <a href=\"https://link.zhihu.com/?target=http%3A//www.elias.cn/Python/PythonStyleGuide\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python 编码风格指南中译版（Google SOC）</a></p><p data-pid=\"7QT0W8MU\">3. <a href=\"https://link.zhihu.com/?target=http%3A//www.pythontab.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">PythonTab中文网</a></p><p data-pid=\"f45HhDyx\">------</p><p data-pid=\"qMY1NrB_\">二、Python社区</p><p data-pid=\"E4_Qtvcb\">1. <a href=\"https://link.zhihu.com/?target=http%3A//www.pythontip.com/coding/code_oj\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python Tip社区</a></p><p data-pid=\"VoyIB4KH\">强烈推荐Python Tip,有刷题挑战赛，同时，也有很多在线教程！练手实操必备！</p><p data-pid=\"3qCB9hF_\">2. <a href=\"https://link.zhihu.com/?target=http%3A//wiki.woodpecker.org.cn/moin/%25E9%25A6%2596%25E9%25A1%25B5\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">啄木鸟社区</a></p><p data-pid=\"kK2ijP5x\">3. <a href=\"https://link.zhihu.com/?target=http%3A//cocode.cc/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">编程指南社区Co</a></p><p data-pid=\"sL8GTNn5\">三、入门阶段</p><p data-pid=\"ulWhLzy1\">介绍一些入门的资料，对于有编程经验的同学来讲，入门资料学习并不是很费力。</p><p data-pid=\"hhDPsQwj\">1.<a href=\"https://link.zhihu.com/?target=http%3A//itlab.idcquan.com/linux/manual/python_chinese/index.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">简明教程</a></p><p data-pid=\"Qc1KQDOO\">入门教程里，简明教程算是细节介绍相对详细的了，知识面也覆盖的挺全，入门资料的好选择。</p><p data-pid=\"o06BpwzY\">2.<a href=\"https://link.zhihu.com/?target=http%3A//www.kancloud.cn/kancloud/learn-python-hard-way/49920\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">笨办法学python</a></p><p data-pid=\"eiZ2jBuk\">坚持看完了，尽管最后几章没去实现（主要是和我现在的需求不一致，不想花精力在那个上面）。有所收获吧，但是，确实是入门的，知识不全面，入门够用，风格特别，采用问答形式，学习过程挺有趣。</p><p data-pid=\"NPQu1Riq\">3.<a href=\"https://link.zhihu.com/?target=https%3A//www.codecademy.com/zh/learn/python\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python|Codecademy</a></p><p data-pid=\"JU8gPeaz\">在线挑战，还没用过，貌似现在对英文资料心里还有种抵触，必须克服！！！</p><p data-pid=\"ufUb_d4g\">4.<a href=\"https://link.zhihu.com/?target=https%3A//www.shiyanlou.com/jobs/python\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">实验楼python研发工程师</a></p><p data-pid=\"KbcSXaLx\">包含了Linux/Vim/Git/SQL/Python/Django/Flask等学习课程。</p><p data-pid=\"NoPqoWCa\">6.<a href=\"https://link.zhihu.com/?target=http%3A//wiki.jikexueyuan.com/project/start-learning-python/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">老齐的零基础学Python（第二版）</a></p><p data-pid=\"DeSyK4cC\">github版本，教程内容覆盖很全，也有实战项目介绍。</p><p data-pid=\"PqtG0Ygl\">7.<a href=\"https://link.zhihu.com/?target=http%3A//www.liaoxuefeng.com/wiki/001374738125095c955c1e6d8bb493182103fac9270762a000\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">廖雪峰python2.7教程</a></p><p data-pid=\"QtrE8LZZ\">现在已经有python3的教程了。</p><p data-pid=\"0MAdwvK8\">8.<a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/09/13/2682778.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Vamei的的python快速教程</a></p><p data-pid=\"G3dpLXCs\">话说，我还加入了博主的粉丝群，后来加入了微信群，逗比一枚啊，现在好像去新加坡深造去了！博客文章还是很详细的~</p><p data-pid=\"TKNkbGIc\">总结到这儿我已经有乱花渐入迷人眼的感觉了！光是入门就这么多资料，看的过来吗？看完得到猴年马月啊！所以必须痛下决心，选择自己觉得好的就OK！青菜萝卜，各有所爱，别人觉得好的，可能在你这儿就是看着不舒服！好吧，自己就选择简明，笨办法，crossin，还有，codecademy!ok,暂时就这样了！</p><p data-pid=\"A0LufPfP\">四、充实阶段</p><p data-pid=\"4s7ptKEj\">入门之后，对于这个语言的细节需要更进一步的了解，那么提升阶段必不可少。同时，对于计算机基础不好的同学，了解计算机及编程相关背景知识也很关键。</p><p data-pid=\"eL-lqapB\">1.<a href=\"https://link.zhihu.com/?target=http%3A//item.jd.com/11461683.html%3Fjd_pop%3Dbd44a477-d2b4-4158-bd1e-ff5dc5a3ff3e%26abt%3D3\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《Python基础教程》</a></p><p data-pid=\"cuP1zrT8\">根据自己学习javascript的经验，一本好的教材真是获益匪浅，它能让你明白很多底层的东西。比如红宝书《js高级程序设计》中关于闭包、原型链的讲解就非常详细！好吧，扯远了，因此，学习python也是一样，光是靠博客，在线文章是不能深入了解它的，选择一门经典教材是你深入了解一门语言的必经之路！当然，也有推荐<a href=\"https://link.zhihu.com/?target=http%3A//union.dangdang.com/transfer.php%3Fsys_id%3D1%26ad_type%3D10%26from%3DP-307250%26backurl%3Dhttp%253A%252F%252Fproduct.dangdang.com%252Fproduct.aspx%253Fproduct_id%253D20255354\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《Python核心编程（第二版）》</a>的，但是自己只买了<a href=\"https://link.zhihu.com/?target=http%3A//item.jd.com/11461683.html%3Fjd_pop%3Dbd44a477-d2b4-4158-bd1e-ff5dc5a3ff3e%26abt%3D3\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《Python基础教程》</a>因此，不做评价了，选中一本经典就可以了！何况自己离吃透它，还有很远的距离的！</p><p data-pid=\"wDij4_op\">2. <a href=\"https://link.zhihu.com/?target=http%3A//v.163.com/special/opencourse/bianchengdaolun.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">MIT计算机科学及编程导论</a></p><p data-pid=\"Vwq-YOne\">3. <a href=\"https://link.zhihu.com/?target=http%3A//v.163.com/special/opencourse/cs50.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Harvard:计算机科学cs50</a></p><p data-pid=\"zUsl7E6Y\">2和3属于计算机入门课，之所以放到这儿，是因为介绍了计算机领域中相关的知识点，了解数据结构相关知识。其中，MIT的导论课老师的知识点是基于Python的，在学习时也能巩固Python。<a href=\"http://zhuanlan.zhihu.com/xiao-jing-mo/19959253\" class=\"internal\">编程入门指南</a>中强烈推荐的的两门公开课。（PS：网易真是良心，这两门课都有中英字幕的视频！）</p><p data-pid=\"n9gAtgGV\">4. <a href=\"https://link.zhihu.com/?target=http%3A//115.29.188.128/static/wechat/index.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Crossin教室</a></p><p data-pid=\"VTgFW-xB\">除了python教程之外，还有小程序，练手很好！在第一阶段入门之后，来这里做应用小程序，会找到成就感！除此之外，还有git等其他教程。是个不错的入门练手的地方。</p><p data-pid=\"tkaJ4nD2\">五、升华阶段</p><p data-pid=\"-yKCK-Mj\">有了扎实的基础，那么方向的选择显得尤为重要了。是数据分析，是web开发，还是游戏开发。下面暂时分为这三个方面整理一下：</p><p data-pid=\"bAq1ybXI\">5.1 数据分析</p><p data-pid=\"tRoMdJKa\">1. <a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/25779298/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《利用Python进行数据分析》</a></p><p data-pid=\"6tUcwhvz\">这本书是一本大而全的利用Python数据分析的书，数据分析入门肯定够够的，写的也很详细。书的作者就是开发了用于数据分析的著名开源Python库——pandas的作者！</p><p data-pid=\"jM9gZIRr\">2. <a href=\"https://link.zhihu.com/?target=http%3A//nbviewer.ipython.org/github/jrjohansson/scientific-python-lectures/tree/master\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">scientific-python-lectures</a></p><p data-pid=\"iOB0ZZoX\">英文资料，对Python数据分析中要用到的一些库，pandas,numpy,matplotlib等等做了简要介绍。Ipython Notebook形式的资料，示例代码都很全。</p><p data-pid=\"cUt5Pd9C\">3. <a href=\"https://link.zhihu.com/?target=http%3A//reverland.org/python/2012/09/07/matplotlib-tutorial/%23scatter-plots\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Matplotlib Tutorial(译)</a></p><p data-pid=\"R5C-pl9U\">Python制图的入门资料，强烈推荐！在线版的资料，作者排版也很舒服，示例代码也有，推荐！</p><p data-pid=\"dFHkv5HJ\">4. <a href=\"https://link.zhihu.com/?target=http%3A//old.sebug.net/paper/books/scipydoc/index.html%23\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">用Python做科学计算</a></p><p data-pid=\"erdcEBJS\">最新发现的科学计算很棒的综合性教程，更新到这儿，需要的同学自取！强烈推荐！</p><p data-pid=\"2xF31iY9\">5.2 web开发</p><p data-pid=\"NRRcGafn\">1. <a href=\"https://link.zhihu.com/?target=http%3A//www.ziqiangxuetang.com/django/django-tutorial.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">自强学堂Django基础教程</a></p><p data-pid=\"6phavdVh\">很详细的一个Django教程，作者很详细的介绍了每一步。有问题，作者回复也很详细，推荐！同时，自强学堂上也有很多其他教程，是个不错的网站，收藏！</p><p data-pid=\"auHefI0x\">2. <a href=\"https://link.zhihu.com/?target=https%3A//andrew-liu.gitbooks.io/django-blog/content/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Django搭建简易博客教程</a></p><p data-pid=\"PyIQJoeZ\">建议和1结合看，1的介绍相对更详细一点。</p><p data-pid=\"rygnjwbC\">3. <a href=\"https://link.zhihu.com/?target=http%3A//www.pythondoc.com/flask-mega-tutorial/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">欢迎进入Flask大型教程项目</a></p><p data-pid=\"dRAKcDhr\">4. <a href=\"https://link.zhihu.com/?target=http%3A//www.pythondoc.com/flask/index.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Flask指南</a></p><p data-pid=\"xxI49z85\">5.3 游戏开发</p><p data-pid=\"lVVlQ8mI\">1. <a href=\"https://link.zhihu.com/?target=http%3A//eyehere.net/2011/python-pygame-novice-professional-index/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">用Python和Pygame写游戏-从入门到精通</a></p><p data-pid=\"yIBdmvg2\">六、 计算机素养</p><p data-pid=\"jJ1yqPAc\">1.<a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/5333562/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《深入理解计算机系统》</a></p><p data-pid=\"OngB1VOJ\">七、 Python面试题</p><p data-pid=\"FVTKMGk2\">1. <a href=\"https://link.zhihu.com/?target=http%3A//www.dongwm.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">聊聊Python面试那些事儿</a></p><p class=\"ztext-empty-paragraph\"><br/></p><blockquote data-pid=\"4yleA8El\"><b>[]~(￣▽￣)~* 欢迎关注个人公众号「Coder魔法院」，会持续分享有用的东西</b>   </blockquote><p data-pid=\"2Xo-SEdx\">欢迎回访个人博客☺</p><a data-draft-node=\"block\" data-draft-type=\"link-card\" href=\"https://link.zhihu.com/?target=https%3A//michael728.github.io/2015/12/02/python-files-list/\" data-image=\"https://pic1.zhimg.com/v2-1bb33c5aa39f520769959c9cf21a32ba_180x120.jpg\" data-image-width=\"267\" data-image-height=\"189\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python入门资料大全(更新ing)</a><p data-pid=\"S9J_018c\">补充：</p><a data-draft-node=\"block\" data-draft-type=\"link-card\" href=\"https://link.zhihu.com/?target=http%3A//segmentfault.com/a/1190000004285821\" data-image=\"https://picx.zhimg.com/v2-11debaada78cc2a630b8afb78eec12d1.jpg\" data-image-width=\"128\" data-image-height=\"128\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python机器学习入门资料整理 - SegmentFault 思否</a><p data-pid=\"Jhaqeqmg\">------更新------</p><p data-pid=\"5SpTkQvV\">最近知道了一个Python学习平台——夜曲编程，特别适合小白，该平台既提供了网页版，也提供了APP，方便随时随地学习。这个产品是由「百词斩」团队打造的，安利一下新手～</p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-c6981562835ef44aebf27194b03dc7bf_b.jpg\" data-rawwidth=\"1176\" data-rawheight=\"1513\" data-size=\"normal\" data-original-token=\"v2-83e18575c91f2c03495b7e3af51b1723\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-88466e6af39154befd616b6595d9ee46_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1176\" data-original=\"https://pic2.zhimg.com/v2-c6981562835ef44aebf27194b03dc7bf_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1176&#39; height=&#39;1513&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1176\" data-rawheight=\"1513\" data-size=\"normal\" data-original-token=\"v2-83e18575c91f2c03495b7e3af51b1723\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-88466e6af39154befd616b6595d9ee46_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1176\" data-original=\"https://pic2.zhimg.com/v2-c6981562835ef44aebf27194b03dc7bf_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-c6981562835ef44aebf27194b03dc7bf_b.jpg\"/></figure><p data-pid=\"lnoXj_lr\">像之前学单词一样，卡片式的交互去学习一个个Python知识点，尤其是编程零基础的同学！</p>", "excerpt": "一、说明 面对网络上纷繁复杂的资料，自己真是眼花缭乱，学的毫无章法，东一榔头西一棒子，这样不仅知识不能成为体系，自己的学习进度也不容易掌握，收效甚微。突然有个想法，就是把自己这几天收藏的资料整理出文章出来，方便自己有章可依，逐步走上python小牛的境界…… PS:附上一些python相关的好文： 1. <a href=\"http://zhuanlan.zhihu.com/xiao-jing-mo/19959253\" class=\"internal\">萧大的编程入门指南</a>知乎获赞无数的编程指南，介绍的不光是一门语言的入门，也是关于编程的入门，谈到了作为一名程序员，…", "excerpt_new": "一、说明 面对网络上纷繁复杂的资料，自己真是眼花缭乱，学的毫无章法，东一榔头西一棒子，这样不仅知识不能成为体系，自己的学习进度也不容易掌握，收效甚微。突然有个想法，就是把自己这几天收藏的资料整理出文章出来，方便自己有章可依，逐步走上python小牛的境界…… PS:附上一些python相关的好文： 1. <a href=\"http://zhuanlan.zhihu.com/xiao-jing-mo/19959253\" class=\"internal\">萧大的编程入门指南</a>知乎获赞无数的编程指南，介绍的不光是一门语言的入门，也是关于编程的入门，谈到了作为一名程序员，…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "disallowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1481574403, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1481574396562", "type": "feed", "target": {"id": "64926634", "type": "answer", "url": "https://api.zhihu.com/answers/64926634", "voteup_count": 20865, "thanks_count": 11396, "question": {"id": "20039623", "title": "编程零基础应当如何开始学习 Python？", "url": "https://api.zhihu.com/questions/20039623", "type": "question", "question_type": "normal", "created": 1327915953, "answer_count": 566, "comment_count": 17, "follower_count": 44312, "detail": "", "excerpt": "", "bound_topic_ids": [307, 870, 872, 932], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1701256868, "created_time": 1443020451, "author": {"id": "2f12a2c52d1af80e091341c00f67227d", "name": "知乎用户5Qo1ab", "headline": "《时代周刊》2006年度风云人物奖获得者！", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/code123", "url_token": "code123", "avatar_url": "https://pica.zhimg.com/v2-fd9bf372e4398113415f56131ead24a3_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "identity", "description": "互联网行业 从业人员", "topic_names": [], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "互联网行业 从业人员", "detail_type": "identity", "icon": "", "night_icon": "", "sources": [], "title": "认证", "type": "identity", "url": "https://zhuanlan.zhihu.com/p/96956163"}], "title": "互联网行业 从业人员"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 253, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"Q2hstmjw\">零基础学编程，用python入门是个不错的选择，虽然国内基本上还是以c语言作为入门开发语言，但在国外，已经有很多的学校使用python作为入门编程语言。此外，python在机器学习，人工智能领域也非常流行，算得上是算法工程师的标配编程语言。</p><p data-pid=\"GQ0ToOZh\"><b>下面的内容由浅入深，建议按照先后顺序阅读学习。如果你觉得内容不错，希望别做松鼠党，收藏了就不了了之，坚持读完，有条件的最好是购买书籍阅读效果更好。本文分享的内容和文末推荐的书籍均出自vamei君。</b></p><h2>一. Python基础</h2><p data-pid=\"lgxzBshc\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/05/28/2521650.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础01 Hello World!</a></p><p data-pid=\"GN7fO-tR\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/05/28/2522385.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础02 基本数据类型</a></p><p data-pid=\"gggK9OBd\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/05/28/2522677.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础03 序列</a></p><p data-pid=\"xQTdqi5s\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/05/29/2524376.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础04 运算</a></p><p data-pid=\"WdXW7yuB\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/05/29/2524706.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础05 缩进和选择</a></p><p data-pid=\"PR0xzwU3\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/05/30/2526357.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础06 循环</a></p><p data-pid=\"ne9ZDBJW\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/06/01/2529500.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础07 函数</a></p><p data-pid=\"GM_UKxu5\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/06/02/2531515.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础08 面向对象的基本概念</a></p><p data-pid=\"VoU1lpE9\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/06/02/2532018.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础09 面向对象的进一步拓展</a></p><p data-pid=\"PkbwxQa8\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/06/02/2532274.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python基础10 反过头来看看</a></p><h2>二. Python进阶</h2><p data-pid=\"n0OgPHuB\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/06/06/2537436.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python进阶01 词典</a></p><p data-pid=\"IztZXgaN\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/06/06/2537868.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python进阶02 文本文件的输入输出 </a></p><p data-pid=\"dIftdod5\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/07/03/2574436.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python进阶03 模块 </a></p><p data-pid=\"qbBTKg9_\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/07/08/2581264.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python进阶04 函数的参数传递 </a></p><p data-pid=\"IoYIJuiV\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/07/09/2582435.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python进阶05 循环设计 </a></p><p data-pid=\"enmqv2ez\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/07/09/2582499.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python进阶06 循环对象 </a></p><p data-pid=\"ywutHb73\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/07/10/2582772.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python进阶07 函数对象 </a></p><p data-pid=\"W5qicR3L\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/07/10/2582787.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python进阶08 错误处理 </a></p><p data-pid=\"RW8wwf_o\">三. Python深入 </p><p data-pid=\"ZoyINGwA\">到现在为止，Python学习已经可以告一段落。下面的部分，我想讨论Python的高级语法和底层实现。这一部分的内容并不是使用Python所必须的。但如果你想从事一些大型的Python开发(比如制作Python工具、写一个框架等)，你会希望对这一部分内容有所的了解。 <br/><br/><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/11/19/2772441.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python深入01 特殊方法与多范式</a></p><p data-pid=\"prRlUOx7\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/11/23/2772445.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python深入02 上下文管理器</a></p><p data-pid=\"KWxygQT3\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/12/11/2772448.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python深入03 对象的属性</a></p><p data-pid=\"48QBUGoD\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/12/15/2772451.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python深入04 闭包</a></p><p data-pid=\"76Z3PzAG\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2013/02/16/2820212.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python深入05 装饰器</a></p><p data-pid=\"8NhDO0j8\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/p/3232088.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python深入06 内存管理</a></p><h2>四. Python标准库 </h2><p data-pid=\"dAUWld9z\">Python标准库的重要性在于：</p><ul><li data-pid=\"FxcV_zv-\">标准库是Python的一个组成部分。</li><li data-pid=\"ywy1Qs2n\">Python的哲学是一个问题只有一个最好的解决方法。这些标准库为许多问题提供了一个标准的解决方案。</li></ul><p data-pid=\"Pe4XCS6f\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/07/18/2597212.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库——走马观花</a></p><p data-pid=\"APz4sZMA\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/07/23/2605345.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库的学习准备</a></p><p data-pid=\"DzLbbSUL\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/08/31/2661870.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库01 正则表达式 (re包)</a></p><p data-pid=\"9ZnDqfIz\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/09/03/2669426.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库02 时间与日期 (time, datetime包)</a></p><p data-pid=\"K8M4BOYp\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/09/05/2671198.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库03 路径与文件 (os.path包, glob包)</a></p><p data-pid=\"yxlMAcVW\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/09/14/2684775.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库04 文件管理 (部分os包，shutil包)</a></p><p data-pid=\"TCoybRGL\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/09/15/2684781.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库05 存储对象 (pickle包，cPickle包)</a></p><p data-pid=\"ABA_Noa0\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/09/23/2698014.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库06 子进程 (subprocess包)</a></p><p data-pid=\"-mRW3gds\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/10/06/2712683.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库07 信号 (signal包)</a></p><p data-pid=\"sOofFw2N\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/10/11/2720042.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库08 多线程与同步 (threading包)</a></p><p data-pid=\"-v5MKLj3\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/10/12/2721016.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库09 进程信息 (部分os包)</a></p><p data-pid=\"lLzqEcgP\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/10/12/2721484.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库10 多进程初步 (multiprocessing包)</a></p><p data-pid=\"dtsXm67e\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/10/13/2722254.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库11 多进程探索 (multiprocessing包)</a></p><p data-pid=\"eF6zrpUd\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/archive/2012/10/26/2741702.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库12 数学与随机数 (math包，random包)</a></p><p data-pid=\"pylErmaY\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/p/3174796.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库13 循环器 (itertools)</a></p><p data-pid=\"3AwRvA2D\"><a href=\"https://link.zhihu.com/?target=http%3A//www.cnblogs.com/vamei/p/3794388.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Python标准库14 数据库 (sqlite3)</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"U1p-cRDU\">附：<b>一定要实践！实践！实践！</b></p>", "excerpt": "零基础学编程，用python入门是个不错的选择，虽然国内基本上还是以c语言作为入门开发语言，但在国外，已经有很多的学校使用python作为入门编程语言。此外，python在机器学习，人工智能领域也非常流行，算得上是算法工程师的标配编程语言。 <b>下面的内容由浅入深，建议按照先后顺序阅读学习。如果你觉得内容不错，希望别做松鼠党，收藏了就不了了之，坚持读完，有条件的最好是购买书籍阅读效果更好。本文分享的内容和文末推荐的书籍…</b>", "excerpt_new": "零基础学编程，用python入门是个不错的选择，虽然国内基本上还是以c语言作为入门开发语言，但在国外，已经有很多的学校使用python作为入门编程语言。此外，python在机器学习，人工智能领域也非常流行，算得上是算法工程师的标配编程语言。 <b>下面的内容由浅入深，建议按照先后顺序阅读学习。如果你觉得内容不错，希望别做松鼠党，收藏了就不了了之，坚持读完，有条件的最好是购买书籍阅读效果更好。本文分享的内容和文末推荐的书籍…</b>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1481574396, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1481574367894", "type": "feed", "target": {"id": "42847756", "type": "answer", "url": "https://api.zhihu.com/answers/42847756", "voteup_count": 3816, "thanks_count": 1484, "question": {"id": "20702054", "title": "你是如何自学 Python 的？", "url": "https://api.zhihu.com/questions/20702054", "type": "question", "question_type": "normal", "created": 1357659384, "answer_count": 1020, "comment_count": 39, "follower_count": 57095, "detail": "", "excerpt": "", "bound_topic_ids": [872, 1354, 4848], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "513b8198d0364e862d72e9e877da7a90", "name": "Ariel", "headline": "知足知不足，有为有不为...", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/rainoy", "url_token": "rainoy", "avatar_url": "https://pic1.zhimg.com/e82c69b36_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1427171899, "created_time": 1427171652, "author": {"id": "b830f90d42560328acfd2a85ffcfc35d", "name": "gashero", "headline": "Overflow Engineer", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/gashero", "url_token": "gashero", "avatar_url": "https://pic1.zhimg.com/20623a6d7_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["编程", "程序员", "Python"], "topics": []}, {"type": "identity", "description": "互联网行业 从业人员", "topic_names": [], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "新知答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": null, "title": "年度新知答主", "type": "best", "url": "https://www.zhihu.com/question/510340037"}, {"badge_status": "passed", "description": "互联网行业 从业人员", "detail_type": "identity", "icon": "", "night_icon": "", "sources": [], "title": "认证", "type": "identity", "url": "https://zhuanlan.zhihu.com/p/96956163"}], "title": "新知答主"}, "vip_info": {"is_vip": false, "widget": {"url": "https://pica.zhimg.com/v2-48979508dddd0d1253cd62e2d2cbe189_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-48979508dddd0d1253cd62e2d2cbe189_r.jpg?source=5a24d060", "id": 40098}, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 117, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"u3CNfDjL\">这是我在过去几家公司招聘到工程师，Python入职培训的过程。</p><p data-pid=\"GmU9MdjS\">时间分为4周，全部自学，仅提供大纲。适用于Web方向：</p><p data-pid=\"LG_Y4JBh\">1、Week1：读完《简明Python教程》，适应Python开发环境</p><p data-pid=\"YZRhsZH3\">2、Week2：写个爬虫，需要深入了解re、urllib2、sqlite3、threading，Queue等几个模块。需要用上多线程抓取，正则表达式分析，并发资源控制，重新开启程序自动继续抓取和分析</p><p data-pid=\"RMvgfjMH\">3、Week3：学习一种Web开发框架，推荐Flask、webpy之类的，学个数据库接口如sqlite3，写个简单的web应用如博客</p><p data-pid=\"8XItf_Jr\">4、Week4：给产品做个小功能并走完测试和上线流程，各个时期是不同的</p><p data-pid=\"6Pktyxoj\">我在之前的几家公司招聘工程师时，学过Python的其实较少。更常见的情况是人聪明，招来再学Python。就是按照如上流程。这个流程安排的挺轻松的，我找到的所有人都成功完成了这个流程。并且之后工作也很顺利。</p>", "excerpt": "这是我在过去几家公司招聘到工程师，Python入职培训的过程。 时间分为4周，全部自学，仅提供大纲。适用于Web方向： 1、Week1：读完《简明Python教程》，适应Python开发环境 2、Week2：写个爬虫，需要深入了解re、urllib2、sqlite3、threading，Queue等几个模块。需要用上多线程抓取，正则表达式分析，并发资源控制，重新开启程序自动继续抓取和分析 3、Week3：学习一种Web开发框架，推荐Flask、webpy之类的，学个数据库接口如sql…", "excerpt_new": "这是我在过去几家公司招聘到工程师，Python入职培训的过程。 时间分为4周，全部自学，仅提供大纲。适用于Web方向： 1、Week1：读完《简明Python教程》，适应Python开发环境 2、Week2：写个爬虫，需要深入了解re、urllib2、sqlite3、threading，Queue等几个模块。需要用上多线程抓取，正则表达式分析，并发资源控制，重新开启程序自动继续抓取和分析 3、Week3：学习一种Web开发框架，推荐Flask、webpy之类的，学个数据库接口如sql…", "preview_type": "expand", "preview_text": "<p data-pid=\"u3CNfDjL\">这是我在过去几家公司招聘到工程师，Python入职培训的过程。</p><p data-pid=\"GmU9MdjS\">时间分为4周，全部自学，仅提供大纲。适用于Web方向：</p><p data-pid=\"LG_Y4JBh\">1、Week1：读完《简明Python教程》，适应Python开发环境</p><p data-pid=\"YZRhsZH3\">2、Week2：写个爬虫，需要深入了解re、urllib2、sqlite3、threading，Queue等几个模块。需要用上多线程抓取，正则表达式分析，并发资源控制，重新开启程序自动继续抓取和分析</p><p data-pid=\"RMvgfjMH\">3、Week3：学习一种Web开发框架，推荐Flask、webpy之类的，学个数据库接口如sqlite3，写个简单的web应用如博客</p><p data-pid=\"8XItf_Jr\">4、Week4：给产品做个小功能并走完测试和上线流程，各个时期是不同的</p><p data-pid=\"6Pktyxoj\">我在之前的几家公司招聘工程师时，学过Python的其实较少。更常见的情况是人聪明，招来再学Python。就是按照如上流程。这个流程安排的挺轻松的，我找到的所有人都成功完成了这个流程。并且之后工作也很顺利。</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1481574367, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1481574367894&page_num=80"}}