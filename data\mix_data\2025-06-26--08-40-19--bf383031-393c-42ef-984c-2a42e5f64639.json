{"data": [{"id": "60_**********.510", "type": "feed", "offset": 60, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": **********, "updated_time": **********, "target": {"id": "**********", "type": "answer", "url": "https://api.zhihu.com/answers/**********", "author": {"id": "000bb05eb79fb2881e043ac00b7420c8", "url": "https://api.zhihu.com/people/000bb05eb79fb2881e043ac00b7420c8", "user_type": "people", "url_token": "shang-guan-ren", "name": "上官人", "headline": "企业创新教练，说真话，不变现", "avatar_url": "https://pic1.zhimg.com/50/1ab482a224f9802a640dcadae0b2d347_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 112957, "is_following": false, "is_followed": false}, "created_time": 1686818615, "updated_time": 1686818615, "voteup_count": 7492, "thanks_count": 1430, "comment_count": 238, "is_copyable": true, "question": {"id": "38531356", "type": "question", "url": "https://api.zhihu.com/questions/38531356", "author": {"id": "9b7ddfe9790fa92b1ce1564199492925", "url": "https://api.zhihu.com/people/9b7ddfe9790fa92b1ce1564199492925", "user_type": "people", "url_token": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "杨捷", "headline": "", "avatar_url": "https://pic1.zhimg.com/50/v2-8c412bb1d7181bdae29ba93f106a78d2_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 1059, "is_following": false, "is_followed": false}, "title": "什么时候才是开掉「技术合伙人」的最佳时机？", "created": 1450138751, "answer_count": 0, "follower_count": 0, "comment_count": 37, "bound_topic_ids": [12162, 200030], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "这个问题很好，用一个恶心人的视角，揭示了一个技术人不愿意面对的事实，技术合伙人在大多数情况下被开掉是必然的，是正当的，并非某个不懂技术的老板瞎搞。 此时应该有个例子，正好有个答主 @库森学长 给了一个很鲜活的案例：什么时候才是开掉「技术合伙人」的最佳时机？ 这位答主开篇明义，把技术合伙人被开掉总结为一个常见现象，一般发生在创业公司业务稳定后，CEO就会看CTO不顺眼。他给出的解决方案是跟CEO搞好关系。 现象是…", "excerpt_new": "这个问题很好，用一个恶心人的视角，揭示了一个技术人不愿意面对的事实，技术合伙人在大多数情况下被开掉是必然的，是正当的，并非某个不懂技术的老板瞎搞。 此时应该有个例子，正好有个答主 @库森学长 给了一个很鲜活的案例：什么时候才是开掉「技术合伙人」的最佳时机？ 这位答主开篇明义，把技术合伙人被开掉总结为一个常见现象，一般发生在创业公司业务稳定后，CEO就会看CTO不顺眼。他给出的解决方案是跟CEO搞好关系。 现象是…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"pciMYqve\">这个问题很好，用一个恶心人的视角，揭示了一个技术人不愿意面对的事实，技术合伙人在大多数情况下被开掉是必然的，是正当的，并非某个不懂技术的老板瞎搞。</p><p data-pid=\"ClJP6wsA\">此时应该有个例子，正好有个答主 <a class=\"member_mention\" href=\"https://www.zhihu.com/people/0a4b0784aa2c7b384694033addac9d30\" data-hash=\"0a4b0784aa2c7b384694033addac9d30\" data-hovercard=\"p$b$0a4b0784aa2c7b384694033addac9d30\">@库森学长</a> 给了一个很鲜活的案例：</p><a href=\"https://www.zhihu.com/question/38531356/answer/3063261026\" data-draft-node=\"block\" data-draft-type=\"link-card\" class=\"internal\">什么时候才是开掉「技术合伙人」的最佳时机？</a><p data-pid=\"bXUN0Mxt\">这位答主开篇明义，把技术合伙人被开掉总结为一个常见现象，一般发生在创业公司业务稳定后，CEO就会看CTO不顺眼。他给出的解决方案是跟CEO搞好关系。</p><p data-pid=\"Obo0-aXe\">现象是对的，这位答主一看就有实战经验，但是方案是不靠谱的，正如关系型销售必然没落一样，关系型高管也存活不下去，何况技术人还不擅长搞关系。</p><p data-pid=\"UzlVDv8X\">我借着这个案例给大家讲讲，创业公司CTO的困境到底是什么，为什么他必然被干掉，为什么我说这个老板还算仁义。这里要感谢库森学长的回答。</p><blockquote data-pid=\"kqHXwWoG\">前年的时候，也有一家创业型公司让我过去，我跟老板聊了聊，又从侧面打听，发现公司的现金流就只有三个月的，其实并不保险。<br/>另外，老板是销售出身，<a href=\"https://www.zhihu.com/search?q=%E8%9C%9C%E6%9C%88%E6%9C%9F&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3063261026%7D\" class=\"internal\" target=\"_blank\">蜜月期</a>过后，非常容易把研发这块当做纯成本项，觉得贵了慢了的，到时候又是个麻烦事儿。<br/>所以我就直接拒绝了，老板让我给他推荐其他人，我就把我的一个老大哥大东推荐了过去。<br/>果然不出我所料，最后也没能善终。</blockquote><p data-pid=\"RWp4H3xW\">这是个典型的创业小公司，融了A轮，现金流三个月，CEO年轻有为。活着都有风险，但是未来一旦成功也很光明。我之所以说这个案例很典型，就是因为大多数公司还不如这个公司靠谱，你大概率去的是一个没啥希望的小池塘，这公司至少老板靠谱，业务有希望。</p><blockquote data-pid=\"BPGGIIRJ\">当时<a href=\"https://www.zhihu.com/search?q=%E5%A4%A7%E4%B8%9C&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">大东</a>目前刚从阿里离职，在家赋闲中，正盘算下一步路如何走。继续去创业型公司当CTO，还是找个大厂踏实待着赚钱。<br/>听了我的推荐后，大东有些动心，因为这家公司的CEO，他也听说过，确实是年轻有为，在行业内影响力极高，他表示可以聊聊看。<br/>第二周，CEO跟大东见面聊了聊，对履历丰富、为人踏实的大东也比较满意。<br/>CEO表示，如果大东愿意加入的话，来当公司的CTO加合伙人，他会给大东一个点的期权。但公司目前才刚刚A轮，所以工资只能给大东4万。<br/>大东听了表示基本满意，自己这个人老色衰的年龄，还能赶上这样的机会，让他有个梦可以做，也算是实属不易。</blockquote><p data-pid=\"BB1E9nNv\">答主不爱去，大东爱去，大东缺这个机会，也愿意赌一把。从待遇上来看，工资不算高也不算低，毕竟公司现金流有限。给一个点的期权，B轮是8亿美金，A轮1个亿总也有了，那就是七百万人民币期权。毕竟是A轮的技术合伙人，天使轮都没参加，自己也不出钱，给干股说不过去，这个数的期权也不少了。</p><p data-pid=\"838sg5gP\">具体干的过程我就忽略了，简单总结，就是拉来技术骨干，建立流程，引入工具，提升团队能力，这是效果。</p><blockquote data-pid=\"vvmBkkx6\">一年过去了，研发短板没了，与此同时，业务增速也是一个季度比一个季度猛，公司也进行了B轮和B+轮两轮融资，估值也达到了8亿<a href=\"https://www.zhihu.com/search?q=%E7%BE%8E%E9%87%91&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">美金</a>。</blockquote><p data-pid=\"--u-cFYa\">你要注意的是，这里大东所有的发力都是向内的，就是研发团队内部改革，后面我们会看到问题。</p><p data-pid=\"W_ErTjvo\">从答主或者大东的角度来看，公司发展得好，研发瓶颈的解决是功不可没的。实际上应该也八九不离十，但是真正驱动公司发展的一定不是研发。研发只是制约发展的一个关键瓶颈而已。大东在合适的时间拓宽了这个瓶颈，公司得到了一定程度的发展，但这并不等于一劳永逸了。</p><p data-pid=\"JSG25RRT\">答主后面谈大东遇到的问题，主要集中在两个方面，一个是业务反对，一个是CEO不认可。</p><p data-pid=\"ZN5JIipk\">业务反对的原因，我们可以从这段对话中略知一二：</p><blockquote data-pid=\"PRKMuBDi\">每条业务线的业务老大都觉得，<b>大东给自己这条业务线配置的研发资源少了</b>，或是研发资源不给力。<br/>大东：“你看，就你这条业务线工程师最多，总共有27个人，<b>你还有什么不满意的？</b>”<br/>A业务线老大：“但我这条业务线的工程师都是P5 P6，你看其他业务线的，都是P6 P7，再往我这边倾斜一些资源吧，我这边业务都是公司的流量入口。”<br/>大东：“你看，就你这条业务线工程师的<a href=\"https://www.zhihu.com/search?q=%E9%AB%98P&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">高P</a>最多，总共有5个P7，<b>你还有什么不满意的？</b>”<br/>B业务线老大：“产品经理跟我说了，你这边的P7只做<a href=\"https://www.zhihu.com/search?q=%E6%8A%80%E6%9C%AF%E4%BC%98%E5%8C%96&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">技术优化</a>，不做我的业务需求，我想要一些做业务需求的P5 P6，再往我这边倾斜一些资源吧，我这边都是公司的未来十年的发展方向。”<br/>大东：“我把维护<a href=\"https://www.zhihu.com/search?q=%E4%B8%AD%E9%97%B4%E4%BB%B6&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">中间件</a>的人都调过去支援你的业务需求了，<b>你还有什么不满意的？</b>”<br/>C业务线老大霸气地说：“我不管，我这边每进一个<a href=\"https://www.zhihu.com/search?q=%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">产品经理</a>，你这边必须进四个研发支持我，我这边正在跟竞对打仗，你必须在中后台给我<a href=\"https://www.zhihu.com/search?q=%E6%AD%A6%E5%99%A8%E5%BC%B9%E8%8D%AF&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">武器弹药</a>供应。”</blockquote><p data-pid=\"XYBq8Eia\">我标黑的部分是我认为的问题。</p><p data-pid=\"_mR51JSY\">你注意，我并没有觉得所有的业务线都反对大东是问题的核心，我标记的是，他们认为自己的研发资源是大东给配置的，配置得少了。</p><p data-pid=\"ncNXs9PY\">那么业务评价配置的多少的标准是什么呢？与其他业务线比较。比较人数、级别、工作内容。</p><p data-pid=\"InBpjonL\">所以大家隐含的逻辑是，资源是你大东给配的，你必须给我配置得公平。</p><p data-pid=\"5pJ85RMo\">这是一个CTO不应当承担的责任，因为CTO承担不起，不仅仅是大东能力不够，而且公司也不应该让CTO来承担这个责任。</p><p data-pid=\"kZMi93Ys\">项目组合管理、战略管理里，都会就项目预算、战略预算进行制定，然后配置资源。</p><p data-pid=\"wUHXQzrY\">大东越俎代庖，承担了公司预算制定这个职责，等于他决策，来往三个无底洞里扔资源，不仅仅是公司资源的严重错配，也注定了谁也满足不了。</p><p data-pid=\"BoWlN3JU\">这位答主并没有觉得这是大东的问题，他只是觉得业务贪婪，老板轻信：</p><blockquote data-pid=\"ncFfLejl\">CEO本身是业务出身，平时跟三条业务线的老大走得比较近，总一起吃饭什么的。吃饭的时候，CEO听业务线的老大们时不时地吐槽大东，时间久了，情到浓时情转薄，<a href=\"https://www.zhihu.com/search?q=%E5%BF%83%E5%8F%A3%E7%9A%84%E6%9C%B1%E7%A0%82%E7%97%A3&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">心口的朱砂痣</a>也变成了蚊子血，<a href=\"https://www.zhihu.com/search?q=%E7%99%BD%E6%9C%88%E5%85%89&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3063261026%7D\" class=\"internal\" target=\"_blank\">白月光</a>也变成了米饭粒，开始对大东也有了一些看法。</blockquote><p data-pid=\"sL8xBlS1\">从表面上是这样，但是从公司经营的逻辑上，公司发展的增长主体是业务部门，技术部门是后场提供炮弹的。</p><p data-pid=\"HfKWwh0C\">答主之所以写这么细，就是因为他认为，这不是大东的问题，而是业务的问题。这是技术人的一个典型认知误区，业务反对大东，是因为大东制约了业务发展，业务的发展是公司需要、CEO需要、股东需要，而不仅仅是业务老大的私欲。如果业务老大因为一己之私欲而反对你，搞政治，那么CEO、董事会都会干预——然而并没有，反而是大东被干掉了。</p><p data-pid=\"X7Sgci6T\">你对抗的到底是一个业务老大，还是站在他背后的全公司、投资人？</p><p data-pid=\"NQXaDepV\">你要是连你的对手都搞错了，自然死无葬身之地。</p><p data-pid=\"87U4NV3D\">一个聪明的CTO，在此时就要主动放权，要么把事儿摊开了扔给业务自己打架，要么交给CEO拍，要么要求按照战略分配。无论如何，预算制定这件事情不能CTO干，谁干谁死。军需官不是指挥官，不能做战略资源分配的决策，只是执行决议。</p><p data-pid=\"M6ax_U_l\">这是第一个核心问题，第二个核心问题，就是公司的发展阶段已经变了，需要的不是大东这样只会干活的CTO，答主借CEO的嘴说了出来：</p><blockquote data-pid=\"t-oQskCq\">竞对的研发团队才100人，而自家公司的研发都超过150人了。<br/>公司期权池里的<a href=\"https://www.zhihu.com/search?q=%E6%9C%9F%E6%9D%83&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">期权</a>，大部分都分给研发团队了。<br/>故障虽然少了，但这周的一次故障损失的钱，顶过去三个月的故障损失总和了。<br/>研发团队加班比一年前少了，业务团队对此有意见，认为没有跟业务团队共甘苦。<br/>CEO觉得，大东的能力只能把研发团队hold到B+轮，到了<a href=\"https://www.zhihu.com/search?q=C%E8%BD%AE&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">C轮</a>就出现瓶颈了。于是，他开始四处物色能力和履历上都更加出色的技术带头人。</blockquote><p data-pid=\"3NULWIxz\">答主描述的CEO的心理活动，主要围绕着“研发的投入产出比”来说，觉得研发人多、花得多、损失大、加班少，不值了。</p><p data-pid=\"6-ndR_6H\">我认为要么是答主格局小了，猜得偏了，要么是CEO格局小了，看得偏了。</p><p data-pid=\"h1ahKINF\">CEO应该关注的是经营目标和战略目标到底是什么，研发团队是否能够在一个可接受的预算内帮助公司达成。</p><p data-pid=\"HHK_n6vQ\">从这个角度说，CEO的结论是没问题的，大东就是瓶颈，只不过大东的瓶颈不是答主和大东自己以为的那个瓶颈——并不是他不擅长和CEO以及业务老大们搞关系，而是<b>大东他只擅长做管理，听不懂战略</b>。当公司到达野蛮生长的后期，需要有规划地前进时，他跟不上了。</p><p data-pid=\"o9t_6Taj\">大东也委屈，这公司也许就没有战略啊、预算啊的概念。否则，当业务老大说，我这业务代表了公司未来十年的发展时，他就会说，“卧槽这么重要！这是大事儿，兄弟你多跟老板要点预算，我也好帮你挑点精兵强将，咱们好好搞一搞。”</p><p data-pid=\"ulWe2I1U\">你他么代表公司未来十年发展，老板还不给你预算？这不就等于是吹牛逼嘛，你跟我偷偷摸摸要什么人。</p><p data-pid=\"L6IlOqR9\">大东要是狠一点，在高管会上直接就提这个问题，“最近几个业务老大私下里都跟我沟通过，研发资源不够，我给得不均衡。我觉得不能让我来决定公司各个业务的资源投入，咱们今天要不就讨论讨论，到底怎么个分配比例。今天定下来，大家就不用私下里找我了”。</p><p data-pid=\"CNKt5fQY\">让他们打去呗，老板还愿意看他们打，因为打的时候，很多隐藏的潜规则就会暴露出来，老板就有抓手了。</p><p data-pid=\"E8JFpcCG\">我给很多企业做过这方面的咨询，这位答主说的大东是CTO们的典型代表，基本上都是需要先把这个权力从CTO身上摘掉，至少也是集体决策，比如经营会战略会上，大家一起过，有问题会上说。CTO就是执行，并且要管着业务对资源的使用，要和会上决议一致。</p><p data-pid=\"nBvFvuFq\">这是两个层次的CTO，一个本质上就是技术总监，就是个中层，负责建立研发体系，组织兄弟们干活；一个是公司高管，参与公司战略制定，负责公司战略在技术方向上的落实。</p><p data-pid=\"zJmAc5TS\">所以我在那个回答的评论区里说，大东本质上就是个中层，公司发展出高层了，但是大东还没达到这个水平，落后于公司发展，挡了公司的路。</p><p data-pid=\"-NxswOTJ\">在这种情况下，一个尽职的CEO，可以选择给大东机会成长，但是到了一定时期，就必须让大东腾地方，挪位置，不能干扰公司正确的战略执行。</p><p data-pid=\"fMcaOc_S\">而这个CEO的做法是：</p><blockquote data-pid=\"pntXfntS\">大东说：“老板，怎么公司突然间又找了一个CTO啊？而且之前也完全没跟我说啊？”<br/>CEO说：“这一年多，你确实挺辛苦，尽心尽力地为公司做出不少成绩，但确实现在面临瓶颈，我觉得你更适合当我司的<a href=\"https://www.zhihu.com/search?q=%E6%8A%80%E6%9C%AF%E5%90%88%E4%BC%99%E4%BA%BA&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A3061716019%7D\" class=\"internal\" target=\"_blank\">技术合伙人</a>，而不是CTO。新CTO来了，你跟他好好配合，这也给了你成长的机会。”<br/>大东说：“我一时有些接受不了。”<br/>CEO说：“我希望你能留下，但如果实在接受不了，你可以看看其他公司，有没有CTO的机会吧。”</blockquote><p data-pid=\"5vDedf19\">什么叫仁至义尽？这就叫仁至义尽。待遇没降，退二线，只求你不要挡在公司前进的路上。</p><p data-pid=\"8PrZrLot\">CEO并没有下黑手搞掉大东，对比很多喜欢搞小动作的CEO来说，已经是仁至义尽。</p><p data-pid=\"GqW71WkR\">高层一般都是先要保证离职的影响范围可控，然后才摊牌，因为高管更迭对公司的影响非常大，伤筋动骨，CEO也是憋疯了。</p><p data-pid=\"RjwCR82L\">归根结底，不同阶段的公司对“技术合伙人”的要求是不同的，技术合伙人必须保持成长，要比公司的要求快。如果不能做到，下课是必然的。</p><p data-pid=\"XEJNnVbt\">这不仅仅是对技术合伙人的要求，也是对所有公司高管的要求，销售不行销售下课，市场不行市场下课，人力、行政、财务、运营，都是这个逻辑。</p><p data-pid=\"w5wEV6UN\">所以，什么是开掉“技术合伙人”的最佳时机？</p><p data-pid=\"b9oNAXET\">是当这个技术合伙人阻碍了公司发展，并且自身的发展潜力也到头了的时候，无论如何，都需要换人了。</p><p data-pid=\"0hlcBnGq\">那么到底公司什么时候需要什么样的技术合伙人？</p><p data-pid=\"5YgkHMAF\">这问题要说就复杂了，简单说两句：</p><p data-pid=\"eZml3Pdq\">一般一个公司都是先跑通一个业务，这个业务成了以后，做产品线延伸，再做产品线扩展，画第二曲线。</p><p data-pid=\"3aLS0DDP\">在跑通第一个业务的过程中，技术团队先是研发，接着是优化，接着是打造产品线平台，接着是打造多产品线通用平台，最后是维护、颠覆或者死亡。</p><p data-pid=\"ujj5HHsf\">研发和优化的时候，都只是需要一个带头干活的，也就是一个组长就够了，属于基层管理者。</p><p data-pid=\"1f9aF7gz\">打造产品线平台，需要一个总监，也就是中层，能定规矩建体系带团队。</p><p data-pid=\"8Ak6yD2c\">打造多产品线通用平台，就需要一个CTO来参与公司战略，协调技术与业务的关系，保障战略落地，这是个高层。</p><p data-pid=\"T8cO-7r6\">如果公司发展得好，那么对技术合伙人的要求就是不断升级。</p><p data-pid=\"HFT967pn\">如果公司发展得不好，那么就很有可能换个人试试，或者干脆砍掉技术团队，缩小业务，这就没办法了。</p><p data-pid=\"4UYGVrDl\">回答区大多数都是技术管理者从自身狭隘视角的阴谋论，看不到技术合伙人的使命在公司生命周期中的变迁，也就不可能产生任何有价值的解决方案，无助于技术合伙人的成功。</p><p data-pid=\"KuP6bJpz\">我帮助很多CTO坐稳位置，与公司合作共赢了，这事儿我还是比较有发言权，至少是成功者的经验。</p><p data-pid=\"asS9rCpF\">如果有兴趣的朋友也可以去我的回答里看看，那里边的花臂，遇到的就是和大东类似的情况。</p><a href=\"https://www.zhihu.com/question/329993317/answer/2546553904\" data-draft-node=\"block\" data-draft-type=\"link-card\" class=\"internal\">企业里的高管都是如何被干掉的？</a><p></p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 695075, "favorite_count": 10598, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": **********}", "attached_info": "CrYGCPq2yZP9xtXXnwEQBBoJNTg5MjE5ODk0ILeeq6QGKMQ6MO4BQDxKQQosVFNfU09VUkNFX1RXT1RPV0VSX1NIT1JUSU5URVJFU1RfUkVDQUxMX1RFWFQSATAYACAAOgp7InJhdyI6IiJ9Wgc3NjE5OTQwYiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3IKMzA3NTI0MDk0OYoBCDM4NTMxMzU2qgEJcmVjb21tZW5kwgEgMDAwYmIwNWViNzlmYjI4ODFlMDQzYWMwMGI3NDIwYzjyAQoIDBIGTm9ybWFs8gEoCAoSJDQ0NGI2YWEwLTE3ZTEtNGJjZC1iY2NhLTI0ZTMyNzhlNDVmNPIBBggLEgIxMYICAIgCyqy5zfoykgIgMDAwYmIwNWViNzlmYjI4ODFlMDQzYWMwMGI3NDIwYziaAgDKAhZTaG9ySW50ZXJlc3RXZWlnaHRSdWxlygIWQWN0aW9uU2hvckludGVyZXN0UnVsZcoCG0ludGVyYWN0aW9uU2hvckludGVyZXN0UnVsZcoCGFBlcmlvZEludGVyZXN0V2VpZ2h0UnVsZcoCFVVzZXJMY25FeGl0V2VpZ2h0UnVsZcoCFENvbnRlbnRBZ2VXZWlnaHRSdWxl2gIsVFNfU09VUkNFX1RXT1RPV0VSX1NIT1JUSU5URVJFU1RfUkVDQUxMX1RFWFToAgP6AgtOT1JNQUxfRkxPV4oDIGRhNDlmMmE4YjQ0YjQ1YTNhZjI0ZTQ1OGFkMmY0MGIymgMNCgJ2MhAAGgVvdGhlcqgDo7Yq2AMA6gMaZmVlZF9hdHRtX3R3b3Rvd2VyX3YyX3RleHT6Ax8SDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFgAQAiAQAkgQGTm9ybWFsmgQBM6AEAKgEALAEALoEBm1hbnVhbMIEAzE2MMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAoAF6uz+BBQAAAAAAAAAAiQVaEpUIpKzSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AULkAYAoAZAqAYBkgIlCgk1ODkyMTk4OTQSCjMwNzUyNDA5NDkYBCIKSU1BR0VfVEVYVA==", "action_card": false}, {"id": "61_**********.24", "type": "feed", "offset": 61, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": **********, "updated_time": **********, "target": {"id": "30201040247", "type": "article", "url": "https://api.zhihu.com/articles/30201040247", "author": {"id": "ef9d1602fc7c41e122b227cc42dfe3e7", "url": "https://api.zhihu.com/people/ef9d1602fc7c41e122b227cc42dfe3e7", "user_type": "people", "url_token": "to<PERSON><PERSON><PERSON>", "name": "to<PERSON><PERSON><PERSON>", "headline": "GZH：《零一瓦舍》", "avatar_url": "https://pica.zhimg.com/50/00dc3b076fb38562011789cc568a8edf_l.jpg?source=b6762063", "is_org": false, "gender": 1, "badge": [{"type": "super_activity", "description": "知势榜科技互联网领域成长力榜答主"}, {"type": "identity_people", "description": "字节跳动 员工"}], "followers_count": 10339, "is_following": false, "is_followed": false}, "title": "全景解读 LLM 后训练技术", "comment_permission": "all", "created": 1741929251, "updated": 1741929251, "voteup_count": 338, "voting": 0, "comment_count": 3, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "这篇文章是以2025年2月的一篇综述论文为蓝本，对「LLM后训练技术」的全景讲解： [2502.21321] LLM Post-Training: A Deep Dive into Reasoning Large Language Models 引言：从预训练到后训练2023 年，当 ChatGPT 惊艳世界时，很多人第一次意识到：原来 AI 不仅能背课文，还能写代码、编故事、解数学题。这些聪明表现的背后，得益于大语言模型（LLM）的两个关键训练阶段： 预训练（Pretraining）和后训练（Post-training）。预训…", "excerpt_new": "这篇文章是以2025年2月的一篇综述论文为蓝本，对「LLM后训练技术」的全景讲解： [2502.21321] LLM Post-Training: A Deep Dive into Reasoning Large Language Models 引言：从预训练到后训练2023 年，当 ChatGPT 惊艳世界时，很多人第一次意识到：原来 AI 不仅能背课文，还能写代码、编故事、解数学题。这些聪明表现的背后，得益于大语言模型（LLM）的两个关键训练阶段： 预训练（Pretraining）和后训练（Post-training）。预训…", "preview_type": "default", "preview_text": "", "column": {"id": "c_1771274012468887552", "type": "column", "url": "https://api.zhihu.com/columns/c_1771274012468887552", "author": {"id": "", "url": "", "user_type": "people", "url_token": "", "name": "匿名用户", "headline": "", "avatar_url": "https://pic1.zhimg.com/v2-d41c2ceaed8f51999522f903672a521f_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "硅基进化", "imageUrl": "https://picx.zhimg.com/v2-f111d7ee1c41944859e975a712c0883b_720w.jpg?source=d16d100b", "comment_permission": "private", "intro": "AI技术的自学与教学", "updated": 1732339440, "is_following": false}, "content": "<p data-pid=\"31EHZWrY\">这篇文章是以2025年2月的一篇综述论文为蓝本，对「LLM后训练技术」的全景讲解：<a href=\"https://link.zhihu.com/?target=https%3A//arxiv.org/abs/2502.21321\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">[2502.21321] LLM Post-Training: A Deep Dive into Reasoning Large Language Models</a></p><h2>引言：从预训练到后训练</h2><p data-pid=\"8YcmvKhx\">2023 年，当 ChatGPT 惊艳世界时，很多人第一次意识到：原来 AI 不仅能背课文，还能写代码、编故事、解数学题。这些聪明表现的背后，得益于大语言模型（LLM）的两个关键训练阶段：<b>预训练</b>（Pretraining）和<b>后训练</b>（Post-training）。</p><p data-pid=\"BCtP8fww\">预训练阶段通过海量文本数据（通常达到 TB 级别）的自我监督学习，使模型掌握基本的语言规律和世界知识。但仅有预训练的LLM，就好像刚学会六脉神剑的段誉，一身内功，但不会施展。这时，我们就需要通过「后训练」来给模型能力进行「塑型」—— 通过特定方法让模型在医疗诊断、法律咨询、编程开发等专业领域大显身手，同时学会遵守伦理规范、避免信口开河。正是这些「精装修」步骤，把原始的语言模型变成了我们日常使用的智能助手。</p><p data-pid=\"5llMe1IB\">后训练技术的核心价值体现在三个维度：</p><ul><li data-pid=\"_IFxM8pl\"><b>知识精炼</b>：修正预训练阶段的知识偏差与事实错误</li><li data-pid=\"EXCE8jIZ\"><b>能力对齐</b>：使模型输出符合人类价值观和任务需求</li><li data-pid=\"O7vkw_rk\"><b>推理增强</b>：赋予模型多步推理、逻辑验证等高级认知能力</li></ul><p data-pid=\"MbCQcFOv\">在后面的讲述中，我们沿用上述原论文给出的分类视角（taxonomy），从「微调」、「强化学习」、「测试时拓展」三个类别去认识各种后训练技术。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-cf1c8733ab5796262b481847a0816b5c_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"726\" data-rawheight=\"690\" data-original-token=\"v2-cf1c8733ab5796262b481847a0816b5c\" class=\"origin_image zh-lightbox-thumb\" width=\"726\" data-original=\"https://pic3.zhimg.com/v2-cf1c8733ab5796262b481847a0816b5c_r.jpg\"/></figure><h2>一、微调技术：模型的定向进化</h2><h3>1.1 全参数微调</h3><p data-pid=\"3I2pdrzx\">全参数微调（Full Fine-tuning）是指在预训练模型的基础上，使用下游任务的数据集，更新模型中的<b>所有参数</b>，以使模型适应特定任务。这种方法在早期的深度学习中非常常见，但随着模型规模的增大，其弊端也逐渐显现。</p><table data-draft-node=\"block\" data-draft-type=\"table\" data-size=\"normal\" data-row-style=\"normal\"><tbody><tr><th>挑战维度</th><th>具体表现</th><th>解决方案</th></tr><tr><td>计算资源</td><td>GPU 内存占用高，训练时间长</td><td>参数高效微调 (PEFT)</td></tr><tr><td>灾难性遗忘</td><td>新任务覆盖旧知识</td><td>知识蒸馏 + 持续学习</td></tr><tr><td>数据依赖</td><td>需要大量标注数据</td><td>提示学习 + 数据增强</td></tr></tbody></table><h3>1.2 参数高效微调 (PEFT)</h3><p data-pid=\"lXlYwRmt\">参数高效微调（Parameter-Efficient Fine-Tuning，PEFT）是一系列旨在以较少的计算资源和数据量，实现与全参数微调相近性能的技术。这类方法通常<b>冻结</b>预训练模型的大部分参数，只训练<b>少量额外的参数</b>。</p><h3>1.2.1 LoRA 系列技术</h3><p data-pid=\"eUu9a9-i\"><b>低秩适配（LoRA）</b> 的核心思想是冻结原始参数，通过低秩分解引入可训练参数。</p><p data-pid=\"uDtEFRzD\"><b>数学原理</b>：</p><p data-pid=\"cqiZg5HG\">LoRA 假设预训练模型的参数矩阵的更新可以表示为一个低秩矩阵。具体来说，对于一个预训练好的权重矩阵<img src=\"https://www.zhihu.com/equation?tex=W_0+%5Cin+%5Cmathbb%7BR%7D%5E%7Bd+%5Ctimes+k%7D%5C\" alt=\"W_0 \\in \\mathbb{R}^{d \\times k}\\\" eeimg=\"1\"/>，LoRA 引入两个低秩矩阵<img src=\"https://www.zhihu.com/equation?tex=A+%5Cin+%5Cmathbb%7BR%7D%5E%7Bd+%5Ctimes+r%7D%5C\" alt=\"A \\in \\mathbb{R}^{d \\times r}\\\" eeimg=\"1\"/>和<img src=\"https://www.zhihu.com/equation?tex=B+%5Cin+%5Cmathbb%7BR%7D%5E%7Br+%5Ctimes+k%7D%5C\" alt=\"B \\in \\mathbb{R}^{r \\times k}\\\" eeimg=\"1\"/>，其中<img src=\"https://www.zhihu.com/equation?tex=r+%5Cll+%5Cmin%28d%2C+k%29%5C\" alt=\"r \\ll \\min(d, k)\\\" eeimg=\"1\"/>是秩。在微调过程中，只优化<img src=\"https://www.zhihu.com/equation?tex=A%5C\" alt=\"A\\\" eeimg=\"1\"/>和<img src=\"https://www.zhihu.com/equation?tex=B%5C\" alt=\"B\\\" eeimg=\"1\"/>，而<img src=\"https://www.zhihu.com/equation?tex=W_0%5C\" alt=\"W_0\\\" eeimg=\"1\"/>保持不变。更新后的权重矩阵为：</p><p data-pid=\"CXbOv7Tk\"><img src=\"https://www.zhihu.com/equation?tex=W+%3D+W_0+%2B+BA%5C\" alt=\"W = W_0 + BA\\\" eeimg=\"1\"/></p><p data-pid=\"q5HdFmWk\">由于<img src=\"https://www.zhihu.com/equation?tex=r%5C\" alt=\"r\\\" eeimg=\"1\"/>远小于<img src=\"https://www.zhihu.com/equation?tex=d%5C\" alt=\"d\\\" eeimg=\"1\"/>和<img src=\"https://www.zhihu.com/equation?tex=k%5C\" alt=\"k\\\" eeimg=\"1\"/>，因此 LoRA 只需要训练很少的参数，就可以达到与全参数微调相近的性能。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-36a022d024c1348e5822f4416a366855_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"914\" data-rawheight=\"1328\" data-original-token=\"v2-36a022d024c1348e5822f4416a366855\" class=\"origin_image zh-lightbox-thumb\" width=\"914\" data-original=\"https://pic4.zhimg.com/v2-36a022d024c1348e5822f4416a366855_r.jpg\"/></figure><p data-pid=\"1Qid6k4M\"><b>伪代码示例</b>：</p><div class=\"highlight\"><pre><code class=\"language-python3\"><span class=\"kn\">import</span> <span class=\"nn\">torch</span>\n<span class=\"kn\">import</span> <span class=\"nn\">torch.nn</span> <span class=\"k\">as</span> <span class=\"nn\">nn</span>\n\n<span class=\"k\">class</span> <span class=\"nc\">LoRALayer</span><span class=\"p\">(</span><span class=\"n\">nn</span><span class=\"o\">.</span><span class=\"n\">Module</span><span class=\"p\">):</span>\n\t<span class=\"k\">def</span> <span class=\"nf\">__init__</span><span class=\"p\">(</span><span class=\"bp\">self</span><span class=\"p\">,</span> <span class=\"n\">in_dim</span><span class=\"p\">,</span> <span class=\"n\">out_dim</span><span class=\"p\">,</span> <span class=\"n\">rank</span><span class=\"p\">):</span>\n\t\t<span class=\"nb\">super</span><span class=\"p\">()</span><span class=\"o\">.</span><span class=\"fm\">__init__</span><span class=\"p\">()</span>\n\t\t<span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">A</span> <span class=\"o\">=</span> <span class=\"n\">nn</span><span class=\"o\">.</span><span class=\"n\">Parameter</span><span class=\"p\">(</span><span class=\"n\">torch</span><span class=\"o\">.</span><span class=\"n\">randn</span><span class=\"p\">(</span><span class=\"n\">in_dim</span><span class=\"p\">,</span> <span class=\"n\">rank</span><span class=\"p\">))</span>\n\t\t<span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">B</span> <span class=\"o\">=</span> <span class=\"n\">nn</span><span class=\"o\">.</span><span class=\"n\">Parameter</span><span class=\"p\">(</span><span class=\"n\">torch</span><span class=\"o\">.</span><span class=\"n\">zeros</span><span class=\"p\">(</span><span class=\"n\">rank</span><span class=\"p\">,</span> <span class=\"n\">out_dim</span><span class=\"p\">))</span>\n\t\t<span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">rank</span> <span class=\"o\">=</span> <span class=\"n\">rank</span> <span class=\"c1\">#  秩的大小</span>\n\t<span class=\"k\">def</span> <span class=\"nf\">forward</span><span class=\"p\">(</span><span class=\"bp\">self</span><span class=\"p\">,</span> <span class=\"n\">x</span><span class=\"p\">):</span>\n\t\t<span class=\"k\">return</span> <span class=\"n\">x</span> <span class=\"o\">@</span> <span class=\"p\">(</span><span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">A</span> <span class=\"o\">@</span> <span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">B</span><span class=\"p\">)</span>  <span class=\"c1\"># 低秩矩阵乘积</span></code></pre></div><p data-pid=\"W1vhrSTl\"><b>关键技术演进</b>：</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-fab8602c3eb6950bf7c5ce68caa64e5a_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1340\" data-rawheight=\"862\" data-original-token=\"v2-fab8602c3eb6950bf7c5ce68caa64e5a\" class=\"origin_image zh-lightbox-thumb\" width=\"1340\" data-original=\"https://pic1.zhimg.com/v2-fab8602c3eb6950bf7c5ce68caa64e5a_r.jpg\"/></figure><ul><li data-pid=\"cSC-zVN_\"><b>AdaLoRA</b>：</li><ul><li data-pid=\"G_Z6VL6l\"><b>核心思想</b>：AdaLoRA（Adaptive LoRA）旨在<b>动态调整各层的秩分配</b>。</li><li data-pid=\"VSCFAsoR\"><b>实现方式</b>：AdaLoRA 通过一些指标（例如，梯度范数）来评估不同层的重要性，并根据重要性动态地调整 LoRA 的秩。更重要的层分配更高的秩，从而获得更好的性能。</li></ul><li data-pid=\"593xV9kN\"><b>QLoRA</b>：</li><ul><li data-pid=\"HP5neoy5\"><b>核心思想</b>：QLoRA（Quantized LoRA）将<b>4-bit 量化</b>与 LoRA 相结合，以进一步降低显存占用。</li><li data-pid=\"wH2xOK9J\"><b>实现方式</b>：QLoRA 首先将预训练模型的权重<b>量化</b>为 4-bit 精度，然后在此基础上应用 LoRA。由于 4-bit 量化可以显著降低显存占用，因此 QLoRA 可以在有限的 GPU 资源上微调更大的模型。</li><li data-pid=\"NHUTTehb\"><b>显存节省</b>：QLoRA 可以节省高达 70% 的显存。</li></ul><li data-pid=\"V2QpuvJT\"><b>Delta-LoRA</b>：</li><ul><li data-pid=\"6WNXE3nV\"><b>核心思想</b>：Delta-LoRA 引入<b>参数更新量的动量机制</b>。</li><li data-pid=\"zKBnyvK6\"><b>实现方式</b>：Delta-LoRA 在更新 LoRA 参数时，考虑之前的更新方向和幅度，从而更稳定地进行微调。</li></ul></ul><h3>1.2.2 提示微调技术</h3><p data-pid=\"tfEUjD5q\"><b>提示微调（Prompt Tuning）</b> 是一种通过<b>设计合适的提示（Prompt）</b> 来引导预训练模型完成下游任务的技术。与全参数微调和 LoRA 不同，提示微调通常<b>不直接修改预训练模型的参数</b>（注意不是完全不修改参数），而是通过优化提示相关的向量来调整模型的行为。</p><p data-pid=\"aPbPvXvN\"><b>核心思想</b>：</p><ul><li data-pid=\"xLUicmIU\"><b>人工设计到可学习</b>：提示工程（Prompt Engineering）经历了从人工设计提示到可学习提示的演进过程。</li><li data-pid=\"DYEhTNnw\"><b>利用预训练知识</b>：通过优化提示，引导模型利用预训练知识，从而减少对标注数据的依赖。</li></ul><p data-pid=\"lbu_UFMo\"><b>数学原理</b>：</p><p data-pid=\"LcUhh3tF\">公式</p><p data-pid=\"oOCjz5Vd\"><img src=\"https://www.zhihu.com/equation?tex=h_%7Bprompt%7D+%3D+%5BP_1%3BP_2%3B%E2%80%A6%3BP_k%5D+%5Cquad+P_i+%5Cin+%5Cmathbb%7BR%7D%5E%7Bd%7D%5C\" alt=\"h_{prompt} = [P_1;P_2;…;P_k] \\quad P_i \\in \\mathbb{R}^{d}\\\" eeimg=\"1\"/></p><p data-pid=\"VTXhcu87\">描述了<b>可学习的提示向量</b>。其中，<img src=\"https://www.zhihu.com/equation?tex=h_%7Bprompt%7D%5C\" alt=\"h_{prompt}\\\" eeimg=\"1\"/>表示提示向量，<img src=\"https://www.zhihu.com/equation?tex=P_i%5C\" alt=\"P_i\\\" eeimg=\"1\"/>表示第<img src=\"https://www.zhihu.com/equation?tex=i%5C\" alt=\"i\\\" eeimg=\"1\"/>个可训练的提示向量，<img src=\"https://www.zhihu.com/equation?tex=k%5C\" alt=\"k\\\" eeimg=\"1\"/>表示提示的长度，<img src=\"https://www.zhihu.com/equation?tex=d%5C\" alt=\"d\\\" eeimg=\"1\"/>表示提示向量的维度。</p><p data-pid=\"1TOhAHMD\"><b>位置选择策略</b>：</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-cd3455b76c8120f745d84f2d87147385_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1150\" data-rawheight=\"744\" data-original-token=\"v2-cd3455b76c8120f745d84f2d87147385\" class=\"origin_image zh-lightbox-thumb\" width=\"1150\" data-original=\"https://pic4.zhimg.com/v2-cd3455b76c8120f745d84f2d87147385_r.jpg\"/></figure><ul><li data-pid=\"3nkTjEMF\"><b>Prefix-Tuning</b>（粉色）：</li><ul><li data-pid=\"x05jQ4Cx\"><b>核心思想</b>：只在<b>每层</b>的<b>开头</b>插入可训练的提示向量。</li><li data-pid=\"71I-nIp3\"><b>数学形式</b>：<img src=\"https://www.zhihu.com/equation?tex=h%5E%7Bl%7D+%3D+%5Ctext%7BTransformer%7D%28%5B%5Ctext%7BPrefix%7D%5El%3B+x%5E%7Bl%7D%5D%29%5C\" alt=\"h^{l} = \\text{Transformer}([\\text{Prefix}^l; x^{l}])\\\" eeimg=\"1\"/></li><li data-pid=\"LbIbuUcf\"><b>参数量</b>：每层新增参数<img src=\"https://www.zhihu.com/equation?tex=d+%5Ctimes+r%5C\" alt=\"d \\times r\\\" eeimg=\"1\"/>（<img src=\"https://www.zhihu.com/equation?tex=d%5C\" alt=\"d\\\" eeimg=\"1\"/>为维度，<img src=\"https://www.zhihu.com/equation?tex=r%5C\" alt=\"r\\\" eeimg=\"1\"/>为前缀长度）</li><li data-pid=\"pU0T_8EW\"><b>优点</b>：Prefix-Tuning 可以有效地影响模型的每一层，从而更好地调整模型的行为。</li><li data-pid=\"yLAwwe-Y\"><b>缺点</b>：Prefix-Tuning 需要插入大量的提示向量，可能会增加计算成本。</li></ul><li data-pid=\"6tSMHc7l\"><b>P-Tuning v2</b>（绿色）：</li><ul><li data-pid=\"Q9H6T5d1\"><b>核心思想</b>：<b>分层插入</b>位置<b>可学习</b>。</li><li data-pid=\"4hSSn3L7\"><b>实现方式</b>：P-Tuning v2 首先将提示向量插入到不同的层中，然后通过训练来确定每个提示向量的最佳位置。</li><li data-pid=\"Ij2Cd8nJ\"><b>数学形式</b>：<img src=\"https://www.zhihu.com/equation?tex=%5Ctext%7BPosition%7D%5El+%3D+%5Ctext%7Bsoftmax%7D%28W_p%5El+%5Ccdot+x%5El%29%5C\" alt=\"\\text{Position}^l = \\text{softmax}(W_p^l \\cdot x^l)\\\" eeimg=\"1\"/></li><li data-pid=\"YBerba3b\"><b>优点</b>：P-Tuning v2 可以更灵活地调整提示向量的位置，从而更好地适应不同的任务。</li></ul><li data-pid=\"weJXdC0z\"><b>Prompt-Tuning</b>（蓝色）：</li><ul><li data-pid=\"iZl_-DLQ\"><b>核心思想</b>：仅在<b>输入层</b>添加可训练提示词。</li><li data-pid=\"IUMjaEm4\"><b>参数量</b>：仅需<img src=\"https://www.zhihu.com/equation?tex=k+%5Ctimes+d%5C\" alt=\"k \\times d\\\" eeimg=\"1\"/>（<img src=\"https://www.zhihu.com/equation?tex=k%5C\" alt=\"k\\\" eeimg=\"1\"/>为提示词数量）</li><li data-pid=\"VouPBzxw\"><b>数学形式</b>：<img src=\"https://www.zhihu.com/equation?tex=h+%3D+%5Ctext%7BLM%7D%28%5B%5Ctext%7BPrompt%7D%3B+x%5D%29%5C\" alt=\"h = \\text{LM}([\\text{Prompt}; x])\\\" eeimg=\"1\"/></li><li data-pid=\"mwgsfDhf\"><b>优点</b>：Prompt-Tuning 的实现简单，计算成本低。</li><li data-pid=\"YxOhbiYx\"><b>缺点</b>：Prompt-Tuning 的效果可能不如 Prefix-Tuning 和 P-Tuning v2。</li></ul></ul><p data-pid=\"zGDapIkQ\"><b>关键差异对比表：</b></p><table data-draft-node=\"block\" data-draft-type=\"table\" data-size=\"normal\" data-row-style=\"normal\"><tbody><tr><th>维度</th><th>Prefix-Tuning</th><th>P-Tuning v2</th><th>Prompt-Tuning</th></tr><tr><td>插入位置</td><td>每层输入序列头部</td><td>每层可学习位置</td><td>仅输入层头部</td></tr><tr><td>可训练参数占比</td><td>0.5%-2%</td><td>0.3%-1.5%</td><td>0.01%-0.1%</td></tr><tr><td>典型应用场景</td><td>复杂逻辑推理</td><td>多任务适配</td><td>轻量化快速部署</td></tr><tr><td>显存占用</td><td>高（需存储各层前缀）</td><td>中</td><td>极低</td></tr><tr><td>训练速度</td><td>慢（需反向传播至各层）</td><td>中等</td><td>快（仅输入层梯度）</td></tr></tbody></table><p data-pid=\"TAMiZL_N\"><b>技术演进趋势：</b></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-6b046f9b3fb3e0cd868514e7991e280e_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1194\" data-rawheight=\"98\" data-original-token=\"v2-6b046f9b3fb3e0cd868514e7991e280e\" class=\"origin_image zh-lightbox-thumb\" width=\"1194\" data-original=\"https://pic3.zhimg.com/v2-6b046f9b3fb3e0cd868514e7991e280e_r.jpg\"/></figure><p data-pid=\"MFKN1POx\"><b>动态混合提示</b>作为最新发展方向，允许模型自主决定：</p><ul><li data-pid=\"xFd5ORX9\">是否需要在某层插入提示</li><li data-pid=\"R7-Xbt_M\">最佳提示插入位置</li><li data-pid=\"GUixNZSF\">不同提示向量的权重分配</li></ul><h3>1.3 领域自适应微调</h3><p data-pid=\"AKLtz2Ev\"><b>领域自适应微调（Domain Adaptive Fine-Tuning）</b> 是指在特定领域的数据上对预训练模型进行微调，以使其更好地适应该领域的任务。这种方法在医疗、法律等专业领域尤其重要，因为这些领域的数据具有独特的特点和术语。</p><p data-pid=\"lWGAgZGN\"><b>案例：医疗问答系统的微调策略</b></p><p data-pid=\"FdTOp061\">以一个医疗问答系统的微调策略为例：</p><ul><li data-pid=\"8Jddh8q5\"><b>数据构造</b>：</li><ul><li data-pid=\"PFBzuOgh\"><b>混合通用指令数据 (20%) + 专业文献问答 (80%)</b>：</li><ul><li data-pid=\"72oAys_J\"><b>通用指令数据</b>：例如，&#34;请解释什么是高血压？&#34;、&#34;如何预防感冒？&#34; 等。这些数据可以帮助模型保持通用的语言理解能力。</li><li data-pid=\"3TDnCgGy\"><b>专业文献问答</b>：从医学文献中提取问题和答案，例如，&#34;《柳叶刀》杂志最近发表了关于 COVID-19 疫苗有效性的研究，请总结其主要发现。&#34;。这些数据可以帮助模型学习专业的医学知识。</li><li data-pid=\"fmvztHGv\"><b>比例</b>：混合比例的选择需要根据实际情况进行调整。一般来说，如果领域数据比较稀缺，可以适当增加通用指令数据的比例。</li></ul></ul><li data-pid=\"RFGGTOMD\"><b>分层微调</b>：</li><ul><li data-pid=\"b75OU8zY\"><b>计算效率</b>：只微调部分层可以显著降低计算成本。</li><li data-pid=\"zZKQcuCY\"><b>避免灾难性遗忘</b>：冻结大部分层可以保留预训练模型的通用知识，从而避免灾难性遗忘。</li><li data-pid=\"aJPAyfGC\"><b>微调后 10% 的原因</b>：这是一个经验性的选择，通常认为后几层更接近特定任务，因此微调这些层可以更好地适应领域数据。</li><li data-pid=\"7D5mOSby\"><b>需要实验验证</b>：最佳的微调层数需要根据实际情况进行实验验证。</li></ul><li data-pid=\"D0RQMLl-\"><b>评估指标</b>：</li><ul><li data-pid=\"1m8lz3jx\"><b>FActScore（事实一致性评分）&gt; 0.85</b>：</li><ul><li data-pid=\"ahg5oASp\"><b>FActScore</b>：一种用于评估模型生成答案的事实一致性的指标。FActScore 越高，表示模型生成的答案与事实越一致。</li><li data-pid=\"eZFUcPHN\"><b>重要性</b>：在医疗领域，事实一致性至关重要。如果模型生成错误的医疗信息，可能会对患者的健康造成严重影响。</li></ul></ul></ul><h2>二、强化学习：从对齐到推理</h2><h3>2.1 LLM推理技术全景图</h3><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-b4f88aeb2659f158f0313a8213693bb9_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1394\" data-rawheight=\"678\" data-original-token=\"v2-b4f88aeb2659f158f0313a8213693bb9\" class=\"origin_image zh-lightbox-thumb\" width=\"1394\" data-original=\"https://pic2.zhimg.com/v2-b4f88aeb2659f158f0313a8213693bb9_r.jpg\"/></figure><h3>2.2 奖励建模</h3><p data-pid=\"lBixv0Ak\"><b>奖励建模（Reward Modeling）</b> 是很多 RL 方法的关键步骤之一。它的目标是根据人类的偏好数据，训练一个能够预测人类对模型输出的偏好程度的模型。</p><ul><li data-pid=\"PDaYKqVY\"><b>偏好数据</b>：训练奖励模型需要大量的偏好数据。这些数据通常由人工标注，例如，让人类对不同的模型输出进行排序或打分。</li><li data-pid=\"C0-FkTuh\"><b>奖励模型的选择</b>：奖励模型可以是任何一种机器学习模型，例如，线性模型、神经网络等。常见的选择是使用与预训练语言模型结构相似的模型。</li></ul><p data-pid=\"oq5OzIgt\"><b>Bradley-Terry 模型</b>是一种常用的奖励建模方法：假设对于给定的输入<img src=\"https://www.zhihu.com/equation?tex=x%5C\" alt=\"x\\\" eeimg=\"1\"/>，人类更喜欢输出<img src=\"https://www.zhihu.com/equation?tex=y_i%5C\" alt=\"y_i\\\" eeimg=\"1\"/>而不是<img src=\"https://www.zhihu.com/equation?tex=y_j%5C\" alt=\"y_j\\\" eeimg=\"1\"/>的概率可以表示为：</p><p data-pid=\"ElcBKZL2\"><img src=\"https://www.zhihu.com/equation?tex=P%28y_i+%5Csucc+y_j%7Cx%29+%3D+%5Cfrac%7B%5Cexp%28R%28x%2Cy_i%29%29%7D%7B%5Cexp%28R%28x%2Cy_i%29%29+%2B+%5Cexp%28R%28x%2Cy_j%29%29%7D%5C\" alt=\"P(y_i \\succ y_j|x) = \\frac{\\exp(R(x,y_i))}{\\exp(R(x,y_i)) + \\exp(R(x,y_j))}\\\" eeimg=\"1\"/></p><p data-pid=\"UxQihDjT\">其中，<img src=\"https://www.zhihu.com/equation?tex=R%28x%2C+y%29%5C\" alt=\"R(x, y)\\\" eeimg=\"1\"/>是奖励模型给出的奖励值，表示模型认为输出<img src=\"https://www.zhihu.com/equation?tex=y%5C\" alt=\"y\\\" eeimg=\"1\"/>对于输入<img src=\"https://www.zhihu.com/equation?tex=x%5C\" alt=\"x\\\" eeimg=\"1\"/>的好坏程度。<img src=\"https://www.zhihu.com/equation?tex=%5Csucc%5C\" alt=\"\\succ\\\" eeimg=\"1\"/>表示偏好关系，即<img src=\"https://www.zhihu.com/equation?tex=y_i+%5Csucc+y_j%5C\" alt=\"y_i \\succ y_j\\\" eeimg=\"1\"/>表示人类更喜欢<img src=\"https://www.zhihu.com/equation?tex=y_i%5C\" alt=\"y_i\\\" eeimg=\"1\"/>而不是<img src=\"https://www.zhihu.com/equation?tex=y_j%5C\" alt=\"y_j\\\" eeimg=\"1\"/>。</p><p data-pid=\"nFyhzNDk\"><b>训练目标</b></p><p data-pid=\"v1P0HwLZ\">训练奖励模型的<b>目标</b>是<b>最小化负对数似然</b>：</p><p data-pid=\"378yH1i8\"><img src=\"https://www.zhihu.com/equation?tex=%5Cmathcal%7BL%7D_%7BBT%7D+%3D+-%5Csum+%5Clog+P%28y_i+%5Csucc+y_j%7Cx%29%5C\" alt=\"\\mathcal{L}_{BT} = -\\sum \\log P(y_i \\succ y_j|x)\\\" eeimg=\"1\"/></p><p data-pid=\"ySRtZB3Q\">这个公式表示，我们希望奖励模型给出的奖励值能够尽可能地符合人类的偏好。也就是说，如果人类更喜欢<img src=\"https://www.zhihu.com/equation?tex=y_i%5C\" alt=\"y_i\\\" eeimg=\"1\"/>而不是<img src=\"https://www.zhihu.com/equation?tex=y_j%5C\" alt=\"y_j\\\" eeimg=\"1\"/>，那么我们希望<img src=\"https://www.zhihu.com/equation?tex=R%28x%2C+y_i%29%5C\" alt=\"R(x, y_i)\\\" eeimg=\"1\"/>尽可能地大于<img src=\"https://www.zhihu.com/equation?tex=R%28x%2C+y_j%29%5C\" alt=\"R(x, y_j)\\\" eeimg=\"1\"/>。</p><h3>2.3 主流优化算法对比</h3><table data-draft-node=\"block\" data-draft-type=\"table\" data-size=\"normal\" data-row-style=\"normal\"><tbody><tr><th>方法</th><th>更新机制</th><th>优势</th><th>局限</th></tr><tr><td>PPO</td><td>带约束的策略梯度</td><td>训练稳定</td><td>需要价值函数估计</td></tr><tr><td>DPO</td><td>直接偏好优化</td><td>无需奖励模型</td><td>依赖高质量偏好数据</td></tr><tr><td>GRPO</td><td>组相对策略优化</td><td>降低方差</td><td>需要并行采样多个响应</td></tr></tbody></table><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-7afd94ac417b0429eee717697839ed91_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"676\" data-rawheight=\"666\" data-original-token=\"v2-7afd94ac417b0429eee717697839ed91\" class=\"origin_image zh-lightbox-thumb\" width=\"676\" data-original=\"https://pic2.zhimg.com/v2-7afd94ac417b0429eee717697839ed91_r.jpg\"/></figure><h3>2.4 过程奖励 Vs 结果奖励</h3><p data-pid=\"a4TAEyB8\">在强化学习中，<b>奖励函数</b>的设计至关重要。奖励函数定义了模型在不同状态下应该获得的奖励，从而引导模型学习到期望的行为。</p><p data-pid=\"VETMVtFc\">根据奖励的来源，可以将奖励分为<b>过程奖励</b>和<b>结果奖励</b>。</p><p data-pid=\"jaqBTwTp\"><b>过程奖励（Process Reward）</b></p><p data-pid=\"HWM0_GRM\">过程奖励是指在<b>每个步骤</b>中，根据模型的<b>行为</b>给出的奖励。过程奖励可以提供更<b>密集</b>的反馈信号，帮助模型更快地学习。但缺点是设计比较<b>困难</b>，需要对任务有深入的理解。</p><p data-pid=\"b8b8zSxM\">举个例子：</p><div class=\"highlight\"><pre><code class=\"language-python3\"><span class=\"k\">def</span> <span class=\"nf\">calculate_step_reward</span><span class=\"p\">(</span><span class=\"n\">response</span><span class=\"p\">):</span>\n\t<span class=\"c1\"># 1. 语法正确性检查</span>\n\t<span class=\"n\">syntax</span> <span class=\"o\">=</span> <span class=\"n\">check_syntax</span><span class=\"p\">(</span><span class=\"n\">response</span><span class=\"p\">)</span>\n\t<span class=\"c1\"># 2. 逻辑连贯性评估</span>\n\t<span class=\"n\">coherence</span> <span class=\"o\">=</span> <span class=\"n\">model</span><span class=\"o\">.</span><span class=\"n\">predict_coherence</span><span class=\"p\">(</span><span class=\"n\">response</span><span class=\"p\">)</span>\n\t<span class=\"c1\"># 3. 事实一致性验证</span>\n\t<span class=\"n\">fact_check</span> <span class=\"o\">=</span> <span class=\"n\">retrieve_evidence</span><span class=\"p\">(</span><span class=\"n\">response</span><span class=\"p\">)</span>\n\t<span class=\"k\">return</span> <span class=\"mf\">0.3</span><span class=\"o\">*</span><span class=\"n\">syntax</span> <span class=\"o\">+</span> <span class=\"mf\">0.5</span><span class=\"o\">*</span><span class=\"n\">coherence</span> <span class=\"o\">+</span> <span class=\"mf\">0.2</span><span class=\"o\">*</span><span class=\"n\">fact_check</span></code></pre></div><p data-pid=\"QiApRqTq\">在这个例子中，奖励函数考虑了三个方面：</p><ul><li data-pid=\"U6ocCx42\"><b>语法正确性检查</b>：检查模型生成的文本是否符合语法规则。例如，可以使用语法分析器来判断文本是否存在语法错误。</li><li data-pid=\"MIxseTYX\"><b>逻辑连贯性评估</b>：评估模型生成的文本是否逻辑连贯。例如，可以使用语言模型来预测文本的连贯性。</li><li data-pid=\"wTsyLMuW\"><b>事实一致性验证</b>：验证模型生成的文本是否与事实相符。例如，可以使用知识库来检索相关信息，然后判断模型生成的文本是否与知识库中的信息一致。</li></ul><p data-pid=\"2oGe_wvP\">对这三个方面进行加权求和，得到最终的奖励值。<b>权重</b>的选择需要根据实际情况进行调整。一般来说，更重要的方面应该分配更高的权重。</p><p data-pid=\"IO0M_iuw\"><b>结果奖励（Outcome Reward）</b></p><p data-pid=\"ElbJ3LAQ\">结果奖励是指在<b>任务完成后</b>，根据模型的<b>最终结果</b>给出的奖励。结果奖励的设计比较<b>简单</b>，只需要关注最终结果即可。但可能提供较<b>稀疏</b>的反馈信号，导致模型学习困难。典型应用场景包括：</p><ul><li data-pid=\"FSSQOAfy\"><b>数学问题</b>：最终答案正确性。例如，如果模型生成的答案与正确答案一致，则给出正奖励；否则，给出负奖励。</li><li data-pid=\"40Z5o4ww\"><b>代码生成</b>：通过单元测试的比例。例如，如果模型生成的代码能够通过所有的单元测试，则给出正奖励；否则，给出负奖励。</li><li data-pid=\"SF--V4J7\"><b>对话系统</b>：用户满意度评分。例如，如果用户对模型的回复感到满意，则给出正奖励；否则，给出负奖励。</li></ul><h3>2.5 强化学习的推理增强实践</h3><p data-pid=\"MD6eXeSf\">举一个 <b>思维树（ToT）算法框架</b> 的例子：</p><ul><li data-pid=\"anESb0ov\">思维生成：基于当前状态生成候选思考</li><li data-pid=\"RTR-ggF-\">状态评估：使用价值函数评估每个候选</li><li data-pid=\"qIeKFpB6\">广度优先搜索：保留 top-k 候选继续扩展</li><li data-pid=\"vvNtiK_x\">回溯更新：反向传播累积奖励</li></ul><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-b40e19c9b5b2477204b7e549a89ea6a1_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1092\" data-rawheight=\"1224\" data-original-token=\"v2-b40e19c9b5b2477204b7e549a89ea6a1\" class=\"origin_image zh-lightbox-thumb\" width=\"1092\" data-original=\"https://picx.zhimg.com/v2-b40e19c9b5b2477204b7e549a89ea6a1_r.jpg\"/></figure><h2>三、测试时扩展：推理即搜索</h2><h3>3.1 主流推理增强技术</h3><table data-draft-node=\"block\" data-draft-type=\"table\" data-size=\"normal\" data-row-style=\"normal\"><tbody><tr><th>方法</th><th>核心思想</th><th>适用场景</th></tr><tr><td>链式思考（CoT）</td><td>显式生成推理步骤</td><td>数学解题、逻辑推理</td></tr><tr><td>自洽性解码（SC）</td><td>多路径投票机制</td><td>开放域问答</td></tr><tr><td>思维树（ToT）</td><td>树形结构空间搜索</td><td>复杂规划问题</td></tr><tr><td>蒙特卡洛树搜索（MCTS）</td><td>模拟 - 评估 - 回溯机制</td><td>游戏类、策略性问题</td></tr></tbody></table><h3>3.2 计算最优扩展策略</h3><p data-pid=\"ac6zeq7C\">不同的推理增强技术适用于不同的场景。如何根据具体的问题选择最优的推理增强技术呢？这就是 <b>计算最优扩展策略</b> 要解决的问题。</p><p data-pid=\"4mwYa46S\"><b>动态计算分配算法</b> 的核心思想是<b>根据问题的难度动态地分配计算资源</b>。对于简单的问题，可以使用简单的推理方法；对于复杂的问题，可以使用更复杂的推理方法。</p><p data-pid=\"rsFFtL7t\"><b>伪代码示例</b>：</p><div class=\"highlight\"><pre><code class=\"language-python3\"><span class=\"k\">def</span> <span class=\"nf\">dynamic_compute_allocation</span><span class=\"p\">(</span><span class=\"n\">query</span><span class=\"p\">):</span>\n\t<span class=\"n\">difficulty</span> <span class=\"o\">=</span> <span class=\"n\">estimate_difficulty</span><span class=\"p\">(</span><span class=\"n\">query</span><span class=\"p\">)</span>\n\t<span class=\"k\">if</span> <span class=\"n\">difficulty</span> <span class=\"o\">&lt;</span> <span class=\"mf\">0.3</span><span class=\"p\">:</span>\n\t\t<span class=\"k\">return</span> <span class=\"n\">greedy_decode</span><span class=\"p\">()</span>\n\t<span class=\"k\">elif</span> <span class=\"mf\">0.3</span> <span class=\"o\">&lt;=</span> <span class=\"n\">difficulty</span> <span class=\"o\">&lt;</span> <span class=\"mf\">0.7</span><span class=\"p\">:</span>\n\t\t<span class=\"k\">return</span> <span class=\"n\">beam_search</span><span class=\"p\">(</span><span class=\"n\">width</span><span class=\"o\">=</span><span class=\"mi\">3</span><span class=\"p\">)</span>\n\t<span class=\"k\">else</span><span class=\"p\">:</span>\n\t\t<span class=\"k\">return</span> <span class=\"n\">monte_carlo_tree_search</span><span class=\"p\">(</span><span class=\"n\">depth</span><span class=\"o\">=</span><span class=\"mi\">5</span><span class=\"p\">)</span></code></pre></div><p data-pid=\"x_IjGsOc\">在这个例子中，<code>estimate_difficulty</code> 函数用于评估问题的难度。根据问题的难度，选择不同的推理方法。</p><p data-pid=\"OW755e9n\">如何评估问题的难度呢？一个思路是<b>根据问题的特征来预测</b>。比如：</p><ul><li data-pid=\"NiDVpZ9L\"><b>问题长度与复杂度</b>：问题越长，复杂度越高，难度越大。</li><li data-pid=\"r4IExk36\"><b>领域专业性指标</b>：问题涉及的领域越专业，难度越大。例如，医学问题的难度通常高于一般问题。</li><li data-pid=\"-VNfjSqq\"><b>历史正确率统计</b>：如果模型在过去的历史数据中对类似问题的正确率较低，则说明该问题的难度较大。</li><li data-pid=\"Mb2s3WMu\"><b>语义模糊性评分</b>：如果问题存在语义模糊性，则难度较大。例如，&#34;苹果公司最近发布了什么？&#34; 这个问题存在语义模糊性，因为 &#34;发布&#34; 可以指发布新产品、发布财报等。</li></ul><h3>3.3 验证器增强推理</h3><p data-pid=\"aOAxomQ4\"><b>验证器增强推理</b> 是一种通过使用验证器（Verifier）来检查模型生成的答案的正确性，从而提高推理准确率的技术。</p><p data-pid=\"UMjeC2g6\">可以构建一个多层验证体系，对模型生成的答案进行多方面的验证。</p><ul><li data-pid=\"ajar-TlW\"><b>语法验证器</b>：检查代码/公式语法。例如，可以使用语法分析器来判断代码或公式是否存在语法错误。</li><li data-pid=\"tJDnL-pd\"><b>逻辑验证器</b>：命题逻辑一致性检查。例如，可以使用逻辑推理引擎来判断命题是否符合逻辑。</li><li data-pid=\"H4liIs6Q\"><b>事实验证器</b>：知识库检索验证。例如，可以使用知识库来检索相关信息，然后判断模型生成的答案是否与知识库中的信息一致。</li><li data-pid=\"R5D3OiY6\"><b>安全验证器</b>：有害内容过滤。例如，可以使用有害内容检测模型来判断模型生成的文本是否包含有害内容。</li></ul><p data-pid=\"duXsRiYJ\">下面这个公式描述了如何将多个验证器的结果组合起来：</p><p data-pid=\"yP4KTLTv\"><img src=\"https://www.zhihu.com/equation?tex=V_%7Btotal%7D+%3D+%5Cprod_%7Bi%3D1%7D%5En+V_i%28s%29+%5Cquad+V_i+%5Cin+%5B0%2C1%5D%5C\" alt=\"V_{total} = \\prod_{i=1}^n V_i(s) \\quad V_i \\in [0,1]\\\" eeimg=\"1\"/></p><p data-pid=\"NdcnlRD7\">其中，<img src=\"https://www.zhihu.com/equation?tex=V_%7Btotal%7D%5C\" alt=\"V_{total}\\\" eeimg=\"1\"/>表示总的验证分数，<img src=\"https://www.zhihu.com/equation?tex=V_i%28s%29%5C\" alt=\"V_i(s)\\\" eeimg=\"1\"/>表示第<img src=\"https://www.zhihu.com/equation?tex=i%5C\" alt=\"i\\\" eeimg=\"1\"/>个验证器对答案<img src=\"https://www.zhihu.com/equation?tex=s%5C\" alt=\"s\\\" eeimg=\"1\"/>的评分，<img src=\"https://www.zhihu.com/equation?tex=n%5C\" alt=\"n\\\" eeimg=\"1\"/>表示验证器的数量。这个公式表示，总的验证分数是所有验证器评分的乘积。每个验证器的评分都在 0 到 1 之间，评分越高，表示答案越可靠。</p><ul><li data-pid=\"sTuIPO85\"><b>乘积的原因</b>：使用乘积的原因是，如果有一个验证器的评分很低，则总的验证分数也会很低，这表示答案的可靠性较低。</li><li data-pid=\"fQmptRHw\"><b>其他组合方式</b>：除了乘积之外，还可以使用其他的组合方式，例如，加权平均。</li></ul><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-70daf2f26529e3b4a4d9b0eeb489bbcc_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1072\" data-rawheight=\"856\" data-original-token=\"v2-70daf2f26529e3b4a4d9b0eeb489bbcc\" class=\"origin_image zh-lightbox-thumb\" width=\"1072\" data-original=\"https://pica.zhimg.com/v2-70daf2f26529e3b4a4d9b0eeb489bbcc_r.jpg\"/></figure><h2>四、挑战与未来方向</h2><h3>4.1 现有技术瓶颈</h3><p data-pid=\"itZU-Izm\"><b>奖励误导（Reward Hacking）</b></p><ul><li data-pid=\"nHRPNzKE\"><b>具体挑战</b>：<b>过度优化表面指标</b>。奖励误导是指模型为了获得更高的奖励，采取了一些不符合人类意图的行为。例如，模型可能会生成一些表面上看起来很好，但实际上毫无意义或有害的输出。</li><li data-pid=\"NUb_D0W0\"><b>示例</b>：</li><ul><li data-pid=\"IyjIsNNu\"><b>社交媒体推荐系统</b>：为了提高用户点击率，模型可能会推荐一些标题耸人听闻、内容低俗的文章，而不是真正有价值的文章。</li><li data-pid=\"IPyoBssJ\"><b>对话系统</b>：为了获得更高的用户满意度评分，模型可能会生成一些讨好用户、但实际上不解决问题的回复。</li></ul><li data-pid=\"ZzsHVIcA\"><b>当前最佳方案</b>：<b>多目标对抗训练</b>。多目标对抗训练是一种通过同时优化多个目标，并引入对抗性损失来防止模型过度优化表面指标的技术。</li><ul><li data-pid=\"ZlNYJ-6V\"><b>多目标</b>：例如，可以同时优化用户点击率和内容质量。</li><li data-pid=\"DBJLYzVT\"><b>对抗性损失</b>：引入一个对抗模型，用于识别模型生成的欺骗性输出，并惩罚生成这些输出的模型。<br/></li></ul></ul><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-5140cf182f996bce679d5c7e29ffbc42_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1194\" data-rawheight=\"734\" data-original-token=\"v2-5140cf182f996bce679d5c7e29ffbc42\" class=\"origin_image zh-lightbox-thumb\" width=\"1194\" data-original=\"https://pic3.zhimg.com/v2-5140cf182f996bce679d5c7e29ffbc42_r.jpg\"/></figure><p data-pid=\"I0FWuuRI\"><b>长程推理（Long-Range Reasoning）</b></p><ul><li data-pid=\"tH1083HA\"><b>具体挑战</b>：<b>超过 128k tokens 的连贯性</b>。长程推理是指模型在处理长文本时，保持上下文连贯性的能力。由于 Transformer 模型的计算复杂度与序列长度成正比，因此处理长文本的计算成本非常高。此外，模型在长文本中容易出现信息丢失和梯度消失等问题，导致连贯性下降。</li><li data-pid=\"FUmzYjmU\"><b>示例</b>：</li><ul><li data-pid=\"kEUFFhC5\"><b>小说生成</b>：模型难以生成超过 128k tokens 的连贯小说，因为在长文本中，角色的行为和情节的发展容易出现矛盾。</li><li data-pid=\"usVdolGB\"><b>代码理解</b>：模型难以理解超过 128k tokens 的复杂代码库，因为代码中的依赖关系和调用关系非常复杂。</li></ul><li data-pid=\"ax1G27Cg\"><b>当前最佳方案</b>：<b>层次化记忆机制</b>。层次化记忆机制是一种通过将长文本分解为多个段落或章节，并使用层次化的记忆结构来存储和检索信息的技术。</li><ul><li data-pid=\"1LrVAQzT\"><b>段落或章节</b>：例如，可以将一篇小说分解为多个章节，每个章节包含若干段落。</li><li data-pid=\"Jy3Huo7M\"><b>层次化的记忆结构</b>：例如，可以使用一个全局记忆模块来存储整篇小说的概要信息，并使用多个局部记忆模块来存储每个章节的详细信息。</li></ul></ul><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-51be0533472c09590d127ef517351f70_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1434\" data-rawheight=\"718\" data-original-token=\"v2-51be0533472c09590d127ef517351f70\" class=\"origin_image zh-lightbox-thumb\" width=\"1434\" data-original=\"https://pica.zhimg.com/v2-51be0533472c09590d127ef517351f70_r.jpg\"/></figure><p data-pid=\"Y83-9ohh\"><b>个性化安全（Personalized Safety）</b></p><ul><li data-pid=\"nNCsDpP7\"><b>具体挑战</b>：<b>用户特定偏好与通用安全的平衡</b>。不同的用户对安全的定义和容忍度不同。例如，一些用户可能对幽默的容忍度较高，而另一些用户可能认为某些幽默是冒犯性的。如何在满足用户个性化偏好的同时，保证模型的输出是安全的，是一个具有挑战性的问题。</li><li data-pid=\"89eyaqpi\"><b>示例</b>：</li><ul><li data-pid=\"iYBGRhzb\"><b>对话系统</b>：如何根据用户的个人喜好，生成既有趣又不会冒犯用户的回复？</li><li data-pid=\"F1zDjtPv\"><b>内容生成</b>：如何根据用户的兴趣，生成既有创意又不会传播虚假信息的文章？</li></ul><li data-pid=\"30Am4B6Y\"><b>当前最佳方案</b>：<b>差分隐私强化学习</b>。差分隐私强化学习是一种通过在训练过程中添加噪声来保护用户隐私，并在推理时根据用户的偏好进行调整的技术。</li><ul><li data-pid=\"R8k-MCK-\"><b>添加噪声</b>：在训练过程中，向用户的偏好数据中添加噪声，使得模型无法精确地学习用户的偏好，从而保护用户的隐私。</li><li data-pid=\"gXbNWFBd\"><b>根据用户的偏好进行调整</b>：在推理时，根据用户的偏好，对模型的输出进行调整，使其更符合用户的需求。<br/></li></ul></ul><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-82c472044a92c65a1b1facc4e526986f_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1412\" data-rawheight=\"720\" data-original-token=\"v2-82c472044a92c65a1b1facc4e526986f\" class=\"origin_image zh-lightbox-thumb\" width=\"1412\" data-original=\"https://picx.zhimg.com/v2-82c472044a92c65a1b1facc4e526986f_r.jpg\"/></figure><h3>4.2 前沿研究方向</h3><p data-pid=\"EfvBfdE-\">为了解决上述技术瓶颈，研究人员正在探索一些新的研究方向。比如 <b>元认知机制</b>、<b>物理推理融合</b> 、 <b>群体智能系统</b>等。下面我将对这些方向进行更详细的讲解：</p><p data-pid=\"bbG_EE1f\"><b>元认知机制（Meta-Cognition）</b></p><p data-pid=\"cjTCH4J6\">核心思想是<b>让模型学会何时需要深入思考</b>。元认知是指对认知的认知，即对自己的思考过程的思考。通过赋予模型元认知能力，可以使其能够判断自身是否能够解决当前的问题，并根据需要分配更多的计算资源。</p><p data-pid=\"4BHucJPU\"><b>伪代码示例</b>：</p><div class=\"highlight\"><pre><code class=\"language-python3\"><span class=\"k\">def</span> <span class=\"nf\">meta_cognition</span><span class=\"p\">(</span><span class=\"n\">query</span><span class=\"p\">):</span>\n\t<span class=\"n\">uncertainty</span> <span class=\"o\">=</span> <span class=\"n\">calculate_uncertainty</span><span class=\"p\">(</span><span class=\"n\">query</span><span class=\"p\">)</span>\n\t<span class=\"k\">if</span> <span class=\"n\">uncertainty</span> <span class=\"o\">&gt;</span> <span class=\"n\">threshold</span><span class=\"p\">:</span>\n\t\t<span class=\"k\">return</span> <span class=\"n\">allocate_more_compute</span><span class=\"p\">(</span><span class=\"n\">query</span><span class=\"p\">)</span>\n\t<span class=\"k\">else</span><span class=\"p\">:</span>\n\t\t<span class=\"k\">return</span> <span class=\"n\">fast_response</span><span class=\"p\">(</span><span class=\"n\">query</span><span class=\"p\">)</span></code></pre></div><p data-pid=\"WkEuN5qu\">在这个例子中，<code>calculate_uncertainty</code> 函数用于评估模型对当前问题的<b>不确定性</b>。如果模型对当前问题的不确定性超过某个阈值，则说明该问题比较复杂，需要分配更多的计算资源（<code>allocate_more_compute</code>）；否则，说明该问题比较简单，可以使用快速响应（<code>fast_response</code>）。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-d7a136e5a6c63228263e9885ee210538_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"952\" data-rawheight=\"924\" data-original-token=\"v2-d7a136e5a6c63228263e9885ee210538\" class=\"origin_image zh-lightbox-thumb\" width=\"952\" data-original=\"https://pic3.zhimg.com/v2-d7a136e5a6c63228263e9885ee210538_r.jpg\"/></figure><p data-pid=\"WdnLWCGi\"><b>物理推理融合（Physical Reasoning）</b></p><p data-pid=\"4hgcg3Aa\">核心思想是<b>将符号推理与神经网络结合</b>。物理推理是指模型根据物理定律进行推理的能力。通过将符号推理与神经网络结合，可以使模型既能够利用神经网络的强大表示能力，又能够利用符号推理的精确性和可解释性。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-1ab879a7e71c9fa5154b720d421003a7_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"834\" data-rawheight=\"698\" data-original-token=\"v2-1ab879a7e71c9fa5154b720d421003a7\" class=\"origin_image zh-lightbox-thumb\" width=\"834\" data-original=\"https://picx.zhimg.com/v2-1ab879a7e71c9fa5154b720d421003a7_r.jpg\"/></figure><p data-pid=\"gCU1rrK9\"><br/>通过将这些物理规则嵌入到模型中，可以使模型能够进行物理推理，例如，预测物体在自由落体运动中的位置和速度。</p><p data-pid=\"N1SEgeSz\"><b>群体智能系统（Swarm Intelligence）</b></p><p data-pid=\"zH35x9xU\">群体智能是指由多个个体组成的系统，通过个体之间的协作来实现复杂问题的解决。通过构建多模型协作推理框架，可以使不同的模型发挥各自的优势，共同完成复杂的推理任务。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-3367ddcf3386cb827ab567889bbe77ac_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1182\" data-rawheight=\"370\" data-original-token=\"v2-3367ddcf3386cb827ab567889bbe77ac\" class=\"origin_image zh-lightbox-thumb\" width=\"1182\" data-original=\"https://pic3.zhimg.com/v2-3367ddcf3386cb827ab567889bbe77ac_r.jpg\"/></figure><p data-pid=\"06mhOp2S\">在这个流程图中，「任务分解」Agent 负责将问题分解为多个子问题，并将这些子问题分配给各个子任务Agent。「结果整合」Agent 负责将这些结果整合起来，得到最终的答案。</p><h2>五、实践指南：如何选择后训练方案</h2><h3>5.1 决策流程图</h3><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-44b1e97efa5e13d3183a9b45f8542935_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1712\" data-rawheight=\"582\" data-original-token=\"v2-44b1e97efa5e13d3183a9b45f8542935\" class=\"origin_image zh-lightbox-thumb\" width=\"1712\" data-original=\"https://picx.zhimg.com/v2-44b1e97efa5e13d3183a9b45f8542935_r.jpg\"/></figure><h3>5.2 工具链推荐</h3><table data-draft-node=\"block\" data-draft-type=\"table\" data-size=\"normal\" data-row-style=\"normal\"><tbody><tr><th>工具类型</th><th>推荐选项</th><th>核心功能</th></tr><tr><td>微调框架</td><td>HuggingFace PEFT</td><td>LoRA/QLoRA实现</td></tr><tr><td>RLHF平台</td><td>TRL (Transformer RL)</td><td>PPO/DPO实现</td></tr><tr><td>推理加速</td><td>vLLM</td><td>分页注意力机制</td></tr><tr><td>监控评估</td><td>Weights &amp; Biases</td><td>训练过程可视化</td></tr></tbody></table><p></p>", "is_labeled": false, "visited_count": 10930, "thumbnails": ["https://pic1.zhimg.com/50/v2-67cf28dcafb3c822d9599335428a87dd_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-50bef4bcf97bd4710f5f5814adfdce21_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-2dbf66043653a0f1fb3e05087d6e4d06_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-f58473c340a87bfc46c2b55c97d3491b_720w.jpg?source=b6762063", "https://pica.zhimg.com/50/v2-6c1fd8992f04441ab53106bfd7241fed_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-15fc8cdf4f0fc59c3131f8c552319167_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-ed0c1fcaa176691228968c86018fade5_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-c506139d52c6a9a08293f63fc1ede887_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-a4324b8f968d3c0cd664795d6fb936ae_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-1212a51f9fdf572cd5a9ce5da234f478_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-dad9007e8349a55e291c86a5dba65c26_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-46e8c39a75150e3b8ebd68cb40a357cd_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-670a81bdd87f9c9c9f8696049361f7c9_720w.jpg?source=b6762063"], "favorite_count": 1005, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": 30201040247}", "attached_info": "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", "action_card": false}, {"id": "62_**********.947", "type": "feed", "offset": 62, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": **********, "updated_time": **********, "target": {"id": "*********", "type": "article", "url": "https://api.zhihu.com/articles/*********", "author": {"id": "e92fdb27894dfa792af21058a5a0b426", "url": "https://api.zhihu.com/people/e92fdb27894dfa792af21058a5a0b426", "user_type": "people", "url_token": "wen36", "name": "洗了都要爱", "headline": "当不了人民艺术家，那就当个人民的艺术家吧！", "avatar_url": "https://picx.zhimg.com/50/v2-c1be5b32d88658609522d3ed728cb821_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 311, "is_following": false, "is_followed": false}, "title": "林彪谈读书习惯", "comment_permission": "all", "created": 1675560400, "updated": 1675560400, "voteup_count": 1032, "voting": 0, "comment_count": 59, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "林彪的书桌上平时只摆一部书，其它书刊统统不能放。这部书读完，再读另一部。 应该是用啥学啥，需要什么东西，学什么东西。搞什么“完整的”那一套，把那个东西神秘化，费力大，得不到什么好处。费力很大，使用很少。每读一本书，都要有明确的目的，有重点，有针对性。有些章节要精读，有些可以粗读，有些索性不读。读书切忌平均使用力量，有时一目一页，有时一目十行，有时则十目一行，这样就可以大省精力。读书有明确的目的性…", "excerpt_new": "林彪的书桌上平时只摆一部书，其它书刊统统不能放。这部书读完，再读另一部。 应该是用啥学啥，需要什么东西，学什么东西。搞什么“完整的”那一套，把那个东西神秘化，费力大，得不到什么好处。费力很大，使用很少。每读一本书，都要有明确的目的，有重点，有针对性。有些章节要精读，有些可以粗读，有些索性不读。读书切忌平均使用力量，有时一目一页，有时一目十行，有时则十目一行，这样就可以大省精力。读书有明确的目的性…", "preview_type": "default", "preview_text": "", "content": "<p data-pid=\"YHjnBlC_\">林彪的书桌上平时只摆一部书，其它书刊统统不能放。这部书读完，再读另一部。</p><ul><li data-pid=\"REu5uHFy\">应该是用啥学啥，需要什么东西，学什么东西。</li><li data-pid=\"bxCKPnfQ\">搞什么“完整的”那一套，把那个东西神秘化，费力大，得不到什么好处。费力很大，使用很少。</li><li data-pid=\"4xeqscWw\">每读一本书，都要有明确的目的，有重点，有针对性。有些章节要精读，有些可以粗读，有些索性不读。读书切忌平均使用力量，有时一目一页，有时一目十行，有时则十目一行，这样就可以大省精力。</li><li data-pid=\"ZxiqenmP\">读书有明确的目的性，总是围绕当前的问题来读书。</li><li data-pid=\"2YcWRvkp\">学习毛主席著作，要带着问题学，活学活用，学用结合。急用先学，立竿见影，在用字上狠下功夫。</li><li data-pid=\"SVeQq6ki\">什么东西是‘完整’的，‘系统’的，我说这些都是糊涂观念。不同的对象，应该有不同的学习方法。对自然科学的学习，是应该比较系统的。对社会科学，则不一定。</li><li data-pid=\"lee6syvS\">我们要站在书上来读书，不要爬在书下来读书。要批判地读，要吸收地读。书应该为我服务，而不是我为书服务。让书牵着鼻子走，我不干。</li><li data-pid=\"qIuDmbPS\">通过做卡片，把分散的论点按问题集中起来。凡属同一内容，同一含义的都要，字句差不多的也要，不要怕重复。只有一两条就记不住，有几十条就可以加深印象。这样学力量就够，才可能。不然力量不够，也不可能，这也要集中精力打歼灭战。</li></ul>", "is_labeled": false, "visited_count": 124832, "favorite_count": 3669, "reaction_instruction": {"REACTION_COMMENT_NEWEST_LIST": "HIDE"}, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": *********}", "attached_info": "CqgGCPq2yZP9xtXXnwEQBxoJMjIyMjYxNTgwINCL/J4GKIgIMDtAPkowChtUU19TT1VSQ0VfQkFTSUNfSU5GT19SRUNBTEwSATAYACAAOgp7InJhdyI6IiJ9YiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3IJNjAzNTgyNjAzqgEJcmVjb21tZW5kwgEgZTkyZmRiMjc4OTRkZmE3OTJhZjIxMDU4YTVhMGI0MjbyAQoIDBIGTm9ybWFs8gEoCAoSJDM1MDQ1ZGE1LTYwZTktNGJlZi1hMWYzLTQ0NjgwNjZhN2NiN/IBBggLEgIxMYICAIgCyqy5zfoykgIgZTkyZmRiMjc4OTRkZmE3OTJhZjIxMDU4YTVhMGI0MjaaAgDKAhZTaG9ySW50ZXJlc3RXZWlnaHRSdWxlygIWQWN0aW9uU2hvckludGVyZXN0UnVsZcoCG0ludGVyYWN0aW9uU2hvckludGVyZXN0UnVsZcoCFlJldmlzaXRWYWx1ZVdlaWdodFJ1bGXKAhhQZXJpb2RJbnRlcmVzdFdlaWdodFJ1bGXKAhVVc2VyTGNuRXhpdFdlaWdodFJ1bGXKAhRDb250ZW50QWdlV2VpZ2h0UnVsZcoCF1Rlc3RlZEFuZFdvcmtXZWlnaHRSdWxl2gIbVFNfU09VUkNFX0JBU0lDX0lORk9fUkVDQUxM6AIC+gILTk9STUFMX0ZMT1eKAyBkYTQ5ZjJhOGI0NGI0NWEzYWYyNGU0NThhZDJmNDBiMpoDDQoCdjIQABoFb3RoZXKoA6DPB9gDAOoDEWJhc2ljX2luZm9fcmVjYWxs+gMfEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERYAEAIgEAJIEBk5vcm1hbJoEATKgBACoBACwBAC6BAZtYW51YWzCBAMxNzDIBADSBA/mjqjojZDlt7Lmm7TmlrDYBADwBAD5BAAAAGCNlMg/gQUAAAAAAAAAAIkFWhKVCKSs0j+SBQCaBQNkZnSiBQNkZnSyBQExuQUAAAAAAAAAANAFAOAFAOgFAPAFC5AGAKAGQqgGAJICJAoJMjIyMjYxNTgwEgk2MDM1ODI2MDMYByIKSU1BR0VfVEVYVA==", "action_card": false}, {"id": "63_**********.4", "type": "feed", "offset": 63, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": **********, "updated_time": **********, "target": {"id": "1897298439471073252", "type": "answer", "url": "https://api.zhihu.com/answers/1897298439471073252", "author": {"id": "7ea6322f7ea810bd44f5ba58b2524a25", "url": "https://api.zhihu.com/people/7ea6322f7ea810bd44f5ba58b2524a25", "user_type": "people", "url_token": "sonofabitch", "name": "哈五高", "headline": "逻辑能解释一切", "avatar_url": "https://picx.zhimg.com/50/v2-3f578602ba4784fd4659ed74ab4724f5_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 5848, "is_following": false, "is_followed": false}, "created_time": 1745131198, "updated_time": 1747664764, "voteup_count": 1529, "thanks_count": 65, "comment_count": 710, "is_copyable": true, "question": {"id": "867751968", "type": "question", "url": "https://api.zhihu.com/questions/867751968", "author": {"id": "407c39a2d035fd6e8de7ad4a9462151e", "url": "https://api.zhihu.com/people/407c39a2d035fd6e8de7ad4a9462151e", "user_type": "people", "url_token": "shanxia-23", "name": "鹏翼", "headline": "", "avatar_url": "https://picx.zhimg.com/50/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 768, "is_following": false, "is_followed": false}, "title": "为什么特斯拉坚持用纯视觉智驾？", "created": 1728863302, "answer_count": 0, "follower_count": 0, "comment_count": 6, "bound_topic_ids": [28480, 169215, 2091567, 2269969, 3723218], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "有一片沙滩。 有人愿意花十年时间，投入几百亿研究沙子，然后用整个沙滩上取之不尽沙子来打造芯片，点沙成金。 有人只想快速变现赚钱，他们既没有几百亿也等不了十年，就只能在沙滩上捡贝壳卖钱。 为了让人相信贝壳比芯片值钱，后者就花大钱推广贝壳，然后把芯片贬低成沙子。 久而久之，在后者受众眼里，那些芯片的成本就是一堆沙子。 贝壳当然比沙子值钱。 于是那些花大钱买芯片的人都成了傻子。 直到有一天大家都用上了电脑和…", "excerpt_new": "有一片沙滩。 有人愿意花十年时间，投入几百亿研究沙子，然后用整个沙滩上取之不尽沙子来打造芯片，点沙成金。 有人只想快速变现赚钱，他们既没有几百亿也等不了十年，就只能在沙滩上捡贝壳卖钱。 为了让人相信贝壳比芯片值钱，后者就花大钱推广贝壳，然后把芯片贬低成沙子。 久而久之，在后者受众眼里，那些芯片的成本就是一堆沙子。 贝壳当然比沙子值钱。 于是那些花大钱买芯片的人都成了傻子。 直到有一天大家都用上了电脑和…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"86qLcU8x\">有一片沙滩。</p><p data-pid=\"kAKzPMNv\">有人愿意花十年时间，投入几百亿研究沙子，然后用整个沙滩上取之不尽沙子来打造芯片，点沙成金。</p><p data-pid=\"gAB2R5BK\">有人只想快速变现赚钱，他们既没有几百亿也等不了十年，就只能在沙滩上捡贝壳卖钱。</p><p data-pid=\"SsBo2mEW\">为了让人相信贝壳比芯片值钱，后者就花大钱推广贝壳，然后把芯片贬低成沙子。</p><p data-pid=\"H8OCkMhU\">久而久之，在后者受众眼里，那些芯片的成本就是一堆沙子。</p><p data-pid=\"mSnhqQVM\">贝壳当然比沙子值钱。</p><p data-pid=\"2SK-ZjfB\">于是那些花大钱买芯片的人都成了傻子。</p><p data-pid=\"bo7O8LMN\">直到有一天大家都用上了电脑和手机。</p><hr/><p data-pid=\"W6Xuy1Xh\">两个结论：</p><p data-pid=\"DUMIGuqf\">1.“室外光子密度下限”远高于“雷达点云密度上限”。</p><p data-pid=\"iD0GoYP_\">2.成本不变，“CMOS成像方案的信噪比提升速度”高于“雷达点云密度提升速度”。</p><p data-pid=\"WuonU4-K\">就跟星舰不用碳纤维钛铝合金，而去用不锈钢的逻辑是一样的。前者在这片沙滩上看到的不是贝壳和沙子，而是真空管和晶体管，是雷达点云密度和光子密度。</p><p data-pid=\"TriHX-LF\">根据我的理解，所谓“自动驾驶”大致分为“对外部信息的获取”和“对自身行为的规划”两部分。</p><p data-pid=\"TBisZlhX\">而“纯视觉”和“雷达”两个方案主要涉及“对外部信息的获取”这部分。</p><p data-pid=\"62ZmPtYE\">试着以第一性原理去分析。</p><p data-pid=\"wVZpyP_K\">“广义上的亮度”与“单位面积上的光子密度”成正比。</p><p data-pid=\"kuVBqLv2\">而即便是在漆黑的夜晚，哪怕人的肉眼几乎无法看见任何东西，理论上室外的光子密度依然远高于最先进的激光雷达能够达到的点云密度上限好几个数量级。</p><p data-pid=\"DKUt-VxG\">雷达是把激光或者毫米波投射到物体上再反射回来，<b>检测到物体的“外形”和“距离”两大信息</b>，从而构建出一个3D点云。</p><p data-pid=\"PUOIO1U9\">而cmos是通过捕获从车灯路灯，天上的太阳和星星发出来的光射到物体上再反射到摄像头，生成一帧帧2d图像，再通过不同角度的摄像头的图像组合，<b>推测出物体的“外形”和“距离”两大信息</b>，最后生成一个3D场景。</p><p data-pid=\"Pr_c37CT\">虽然成像精度也取决于摄像头的动态范围，曝光长度，光电转换效率，还有光的波长等因素，但可以说<b>“单位面积上的光子密度”决定了cmos成像精度的上限</b>。</p><p data-pid=\"_Zmld0kQ\">关键就在于cmos感光成像方案在捕获光子信号时会产生噪点，而且对3D空间的感知高度依赖于AI大模型和多目摄像头3D重建算法的支持。</p><p data-pid=\"wlK9KYYg\">而现在主流的雷达对3D空间的感知方案更加成熟稳定，技术门槛更低，相比摄像头，更容易构建出一个粗糙，低帧率但相对可靠的3D空间。</p><p data-pid=\"Fh5d1DZT\"><b>相比这种高识别率却粗糙的3D空间模型，高清彩色图像提供的信息必然远多于前者</b>，还能能最大限度利用马斯克在AI上的多年积累，让车载电脑不但能“看到”整个环境，<b>不仅仅能让你识别到障碍物，还能更好的去“理解”眼前的一切</b>。</p><p data-pid=\"SqAE5UM0\">这是个活人还是人形立牌？</p><p data-pid=\"l4UQh-uk\">如果是活人，他现在是打算横穿马路吗？我是否需要减速？</p><p data-pid=\"clXYcMle\">如果他打算横穿马路，那这个人到底是交警还是路人？</p><p data-pid=\"o3TV2eDV\">他的手势到底是让我快走还是让我调头？</p><p data-pid=\"32lI9ORY\">我该不该听他的？</p><p data-pid=\"8TTdTzjl\"><b>越是对自己视觉处理能力没信心，才会越强调雷达方案提供的下限保障</b>。</p><p data-pid=\"C2u7WQzt\">但是当你有信心确保自己的视觉处理能力下限能够超越雷达方案的上限，你追求的就不再是“如何轻松辨别出障碍物”，而是“如何理解这个场景里的一切，帮助我更好的做出决策”，那你自然就不再需要雷达了。</p><p data-pid=\"GUW6Xjwa\">咱们国内的“高科技”企业，基本都是对欧美那些经过大量试错最终得到市场检验的行业，在国内进行排外式的复刻（大疆和抖音可能是唯二的例外）。</p><p data-pid=\"cfysukOn\">而且由于环境特殊，高科技行业里经常能看到大量打着对标欧美前沿领域旗号的“民族企业”，但凡你稍加了解，很容易发现其实就是靠炒作营销来骗补贴。</p><p data-pid=\"SsOAHAJg\">根据这个逻辑，你会发现咱们的很多“高科技”企业在各种技术路线中，往往不优先考虑未来的发展上限。</p><p data-pid=\"6zFjc6ct\">咱们的创业者想的往往是如何最快速度达到60分及格，先确保一个稳定的下限，然后就开启萨哈夫模式卖货赚钱，营销预算甚至高于研发预算，在前期的优势环节跟对手的劣势进行各种对比，后期利用信息差依然能大赢特赢，直到被上限更高的对手全面超越，然后黯然退场，选择卷款跑路或者换一条赛道继续赢。</p><p data-pid=\"D86v9Fgi\">尤其可以观察马斯克涉及的那几条赛道，电车和FSD就不说了，什么paypal啊，星舰啊，星链啊，筷子夹火箭啊，脑机接口啊，机器人啊，你都能在国内找到一堆正在“弯道超车”的亲戚。</p><p data-pid=\"QK4DMdMt\">咱们一直在用前期英雄错位“碾压”对手的后期英雄，只考虑前期场面好看，方便你吹牛逼骗补贴骗投资，而完全不考虑最后比赛输赢。</p><p data-pid=\"zMxuM3Kh\">什么“弯道超车”啦，“遥遥领先”啦，其实都是这种语境下的宣传口号。</p><p data-pid=\"CM0lR7SM\">别看现在那些“新势力”一个个说自己如何朝着L5智驾努力，万一那一天特斯拉真的率先推出L4，L5智驾，承诺由特斯拉承担全程事故责任，国内这帮车企大概率会180度调头，联合抵制一切超过L3的智驾，开始宣扬“自动驾驶不安全”“辅助驾驶永远只能辅助人类”的口号。</p><p data-pid=\"j0zqgrhK\">得不到这个市场，那就必须毁了它。</p><p data-pid=\"vtoFLqwx\">稍有一些常识，就知道本质上并不是“雷达比摄像头安全”。</p><p data-pid=\"dOomf8ah\">而是“通过雷达点云重建3D空间”比“通过算法分析光子重建3D空间”更简单，相关技术更成熟，前期的研发投入，算力中心等基建投入更少。</p><p data-pid=\"FY-k5WM2\">学自行车肯定比考驾照简单，还不用花钱培训，可能你已经往前骑行3天了，人家才刚刚考上驾照。</p><p data-pid=\"E3Nouv3Z\">但人家开车3小时就能追上你，然后远远把你抛在身后。</p><p data-pid=\"sYT5WE3Q\">回到这个问题，“为什么特斯拉坚持用纯视觉智驾？”</p><p data-pid=\"LUCWC0JB\">我想说如果基于cmos成像方案的摄像头画面清晰程度，取决于将光子转化为电子过程中的信噪比。</p><p data-pid=\"-KPlYXgy\">那么两者的应用前景基本取决于：</p><p data-pid=\"25CNmOJI\">在成本不变的情况下，未来“人类前沿领域在相同光子密度下成像信噪比的提升速度”，与“人类前沿领域对激光雷达和相控阵雷达点云密度的提升速度”哪个更快。</p><p data-pid=\"C19-1Lnw\">至少根据当前的技术发展，以及未来可预见的趋势来看，“CMOS成像方案的信噪比提升速度”显然是要快于“激光雷达和相控阵雷达点云密度提升速度”的。</p><p data-pid=\"T8PTFz2i\">我的判断依据是，前者可以通过“制程工艺”，“材料学”和“AI算法”等方式对<b>收集端进一步迭代提升</b>。</p><p data-pid=\"fpXN2slh\">而后者已经能看到比如“转速”“散热”“最大帧率”等<b>发射端的根本限制</b>。</p><p data-pid=\"oKbwbzVt\">在“对外部信息的获取”上，<b>“雷达”等于是主动抛弃了大自然里免费的海量光学信息，抛弃了整片沙滩上取之不尽的沙子，把信息量上限局限到了自身的发射端，也就是那几个贝壳上</b>。</p><p data-pid=\"GydX9WJg\">那么马斯克更看好纯视觉，而非雷达的态度，也就不难理解了。</p><p data-pid=\"v7wFIRBq\">时间，将是纯视觉方案最好的伙伴。</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 226595, "favorite_count": 737, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1897298439471073252}", "attached_info": "CrAGCPq2yZP9xtXXnwEQBBoJNzIzNzUwMDgyIL6tksAGKPkLMMYFQD9KMAobVFNfU09VUkNFX0JBU0lDX0lORk9fUkVDQUxMEgEwGAAgADoKeyJyYXciOiIifVoJMTExMTM1OTg1YiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3ITMTg5NzI5ODQzOTQ3MTA3MzI1MooBCTg2Nzc1MTk2OKoBCXJlY29tbWVuZMIBIDdlYTYzMjJmN2VhODEwYmQ0NGY1YmE1OGIyNTI0YTI18gEKCAwSBk5vcm1hbPIBKAgKEiQ1MjUzNGNiNi1iNTM3LTQ5NTUtOTY1Ny0zMTlmOGRiMzAwMGTyAQYICxICMTGCAgCIAsqsuc36MpICIDdlYTYzMjJmN2VhODEwYmQ0NGY1YmE1OGIyNTI0YTI1mgIAygIWU2hvckludGVyZXN0V2VpZ2h0UnVsZcoCFkFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhtJbnRlcmFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhZSZXZpc2l0VmFsdWVXZWlnaHRSdWxlygIYUGVyaW9kSW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxlygIUQ29udGVudEFnZVdlaWdodFJ1bGXaAhtUU19TT1VSQ0VfQkFTSUNfSU5GT19SRUNBTEzoAgP6AgtOT1JNQUxfRkxPV4oDIGRhNDlmMmE4YjQ0YjQ1YTNhZjI0ZTQ1OGFkMmY0MGIymgMNCgJ2MhAAGgVvdGhlcqgDo+oN2AMA6gMRYmFzaWNfaW5mb19yZWNhbGz6Ax8SDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFgAQAiAQAkgQGTm9ybWFsmgQBM6AEAKgEALAEALoEBm1hbnVhbMIEAzE2MMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAgKNBwz+BBQAAAAAAAAAAiQVaEpUIpKzSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AULkAYAoAZDqAYAkgIuCgk3MjM3NTAwODISEzE4OTcyOTg0Mzk0NzEwNzMyNTIYBCIKSU1BR0VfVEVYVA==", "action_card": false}, {"id": "64_**********.83", "type": "feed", "offset": 64, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": **********, "updated_time": **********, "target": {"id": "1921220878567449158", "type": "answer", "url": "https://api.zhihu.com/answers/1921220878567449158", "author": {"id": "786abb58bed6418a143d856bd17886a4", "url": "https://api.zhihu.com/people/786abb58bed6418a143d856bd17886a4", "user_type": "people", "url_token": "xiao-tian-shi-91-95-42", "name": "意升", "headline": "爱看书，爱看电影，爱写感悟！", "avatar_url": "https://pic1.zhimg.com/50/v2-3b024ba421bf229a0d2829e5ea619bf2_l.jpg?source=b6762063", "is_org": false, "gender": 1, "badge": [{"type": "identity_people", "description": "英格玛（上海）企业服务外包有限公司 员工"}], "followers_count": 1439, "is_following": false, "is_followed": false}, "created_time": 1750834752, "updated_time": 1750834752, "voteup_count": 3, "thanks_count": 1, "comment_count": 0, "is_copyable": false, "question": {"id": "1889620823519766303", "type": "question", "url": "https://api.zhihu.com/questions/1889620823519766303", "author": {"id": "d8114fb4877269a0ec78198b9ce54680", "url": "https://api.zhihu.com/people/d8114fb4877269a0ec78198b9ce54680", "user_type": "people", "url_token": "he-he-72-74-80", "name": "呵呵", "headline": "", "avatar_url": "https://pica.zhimg.com/50/v2-8b4dd85f10e3037b14a550f06b8f8c19_l.jpg?source=b6762063", "is_org": false, "gender": 0, "followers_count": 6, "is_following": false, "is_followed": false}, "title": "想买书来打发无聊的时光，有什么好书推荐？", "created": 1743300711, "answer_count": 0, "follower_count": 0, "comment_count": 0, "bound_topic_ids": [23139, 1322883, 1324569], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "推荐你看《隐谷路:一个精神分裂症家族的绝望与希望》。 这是一本纪实的书籍，详细全面记录了加尔文一家人的生活轨迹。他们虽然被各种困难缠身，尤其是精神分裂症的严重影响。可谓一路走来，荆棘满布，很多时候几乎把加尔文一家逼入绝境。但实际上，他们却坚强地活了下来，每一个家庭成员，用自己的方式，过着生活。疗愈，沉沦，绝望与希望。 另外值得一提的是，这一家人的存在，对于精神分裂症的攻克有着非同一般的作用。读这本…", "excerpt_new": "推荐你看《隐谷路:一个精神分裂症家族的绝望与希望》。 这是一本纪实的书籍，详细全面记录了加尔文一家人的生活轨迹。他们虽然被各种困难缠身，尤其是精神分裂症的严重影响。可谓一路走来，荆棘满布，很多时候几乎把加尔文一家逼入绝境。但实际上，他们却坚强地活了下来，每一个家庭成员，用自己的方式，过着生活。疗愈，沉沦，绝望与希望。 另外值得一提的是，这一家人的存在，对于精神分裂症的攻克有着非同一般的作用。读这本…", "preview_type": "default", "preview_text": "", "reshipment_settings": "disallowed", "content": "<p data-pid=\"-rlXRc0x\">推荐你看《隐谷路:一个精神分裂症家族的绝望与希望》。</p><p data-pid=\"qIKfoGkb\">这是一本纪实的书籍，详细全面记录了加尔文一家人的生活轨迹。他们虽然被各种困难缠身，尤其是精神分裂症的严重影响。可谓一路走来，荆棘满布，很多时候几乎把加尔文一家逼入绝境。但实际上，他们却坚强地活了下来，每一个家庭成员，用自己的方式，过着生活。疗愈，沉沦，绝望与希望。</p><p data-pid=\"16GASy9J\">另外值得一提的是，这一家人的存在，对于精神分裂症的攻克有着非同一般的作用。读这本书，除了看到加尔文一家的生活轨迹，体验他们的希望和绝望。也对比自己的生活，更加自信，更加积极寻找自己的存在的意义。</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 33, "favorite_count": 0, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1921220878567449158}", "attached_info": "CtsFCPq2yZP9xtXXnwEQBBoJNzMzOTQ1NjkzIMC87sIGKAMwAEBASiQKGVRTX1NPVVJDRV9XQVJNX1VQX05PUk1BTDISATAYACAAOgBKIgoXVFNfU09VUkNFX1dBUk1VUF9SVUNFTkUSATAYACAAOgBaCTExNDI0MjAxM2IgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyEzE5MjEyMjA4Nzg1Njc0NDkxNTiKARMxODg5NjIwODIzNTE5NzY2MzAzqgEJcmVjb21tZW5kwgEgNzg2YWJiNThiZWQ2NDE4YTE0M2Q4NTZiZDE3ODg2YTTyAQoIDBIGTm9ybWFs8gEoCAoSJDUxMDhhMTE3LTg3N2YtNGJkMC1iYjBmLTBkMWI2MzM0OTg3YfIBBggLEgIxMYICAIgCyqy5zfoykgIgNzg2YWJiNThiZWQ2NDE4YTE0M2Q4NTZiZDE3ODg2YTSaAgDKAhZTaG9ySW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxlygIYQ29udGVudFdhcm1VcEJyZWFrSW5SdWxl2gIZVFNfU09VUkNFX1dBUk1fVVBfTk9STUFMMugCAvoCC05PUk1BTF9GTE9XigMgZGE0OWYyYThiNDRiNDVhM2FmMjRlNDU4YWQyZjQwYjKaAw0KAnYyEAAaBW90aGVyqAMh2AMA6gMLdGV4dF9ydWNlbmX6Ax8SDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFgAQAiAQAkgQGTm9ybWFsmgQBMqAEAKgEALAEALoEAmFpwgQDNDAwyAQA0gQP5o6o6I2Q5bey5pu05paw2AQA8AQA+QQAAAAgQF6hP4EFAAAAAAAAAACJBVoSlQikrNI/kgUAmgUDZGZ0ogUDZGZ0sgUBMbkFAAAAAAAAAADQBQDgBQDoBQDwBQuQBgCgBkSoBgGSAi4KCTczMzk0NTY5MxITMTkyMTIyMDg3ODU2NzQ0OTE1OBgEIgpJTUFHRV9URVhU", "action_card": false}, {"id": "65_**********.708", "type": "feed", "offset": 65, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": **********, "updated_time": **********, "target": {"id": "1907718327259861976", "type": "answer", "url": "https://api.zhihu.com/answers/1907718327259861976", "author": {"id": "2ddc134b2d6bcde89f883e4c051f1bc2", "url": "https://api.zhihu.com/people/2ddc134b2d6bcde89f883e4c051f1bc2", "user_type": "people", "url_token": "ke-ke-neng-xing", "name": "晏北", "headline": "公众号——晏北，理解芯片，理解经济", "avatar_url": "https://picx.zhimg.com/50/v2-a0874375d3926d8580eec8f4e8de3ce1_l.jpg?source=b6762063", "is_org": false, "gender": 1, "badge": [{"type": "best_answerer", "description": "职场话题下的优秀答主", "topic_names": ["职场"], "topic_ids": [2566]}, {"type": "super_activity", "description": "知势榜职场领域成长力榜答主"}], "followers_count": 5360, "is_following": false, "is_followed": false}, "created_time": 1747615492, "updated_time": 1747621507, "voteup_count": 12, "thanks_count": 0, "comment_count": 13, "is_copyable": true, "question": {"id": "428730647", "type": "question", "url": "https://api.zhihu.com/questions/428730647", "author": {"id": "578d0e2464faeb326ebc06cd0b6de36b", "url": "https://api.zhihu.com/people/578d0e2464faeb326ebc06cd0b6de36b", "user_type": "people", "url_token": "qing-feng-44-51", "name": "清风", "headline": "", "avatar_url": "https://pica.zhimg.com/50/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "辞职在家搞自媒体现实吗？", "created": 1604459481, "answer_count": 0, "follower_count": 0, "comment_count": 11, "bound_topic_ids": [307, 2566, 68370, 123355, 32797], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "thumbnail": "https://pic1.zhimg.com/50/v2-6b9615ee6071b59947b978296a0d6b0a_720w.jpg?source=b6762063", "excerpt": "昨天， 在逍遥津，一晤大V @大成职场 ，这段经历，对想从事自媒体的朋友，也许有所启发。 周末，回老家，参加同学婚礼， 见证10年前就认识的两个家伙，走进新生活，很奇妙的感觉。早上八点，动身返宁， 在逍遥津公园，和一位 上市公司前高管，如今的知乎大V，聊了几小时，   大V说：你这家伙，看文章，以为至少三十多岁，没想到这么年轻， 我说：俺的文字比较早熟，一见面，就原形毕露，你啥时候开始搞自媒体？ 大V说：1年前，当时…", "excerpt_new": "昨天， 在逍遥津，一晤大V @大成职场 ，这段经历，对想从事自媒体的朋友，也许有所启发。 周末，回老家，参加同学婚礼， 见证10年前就认识的两个家伙，走进新生活，很奇妙的感觉。早上八点，动身返宁， 在逍遥津公园，和一位 上市公司前高管，如今的知乎大V，聊了几小时，   大V说：你这家伙，看文章，以为至少三十多岁，没想到这么年轻， 我说：俺的文字比较早熟，一见面，就原形毕露，你啥时候开始搞自媒体？ 大V说：1年前，当时…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"nXP2Lk2R\">昨天，<b>在逍遥津，一晤大V</b> <a class=\"member_mention\" href=\"https://www.zhihu.com/people/dc810723af6326722801a4f8829b589f\" data-hash=\"dc810723af6326722801a4f8829b589f\" data-hovercard=\"p$b$dc810723af6326722801a4f8829b589f\">@大成职场</a> ，</p><p data-pid=\"TSuMd-PH\">这段经历，对想从事自媒体的朋友，也许有所启发。</p><p data-pid=\"GsO3udZL\">周末，回老家，参加同学婚礼，</p><p data-pid=\"Jxsm4Sy0\"><b>见证10年前就认识的两个家伙，走进新生活</b>，很奇妙的感觉。</p><p data-pid=\"fSRP6IXb\">早上八点，动身返宁，</p><p data-pid=\"x3juHf7e\">在逍遥津公园，和一位<b>上市公司前高管，如今的知乎大V，</b>聊了几小时，</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-022f872f82a8250d7cd6dd31d77a213d_1440w.jpg\" data-rawwidth=\"402\" data-rawheight=\"306\" data-size=\"normal\" data-original-token=\"v2-e37f5c68e4e87f7b2dc1a58eff5f645a\" data-default-watermark-src=\"https://pic4.zhimg.com/v2-92320e9e3cd2fe1be8b712ed923e48a3_b.jpg\" class=\"content_image\" width=\"402\"/><figcaption>图片来源：网络</figcaption></figure><p data-pid=\"0Jun35ao\"><b>大V说：</b>你这家伙，<b>看文章，以为至少三十多岁，没想到这么年轻</b>，</p><p data-pid=\"fii0FOh0\"><b>我说：俺的文字比较早熟，一见面，就原形毕露</b>，</p><p data-pid=\"zl1qFePP\">你啥时候开始搞自媒体？</p><p data-pid=\"k--MKeUI\"><b>大V说：</b>1年前，</p><p data-pid=\"L3BA8F0B\">当时，我从前东家辞职，<b>在朋友公司，挂了个顾问</b>，</p><p data-pid=\"3WRV7iRA\">不用天天上班，很闲，</p><p data-pid=\"3KN7E4bA\">另一朋友说，<b>你在职场混了这么多年，小有所成，不如将这些经验，写成文章，分享到网上</b>，还能赚点钱，</p><p data-pid=\"GTDsoJK2\">闲着也是闲着，就开始写知乎。</p><p data-pid=\"rGooXu0O\">没想到，<b>难度超乎想象</b>。</p><p data-pid=\"-nUDkL2s\">起初半年，基本没挣到钱，搞得我很焦虑，</p><p data-pid=\"onlmjjH6\"><b>在商业世界看来，不能变现的东西，毫无价值</b>，</p><p data-pid=\"q8gQmaJ-\">难道我引以为傲的职业生涯，如此不值一提？</p><p data-pid=\"zYGCOsok\">所幸，我没有放弃，<b>每天坚持更新，才有了现在的用户数量和收入</b>。</p><p data-pid=\"y5IHEWsV\"><b>我说：</b>起步的艰难，感同身受，</p><p data-pid=\"O2C6aK21\">再看这个过程，感触最深的是什么？</p><p data-pid=\"09ZIIFau\"><b>大V说：</b>最大的感受，是<b>内容质量</b>的重要性，</p><p data-pid=\"GNpD1-Zd\">一开始，有些误入歧途，</p><p data-pid=\"tFuBJIG7\"><b>和其他创作者，互动过于频繁，</b></p><p data-pid=\"ZR7nu1cj\">坚持半年，才慢慢体会到，质量的重要性，</p><p data-pid=\"ZeWWri4Z\">产生一种意识，<b>每个看我文章的家伙，都是我的客户</b>，</p><p data-pid=\"YjBuBKh5\">他们需要的，也许是管理经验，也许是对付领导的方法，</p><p data-pid=\"8eQyffzT\">总之，我必须提供些干货，<b>能够真正对客户产生帮助的干货</b>，这大概就是知乎提倡的“<b>获得感</b>”。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-5d9e277f87485282992448c4ef6b57bf_1440w.jpg\" data-rawwidth=\"1200\" data-rawheight=\"662\" data-size=\"normal\" data-original-token=\"v2-db97cc8e03ac7751a62f6417e4c21a78\" data-default-watermark-src=\"https://pic2.zhimg.com/v2-934396da73a110befa5074984c0e519b_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1200\" data-original=\"https://pic2.zhimg.com/v2-5d9e277f87485282992448c4ef6b57bf_r.jpg\"/><figcaption>图片来源：网络</figcaption></figure><p data-pid=\"llk8Tb21\"><b>我说：</b>非常认同，</p><p data-pid=\"4aZ3Jwbt\">我最大的感受，是要<b>用产品思维，写文章，</b></p><p data-pid=\"1WaNmQzg\">一开始，想到哪写到哪，觉得表达自我最重要，</p><p data-pid=\"WQTWCRA8\">慢慢意识到，自媒体文章，不是这个逻辑，</p><p data-pid=\"7BnHt0PH\">你不能随心所欲，而是应该<b>用制作产品的严谨性，制作文章</b>。</p><p data-pid=\"_tEL8uBq\">什么是好产品？</p><p data-pid=\"9agdBbB3\"><b>创造性+标准化</b>，缺一不可，</p><p data-pid=\"B19jGNuv\">以芯片产品为例，</p><p data-pid=\"ps18NdWj\">芯片结构设计，需要<b>创造性</b>，以实现<b>性能突破</b>，</p><p data-pid=\"onD9NmYr\">实际制作工艺，需要<b>标准化</b>，以确保<b>稳定生产</b>。</p><p data-pid=\"GyMU1WEs\"><b>内容产品</b>，也是这个逻辑，</p><p data-pid=\"SvyLT463\"><b>具体内容</b>，可以有天马行空的想象力，这是<b>创造性</b>，</p><p data-pid=\"cjFKBcMs\"><b>表现形式</b>，必须符合基本的传播规律，这是<b>标准化</b>。</p><p data-pid=\"RcV59Y3V\"><b>大V问：</b>具体解释一下传播规律？</p><p data-pid=\"Yx6hvU0Y\"><b>我说：</b>比如，提供情绪价值，</p><p data-pid=\"AamU5oq6\"><b>我曾经以为，只要内容质量足够好，不愁没人看</b>，</p><p data-pid=\"6b4GNJ20\">后来才发现，不是这么回事，</p><p data-pid=\"T7vVGg9h\"><b>学术性与传播性，是此消彼长的两极</b>，</p><p data-pid=\"aCsEpsMb\">大家工作之余，看个文章，多数只图一乐，</p><p data-pid=\"OzksjcPf\">你要是长篇大论，各种学术名词堆积，没人看得下去，</p><p data-pid=\"qcin3tbY\">最好的文章，<b>是在逗你开心的同时，引人深思</b>，</p><p data-pid=\"1tzE3gS5\">换言之，<b>兼具功能价值和情绪价值</b>，</p><p data-pid=\"OkvNRpkx\">六神磊磊，便是此中高手，非常牛逼。</p><p data-pid=\"H6uYxkqd\"><b>幽默的本质，是只需20%的CPU，便能轻松论述问题</b>，</p><p data-pid=\"WrKdhUEU\">剩下的算力，用来逗汝一笑，</p><p data-pid=\"PO5HtxOx\"><b>这，才是玩弄文字的高手，才是真正高级的能力</b>。</p><p data-pid=\"G7lAQBsF\"><b>大V说：</b>难怪你的文章，每篇都有好玩的图，</p><p data-pid=\"LKdjUu8o\">之前就想问你，那些图怎么准备，</p><p data-pid=\"atHGyXc6\">这块，是我的劣势。</p><p data-pid=\"zU63atRX\"><b>我说：</b>咱俩打法不同，</p><p data-pid=\"PozBPgGx\">你侧重于量，我侧重于质，</p><p data-pid=\"xmoJFlbW\"><b>如果一篇文章花的时间过长，势必要牺牲数量</b>，这是每个创作者都要面对的取舍问题。</p><p data-pid=\"2MjFxS1S\">你现在，已获自由身，不考虑做视频？</p><p data-pid=\"Iz5Bsmrr\">图文领域的流量，还是太低。</p><p data-pid=\"9gx7TIlc\"><b>大V说：</b>正在筹划，迟早会做，</p><p data-pid=\"WzgmTNxQ\">你这家伙，<b>眉清目秀，逻辑清晰，知识储备让人惊叹</b>，不考虑做视频？</p><p data-pid=\"Syr-cv7R\"><b>我哈哈一笑：</b>等俺脱离苦海，迟早要走出这一步，到时候，还得请您指点迷津。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-688daaad63b5689cf69a5aa0b4ed3915_1440w.jpg\" data-rawwidth=\"1080\" data-rawheight=\"737\" data-size=\"normal\" data-original-token=\"v2-cf40eddc7afc96b64dce45b33ebb93cf\" data-default-watermark-src=\"https://pic1.zhimg.com/v2-3019740688a42f68885888bcd9866ac2_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-688daaad63b5689cf69a5aa0b4ed3915_r.jpg\"/><figcaption>图片来源：网络</figcaption></figure><p data-pid=\"S5Ngzwrb\">在高铁上，复盘这段经历，</p><p data-pid=\"sCoDRw1-\"><b>再次体会到，强连接与弱连接的区别</b>。</p><p data-pid=\"Z49oaHhe\">出生在哪个地区、何样家庭，并非我们能决定。</p><p data-pid=\"z-yPHgA3\"><b>血缘和地缘关系，带来的连接，可称之为强连接</b>。</p><p data-pid=\"H_Qj34Cr\">七大姑八大姨、同学同乡，都是如此，</p><p data-pid=\"Q76K01dR\">强连接的上限，在考上大学的那刻，基本已经锁定。</p><p data-pid=\"n4_Z1eAN\"><b>素不相识的陌生人，是弱连接</b>，</p><p data-pid=\"542h3Y26\">每天记录些感悟，扔到网上，吸引一批志同道合、却又位于天南海北的人，</p><p data-pid=\"pcot7rC8\">甚至更进一步，<b>面对面交流，分享不同领域的洞见，寻找原先无法想象的合作机会</b>，</p><p data-pid=\"IqbC4zrH\">这，便是弱连接的魔力。</p><p data-pid=\"EATGC7UH\">也欢迎各位，私信俺，有机会线下交流，</p><p data-pid=\"ZpmZCTUK\">因为，<b>思想的碰撞，是最令人兴奋的生命体验之一。</b></p><p data-pid=\"6CtREp7h\">关注 <a class=\"member_mention\" href=\"https://www.zhihu.com/people/2ddc134b2d6bcde89f883e4c051f1bc2\" data-hash=\"2ddc134b2d6bcde89f883e4c051f1bc2\" data-hovercard=\"p$b$2ddc134b2d6bcde89f883e4c051f1bc2\">@晏北</a>，理解芯片与经济~</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 454, "thumbnails": ["https://pic1.zhimg.com/50/v2-6b9615ee6071b59947b978296a0d6b0a_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-ba92fcf5c320e9c9cb04d264d233ee86_720w.jpg?source=b6762063"], "favorite_count": 8, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1907718327259861976}", "attached_info": "CqIHCPq2yZP9xtXXnwEQBBoJNzI4MDAyMDg5IIT+qcEGKAwwDUBBSigKHVRTX1NPVVJDRV9ORUFSTElORV9DT05URU5UX1YyEgEwGAAgADoAWgg1NzU1Mjc5NGIgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyEzE5MDc3MTgzMjcyNTk4NjE5NzaKAQk0Mjg3MzA2NDeqAQlyZWNvbW1lbmTCASAyZGRjMTM0YjJkNmJjZGU4OWY4ODNlNGMwNTFmMWJjMvIBCggMEgZOb3JtYWzyASgIChIkYTRhNGJiYzYtNDgyYi00MGYzLThlMmQtNzdiYTQ1ZDkwNDFh8gEGCAsSAjExggIAiALKrLnN+jKSAiAyZGRjMTM0YjJkNmJjZGU4OWY4ODNlNGMwNTFmMWJjMpoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhZBY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIbSW50ZXJhY3Rpb25TaG9ySW50ZXJlc3RSdWxlygIYUGVyaW9kSW50ZXJlc3RXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxlygIUQ29udGVudEFnZVdlaWdodFJ1bGXKAhxCYXllc0ZpcnN0TGV2ZWxJc29sYXRpb25SdWxl2gIdVFNfU09VUkNFX05FQVJMSU5FX0NPTlRFTlRfVjLoAgT6AgtOT1JNQUxfRkxPV4oDIGRhNDlmMmE4YjQ0YjQ1YTNhZjI0ZTQ1OGFkMmY0MGIymgMNCgJ2MhAAGgVvdGhlcqgDxgPYAwD6A6wBEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERTotCAIQkgMYsgIiI3YyLWUzN2Y1YzY4ZTRlODdmN2IyZGMxYTU4ZWZmNWY2NDVhOi0IAhCwCRiWBSIjdjItZGI5N2NjOGUwM2FjNzc1MWE2MmY2NDE3ZTRjMjFhNzg6LQgDELgIGOEFIiN2Mi1jZjQwZWRkYzdhZmM5NmI2NGRjZTQ1YjMzZWJiOTNjZoAEAIgEAJIEBk5vcm1hbJoEATSgBACoBACwBAC6BAJhacIEAzQwMMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAoDDjxj+BBQAAAAAAAAAAiQVaEpUIpKzSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AULkAYAoAZFqAYAkgIuCgk3MjgwMDIwODkSEzE5MDc3MTgzMjcyNTk4NjE5NzYYBCIKSU1BR0VfVEVYVA==", "action_card": false}], "paging": {"is_end": false, "is_start": false, "next": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=down&ad_interval=-10&after_id=65&desktop=true&end_offset=69&page_number=12&session_token=5901993897b2b671534b41dfe1703947", "previous": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=pull&ad_interval=-10&before_id=65&desktop=true&end_offset=69&page_number=12&session_token=5901993897b2b671534b41dfe1703947", "totals": 0}, "fresh_text": "推荐已更新"}