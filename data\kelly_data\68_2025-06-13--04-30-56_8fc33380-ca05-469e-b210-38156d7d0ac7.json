{"data": [{"id": "1522894952910", "type": "feed", "target": {"id": "22248752", "title": "捐献骨髓（干细胞）真的对人体毫无损害吗？", "url": "https://api.zhihu.com/questions/22248752", "type": "question", "question_type": "normal", "created": 1386753339, "answer_count": 447, "comment_count": 45, "follower_count": 6721, "detail": "<p>最近学校组织入库，抽两管血就行了。我将此事告诉了爸妈，他们反应很大，说捐骨髓对身体有害。中华骨髓库宣传册上说对人体无害。<br/>请着重于科学角度，而非道德角度。</p><p>============分割线==============</p><p>感谢大家对此问题的关注。</p><p>还是解释一下吧。严格来讲这次活动的组织方是中华骨髓库。中华骨髓库来到我们学校号召我们抽血入库，并对捐献骨髓的相关知识进行宣传。学校只是对这个活动做了协助和宣传工作。抽血入库全凭自愿，学校并没有强制要求什么。</p><p>既然我选择了入库，当然是想有机会能与某个白血病配型成功，能够挽救一条生命。随后我将我的想法跟父母说了一下，他们坚决反对，意思是就算配型成功也不会同意让我捐献。他们文化程度不高，对捐骨髓的了解还停留在骨穿，也分不清骨髓和造血干细胞的区别。在我对此做了一番解释解释之后，他们还是认为捐骨髓对身体不好。然后我心中也有了一丝疑虑，捐骨髓是否真的对人体健康有害？搜索引擎中没有找到确切的答案，知乎上也没有相关的问题。于是我便提了这么个问题，希望能了解捐献骨髓对人体是否有损害，如果有，其程度如何。</p><p>比较赞同@曹文安 这句话，“比起动员周围的人都入库，不如在之前把风险说清楚。”希望此题能增加大家对捐献骨髓的了解。</p>", "excerpt": "最近学校组织入库，抽两管血就行了。我将此事告诉了爸妈，他们反应很大，说捐骨髓对…", "bound_topic_ids": [237, 2713, 8437, 18012, 20858], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1522894952, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1522783930211", "type": "feed", "target": {"id": "26541419", "title": "有哪些高端的人物访谈节目比较好？", "url": "https://api.zhihu.com/questions/26541419", "type": "question", "question_type": "normal", "created": 1415507566, "answer_count": 13, "comment_count": 0, "follower_count": 89, "detail": "采访一些企业高层、CEO、企业家、行业精英名流等等", "excerpt": "采访一些企业高层、CEO、企业家、行业精英名流等等", "bound_topic_ids": [52, 63, 627, 15920, 35398], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1522783930, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1521522788943", "type": "feed", "target": {"id": "24684319", "title": "在爱彼迎（Airbnb）工作是怎样一番体验？", "url": "https://api.zhihu.com/questions/24684319", "type": "question", "question_type": "normal", "created": 1406793985, "answer_count": 14, "comment_count": 3, "follower_count": 2778, "detail": "", "excerpt": "", "bound_topic_ids": [3282, 153588, 153611], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "ed1bfc56a72a7a237a33eb0ca490b598", "name": "<PERSON>", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/davidc", "url_token": "<PERSON><PERSON><PERSON>", "avatar_url": "https://pic1.zhimg.com/fd3218195b038222cf627bf380327ad8_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1521522788, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1521258593806", "type": "feed", "target": {"id": "34301270", "type": "article", "author": {"id": "0810194be3a5f2ba22860d382f435b73", "name": "ZZZZ", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/syzdemonhunter", "url_token": "syzdemonhunter", "avatar_url": "https://picx.zhimg.com/v2-686cd07e425925c3d18fdf854d025b04_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1520404354, "updated": 1520404354, "title": "A/B testing（一）：随机分配(Random Assignment)里的Why and How", "excerpt_title": "", "content": "<p data-pid=\"bNgQJOnb\">稍微对互联网A/B testing有一点了解的同学们应该都清楚，做A/B testing的时候希望把每一个用户随机分配到试验组(treatment group)和控制组(control group)。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"JETrstH5\">今天在这篇文章里，想简单聊聊，为什么我们要做随机分配(why we need to do random assignment)，和应该怎么做随机分配(how to do random assignment)。</p><hr/><p data-pid=\"m6wUsAEb\">------------------------------------------Part I-------------------------------------------</p><p data-pid=\"POsXiGQz\">先说说为什么要随机分配（Why）：</p><p data-pid=\"f_trSGIe\">从<b>因果推断</b>(<b>Causal Inference</b>)的<b>反事实模型</b>(<b>Counterfactual Model</b>)说起。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"cU10NfOK\">举一个例子：</p><p data-pid=\"ZnVBvdK-\">假设现在有8个用户(users) <img src=\"https://www.zhihu.com/equation?tex=%5C%7Bu_1%2C+u_2%2C%5Cldots%2Cu_8%5C%7D\" alt=\"\\{u_1, u_2,\\ldots,u_8\\}\" eeimg=\"1\"/> ，这里的用户在统计学中也被称为实验单元(Experimental Unit)，指直接在其上施加treatment的单元。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"7DOvZdgm\">令 <img src=\"https://www.zhihu.com/equation?tex=X\" alt=\"X\" eeimg=\"1\"/> 是一个binary indicator， 如果<img src=\"https://www.zhihu.com/equation?tex=X_i+%3D+1\" alt=\"X_i = 1\" eeimg=\"1\"/> ，把 <img src=\"https://www.zhihu.com/equation?tex=u_i\" alt=\"u_i\" eeimg=\"1\"/> 标记为试验组的成员；如果 <img src=\"https://www.zhihu.com/equation?tex=X_i+%3D+0\" alt=\"X_i = 0\" eeimg=\"1\"/> ，把 <img src=\"https://www.zhihu.com/equation?tex=u_i\" alt=\"u_i\" eeimg=\"1\"/> 标记为控制组成员。简单来说我们可以把 <img src=\"https://www.zhihu.com/equation?tex=X\" alt=\"X\" eeimg=\"1\"/> 看成一个硬币，正面标记为1反面标记为0，等轮到第 <img src=\"https://www.zhihu.com/equation?tex=i\" alt=\"i\" eeimg=\"1\"/> 个用户的时候我们丢一下硬币，如果是正面 <img src=\"https://www.zhihu.com/equation?tex=%28X%3D1%29\" alt=\"(X=1)\" eeimg=\"1\"/> 就把这个用户扔到试验组去，反之亦然。令 <img src=\"https://www.zhihu.com/equation?tex=Y_i\" alt=\"Y_i\" eeimg=\"1\"/> 是在第 <img src=\"https://www.zhihu.com/equation?tex=i\" alt=\"i\" eeimg=\"1\"/> 个实验单元 <img src=\"https://www.zhihu.com/equation?tex=u_i+\" alt=\"u_i \" eeimg=\"1\"/> 上得到的观测值。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"vwlfch6L\">这里我们再引入一对变量 <img src=\"https://www.zhihu.com/equation?tex=%28C_0%2C+C_1%29\" alt=\"(C_0, C_1)\" eeimg=\"1\"/> ， <img src=\"https://www.zhihu.com/equation?tex=C_%7B0i%7D+\" alt=\"C_{0i} \" eeimg=\"1\"/> 代表把用户 <img src=\"https://www.zhihu.com/equation?tex=i\" alt=\"i\" eeimg=\"1\"/> 分配到控制组后所得到的测量值， <img src=\"https://www.zhihu.com/equation?tex=C_%7B1i%7D\" alt=\"C_{1i}\" eeimg=\"1\"/> 代表把用户 <img src=\"https://www.zhihu.com/equation?tex=i\" alt=\"i\" eeimg=\"1\"/> 分配到试验组后所得到的测量值，所以我们有：</p><figure data-size=\"small\"><img src=\"https://pic2.zhimg.com/v2-177447f493787668ea2ed6ff4f067be5_1440w.jpg\" data-caption=\"\" data-size=\"small\" data-rawwidth=\"1084\" data-rawheight=\"476\" class=\"origin_image zh-lightbox-thumb\" width=\"1084\" data-original=\"https://pic2.zhimg.com/v2-177447f493787668ea2ed6ff4f067be5_r.jpg\" data-original-token=\"v2-177447f493787668ea2ed6ff4f067be5\"/></figure><p data-pid=\"azZHz5zh\">我们把这8个用户中的4个分到了试验组，剩下的4个留在了控制组，数据如下：</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-7f066d4c0207c11490a34543c8fc569c_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"336\" data-rawheight=\"350\" class=\"content_image\" width=\"336\" data-original-token=\"v2-7f066d4c0207c11490a34543c8fc569c\"/></figure><p data-pid=\"whg2-o-r\">在这个表里， <img src=\"https://www.zhihu.com/equation?tex=%2A\" alt=\"*\" eeimg=\"1\"/> 代表不存在的数据。仔细看一下，在这里用户1被分配到了控制组，所以此时 <img src=\"https://www.zhihu.com/equation?tex=Y+%3D+C_%7B0%7D+%3D+4\" alt=\"Y = C_{0} = 4\" eeimg=\"1\"/>， 这时，永远也无法观测到的<img src=\"https://www.zhihu.com/equation?tex=C_1\" alt=\"C_1\" eeimg=\"1\"/> 被称为<b>反事实</b>（<b>counterfactual</b>），因为要想观测到 <img src=\"https://www.zhihu.com/equation?tex=C_1\" alt=\"C_1\" eeimg=\"1\"/> ，我们必须要<b>反转用户1被分配到了控制组的这个事实</b>。同理，用户5被分配到了试验组， <img src=\"https://www.zhihu.com/equation?tex=Y+%3D+C_1+%3D+3\" alt=\"Y = C_1 = 3\" eeimg=\"1\"/> ，此时的 <img src=\"https://www.zhihu.com/equation?tex=C_0\" alt=\"C_0\" eeimg=\"1\"/> 也是一个<b>反事实</b>。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Pru2pNVZ\">所以我们可以把 <img src=\"https://www.zhihu.com/equation?tex=C_0\" alt=\"C_0\" eeimg=\"1\"/> 这个随机变量看成<b>一个世界</b>，在这个世界里面，所有的实验单位都被扔在了控制组，没有任何treatment会施加在他们身上。同理，可以把 <img src=\"https://www.zhihu.com/equation?tex=C_1\" alt=\"C_1\" eeimg=\"1\"/> 这个随机变量看成<b>另一个世界</b>，在这个世界里面，所有的实验单位都被扔在了试验组里，他们都会被施加 treatment。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"aEWoNnqA\">然后这两个的世界的差异，就是所谓的因果效应(<b>Causal Effect</b>)。在这里我们定义<b>平均因果效应</b>(<b>Average Causal Effect</b> 也叫做 <b>Average Treatment Effect</b>(<b>ATE</b>)):</p><p data-pid=\"QlaySiZ0\"><img src=\"https://www.zhihu.com/equation?tex=%5Cmbox%7BE%7D%28C_1%29+-+%5Cmbox%7BE%7D%28C_0%29+%3D+%5Cmbox%7BATE%7D\" alt=\"\\mbox{E}(C_1) - \\mbox{E}(C_0) = \\mbox{ATE}\" eeimg=\"1\"/> </p><p data-pid=\"cn2h-7Dr\">这个 <img src=\"https://www.zhihu.com/equation?tex=%5Cmbox%7BATE%7D\" alt=\"\\mbox{ATE}\" eeimg=\"1\"/> 就是我们想知道的东西，所以现在我们要做的事情就是找到这个 <img src=\"https://www.zhihu.com/equation?tex=%5Cmbox%7BATE%7D\" alt=\"\\mbox{ATE}\" eeimg=\"1\"/> 的估计量(Estimator)。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"L4qfvRge\">定义<b>相关性</b> (<b>association</b>)为：</p><p data-pid=\"SCyNOW11\"><img src=\"https://www.zhihu.com/equation?tex=%5Calpha+%3D%5Cmbox%7BE%7D%5BY+%5Cmid+X+%3D+1%5D+-+%5Cmbox%7BE%7D%5BY%7CX+%3D+0%5D\" alt=\"\\alpha =\\mbox{E}[Y \\mid X = 1] - \\mbox{E}[Y|X = 0]\" eeimg=\"1\"/> </p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Z_PgS6Th\"><b>定理</b>：假定我们丢硬币 <img src=\"https://www.zhihu.com/equation?tex=X\" alt=\"X\" eeimg=\"1\"/> 随机地把实验单元分配到试验组和控制组，这个硬币正的0面和1面的概率都是正的( <img src=\"https://www.zhihu.com/equation?tex=P%28X+%3D+0%29+%3E+0%2C+P%28X%3D1%29+%3E+0\" alt=\"P(X = 0) &gt; 0, P(X=1) &gt; 0\" eeimg=\"1\"/> )，那么 <img src=\"https://www.zhihu.com/equation?tex=%5Calpha+%3D+%5Cmbox%7BATE%7D\" alt=\"\\alpha = \\mbox{ATE}\" eeimg=\"1\"/> 。此时任何 <img src=\"https://www.zhihu.com/equation?tex=%5Calpha\" alt=\"\\alpha\" eeimg=\"1\"/> 的consistent估计量(consistant estimator)，都是 <img src=\"https://www.zhihu.com/equation?tex=%5Cmbox%7BATE%7D\" alt=\"\\mbox{ATE}\" eeimg=\"1\"/> 的consistent估计量(consistent estimator)。对 <img src=\"https://www.zhihu.com/equation?tex=%5Cmbox%7BATE%7D\" alt=\"\\mbox{ATE}\" eeimg=\"1\"/> 的一个最常用的估计量是:<img src=\"https://www.zhihu.com/equation?tex=%5Cwidehat%7B%5Cmbox%7BATE%7D%7D+%3D+%5Cwidehat%7B%5Cmbox%7BE%7D%7D%5BY%5Cmid+X+%3D+1%5D+-+%5Cwidehat%7B%5Cmbox%7BE%7D%7D%5BY%5Cmid+X+%3D+0%5D+%3D+%5Coverline%7BY%7D_1+-+%5Coverline%7BY%7D_0\" alt=\"\\widehat{\\mbox{ATE}} = \\widehat{\\mbox{E}}[Y\\mid X = 1] - \\widehat{\\mbox{E}}[Y\\mid X = 0] = \\overline{Y}_1 - \\overline{Y}_0\" eeimg=\"1\"/> 。在这里， <img src=\"https://www.zhihu.com/equation?tex=%5Coverline%7BY%7D_0+\" alt=\"\\overline{Y}_0 \" eeimg=\"1\"/> 是控制组中的 <img src=\"https://www.zhihu.com/equation?tex=Y\" alt=\"Y\" eeimg=\"1\"/> 的平均值， <img src=\"https://www.zhihu.com/equation?tex=%5Coverline%7BY%7D_1\" alt=\"\\overline{Y}_1\" eeimg=\"1\"/> 是试验组中 <img src=\"https://www.zhihu.com/equation?tex=Y+\" alt=\"Y \" eeimg=\"1\"/> 的平均值。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ZwOTOoXE\">证明：</p><p data-pid=\"bHa4Ep5z\">因为 <img src=\"https://www.zhihu.com/equation?tex=X\" alt=\"X\" eeimg=\"1\"/> 是随机地生成0和1的， 所以 <img src=\"https://www.zhihu.com/equation?tex=X\" alt=\"X\" eeimg=\"1\"/>和 <img src=\"https://www.zhihu.com/equation?tex=%28C_0%2C+C_1%29\" alt=\"(C_0, C_1)\" eeimg=\"1\"/> 并没什么关系，换种说法就是 <img src=\"https://www.zhihu.com/equation?tex=X+\" alt=\"X \" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=%28C_0%2C+C_1%29\" alt=\"(C_0, C_1)\" eeimg=\"1\"/> 这两个随机变量是独立的，我们有： <img src=\"https://www.zhihu.com/equation?tex=+%5Calpha+%3D+%5Cmbox%7BE%7D%28Y+%5Cmid+X+%3D+1%29+-+%5Cmbox%7BE%7D%28Y+%5Cmid+X+%3D+0%29+%3D+%5Cmbox%7BE%7D%28C_1+%5Cmid+X+%3D+1%29+-+%5Cmbox%7BE%7D%28C_0+%5Cmid+X+%3D+0%29+%3D+%5Cmbox%7BE%7D%28C_1%29+-+%5Cmbox%7BE%7D%28C_0%29+%3D%5Cmbox%7BATE%7D\" alt=\" \\alpha = \\mbox{E}(Y \\mid X = 1) - \\mbox{E}(Y \\mid X = 0) = \\mbox{E}(C_1 \\mid X = 1) - \\mbox{E}(C_0 \\mid X = 0) = \\mbox{E}(C_1) - \\mbox{E}(C_0) =\\mbox{ATE}\" eeimg=\"1\"/> </p><p data-pid=\"CM4JkFAJ\">一个比较直观的解释是，只有在随机分配下，才能“击穿”基于<img src=\"https://www.zhihu.com/equation?tex=X\" alt=\"X\" eeimg=\"1\"/> 的条件期望 <img src=\"https://www.zhihu.com/equation?tex=%5Cmbox%7BE%7D%28C_0%7CX%29\" alt=\"\\mbox{E}(C_0|X)\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=%5Cmbox%7BE%7D%28C_1+%5Cmid+X%29\" alt=\"\\mbox{E}(C_1 \\mid X)\" eeimg=\"1\"/> ，真正求出 <img src=\"https://www.zhihu.com/equation?tex=C_1\" alt=\"C_1\" eeimg=\"1\"/> 和 <img src=\"https://www.zhihu.com/equation?tex=C_0\" alt=\"C_0\" eeimg=\"1\"/> 的期望差，直接<b>剥离</b>出<b>两个世界</b>（<b>施加了treatment的世界</b>和<b>没有施加treatment的世界</b>）的<b>本质差异</b>，所谓<b>因果</b>。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Abt_Su8A\">这就是为什么在做A/B testing的时候，要把用户随机地分配到试验组和控制组，这样才能准确地知道，施加在试验组实验单元上面的treatment，到底是不是真的有用。</p><hr/><p data-pid=\"HNwJvzE3\">------------------------------------------Part II-------------------------------------------</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"5Fx3Rxj5\">好了说了一大堆公式定理，现在举一个例子介绍下工业中，A/B testing的随机分配是怎么做的。不同公司会有不同的办法，不过基本上大同小异：</p><p class=\"ztext-empty-paragraph\"><br/></p><ol><li data-pid=\"JrtnAxWt\">现在有一个user，他有一个user_id，这个user_id的值是一个数字；</li><li data-pid=\"ozVZAEMj\">每run一个实验，我们都有一个特定的salt，把这个user_id和这个salt拼接起来成一个长字符串；</li><li data-pid=\"RPzDY7D3\">把2中得到的长字符串扔进一个哈希函数(可以是MD5或者SHA1)，这里用MD5，然后生成一条哈希数据(Hashed data)；</li><li data-pid=\"IPzK0pbm\">取3中得到的哈希数据的头六位字符，转换成一个十六进制整数；</li><li data-pid=\"jiTZE8fM\">拿4中得到的整数去除以最大的六位十六进制数字(0xffffff)，注意用浮点数相除，会得到一个介于0和1之间的浮点数。</li><li data-pid=\"xbxOgRN6\">根据第5步得到的浮点数是否大于预设阈值，决定这个用户的分组。举个例子，如果我们想得到50-50的平均分配，那么我们会预先设定一个阈值0.5，如果第5步得到的是0.4，那么这个用户被分到控制组，因为它小于0.5，如果第5步得到的是0.6，这个用户被分配到试验组。</li></ol><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"PVUqnjkF\">看起来好啰嗦啊，不过这么做其实也是有一些理由的：</p><p class=\"ztext-empty-paragraph\"><br/></p><ol><li data-pid=\"_rlF06Ef\">这样做，实验可以复现(reproducible)，不仅方便精确定位用户，而且实验出问题了也方便debugging。</li><li data-pid=\"NLGaMduM\">一个实验对应一个salt，每个实验不一样，这样保证不同实验有完全不一样的随机分配。因为一家公司一天可能做很多实验，如果一个用户老是被分到试验组，他用户体验会比较那啥（多重treatment冲击！）</li><li data-pid=\"lxn4NFi8\">最后得到的随机分配结果还是比较&#39;Random&#39;的，虽然本质上都是假Random。</li></ol><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"lLsykh0m\">下面用Python对上面介绍得方法做一个模拟：</p><div class=\"highlight\"><pre><code class=\"language-python\"><span class=\"kn\">from</span> <span class=\"nn\">tqdm</span> <span class=\"kn\">import</span> <span class=\"n\">tqdm_notebook</span>\n<span class=\"kn\">import</span> <span class=\"nn\">hashlib</span>\n<span class=\"kn\">import</span> <span class=\"nn\">pandas</span> <span class=\"kn\">as</span> <span class=\"nn\">pd</span>\n<span class=\"kn\">import</span> <span class=\"nn\">scipy.stats</span>\n<span class=\"kn\">from</span> <span class=\"nn\">sklearn.metrics</span> <span class=\"kn\">import</span> <span class=\"n\">mutual_info_score</span>\n<span class=\"kn\">import</span> <span class=\"nn\">statsmodels.api</span> <span class=\"kn\">as</span> <span class=\"nn\">sm</span>\n<span class=\"kn\">import</span> <span class=\"nn\">numpy</span> <span class=\"kn\">as</span> <span class=\"nn\">np</span>\n<span class=\"kn\">from</span> <span class=\"nn\">matplotlib</span> <span class=\"kn\">import</span> <span class=\"n\">pyplot</span> <span class=\"k\">as</span> <span class=\"n\">plt</span>\n\n<span class=\"o\">%</span><span class=\"n\">matplotlib</span> <span class=\"n\">inline</span></code></pre></div><p class=\"ztext-empty-paragraph\"><br/></p><div class=\"highlight\"><pre><code class=\"language-python\"><span class=\"c1\"># 随机分配函数 ab_split</span>\n<span class=\"k\">def</span> <span class=\"nf\">ab_split</span><span class=\"p\">(</span><span class=\"n\">user_id</span><span class=\"p\">,</span> <span class=\"n\">salt</span><span class=\"p\">,</span> <span class=\"n\">control_group_size</span><span class=\"p\">):</span>\n    <span class=\"s1\">&#39;&#39;&#39;\n</span><span class=\"s1\">    Input: \n</span><span class=\"s1\">    user_id, salt, control_group_size(阈值)\n</span><span class=\"s1\">    \n</span><span class=\"s1\">    output:\n</span><span class=\"s1\">    &#39;t&#39; (for test) or &#39;c&#39; (for control), based on the ID and salt.\n</span><span class=\"s1\">    The control_group_size is a float, between 0 and 1, that sets how big the\n</span><span class=\"s1\">    control group is.\n</span><span class=\"s1\">    &#39;&#39;&#39;</span>\n    <span class=\"c1\"># 把用户ID和salt粘贴起来生成一个场字符串test_id</span>\n    <span class=\"n\">test_id</span> <span class=\"o\">=</span> <span class=\"nb\">str</span><span class=\"p\">(</span><span class=\"n\">user_id</span><span class=\"p\">)</span> <span class=\"o\">+</span> <span class=\"s1\">&#39;-&#39;</span> <span class=\"o\">+</span> <span class=\"nb\">str</span><span class=\"p\">(</span><span class=\"n\">salt</span><span class=\"p\">)</span>\n    \n    <span class=\"c1\"># 用哈希函数MD5来hash test_id，生成hased data：test_id_hased</span>\n    <span class=\"n\">test_id_hased</span> <span class=\"o\">=</span> <span class=\"n\">hashlib</span><span class=\"o\">.</span><span class=\"n\">md5</span><span class=\"p\">(</span><span class=\"n\">test_id</span><span class=\"o\">.</span><span class=\"n\">encode</span><span class=\"p\">(</span><span class=\"s1\">&#39;ascii&#39;</span><span class=\"p\">))</span><span class=\"o\">.</span><span class=\"n\">hexdigest</span><span class=\"p\">()</span>\n    \n    <span class=\"c1\"># 取test_id_hased的头6位数得到test_id_first_digits</span>\n    <span class=\"n\">test_id_first_digits</span> <span class=\"o\">=</span> <span class=\"n\">test_id_hased</span><span class=\"p\">[:</span><span class=\"mi\">6</span><span class=\"p\">]</span>\n    \n    <span class=\"c1\"># 把test_id_first_digits转化成整数 test_id_final_int</span>\n    <span class=\"n\">test_id_final_int</span> <span class=\"o\">=</span> <span class=\"nb\">int</span><span class=\"p\">(</span><span class=\"n\">test_id_first_digits</span><span class=\"p\">,</span> <span class=\"mi\">16</span><span class=\"p\">)</span>\n    \n    <span class=\"c1\"># 用浮点数除法，test_id_final_in 除以最大的六位十六进制数FFFFFF，得到决定数据分配结果的的浮点数ab_split</span>\n    <span class=\"n\">ab_split</span> <span class=\"o\">=</span> <span class=\"p\">(</span><span class=\"n\">test_id_final_int</span><span class=\"o\">/</span><span class=\"mh\">0xFFFFFF</span><span class=\"p\">)</span>\n    \n    <span class=\"c1\"># 如果ab_split大于阈值control_group_size，则分配该user到实验组</span>\n    <span class=\"k\">if</span> <span class=\"n\">ab_split</span> <span class=\"o\">&gt;</span> <span class=\"n\">control_group_size</span><span class=\"p\">:</span>\n        <span class=\"k\">return</span> <span class=\"s1\">&#39;t&#39;</span>\n    \n    <span class=\"c1\"># 反之，分配该user到控制组</span>\n    <span class=\"k\">else</span><span class=\"p\">:</span>\n        <span class=\"k\">return</span> <span class=\"s1\">&#39;c&#39;</span></code></pre></div><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"vCIRhfdY\">产生10000个用户，并且把他们随机分配到到控制组(c) 和实验组 (t):</p><div class=\"highlight\"><pre><code class=\"language-python\"><span class=\"c1\"># 生成10000个用户的数据框users，用户的id为0-9999</span>\n<span class=\"n\">users</span> <span class=\"o\">=</span> <span class=\"n\">pd</span><span class=\"o\">.</span><span class=\"n\">DataFrame</span><span class=\"p\">({</span><span class=\"s1\">&#39;id&#39;</span><span class=\"p\">:</span> <span class=\"n\">np</span><span class=\"o\">.</span><span class=\"n\">arange</span><span class=\"p\">(</span><span class=\"mi\">100</span><span class=\"o\">**</span><span class=\"mi\">2</span><span class=\"p\">)})</span>\n\n<span class=\"c1\"># 把用户随机分配到控制组(&#39;c&#39;) 和实验组 (&#39;t&#39;)</span>\n<span class=\"c1\"># 这里的阈值设为0.5，salt为&#39;ticket-3&#39;</span>\n<span class=\"n\">users</span><span class=\"p\">[</span><span class=\"s1\">&#39;test_group&#39;</span><span class=\"p\">]</span> <span class=\"o\">=</span> <span class=\"n\">users</span><span class=\"o\">.</span><span class=\"n\">id</span><span class=\"o\">.</span><span class=\"n\">apply</span><span class=\"p\">(</span><span class=\"k\">lambda</span> <span class=\"nb\">id</span><span class=\"p\">:</span> <span class=\"n\">ab_split</span><span class=\"p\">(</span><span class=\"nb\">id</span><span class=\"p\">,</span> <span class=\"s1\">&#39;ticket-3&#39;</span><span class=\"p\">,</span> <span class=\"mf\">0.5</span><span class=\"p\">))</span>\n\n<span class=\"c1\"># 看一下users数据框的头五行</span>\n<span class=\"k\">print</span><span class=\"p\">(</span><span class=\"n\">users</span><span class=\"o\">.</span><span class=\"n\">head</span><span class=\"p\">())</span></code></pre></div><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-b1e1b2eb0ad9829a3b02e2ee9852f2c6_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"136\" data-rawheight=\"169\" class=\"content_image\" width=\"136\" data-original-token=\"v2-b1e1b2eb0ad9829a3b02e2ee9852f2c6\"/></figure><p data-pid=\"HcQeC8lX\">看一下分配到试验组的用户在总体样本内的百分比，是不是在它的95%置信区间里面：</p><div class=\"highlight\"><pre><code class=\"language-python\"><span class=\"n\">dist</span> <span class=\"o\">=</span> <span class=\"n\">scipy</span><span class=\"o\">.</span><span class=\"n\">stats</span><span class=\"o\">.</span><span class=\"n\">binom</span><span class=\"p\">(</span><span class=\"n\">n</span><span class=\"o\">=</span><span class=\"mi\">10000</span><span class=\"p\">,</span> <span class=\"n\">p</span><span class=\"o\">=</span><span class=\"mf\">0.5</span><span class=\"p\">)</span>\n<span class=\"k\">print</span><span class=\"p\">(</span><span class=\"s1\">&#39;treatment group proportion &#39;</span><span class=\"p\">,</span> <span class=\"p\">(</span><span class=\"n\">users</span><span class=\"o\">.</span><span class=\"n\">test_group</span> <span class=\"o\">==</span> <span class=\"s1\">&#39;t&#39;</span><span class=\"p\">)</span><span class=\"o\">.</span><span class=\"n\">mean</span><span class=\"p\">())</span>\n\n<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">plot</span><span class=\"p\">(</span><span class=\"n\">np</span><span class=\"o\">.</span><span class=\"n\">arange</span><span class=\"p\">(</span><span class=\"mi\">4500</span><span class=\"p\">,</span> <span class=\"mi\">5500</span><span class=\"p\">),</span> <span class=\"n\">dist</span><span class=\"o\">.</span><span class=\"n\">pmf</span><span class=\"p\">(</span><span class=\"n\">np</span><span class=\"o\">.</span><span class=\"n\">arange</span><span class=\"p\">(</span><span class=\"mi\">4500</span><span class=\"p\">,</span> <span class=\"mi\">5500</span><span class=\"p\">)))</span>\n<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">title</span><span class=\"p\">(</span><span class=\"s1\">&#39;Probability of observing a particular size of the control group</span><span class=\"se\">\\n</span><span class=\"s1\">&#39;</span>\n      <span class=\"s1\">&#39;The blue line shows our observed case</span><span class=\"se\">\\n</span><span class=\"s1\">&#39;</span>\n      <span class=\"s1\">&#39;The red lines show the 95% probability bounds</span><span class=\"se\">\\n</span><span class=\"s1\">&#39;</span><span class=\"p\">)</span>\n<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">xlabel</span><span class=\"p\">(</span><span class=\"s1\">&#39;Size of control group&#39;</span><span class=\"p\">)</span>\n\n<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">axvline</span><span class=\"p\">((</span><span class=\"n\">users</span><span class=\"o\">.</span><span class=\"n\">test_group</span> <span class=\"o\">==</span> <span class=\"s1\">&#39;t&#39;</span><span class=\"p\">)</span><span class=\"o\">.</span><span class=\"n\">sum</span><span class=\"p\">(),</span> <span class=\"n\">c</span><span class=\"o\">=</span><span class=\"s1\">&#39;black&#39;</span><span class=\"p\">)</span>\n<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">axvline</span><span class=\"p\">(</span><span class=\"n\">dist</span><span class=\"o\">.</span><span class=\"n\">isf</span><span class=\"p\">(</span><span class=\"mf\">0.95</span><span class=\"p\">),</span> <span class=\"n\">c</span><span class=\"o\">=</span><span class=\"s1\">&#39;red&#39;</span><span class=\"p\">)</span>\n<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">axvline</span><span class=\"p\">(</span><span class=\"n\">dist</span><span class=\"o\">.</span><span class=\"n\">isf</span><span class=\"p\">(</span><span class=\"mf\">0.05</span><span class=\"p\">),</span> <span class=\"n\">c</span><span class=\"o\">=</span><span class=\"s1\">&#39;red&#39;</span><span class=\"p\">)</span>\n<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">show</span><span class=\"p\">()</span></code></pre></div><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-59bec377c570305c47fd0e7d2ff4ea01_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"423\" data-rawheight=\"362\" class=\"origin_image zh-lightbox-thumb\" width=\"423\" data-original=\"https://picx.zhimg.com/v2-59bec377c570305c47fd0e7d2ff4ea01_r.jpg\" data-original-token=\"v2-59bec377c570305c47fd0e7d2ff4ea01\"/></figure><p data-pid=\"1rktmv4I\">我们可以看到0.4965确实是在它的95%置信区间里面，所以我们50-50分配还算均匀。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"g31WYD4a\">最后我们想知道我们用这个随机分配函数ab_split搞出来的分组到底能不能通过随机性测试，统计学里面有一个Run Test可以验证这件事情：</p><div class=\"highlight\"><pre><code class=\"language-python\"><span class=\"k\">print</span><span class=\"p\">(</span><span class=\"n\">sm</span><span class=\"o\">.</span><span class=\"n\">stats</span><span class=\"o\">.</span><span class=\"n\">Runs</span><span class=\"p\">((</span><span class=\"n\">users</span><span class=\"o\">.</span><span class=\"n\">test_group</span> <span class=\"o\">==</span> <span class=\"s1\">&#39;t&#39;</span><span class=\"p\">)</span><span class=\"o\">.</span><span class=\"n\">values</span><span class=\"o\">.</span><span class=\"n\">astype</span><span class=\"p\">(</span><span class=\"s1\">&#39;int&#39;</span><span class=\"p\">))</span><span class=\"o\">.</span><span class=\"n\">runs_test</span><span class=\"p\">()[</span><span class=\"mi\">1</span><span class=\"p\">])</span>\n<span class=\"c1\"># 0.987951208118</span></code></pre></div><p data-pid=\"3l0RUqDR\">就这个例子来看 <img src=\"https://www.zhihu.com/equation?tex=p\" alt=\"p\" eeimg=\"1\"/>-value是0.9879，自然是没法推翻（分配是随机的）这个原假设。：）</p><p class=\"ztext-empty-paragraph\"><br/></p><hr/><p data-pid=\"xbRrvV4T\">总结下，今天扯了这么多，这篇文章其实就是干了两件事情：</p><ol><li data-pid=\"8xrkVjnV\">用因果推断证明了在A/B testing中随机分配的重要性。</li><li data-pid=\"I5sJrGOs\">简单介绍了一个在工业界中A/B testing做随机分配的方法。</li></ol><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"CSXXMApZ\">Reference<b>：</b></p><p data-pid=\"288O1qc7\">[1] Wasserman, Larry. <i>All of statistics: a concise course in statistical inference</i>. Springer Science &amp; Business Media, 2013.</p><p data-pid=\"krVqfUhD\">[2] PSU STAT 414/415: <a href=\"https://link.zhihu.com/?target=https%3A//onlinecourses.science.psu.edu/stat414/node/233\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Run Test and Test for Randomness</a></p><p data-pid=\"MMZyTJkq\">[3] <a href=\"https://link.zhihu.com/?target=http%3A//blog.richardweiss.org/2016/12/25/hash-splits.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Splitting with hashing - Experiments in data science</a></p><p data-pid=\"GV4ZZFHh\">[4] Kohavi, Ron, et al. &#34;Controlled experiments on the web: survey and practical guide.&#34; <i>Data mining and knowledge discovery</i> 18.1 (2009): 140-181.</p>", "excerpt": "稍微对互联网A/B testing有一点了解的同学们应该都清楚，做A/B testing的时候希望把每一个用户随机分配到试验组(treatment group)和控制组(control group)。 今天在这篇文章里，想简单聊聊，为什么我们要做随机分配(why we need to do random assignment)，和应该怎么做随机分配(how to do random assignment)。 ------------------------------------------Part I------------------------------------------- 先说说为什么要随…", "excerpt_new": "稍微对互联网A/B testing有一点了解的同学们应该都清楚，做A/B testing的时候希望把每一个用户随机分配到试验组(treatment group)和控制组(control group)。 今天在这篇文章里，想简单聊聊，为什么我们要做随机分配(why we need to do random assignment)，和应该怎么做随机分配(how to do random assignment)。 ------------------------------------------Part I------------------------------------------- 先说说为什么要随…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/34301270", "comment_permission": "censor", "voteup_count": 161, "comment_count": 3, "image_url": "https://picx.zhimg.com/v2-6698882bc819758e121f3c61e3835759_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1521258593, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1519520611603", "type": "feed", "target": {"id": "33987753", "type": "article", "author": {"id": "3e639baeea24916200a222df2215ac50", "name": "YING zz", "headline": "语言的瘾者", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/bai-niao-35-78", "url_token": "b<PERSON><PERSON><PERSON>ao-35-78", "avatar_url": "https://pic1.zhimg.com/v2-36c8ee7f7bb38ff32e18e89a795db385_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1519480790, "updated": 1519493826, "title": "Mercari Price 比赛分享——语言不仅是算法和公式而已", "excerpt_title": "", "content": "<p></p><p></p><p data-pid=\"f6Wyifsz\">  最近半年一直在忙于各种nlp比赛，除夕因为kaggle的price写到凌晨3点，最后靠rp爬回季军，也算圆了一个solo gold的梦想。这应该是我2017下半年玩到的最有意思的一场比赛了，赛内赛外都学到很多。kernel上有很多优秀的解决方案，有同学觉得我注释太少（源码在此<a href=\"https://link.zhihu.com/?target=https%3A//www.kaggle.com/whitebird/mercari-price-3rd-0-3905-cv-at-pb-in-3300-s\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://www.</span><span class=\"visible\">kaggle.com/whitebird/me</span><span class=\"invisible\">rcari-price-3rd-0-3905-cv-at-pb-in-3300-s</span><span class=\"ellipsis\"></span></a> ），这里聊一下这个比赛，正好也说说nlp这个领域的二三事。</p><p data-pid=\"D-ncDgFi\">  price题目非常简单，是一个通过二手商品的名字类型及用户描述来预测一个定价的场景。有趣的地方在于主办方为比赛做了一个很大的限制，让参赛者所有的方案必须在线上的docker（16gb内存+1g硬盘+4 core cpu）中60分钟内完成预处理训练及预测。所以以往疯狂融合模型的套路走不通了，大家在同一起跑线拼的就是对数据和机器学习模型的理解。比赛的ab榜几乎没有shake，可以说是非常良心的比赛。</p><p data-pid=\"PsNTUNvd\">  思路：</p><p data-pid=\"ywRuk1sE\">  既然是限时赛，那么我们要做的事情就是通过人的先验知识去为模型铺好道路让它沿着最优的梯度一路滑下去。商品定价回归不同于文本分类，并不是简单的截取单个关键字就可以进行判断，而是由<b>关键词之间会有强烈的互作用力</b>：比如苹果+手机产生的价格远远高于他们各自价格相加，所以奠定了FM会是隐层中一个非常有效的回归工具。而且二手市场大部分都是女性用户，冗长的文本拥有大量信息，那么输入端提取特征时nn的embedding也是一个很好的选择。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wZ1Xa2xp\">  预处理部分：</p><p data-pid=\"6MACOQ2Z\">  预处理占据了代码的70%，但却是最不提分的部分...一个优秀的模型奠定了你的baseline，预处理只能给你锦上添花。如果不是参加比赛的选手其实可以忽略冗长的预处理：基本就是给拼写错误的单词纠正，干掉奇怪的符号，把法语字母翻译成英文字母等。</p><p data-pid=\"1_1US1cz\">  提一句英文单词有个很有意思的点：词的多态。显然apple和apples、text和txt是相近的意思，但大部分tokenizer都会把它分成两个。这个问题值得细细琢磨，我个人倾向通过词根和词源演化来解决这个问题，所以用了注重子串复现的翻译评价指标bleu去归并了词语，有兴趣可以自行了解下这个原理。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"-g4559A1\">  NN系列模型    </p><p data-pid=\"drdgI1Ii\">  最早我采用的是一个nn模型，模型结构如下：</p><div class=\"highlight\"><pre><code class=\"language-text\">la= []    \ndef gauss_init():\n    return RandomNormal(mean=0.0, stddev=0.005)\ndef conv1d_maxpool_flatten(filters, kernel_size, pool_size, input):\n    return Flatten()(MaxPooling1D(pool_size)(Conv1D(filters, kernel_size, activation=&#39;sigmoid&#39;, padding=&#39;same&#39;)(input))\n\ninput1 = Input(shape=(sub_featlen.shape[1],), dtype=&#39;float32&#39;)\ninput2 = Input(shape=(1,), dtype=&#39;int32&#39;)\ninput3 = Input(shape=(FEAT_LENGTH-FIX_LEN,), dtype=&#39;int32&#39;)\ninput4 = Input(shape=(FEAT_LENGTH2,), dtype=&#39;int32&#39;)\n\nla.append(Flatten()(Embedding(index3, 30, init=gauss_init(), input_length=1, trainable=True)(input2)))\n\nx31 = Cropping1D(cropping=(0,40))(Embedding(wordnum, 40, init=gauss_init(), trainable=True)(input3))\nla.append(conv1d_maxpool_flatten(15, 3, FEAT_LENGTH-FIX_LEN, x31))\nla.append(conv1d_maxpool_flatten(50, 2, FEAT_LENGTH-FIX_LEN, x31))\n\nembedding_layer1 = Embedding(wordnum, 160, init=gauss_init(), trainable=True)\nla.append(Attention(50)(Cropping1D(cropping=(0,80))(embedding_layer1(input3))))\nla.append(Attention(50)(embedding_layer1(input4)))\n\nla.append(conv1d_maxpool_flatten(55, 2, FEAT_LENGTH2,\n    Cropping1D(cropping=(0,50))(Embedding(wordnum, 50, init=gauss_init(), trainable=True)(input4))))\n\nx1 = BatchNormalization()(merge(la+[input1],mode = &#39;concat&#39;))\nx1 = Dropout(0.02)(merge([Dense(30, activation=&#39;sigmoid&#39;)(x1),\n                          PReLU()(Dense(470)(x1))], mode=&#39;concat&#39;))\nx1 = Dropout(0.02)(merge([Dense(256, activation=&#39;sigmoid&#39;)(x1),\n                          Dense(11, activation=&#39;linear&#39;)(x1),\n                          PReLU()(Dense(11)(x1))], mode=&#39;concat&#39;))\nout = merge([Dense(1, activation=&#39;linear&#39;)(x1),\n             Dense(1, activation=&#39;relu&#39;)(x1),\n             MaxoutDense(1, 30)(x1)], mode=&#39;sum&#39;)\n\nmodel = Model(input=[input1,input2,input3,input4], output=out)</code></pre></div><p data-pid=\"2KAlM0CL\">  四个input分别代表一些普通数值特征、商品分类embedding、商品名称+商标的短文本以及商品详细信息长文本。普通特征用Dense处理、文本使用cnn+attention。</p><p data-pid=\"RQvNFukE\">  为了保证效率，有几个高光的细节：</p><p data-pid=\"UPdro1F7\">  1.文本的处理：首先放弃了lstm使用快速的cnn和attention，让模型训练在cpu下也可以非常快的处理embedding。特征方面把商品名字和商品商标放在一起作为一个短文本，我之前有一篇博客（<a href=\"https://zhuanlan.zhihu.com/p/29394867\" class=\"internal\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"visible\">zhuanlan.zhihu.com/p/29</span><span class=\"invisible\">394867</span><span class=\"ellipsis\"></span></a>）专门谈过这个加速训练的方案，比赛后我惊讶地发现这个思路很多top选手也用了,殊途同归。</p><p data-pid=\"R187JCnJ\">  2.模型训练为了极致优化时间，我甚至将每个ep的batchsize和optimizer手动控制，让前期的batchsize小lr大——小步迅速迭代更新，后期batchsize大lr小——finetune。为了让参数最优，几乎所有参数都是靠不断猜想尝试+盯着loss的每一跳感受模型的梯度下降调整的...养成盯进度条的习惯有好处的。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-22800ff689576d10e9089d5763f8790b_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1293\" data-rawheight=\"902\" class=\"origin_image zh-lightbox-thumb\" width=\"1293\" data-original=\"https://picx.zhimg.com/v2-22800ff689576d10e9089d5763f8790b_r.jpg\" data-original-token=\"v2-22800ff689576d10e9089d5763f8790b\"/><figcaption>batchsize控制</figcaption></figure><p data-pid=\"wNjBrpaZ\">  3.大量运用了concat不同激活函数的方式。本来一个relu的结构，我习惯用一个relu和sigmoid拼接，甚至再加一个linear。我认为每一层的隐层特征里面既有表达是与否的二分类特征也有表达程度的量特征，用任何一个激活函数都是会有信息丢失，最合适的方式是让模型的梯度自行选择合适的激活函数。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-6820460f1244a389db0b5743a56c96ca_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"546\" data-rawheight=\"177\" class=\"origin_image zh-lightbox-thumb\" width=\"546\" data-original=\"https://pica.zhimg.com/v2-6820460f1244a389db0b5743a56c96ca_r.jpg\" data-original-token=\"v2-6820460f1244a389db0b5743a56c96ca\"/><figcaption>Dense concat操作</figcaption></figure><p data-pid=\"yGlhBc2Z\">  另外优秀的NN模型还有第四名的XNN以及第一名的无embedding的文本处理NN。</p><p data-pid=\"Xek8tfSo\">  第四名的XNN结构很复杂这里就不细说，各位可以去阅读他的github（<a href=\"https://link.zhihu.com/?target=https%3A//github.com/ChenglongChen/tensorflow-XNN\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/ChenglongChe</span><span class=\"invisible\">n/tensorflow-XNN</span><span class=\"ellipsis\"></span></a> ），杂揉了cnn、attention、attend、fm等思路，是一个惊艳的nlp解决方案。</p><p data-pid=\"KNTElNY_\">  第一名的NN模型完全舍弃了cnn，用ngram先提取了所有关键词然后tfidf直接扔给模型，效果非常惊人，仅仅用半个小时就远高于现在第二名的成绩，只能说state of the art。精致的80行代码，里面满满的都是对于数据的理解。所谓艺术品对于新手来说如果看不到他雕刻成型的过程，那是很难学习到东西的，不建议新手学习。。。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"QTDblHeA\">  FM系列模型：</p><p data-pid=\"R3K7rU1d\">  一个NN对这个比赛来说是不够的，当我把我的NN压在40分钟左右时，我开始做第二个模型。这时刚巧anttip开源了他的wordbatch库（<a href=\"https://link.zhihu.com/?target=https%3A//github.com/anttttti/Wordbatch\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/anttttti/Wor</span><span class=\"invisible\">dbatch</span><span class=\"ellipsis\"></span></a> ），我把它稍稍做了修改（将部分特征tfidf），作为次模型和NN加权。学会FM的使用是这个比赛给我最大的收获，以前从未想过FM可以在文本处理任务上这么优秀。</p><p data-pid=\"TDvaD6Ij\">  不得不说anttip的ftrl-fm写的非常漂亮，建议大家有兴趣的去拜读一下他的cython代码。FM系列算法在文本处理场景的好处是可以处理上百万维的特征，而embedding+cnn最多能提取上千到上万个关键词。相比之下FM更加迅速暴力，但缺点是不像NN一样可以有3-5个隐层处理特征关联，潜力不足。但个人认为在一些简单的工作生产环境，如商品短文本识别推荐等，高效率的ftrl-fm比NN更加适合。 </p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"s4S7-zFh\">  lgb系列模型：</p><p data-pid=\"sApBmlvL\">  这个是我没有尝试的，因为在文本场景lgb表达力操作感上来说远弱于NN，速度上又逊于FM。。。</p><hr/><p data-pid=\"i3HZJqKx\"><b><i>--------------分割线---------------</i></b></p><p data-pid=\"yuVcWLct\">  比赛说到这里，最后随便说点算法之外的事吧。2017年对我来说无论是在工作还是比赛上写代码，像极了wow那几年打raid的日子。</p><p data-pid=\"1AK37bEq\">  最大的感触是：Things change. 工作、比赛与游戏一样充斥着版本的更替。不断有更加优秀的方案和模型涌现，就像一个个小资料片，瞬间就把以前的方案踩在脚下。</p><p data-pid=\"Xbw066ij\">  版本伊始，早期玩家带着任务装备开荒，战场牌子副本牌子每个cd拼命的刷，种族声望日常任务没有断过。</p><p data-pid=\"sibQrDiF\">  新版本的一切如公众号上热门的那些技术文：计算机视觉、自然语言、自动驾驶、强化学习；各个平台的比赛：天池的科学家积分、kaggle的master成就。日常论文要读，比赛牌子要刷。       </p><p data-pid=\"l0iTHH5-\">  就在休闲玩家刚刚意识到版本的稀缺职业和材料的时候，高玩已经开着g团赚够了g。老板终于砸锅卖铁刷出毕业装的时候，新版本预告片出来了。晚期的紫装贬值，属性还不如下个版本的入门蓝绿。暴雪大手一挥，辛辛苦苦刷了几个月的毕业装和货币化为废土。</p><p data-pid=\"KQORXoVI\">  人生并没有在你艰辛努力后考上大学、买房、成家、打败lich king的那一刻走到幸福的终点，而是从零开始了另一个篇章。NN神经网络和lgb梯度森林已经快把传统的LR、SVM的价值挤压殆尽了。转瞬即逝的比特币已经成为版本弃儿，而这个版本极度稀有的房产和算法科学，两年后的下个版本会不会烂在拍卖行？</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-3eaf4040b8e829945e0a93eb8919508c_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"750\" data-rawheight=\"500\" class=\"origin_image zh-lightbox-thumb\" width=\"750\" data-original=\"https://pica.zhimg.com/v2-3eaf4040b8e829945e0a93eb8919508c_r.jpg\" data-original-token=\"v2-3eaf4040b8e829945e0a93eb8919508c\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"2vXXmfp1\">  没有什么事物抵挡得住时代变迁，除了人。知识会过时，想象力和好奇心不会；算法会被淘汰，工程和学术能力不会。并没有人告诉我们神经网络或者梯度森林什么时候会过时，但我想如果有心把coding做到极致，不妨在提升这些算法熟练度的同时，多做一些算法之外的东西。从在研究Word2vec的时候想起阅读古老的词源学演化开始，从看到埃舍尔的版画能联想到CNN的kernel之间互相影响开始，从看到壮丽的建筑感受到美妙的结构优化开始。</p><p data-pid=\"L3t3AkfV\">  nlp是个非常有意思的领域，至今为止我常常在阅读或者与人交谈时，思考人为什么如此组织语言，词汇在我脑海中以怎样的方式形成话语。正是因为这种对抽象和感性的迷惑促使了近年来这个领域不断出现新的想法。人类拥有动物的兽性，又在长远的进化路上获得了理智。兽性本是天生，生物学可以解释。现在是时候尝试了解陌生的理智了。</p><p data-pid=\"LBd_IKl5\">  最后祝各位狗年运势昌隆，竞赛有成。</p>", "excerpt": "最近半年一直在忙于各种nlp比赛，除夕因为kaggle的price写到凌晨3点，最后靠rp爬回季军，也算圆了一个solo gold的梦想。这应该是我2017下半年玩到的最有意思的一场比赛了，赛内赛外都学到很多。kernel上有很多优秀的解决方案，有同学觉得我注释太少（源码在此 <a href=\"https://link.zhihu.com/?target=https%3A//www.kaggle.com/whitebird/mercari-price-3rd-0-3905-cv-at-pb-in-3300-s\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">https://www.http://kaggle.com/whitebird/mercari-price-3rd-0-3905-cv-at-pb-in-3300-s</a> ），这里聊一下这个比赛，正好也说说nlp这个领域的二三事。 price题目非常简单…", "excerpt_new": "最近半年一直在忙于各种nlp比赛，除夕因为kaggle的price写到凌晨3点，最后靠rp爬回季军，也算圆了一个solo gold的梦想。这应该是我2017下半年玩到的最有意思的一场比赛了，赛内赛外都学到很多。kernel上有很多优秀的解决方案，有同学觉得我注释太少（源码在此 <a href=\"https://link.zhihu.com/?target=https%3A//www.kaggle.com/whitebird/mercari-price-3rd-0-3905-cv-at-pb-in-3300-s\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">https://www.http://kaggle.com/whitebird/mercari-price-3rd-0-3905-cv-at-pb-in-3300-s</a> ），这里聊一下这个比赛，正好也说说nlp这个领域的二三事。 price题目非常简单…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/33987753", "comment_permission": "all", "voteup_count": 173, "comment_count": 50, "image_url": "https://pica.zhimg.com/v2-afcb6f2ef055a3fca5be52a1dac469b3_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1519520611, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1519326017985", "type": "feed", "target": {"id": "146182692", "type": "answer", "url": "https://api.zhihu.com/answers/146182692", "voteup_count": 57322, "thanks_count": 12729, "question": {"id": "25569759", "title": "为什么很多欧洲人和美国人不午睡下午还能满血状态工作？", "url": "https://api.zhihu.com/questions/25569759", "type": "question", "question_type": "normal", "created": 1411928537, "answer_count": 3586, "comment_count": 257, "follower_count": 40871, "detail": "很多回答里提到了咖啡，这么大量喝咖啡不会对健康产生损害吗？", "excerpt": "很多回答里提到了咖啡，这么大量喝咖啡不会对健康产生损害吗？", "bound_topic_ids": [472, 763, 7973, 10203, 12585], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1517466742, "created_time": 1486990005, "author": {"id": "c30da47ed9b7ffd016d6be788e75149d", "name": "蔡明航", "headline": "一不小心滑了12年单板", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/henrycai", "url_token": "he<PERSON><PERSON><PERSON><PERSON>", "avatar_url": "https://picx.zhimg.com/v2-30470d1c502dfee02b6ae74e438eb542_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "censor", "is_copyable": false, "comment_count": 1066, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"eEB0rZ3Z\">在美帝生活10多年，对比白人和亚洲朋友，可以肯定的是<b>午睡和饮食结构有很大关系</b>。</p><p data-pid=\"NrdbAMX_\">刚开始工作的时候，我常常会和同事们一起出去吃午餐，有几个白人同事每次都拒绝吃中餐或者其他亚洲食物。一开始的时候我还以为是同事们事逼，没有尝试新鲜东西的心态。后来混熟了，他们跟我说不敢吃中餐的主要原因是每次吃完都有<b>food coma</b>（没找到翻译，暂且称之为<b>“饭困”</b>吧），影响工作（他们常常用来形容亚洲食物的词是“heavy”）。记得有一次午饭带他们去吃日本拉面，下午1点的会议，那叫一个无精打采（答主也未能幸免）。除了中餐，日本拉面，印度饭制造“<b>饭困”</b>的能力也是杠杠的。后来我们几个约定是不太忙的周五中午去吃这些让人发困但无比好吃的亚洲美食（然后回来喝巨多咖啡 ）</p><p data-pid=\"GtDSDx6b\">老美的午餐，普遍<b>碳水化合物的比例偏低</b>。很多中午健身的美国人，午餐吃的就只有蛋白、蔬菜和水果。即便是Burger和三明治一类的，那个白面包的重量基本也是可以忽略的。</p><p data-pid=\"Xnl3l6qH\">相比之下，中餐里的米饭、白面含量高(动辄3两的量)。大量的精米和精面会瞬间转化为糖，而血糖的升高很容易导致人乏困。我有几个特别能吃米、面的朋友，他们有时候甚至晚餐以后都需要小憩一会。而那反观些精力比较旺盛的朋友，通常都不怎么吃碳水化合物。</p><p data-pid=\"j1HBbROc\">当然，除了饮食结构，天生的基因和锻炼也很有关系。</p><br/><p data-pid=\"iJuSYvyr\">----------------------------------------------------------------------------------------------------</p><p data-pid=\"hHLGh3xo\">二次更新：被知友们的热情打动了，知友们的评论也是干货满满。</p><p data-pid=\"D2_9HjYf\">应部分知友要求，把我最后面的那句话挪到最前面了，那就是</p><p data-pid=\"AoELowIq\"><b>“如果吃得多，啥也拯救不了饭困”</b></p><p data-pid=\"rzaTdt27\">的确，如果想不午餐后犯困，控制食量是最重要的。</p><p data-pid=\"QcWjJ0xO\">但是，我相信，很多知友也和我一样，<b>喜欢吃饭有点饱足感</b>。（虽然说做到7分饱是最好的）</p><p data-pid=\"4pPH4hoL\">怎么做呢？我自己的一点小办法，仅供参考。</p><p data-pid=\"l5wNvoF1\">人的大脑在饱足感上是能被欺骗的，觉得不那么饿，才能吃得少。</p><p data-pid=\"J-9Y9X3b\">首先，我加大了我的早餐摄入量，差不多翻倍。早餐一般以燕麦，牛奶，水果，火腿之类的为主。这样做的效果是，午餐的时候饥饿感暴跌。我以前可能11点半就饿的不行，到了12点开饭很容易就吃多了。</p><p data-pid=\"LZAFqcDe\"><b>“从吃饱，到大脑觉得饱大概有个20分钟的时差。”</b></p><p data-pid=\"xLwMNOLr\">饥饿感十足的情况下，非常容易摄入过多食物。以前纳粹难民营里救出来的人，吃多了撑死便是极端的情况。</p><p data-pid=\"3uEk2qu_\">另外一个很多同事用的方法是，午餐前30分钟去喝一大杯水。这个的原理是，大脑对于饱足感在短时间内无法区分水或者别的食物，也就是<b>通过餐前喝水增加饱足感。</b></p><p data-pid=\"M3qP6F8i\">最后就是想告诉大家，</p><p data-pid=\"M1RnhAHB\"><b>“除了白米和面，这个世界上有太多不同的主食可以吃了。”</b></p><p data-pid=\"35nzj5do\">北非的couscous，brown rice，红米，lentil，鹰嘴豆，等就不一一列举了，大家可以换着不同的主食吃吃。</p><p data-pid=\"P6T8U9HA\">至于有知友担心<b>不吃米饭大姨妈不来</b>的事情，我可以肯定告诉你，地中海地区很多不吃白米的姐姐们大姨妈还是会来的。</p><br/><p data-pid=\"xAgOMwDN\">节选几个有趣的评论</p><p data-pid=\"JnmkoAGG\"><a class=\"member_mention\" href=\"https://www.zhihu.com/people/0576c0a25839ae0d022bc670748cf7b4\" data-hash=\"0576c0a25839ae0d022bc670748cf7b4\" data-hovercard=\"p$b$0576c0a25839ae0d022bc670748cf7b4\">@劉阳河</a> “HK中午吃的米饭还算好，但是肉类是远超过国内的，港人的肉食量我记的是亚洲第一，相比同等能量下，碳水提供能量的比重要小的多。亚洲我记得是大陆碳水占比最多，日本其次，香港很低。。当然，香港蔬菜几乎是完全不吃的，所以他们大肠癌发病率也是亚洲前列（虽然有着世界上第一的人均寿命。。）&#34;</p><p data-pid=\"IocLfODw\"><a class=\"member_mention\" href=\"https://www.zhihu.com/people/675cd094ace53b577024bcd015cfd04e\" data-hash=\"675cd094ace53b577024bcd015cfd04e\" data-hovercard=\"p$b$675cd094ace53b577024bcd015cfd04e\">@于寒冰</a> &#34;亲测有效，个人以前一直是中午不睡下午崩溃，了解了饭困的道理之后，尽量控制饮食并且每天午饭后散步半小时消耗血糖，已经基本三四年没睡过午觉了&#34;</p><p data-pid=\"IZ-CjMTD\"><a class=\"member_mention\" href=\"https://www.zhihu.com/people/d5af5d08855073a1a8257707199f089d\" data-hash=\"d5af5d08855073a1a8257707199f089d\" data-hovercard=\"p$b$d5af5d08855073a1a8257707199f089d\">@田小谜</a> &#34;以前的老板是美国人 刚认识的时候看他带一个饭盒装一盒子圣女果 或者 哈密瓜 我说你挺会保养啊 吃这么多水果 他说那是他的午餐 我就停惊诧的 这能吃饱？！……&#34;</p><p data-pid=\"_CI6qq9p\"><a class=\"member_mention\" href=\"https://www.zhihu.com/people/6953711d52f17b27aa1e52067b81879d\" data-hash=\"6953711d52f17b27aa1e52067b81879d\" data-hovercard=\"p$b$6953711d52f17b27aa1e52067b81879d\">@拜拜小姐</a> &#34;怪不得！我以前在法国读书，碳水吃很少，怎么都不困；回国后明显感觉累好多（摄入了比以前起码三倍的碳水）。原来如此........&#34;</p><br/><p data-pid=\"xVFyUsWw\">----------------------------------------------------------------------------------------------------</p><p data-pid=\"wJhLhp96\">一次更新：没想到这个问题有这么多的知友感兴趣而且非常认真地讨论，我也把自己做的一点点浅显的研究分享出来，供大家交流。</p><br/><p data-pid=\"QeWdWbci\">导致<b>“饭困”</b>的第一个问题是摄入量，第二个是食物本身的升糖指数。第一问题先不讨论，说说升糖指数的问题。</p><p data-pid=\"UZCrhoq-\">这方面研究的比较多的是德国的科学家。</p><p data-pid=\"SKEyLC1Q\">“Scientists in Germany have documented that meals high in carbohydrates that also have a high glycaemic index (meaning they release sugar into the bloodstream quickly) cause an increase in the hormone insulin. Insulin promotes the absorption and use of glucose from the bloodstream after a meal. But it also allows the entry of a special amino acid (we get these from the digestion of proteins), called tryptophan, into the brain.</p><p data-pid=\"Gd31a5kt\">This is important as tryptophan is converted into another chemical in the brain called serotonin, a signalling chemical or neurotransmitter that <a href=\"https://link.zhihu.com/?target=http%3A//www.ncbi.nlm.nih.gov/pubmed/21641703\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">can be associated with calmness and drowsiness</a>, especially in children.” </p><p data-pid=\"BgeKk43y\">from <a href=\"https://link.zhihu.com/?target=http%3A//ww.sciencealert.com\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">ww.sciencealert.com</span><span class=\"invisible\"></span></a></p><p data-pid=\"XXyidrhk\">简单翻译一下，</p><p data-pid=\"MnHPS2P5\">饮食结构中<b>碳水化合物含量较高且升糖指数较高的，</b>会迅速导致血糖升高从而导致大量胰岛素的分泌。  胰岛素让一种特别的氨基酸—色氨酸进入大脑，而色氨酸在大脑中会转变成血清素导致人们犯困。</p><br/><p data-pid=\"aBycPGRO\">那什么是升糖指数呢？有兴趣的可以看看维基百科，<a href=\"https://link.zhihu.com/?target=https%3A//en.wikipedia.org/wiki/Glycemic_index\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Glycemic index - Wikipedia</a></p><p data-pid=\"P5ubBM_l\">简单的说就是，“同样含量的碳水化合物，升糖指数决定了，糖分释放到血液里速度，进而决定了大脑里血清素形成的速度”。那些升糖指数高的食物，会在很短的时间导致大脑内部形成大量的血清素，让人瞬间产生困顿感。</p><p data-pid=\"LpyDOGYk\">升糖指数图，饭后两小时内血糖的变化</p><figure><noscript><img src=\"https://pic1.zhimg.com/v2-8f225764c19755e1dc535bc1b376183c_b.png\" data-rawwidth=\"543\" data-rawheight=\"411\" data-original-token=\"v2-8f225764c19755e1dc535bc1b376183c\" class=\"origin_image zh-lightbox-thumb\" width=\"543\" data-original=\"https://pic1.zhimg.com/v2-8f225764c19755e1dc535bc1b376183c_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;543&#39; height=&#39;411&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"543\" data-rawheight=\"411\" data-original-token=\"v2-8f225764c19755e1dc535bc1b376183c\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"543\" data-original=\"https://pic1.zhimg.com/v2-8f225764c19755e1dc535bc1b376183c_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-8f225764c19755e1dc535bc1b376183c_b.png\"/></figure><br/><p data-pid=\"pEjmNBiA\">家里有人是严重的糖尿病患者，医生为了说服他少吃米饭给他做了几次饭前和饭后血糖含量的测试，只要白米、白面多的时候，饭后都是爆表的。</p><br/><p data-pid=\"kflgrB8S\">哪些食物升糖指数低呢？from Wikipedia</p><figure><noscript><img src=\"https://pic3.zhimg.com/v2-8282e1690fc138795577ea1e7b02b7de_b.png\" data-rawwidth=\"1924\" data-rawheight=\"948\" data-original-token=\"v2-8282e1690fc138795577ea1e7b02b7de\" class=\"origin_image zh-lightbox-thumb\" width=\"1924\" data-original=\"https://pic3.zhimg.com/v2-8282e1690fc138795577ea1e7b02b7de_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1924&#39; height=&#39;948&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1924\" data-rawheight=\"948\" data-original-token=\"v2-8282e1690fc138795577ea1e7b02b7de\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1924\" data-original=\"https://pic3.zhimg.com/v2-8282e1690fc138795577ea1e7b02b7de_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-8282e1690fc138795577ea1e7b02b7de_b.png\"/></figure><br/><p data-pid=\"P2-qhSgR\">这张表里可以看到--白面包，白米，红薯，白薯的升糖指数是最高的</p><p data-pid=\"gVM978ee\">而—黑豆，lentil，鹰嘴豆，芝麻，花生，瓜子，核桃，腰果，糙粮，南瓜，辣椒，蘑菇等的升糖指数最低</p><p data-pid=\"KCRin41E\">除了食物的升糖指数，整个食物结构的配比也有关系的，碳水化合物，脂肪，蛋白以及蔬菜的配比均衡很重要。</p><p data-pid=\"vcEaQ4K6\">说了这么多，如果吃得多，啥也拯救不了饭困。</p>", "excerpt": "在美帝生活10多年，对比白人和亚洲朋友，可以肯定的是 <b>午睡和饮食结构有很大关系</b>。刚开始工作的时候，我常常会和同事们一起出去吃午餐，有几个白人同事每次都拒绝吃中餐或者其他亚洲食物。一开始的时候我还以为是同事们事逼，没有尝试新鲜东西的心态。后来混熟了，他们跟我说不敢吃中餐的主要原因是每次吃完都有 <b>food coma</b>（没找到翻译，暂且称之为<b>“饭困”</b>吧），影响工作（他们常常用来形容亚洲食物的词是“heavy”）。记得有一…", "excerpt_new": "在美帝生活10多年，对比白人和亚洲朋友，可以肯定的是 <b>午睡和饮食结构有很大关系</b>。刚开始工作的时候，我常常会和同事们一起出去吃午餐，有几个白人同事每次都拒绝吃中餐或者其他亚洲食物。一开始的时候我还以为是同事们事逼，没有尝试新鲜东西的心态。后来混熟了，他们跟我说不敢吃中餐的主要原因是每次吃完都有 <b>food coma</b>（没找到翻译，暂且称之为<b>“饭困”</b>吧），影响工作（他们常常用来形容亚洲食物的词是“heavy”）。记得有一…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {"REACTION_CONTENT_SEGMENT_LIKE": "HIDE"}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "disallowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1519326017, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1519245196574", "type": "feed", "target": {"id": "119182497", "type": "answer", "url": "https://api.zhihu.com/answers/119182497", "voteup_count": 825, "thanks_count": 215, "question": {"id": "50045322", "title": "有哪些通过产品上的精妙设计实现用户增长的例子？", "url": "https://api.zhihu.com/questions/50045322", "type": "question", "question_type": "normal", "created": 1472201654, "answer_count": 73, "comment_count": 2, "follower_count": 3777, "detail": "类似 Dropbox 的邀请好友即可得到额外的空间", "excerpt": "类似 Dropbox 的邀请好友即可得到额外的空间", "bound_topic_ids": [368, 386, 583, 166757], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "11ddd687d85542c3f74d6847f7814191", "name": "查理吵", "headline": "猫厨狗灶，方便科学的宠物鲜食", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/qiuchao", "url_token": "qiuchao", "avatar_url": "https://pica.zhimg.com/v2-073bf3adae1ddc849a78caa3f13d4e42_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1472279656, "created_time": 1472278498, "author": {"id": "1226dd3baa23701c8bb28f60e6904a7e", "name": "邹昕", "headline": "互联网数据分析", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xin_zou", "url_token": "xin_zou", "avatar_url": "https://picx.zhimg.com/51c584fb0918672b70d03e5b32619cbd_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["数据分析", "互联网"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "数据分析等 2 个话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "v2-20382e770da3bff41c52c04e821ec6cc", "avatar_url": "https://picx.zhimg.com/v2-20382e770da3bff41c52c04e821ec6cc_720w.jpg?source=32738c0c", "description": "", "id": "19559424", "name": "数据分析", "priority": 0, "token": "19559424", "type": "topic", "url": "https://www.zhihu.com/topic/19559424"}, {"avatar_path": "v2-c1ea3804dc369dbfad3de0c405c0a3d2", "avatar_url": "https://picx.zhimg.com/v2-c1ea3804dc369dbfad3de0c405c0a3d2_720w.jpg?source=32738c0c", "description": "", "id": "19550517", "name": "互联网", "priority": 0, "token": "19550517", "type": "topic", "url": "https://www.zhihu.com/topic/19550517"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}], "title": "数据分析等 2 个话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": false, "comment_count": 36, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"bRK5N2py\">谢邀，抛砖引玉一把。</p><p data-pid=\"LMX2HXyJ\"><b>1. 免费</b></p><p data-pid=\"c-hxD6PZ\">互联网产品免费提供服务，然后通过增值服务或者广告之类变现，现在来看是一个很通行的模式了，但是十多年前可不是这样的，比如说邮箱是需要付费的，照片存储服务 Flickr 是需要付费的，甚至在网络久发达地区，比如说美国，eBay/PayPal 到现在都还是需要手续费的，卖一个东西差不多要交 10% 的手续费，不像我大天朝的阿里。这个设计可能不够精妙，过于简单粗暴，但是实在是太有效了。</p><p data-pid=\"GpEL6rmF\"><b>2. 邀请奖励 (referral bonus)</b></p><p data-pid=\"SN0Z7ae2\">题主提到了 Dropbox 邀请好友获得额外空间，那么或许也听过 PayPal 邀请好友，然后邀请人和被邀请人都拿到 10 块钱。这一措施让 PayPal 在几个月之内用户从一百万增长到五百万，当然这也是有成本的，那就是每个新用户获取成本是 20 块钱。其实这个成本还是相对比较低的，而且随着时间的推移，10 块钱的的奖励变成了 5 块，后来还需要连上借记卡才可以拿到，所以获取成本就进一步降低了。此外，用户量大到一定程度后，带来的网络效应也是巨大的，直接拿下了 20 世纪初美国的线上支付系统的绝大部分，也为后来卖给 eBay 打下了基础。</p><p data-pid=\"ZQH1mhik\">这一设计后来也被广泛应用，最常见的像各种打车软件，Uber, Lyft, 滴滴等都是这么做的。不过时间久了之后，这个邀请机制有一定的困境。比如说对于打车软件来说，一个很理想的状况是邀请人把邀请链接发布到自己的社交网络比如说微信朋友圈。但是这里面就有一个矛盾的地方了，越是人脉广、朋友圈质量高的人，对自己朋友圈的质量可能会更在意，他/她可能越不愿意分享这样的邀请。这里有一个不成熟的想法，被邀请人的奖励还是一样的（比如说 10 元打车券之类），但是给邀请人的奖励不是金钱上的，而是换一个角度，比如邀请一个人，就提供 10 元来资助学生之类，不知道会不会增加大家分享邀请链接的热情。</p><p data-pid=\"r46U3dyP\">像微信红包也有邀请奖励的意思，发红包给朋友，对方最后有余额的话最后需要取出来，那么就要连上银行卡。但是另外一个角度更有意思一点，发红包给对方，对方很有可能会发回来，然后觉得这个还挺好玩的，还想着接着发，但是红包里没钱了啊，那怎么办？于是连上银行卡接着给别人发红包呗。</p><p data-pid=\"Nv6Wfjwa\">此外还有不同的变种，比如说 Flickr 让用户邀请 5 个人以上，然后就可以免费使用 3 个月之类。</p><p data-pid=\"0GGCoHW5\"><b>3. 电话簿、邮箱导入</b></p><p data-pid=\"UMnzuLsu\">白岩松说过：谁控制了电话本，谁就控制了互联网入口。（郑重声明一下，这是开玩笑的。）</p><p data-pid=\"4cuBO8Te\">社交网络相关的基本都会用来这一点，比如说微信通过导入电话簿（以及 QQ）添加好友以及邀请好友，LinkedIn 通过导入邮件联系人等等。可以说基本每一个带社交属性的网站、App 都会做这个功能，因为用处实在是太大了。</p><p data-pid=\"2WzlfNW-\">随着时代的发展，像微信这种社交平台类软件也慢慢取代了电话簿、邮箱的功能，一个明显的趋势就是一些新生 App 可以通过诸如微信、微博直接注册、登陆。一方面节省了注册的时间，基本上点一两下就可以了，另一方面是便于分享到社交网络上，进一步带来新的用户，比如说常见的健身类 App 像 Nike+, Keep 等。</p><p data-pid=\"UjP7VJqG\"><b>4. Airbnb 整合 Craigslist</b></p><p data-pid=\"6czCJYwk\">也是用户增长的经典案例之一了。在其发展早期，Airbnb 的工程师逆向工程了 Craigslist 的发贴机制，让 Airbnb 的用户能够很方便的把房屋信息同步发布到 Craigslist 上。印象中这个功能是一个工程师和一个设计师合计了一下，然后花了一两周的时间做出来的。很久之前看的一个视频里讲的，记得可能不准确了。</p><p data-pid=\"EtNYdYui\">这里多说一句 Craigslist, 美国之外的朋友估计很少用到，因为这个网站的确是有点另类，风格极其简单，N 年不（怎么）变，就长下面这样，咋一看还以为十年前的网站，也就类似 <a href=\"https://link.zhihu.com/?target=http%3A//ptt.cc\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">ptt.cc</span><span class=\"invisible\"></span></a> 这样的网站可以一拼了。</p><figure><noscript><img data-rawheight=\"1732\" data-rawwidth=\"1946\" src=\"https://pic4.zhimg.com/e240ed2b9f31c75a2125830f9fb3fc39_b.png\" data-original-token=\"e240ed2b9f31c75a2125830f9fb3fc39\" class=\"origin_image zh-lightbox-thumb\" width=\"1946\" data-original=\"https://pic4.zhimg.com/e240ed2b9f31c75a2125830f9fb3fc39_r.jpg\"/></noscript><img data-rawheight=\"1732\" data-rawwidth=\"1946\" src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1946&#39; height=&#39;1732&#39;&gt;&lt;/svg&gt;\" data-original-token=\"e240ed2b9f31c75a2125830f9fb3fc39\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1946\" data-original=\"https://pic4.zhimg.com/e240ed2b9f31c75a2125830f9fb3fc39_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/e240ed2b9f31c75a2125830f9fb3fc39_b.png\"/></figure><br/><p data-pid=\"ec_S967W\"><b>5. 自带小尾巴<br/></b>这个比较古老了，二十年前，电子邮箱还是跟网络服务商绑定的时候，Hotmail 推出了可以独立于网络服务商的邮箱服务，同时在用户发出的邮箱末尾会自带个小尾巴，大意是这封邮件是通过免费的 Hotmail 发出的，点击这个链接也来领取你的免费邮箱吧。</p><p data-pid=\"1P_S2QiA\"><b>6. 邀请答题</b></p><p data-pid=\"USMv5850\">说的就是你，知乎！</p><p data-pid=\"vsc_ceNy\">大致就是像知乎，Quora 这种问答社区发现，如果用户答了 N 个题（比如说三个）的话，用户的留存、粘度会大大增加，于是运营人员就会“伪装”成普通用户去邀请大家答题。</p><p data-pid=\"8wMyc4zU\"><b>7. 自动关注</b></p><p data-pid=\"vCWHAbBq\">Twitter 应该是比较早做这个的，他们发现如果用户关注了一定数量的人，那么用户活性、留存都会大幅提高，于是就推出了自动关注功能，让新注册的用户会默认关注一些人。除了自动关注用户，有的会是自动关注一些主题，比如说知乎，Quora, Pinterest 都有类似机制。</p><p data-pid=\"sKIK5dE_\"><b>8. 注册导向</b></p><p data-pid=\"p98j44g0\">如果对比一下现在很多网站跟几年前的区别，有很多网站主页都设计成注册导向的，比如说知乎，打开首面默认界面就是注册新用户。</p><p data-pid=\"QEO7ZhK-\">再举一个例子，Dropbox 的首页简化成两点，一个是视频让用户了解 Dropbox 是怎么用的，另外一个就是注册了，对比几年前的风格，目的明确了不少。</p><p data-pid=\"TMi2jx8H\">参考这里：</p><a class=\" external\" href=\"https://link.zhihu.com/?target=https%3A//blog.kissmetrics.com/dropbox-hacked-growth/\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">blog.kissmetrics.com/dr</span><span class=\"invisible\">opbox-hacked-growth/</span><span class=\"ellipsis\"></span></a><br/><p data-pid=\"fY9efTJy\"><b>9. 邀请码机制</b></p><p data-pid=\"kW-5-gpa\">像 Gmail 刚出来的时候，需要邀请码才能注册，无形中给用户带来一种：这玩意儿很有价值，我要省着点用的感觉。后来像 Mailbox 初期也是需要邀请码的，硬件方面像一加手机刚发布的时候也有这么个策略，感觉效果还不错。</p><p data-pid=\"0kYuFl11\">知乎早期好像也是邀请机制的吧，可惜我没赶上。</p><p data-pid=\"dIbvk4yK\">当然还有 1024 的邀请码机制（咦，这是个什么东西？）</p><p data-pid=\"K1fCObOr\"><b>10. 点赞机制</b></p><p data-pid=\"NUpbl9Cu\">这个更多是体现在用户粘性方面，跟用户增长的关系要小一点，不过运用实在是太广泛了，社交类软件基本上就没有不用的，不信打开手机看看，有多少 App 是能点赞的。</p><p data-pid=\"4FCT3YFO\">比如说知乎，是吧。</p><p data-pid=\"j1P9YIqb\">另外提一句，关于用户增长下面这个网站有一些很翔实有趣的例子，值得研究。</p><a class=\" external\" href=\"https://link.zhihu.com/?target=https%3A//growthhackers.com/growth-studies/\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">growthhackers.com/growt</span><span class=\"invisible\">h-studies/</span><span class=\"ellipsis\"></span></a>", "excerpt": "谢邀，抛砖引玉一把。 <b>1. 免费</b>互联网产品免费提供服务，然后通过增值服务或者广告之类变现，现在来看是一个很通行的模式了，但是十多年前可不是这样的，比如说邮箱是需要付费的，照片存储服务 Flickr 是需要付费的，甚至在网络久发达地区，比如说美国，eBay/PayPal 到现在都还是需要手续费的，卖一个东西差不多要交 10% 的手续费，不像我大天朝的阿里。这个设计可能不够精妙，过于简单粗暴，但是实在是太有效了。 <b>2. 邀请奖励 (r…</b>", "excerpt_new": "谢邀，抛砖引玉一把。 <b>1. 免费</b>互联网产品免费提供服务，然后通过增值服务或者广告之类变现，现在来看是一个很通行的模式了，但是十多年前可不是这样的，比如说邮箱是需要付费的，照片存储服务 Flickr 是需要付费的，甚至在网络久发达地区，比如说美国，eBay/PayPal 到现在都还是需要手续费的，卖一个东西差不多要交 10% 的手续费，不像我大天朝的阿里。这个设计可能不够精妙，过于简单粗暴，但是实在是太有效了。 <b>2. 邀请奖励 (r…</b>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "need_payment", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1519245196, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1519245196574&page_num=68"}}