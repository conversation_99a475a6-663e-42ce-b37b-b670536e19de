{"data": [{"id": "72_1750898471.812", "type": "feed", "offset": 72, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898471, "updated_time": 1750898471, "target": {"id": "1895780507943412163", "type": "answer", "url": "https://api.zhihu.com/answers/1895780507943412163", "author": {"id": "28a4c5e494f61d8d4f7c50f6e9985faf", "url": "https://api.zhihu.com/people/28a4c5e494f61d8d4f7c50f6e9985faf", "user_type": "people", "url_token": "21-88-51-28-84", "name": "石木", "headline": "", "avatar_url": "https://pica.zhimg.com/50/v2-630486e6ce917f166bb015286eff031e_l.jpg?source=b6762063", "is_org": false, "gender": 0, "followers_count": 4023, "is_following": false, "is_followed": false}, "created_time": 1744769295, "updated_time": 1744769295, "voteup_count": 65, "thanks_count": 7, "comment_count": 2, "is_copyable": true, "question": {"id": "571005780", "type": "question", "url": "https://api.zhihu.com/questions/571005780", "author": {"id": "28e87b265274086a09ac205649d500b2", "url": "https://api.zhihu.com/people/28e87b265274086a09ac205649d500b2", "user_type": "people", "url_token": "su-su-86-31-28", "name": "瑜曦", "headline": "自由不是错错的是你没有自由", "avatar_url": "https://pic1.zhimg.com/50/v2-7280c6b72a26ccc0f762df048cd0fca7_l.jpg?source=b6762063", "is_org": false, "gender": 0, "followers_count": 5, "is_following": false, "is_followed": false}, "title": "每天满脑子都想挣钱，但是没有办法怎么办？", "created": 1670415830, "answer_count": 0, "follower_count": 0, "comment_count": 40, "bound_topic_ids": [12705, 183396], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "原天涯神贴： 《手把手教你从一无所有到财务自由》原作者：突然心血来潮 原文完整版PDF（建议及时保存，避免被河蟹）： https://pan.quark.cn/s/9d47e163ea17 备用链接： https://pan.xunlei.com/s/VNgU5bxho_WS6rpaLBpzyZibA1?pwd=xr8d# 前言 这世界，谈创业的人太多太多，教创业的人也太多太多，而且篇幅、内容一个比一个长，一个比一个深，一个比一广。。。。。。 有用吗？对部分人是有用处，但对另外部分人可能是毒药，有可操作性吗？不一定或者不完全有。 系统吗？基本不成系统。 所以…", "excerpt_new": "原天涯神贴： 《手把手教你从一无所有到财务自由》原作者：突然心血来潮 原文完整版PDF（建议及时保存，避免被河蟹）： https://pan.quark.cn/s/9d47e163ea17 备用链接： https://pan.xunlei.com/s/VNgU5bxho_WS6rpaLBpzyZibA1?pwd=xr8d# 前言 这世界，谈创业的人太多太多，教创业的人也太多太多，而且篇幅、内容一个比一个长，一个比一个深，一个比一广。。。。。。 有用吗？对部分人是有用处，但对另外部分人可能是毒药，有可操作性吗？不一定或者不完全有。 系统吗？基本不成系统。 所以…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"T5-DSdUs\">原天涯神贴：<b>《手把手教你从一无所有到财务自由》</b></p><p data-pid=\"TWAdsSPi\">原作者：突然心血来潮</p><p data-pid=\"E_bPVlRR\">原文完整版PDF（建议及时保存，避免被河蟹）：<a href=\"https://link.zhihu.com/?target=https%3A//pan.quark.cn/s/9d47e163ea17\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">pan.quark.cn/s/9d47e163</span><span class=\"invisible\">ea17</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"9DdU39D_\">备用链接：<a href=\"https://link.zhihu.com/?target=https%3A//pan.xunlei.com/s/VNgU5bxho_WS6rpaLBpzyZibA1%3Fpwd%3Dxr8d%23\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">pan.xunlei.com/s/VNgU5b</span><span class=\"invisible\">xho_WS6rpaLBpzyZibA1?pwd=xr8d#</span><span class=\"ellipsis\"></span></a></p><h3><b>前言</b></h3><blockquote data-pid=\"uLYMrV8I\"> 这世界，谈创业的人太多太多，教创业的人也太多太多，而且篇幅、内容一个比一个长，一个比一个深，一个比一广。。。。。。<br/> 有用吗？对部分人是有用处，但对另外部分人可能是毒药，有可操作性吗？不一定或者不完全有。  系统吗？基本不成系统。<br/> 所以，很多人看了很激动，过后还是一头雾水，不知道从何做起，做了以后也不知道路在何方，通往何处。<br/> 明白几个道理有用吗？道理当然有用，但不至于让你创业真正成功，而偶然的一次创业成功也不至于让你最终实现财务自由。 所以，我就写篇鸟文来系统地谈谈如何从一无所有到财务自由。 <br/> 尽可能简洁明了，尽可能通俗易懂。 我写这篇鸟文的动机：希望看到有人能通过看我的鸟文而真正从一无所有一路走到到财务自由。我喜欢指导人，并看到指导有成效。本人较变态比较享受这个。<br/> 创业的动机？任何动机都对。 创业的目的？直白点讲：赚钱，赚大钱。 这篇鸟文就是针对一无所有的家伙们想赚钱赚到财务自由而作。 所以除了这一类人，其他人士不适合看这篇鸟文。 什么叫一无所有？<br/> 我这里给出的定义是： 要资金没资金，要资源没资源。<br/> 从一无所有到财务自由，说白了就分三步走：<br/> 一、寻找项目 二、经营项目，积累财富  三、让财富增值 听起来很简单谁都懂，其实也很难。<br/> 为什么简单呢？因为说明白了就很简单， 谁都能理解。问题是这世界上绝大多数人不去想，或者想不明白。很多人一难就 难了一辈子。就像一个 36 位的组合密码，知道密码的人一分钟就解开了，不知道的人可能一辈子也解不开。</blockquote><h3>第一篇 寻找项目——惊世骇俗的三个甜甜圈</h3><blockquote data-pid=\"oKcdREt8\"> 现在开始要谈的是第一步：寻找项目。 如果铁了心也要创业，那到 底什么样的项目才适合一无所有的人？ 要记得一无所有的人可是要资金没资金，要资源没资源啊。  没资金？<br/> 那么要投入钱稍微多一点的项目就要 pass 掉了， 没资源？ 那就不能靠这个关系靠那个人脉了，只能靠你的勤劳和你的头脑了。 <br/> 一无所有耶，大哥，而你又要创业？那你打算咋办？想想？ 我来告诉你你是怎么想的吧：做点小买卖。 专业点，那就是两个字：销售！ 销售，可以是销售产品，也可以是销售概念，也可是销售服务，等等。 销售什么？销售东西咯，那这东西是什么？东西可以是你自己，也可以是商品，也可以是一个概念，也可以是你提供的服务，也可以是一些观念，等等。<br/> 总而言之，你就是要用你拥有的东西换取别人的现金。 因为你要赚钱嘛，别人不会平白无故地给钱你，你也要给别人一些别人恰好可能需要的东西。 那么，怎样去拥有这个东西、怎样去用这个东西换到钱就称为项目。</blockquote><p data-pid=\"YFnRh7cd\"><b>第一章 创业要像鳄鱼捕食一样</b></p><blockquote data-pid=\"FSU92bTV\"> 至于我呢？ 不想浪费时间激励你如何变得更勤奋，只能把你的头脑变得更有思想。 所以，我现在首先要做的就是教你如何选择项目。 我坚信一句话：正确的选择比盲目的努力重要得多。 有的人一辈子工作勤勤恳恳，任劳任怨，到头来还是衣不遮体，食不果腹，往往还经不起生活中的意外遭遇的打击。而同样起点同样条件同样资质的人，忙 碌几年后就轻轻松松，一辈子衣食无忧，财产颇丰。<br/> 为什么？ 世界上也有很多人常常嫉妒他人飞黄腾达、抱怨自己境况凄凉，百思不得解。  为什么？<br/> 因为选择不同，所以结果不同。 因为今天的状况是由你过去的选择决定的，现在的选择又决定了你未来的状况。 但遗憾的是，大多数人只是被动的接受而不是主动地思考后选择性地决策。<br/> 好了，既然开始的选择是如此的重要，且占据了三大步中的第一步，那到底好的项目应该是怎么样的呢？ 好的项目应该是一部机器，你只需要开始的时候启动它，然后它就能自己自动地运行，给你带来效益，带来源源不断的财富。 而不好的项目则是纯粹的手工活，效率不高事小，关键在于你时时刻刻不能松懈，一旦松懈，进度立刻停止，财富之门立刻向你关拢，就更谈不上源源不断了。<br/> 所以，创业要像鳄鱼一样，不等到机会就一直潜伏，等到机会就速下狠手。 宁愿在寻找、选择中等待，也不能将就屈身，将就屈身会让你在错误的路上越走越迷茫。</blockquote><p data-pid=\"_wzzfNUW\"><b>第二章 第一个圈圈——量大且可重复消费</b></p><blockquote data-pid=\"V76cIsi0\"> 一个好的战略是能够自我强大的，一个好的项目也是如此， 正是因为这样，选择一个好的项目才变得如此之重要。 而对于一个想赚钱而又一无所有的人来说，判断一个项目是否是个好项目有3 个标准，也就是 3 个圈圈，明白以后就可以拿着这 3 个圈圈自己去衡量测评林林种种的项目群了。我现在要做的就是发给你这 3 个圈圈第一个圈圈是最为重要的，谈之前先做个引子。<br/> 有如下 2 个生意：  1.销售感冒药  2.销售心脏病的药 只是从生意的收益方面来说，让你选择，你会选择哪个生意做？ 事实证明，相同条件下，销售感冒药的收入绝对要超过销售心脏病药的收入。 难道不是吗？<br/> 所以有了 2 小点结论： 第一点就是你销售的东西所面对的受众群体越大越好。 有一句话这么说的：量大是致富的关键。 当你销售的东西永远只是面对一小撮人群，最终你是干不过销售大众化产品的人。而并不是别人比你强多少。 好好想想身边的那些从大到小的生意，是不是这样？卖豪车的企业往往最终会被买中低端车型的企业收购；卖奢侈品再卖得怎么贵利润再怎么高也很难挤进 世界五百强。。。。这是规律。 所以，对于想创业赚钱而又一无所有的人来说，要选择大众化的、中低端的东西来销售，消费群体越大，对你的销售能力和体系以及销售难度就要求越低，你就越容易成功，以量取胜。<br/> 有疑问的不妨多认真研究一下，好好体会。 如果现在有家生产日化产品的公司，你和另外一个家伙要选择 2 个业务去做： 1，销售除臭剂 2，销售沐浴露  你会怎么选择？ 答案会不言而喻。<br/> 第二点是你所销售的东西一定要能够在短周期内重复消费。 这点尤为重要。 能重复消费的好处太多了，多到你一旦入迷将不能自拔。 能重复消费的话，你的后期行销成本和利润的比例将会让你赏心悦目。<br/> 能重复消费的话，后期你用可以忽略不计的成本就能带来源源不断的现金。 能重复消费的话，你的大量精力可以得以解放，可以轻轻松松地赚钱或去再弄个项目做做。 当然，达成这些是有条件的，只是相比而下，那些前提条件都显得轻而易举、 微不足道了。<br/> 如果现在又有家生产教学文具的厂家，你和另外一个家伙要选择 2 个业务去做： 1，卖黑板 2，卖粉笔  你会怎么选择？ 答案也会不言而喻。<br/> 选择能重复消费的项目来做的精髓在于：创业者在事业体进入稳定期后，能很轻松地退出具体执行，只需要做适当监管的工作，而利润却不会随创业者身份的转变而减少。<br/> 所以，宁愿选择花很少精力获取小而稳定利润的项目，也不要选择要花全部精力获取较大利润的项目。 因为一个项目稳定后，创业者若还不能抽身出来让事业体自动循环产生效益，那怎么能达到轻松赚钱的目标呢？更谈不上实现财务自由的终极目标了。<br/> 当然，就算是不能重复消费的项目，创业者也有一系列方案能抽身出来让企手把手教你从一无所有到财务自由业自动运行创造效益，问题是作为一无所有的你，有这样高深的武功段位吗？<br/> 只是经营能重复消费的项目，低武功段位的你才更加容易做到这一点。 好了，第一个圈圈已经送出了，那就是：销售的东西所面对的受众群体越大越好，且一定要能够在短周期内重复消费。 有心人不妨拿着这个圈圈，先去框框身边的那些正在进行的或即将进行的项目，也能得到一些感悟估计。<br/> 比如，曾经有 2 个人，所有的起步条件都差不多，都选择了做一次性餐具的 生意，一个人就留着了当地，在当地的一个市场租了几间大门面，做一次性餐具 的生意，而另一个人则去了义乌，在商品城租了个小小的摊位做一次性餐具的生 意，3、4 年后，留在当地的那个人除了又增加了几间门面以外，资产大概区区 百来万，而去了义乌的那个家伙业务遍天下，早已是几千万的身家了。<br/> 这是真人真事，而且这样类似的事情在不同地方不同行业每天都在上演。<br/> 同时，这又印证了正确的选择重于盲目的努力这句话的正确性。 所以，对于没资金没资源的你而言，只要你的项目具备能够重复消费的属性，相同条件下，生意破产的概率是相对比较低的。 如果同样是可以重复消费的东西，面对更为广泛的受众群体者能胜出。 这里的受众群体自然是包括性质上的和数量上的。 同样的东西面对同样性质的受众，受众数量相对占优势者能胜出。 以上所述还只是长征中一小步的一小步，连怎么样选择项目都不明白，何谈能轻松赚钱从而达到目标：财务自由啊？ 但是如果有的人已经到了第二步，已经有了稳定的收入，且还较为可观，那么要实现财务自由的目标就轻松一些了，虽然轻松一些，仍旧难倒了大多数人一辈子， 因为他们不知道怎么样才能让已有的财富不断增值，只知道存在银行被cpi 不断蚕食殆尽，或者他们采用的途径极具风险且不稳定，而且其中侥幸成功者的方法不具备复制性，不值得研究推广。<br/> 同样的道理，道破玄机就轻轻松松，不明白就潦倒挣扎一辈子。 所以这就是我到了第三步就要开始谈的如何让财富增值，现在时机不对，讲到第三步了以后自然会谈。 我不是说做小众化个性化的产品就没有前途，只是资质、条件、起点相同的情况下，最终，做大众化产品的成就会比做小众化产品的成就高。 做小众产品更专业化，需要你具备更高深一点点的武功，更透彻的研究。。。。<br/> 如果你的能力相同，起点相同，做大众产品破产的概率就远远小于做小众产品的概率。你开个提供消毒碗筷服务的公司，是服务中小型餐馆容易成功还是打 进星级酒店容易成功？先掂量掂量自己的段位。<br/> 所以，创业需要做成功概率大的事情。 另外，对于一无所有的人来说，不要谈什么红海蓝海，那是有一定高度的人谈的，与你无关。<br/> 再怎么红的海，也不至于压榨得让你一个月赚 2、3 万元的空隙都没有。<br/> 永远记住，武功低的人一定要找到一件厉害的兵器，不然，学人家高手折柳为剑只有死路一条。 牛人可以上卖飞机，下卖袜子。因为他是牛人。而你不能去卖飞机，只能卖袜子，因为你不是牛人。<br/> 那，这是为什么呢？ 因为买飞机的人很少很少，而牛人武功高，能够搞定那些很少很少想买飞机的人， 所以他去销售飞机很少谈砸，有较高的成交率。<br/> 你呢？武功很低，想买飞机的人一共就那一小撮，没能力没资源没经历，自然搞不定那些人，谈崩一个你的成交率就少了几个百分点，谈崩几个几乎就再也找不到想买飞机的潜在客户了，那你不死谁死？<br/> 但，你如果去卖袜子，情况就不一样了。 你可以在任何地方向任何人推销你的袜子（武功高点还可以卖原味的）。  尽管你很差，谈了 10 个人别人都不买，没关系，坚持下去总有人买的，因为袜子嘛，人人都有不向你买的理由，也人人都有向你买的理由，比如说看你太敬业被你感动了随便丢两个硬币权当可怜你。。。。<br/> 你看你看，你不就有了收入嘛。有了收入至少不会饿死，然后收入渐增，然后盈亏平衡，然后组团忽悠。。</blockquote><p data-pid=\"rdGJ9y7w\">未完待续持续更新。。。</p><p data-pid=\"_Ust9kwB\">原文完整版PDF（建议及时保存，避免被河蟹）：<a href=\"https://link.zhihu.com/?target=https%3A//pan.quark.cn/s/9d47e163ea17\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">pan.quark.cn/s/9d47e163</span><span class=\"invisible\">ea17</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"l4Uxy2Y7\">备用链接：<a href=\"https://link.zhihu.com/?target=https%3A//pan.xunlei.com/s/VNgU5bxho_WS6rpaLBpzyZibA1%3Fpwd%3Dxr8d%23\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">pan.xunlei.com/s/VNgU5b</span><span class=\"invisible\">xho_WS6rpaLBpzyZibA1?pwd=xr8d#</span><span class=\"ellipsis\"></span></a></p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-37f363c9be51fce5134f5e31fe5df234_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"720\" data-rawheight=\"339\" data-original-token=\"v2-13e4beef011a74006b5cc0edb2aa91c0\" data-default-watermark-src=\"https://picx.zhimg.com/v2-6198403cdf6a9759a46d54ded8f862d3_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"720\" data-original=\"https://pica.zhimg.com/v2-37f363c9be51fce5134f5e31fe5df234_r.jpg\"/></figure><p></p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": true, "visited_count": 6608, "favorite_count": 304, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1895780507943412163}", "attached_info": "CuYGCPu2tt6834aghgEQBBoJNzIzMDc2Mzc5II+i/L8GKEEwAkBISkgKH1RTX1NPVVJDRV9aUkVDQUxMX0lURU1DRl9VUFZPVEUSH2RvY190eXBlOiBBbnN3ZXIKaWQ6IDY4Mjc4NTYwMAoYACAAOgBaCDg5MTcyNjg2YiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3ITMTg5NTc4MDUwNzk0MzQxMjE2M4oBCTU3MTAwNTc4MKoBCXJlY29tbWVuZMIBIDI4YTRjNWU0OTRmNjFkOGQ0ZjdjNTBmNmU5OTg1ZmFm8gEKCAwSBk5vcm1hbPIBKAgKEiQ3OGVlYWQ0NS0yNGYxLTRkYzItYWIxMi0xYmUxZTc3YjUwODPyAQYICxICMTOCAgCIAqznuc36MpICIDI4YTRjNWU0OTRmNjFkOGQ0ZjdjNTBmNmU5OTg1ZmFmmgIAygIWU2hvckludGVyZXN0V2VpZ2h0UnVsZcoCFkFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhtJbnRlcmFjdGlvblNob3JJbnRlcmVzdFJ1bGXKAhhQZXJpb2RJbnRlcmVzdFdlaWdodFJ1bGXKAhVVc2VyTGNuRXhpdFdlaWdodFJ1bGXKAhRDb250ZW50QWdlV2VpZ2h0UnVsZdoCH1RTX1NPVVJDRV9aUkVDQUxMX0lURU1DRl9VUFZPVEXoAgL6AgtOT1JNQUxfRkxPV4oDIGM4NDVjMmNhZTkyODQyYjY4MjliOWQ5ZWY1ZDUzMzQ4mgMNCgJ2MhAAGgVvdGhlcqgD0DPYAwDqAxl0ZXh0QWxsU2l0ZUFjdGlvbkl0ZW1DRlYy+gNOEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERTotCAIQ0AUY0wIiI3YyLTEzZTRiZWVmMDExYTc0MDA2YjVjYzBlZGIyYWE5MWMwgAQAiAQAkgQGTm9ybWFsmgQBMqAEAKgEALAEALoEBm1hbnVhbMIEAzE3MMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAA4OnpwD+BBQAAAAAAAAAAiQXFeYIB6bnSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUNkAYAoAZMqAYBkgIuCgk3MjMwNzYzNzkSEzE4OTU3ODA1MDc5NDM0MTIxNjMYBCIKSU1BR0VfVEVYVA==", "action_card": false}, {"id": "73_1750898471.694", "type": "feed", "offset": 73, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898471, "updated_time": 1750898471, "target": {"id": "11477776099", "type": "article", "url": "https://api.zhihu.com/articles/11477776099", "author": {"id": "d58dc780cc61a1c4579536b491e87ebf", "url": "https://api.zhihu.com/people/d58dc780cc61a1c4579536b491e87ebf", "user_type": "people", "url_token": "qiao-han-80-16", "name": "<PERSON>", "headline": "车辆感知博士在读。抖音：@Phd Reedsway", "avatar_url": "https://picx.zhimg.com/50/v2-54ce3a39ff242df488b32bb238ecebf3_l.jpg?source=b6762063", "is_org": false, "gender": 1, "badge": [{"type": "identity_people", "description": "重庆大学 机械工程博士在读"}], "followers_count": 2985, "is_following": false, "is_followed": false}, "title": "外行也能看懂！万字白话串讲SFM到3DGS / 4DGS 动静场景重建（零公式原理细讲/全网最详细）", "image_url": "https://pic1.zhimg.com/v2-51f45b4a3b0d397c0687ddb8051bca0b.jpg?source=7e7ef6e2&needBackground=1", "comment_permission": "all", "created": 1733693671, "updated": 1733693671, "voteup_count": 283, "voting": 0, "comment_count": 1, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "引言机器视觉领域中，新颖视图合成技术的核心目标是通过图像或视频构建可以被计算机处理和理解的3D模型。该技术被认为是机器理解真实世界复杂性的基础，催生了大量的应用，包括3D建模、虚拟现实、自动驾驶等诸多领域。回顾其发展历史，针对真实世界场景重建的研究早在深度学习诞生之前就已经兴起。 早期研究主要集中在光场和结构性数据的恢复上，受限于早期计算机对密集采样和结构化捕获的算力限制，在处理复杂光照场景和不规则…", "excerpt_new": "引言机器视觉领域中，新颖视图合成技术的核心目标是通过图像或视频构建可以被计算机处理和理解的3D模型。该技术被认为是机器理解真实世界复杂性的基础，催生了大量的应用，包括3D建模、虚拟现实、自动驾驶等诸多领域。回顾其发展历史，针对真实世界场景重建的研究早在深度学习诞生之前就已经兴起。 早期研究主要集中在光场和结构性数据的恢复上，受限于早期计算机对密集采样和结构化捕获的算力限制，在处理复杂光照场景和不规则…", "preview_type": "default", "preview_text": "", "content": "<h2>引言</h2><p data-pid=\"bUWHbUHg\">机器视觉领域中，新颖视图合成技术的核心目标是通过图像或视频构建可以被计算机处理和理解的3D模型。该技术被认为是机器理解真实世界复杂性的基础，催生了大量的应用，包括3D建模、虚拟现实、自动驾驶等诸多领域。回顾其发展历史，针对真实世界场景重建的研究早在深度学习诞生之前就已经兴起。</p><p data-pid=\"rJcaZKGD\">早期研究主要集中在光场和结构性数据的恢复上，受限于早期计算机对密集采样和结构化捕获的算力限制，在处理复杂光照场景和不规则结构方面出现了巨大技术瓶颈。2006年，<b>运动结构恢复算法</b>（Structure from Motion，后文简称SFM）和<b>多视图立体算法</b>（multi-view stereo，后文简称MVS）的出现打破了这一僵局，为后续新颖视图合成技术提供了强大的基础框架。2020年，ECCV 2020，<b>隐式神经辐射场技术</b>（NeRF）诞生，实现了这一技术的阶段性飞跃。NeRF利用深度神经网络，实现空间坐标到图像色彩及密度的直接映射，构建了连续、立体场景功能，实现了该领域中三维模型前所未有的细节和真实感，但代价也十分明显：1）计算强度极高：NeRF的场景构建方法为计算密集型，单一场景构建需要大量的算力资源和时间成本，尤其是高分辨率输出时；2）可编辑性差：单一场景的编辑需要重新训练整个流程。操纵隐式表达的场景具有较大挑战，中间环节的神经网络参数都需手动调控。</p><p data-pid=\"UuvS5lwH\">针对上述NeRF存在的困境，<b>三维高斯飞溅技术</b>（3D Gaussian Splatting，后文简称3DGS）应运而生。3DGS源自ACM顶会SIGGRAPH 2023，其原文《3D Gaussian Splatting for Real-Time Radiance Field Rendering》的主要思想在于使用3D Gaussian椭球表示三维模型，并通过Splatting技术渲染模型图像。该方法实现了辐射场的实时渲染，能够在较少的训练时间内，实现SOTA级别的视觉效果。3DGS针对多张图像或视频拍摄的场景时，允许以1080P分辨率进行实时（≥30FPS）的新视图渲染。</p><p data-pid=\"CKb-r8Oq\">3DGS的引入不仅仅是一项技术的进步，其代表了计算机图形学在如何处理真实场景表示和渲染技术上的彻底变革，通过在不影响视觉质量的前提下实现实时渲染功能，为虚拟现实和增强现实及大量衍生产业提供了全新可能。同时，3DGS的显式场景提供了前所未有的灵活性来控制对象和场景动态，这是解决复杂几何形状表达和复杂光照表达的关键要素，同时，其强大的自编辑能力和渲染效率成为了塑造相关领域未来发展的中坚力量。</p><h2>三维高斯飞溅(3DGS)</h2><h3>总体流程</h3><p data-pid=\"38enD6H9\">3DGS原始的代码仅支持静态场景的重建。其大致流程如图1所示，包括：</p><p data-pid=\"8WQ8dMM8\">1）输入一组静态场景的图像。由SFM算法，获取场景中的稀疏点云及所有图像对应的相机位姿。</p><p data-pid=\"vsBmd92b\">2）对获取的每个稀疏点云创建初始化3D高斯椭球，其由位置（平均值）、协方差矩阵（XYZ轴缩放因子、旋转因子等）、不透明度和球谐函数系数（后文简称SH系数）所定义。该定义允许3D场景合理紧凑的表示，并通过调节参数紧凑化表示精细化场景结构。</p><p data-pid=\"-O9-zLmC\">3）利用球谐函数来映射整个辐射场的方向性外观分量（RGB颜色）</p><p data-pid=\"1UDDkzq1\">4）通过固定的场景图像与原始输入图像（真值图像）进行比对优化，对初始化后的参数（位置、协方差矩阵、SH系数、高斯球密度的自适应控制）进行优化，并构建光栅化渲染。</p><p data-pid=\"U_gi_yD7\">5）允许高斯球和高斯球之间重合，设计了跟踪快速后向通道，自适应调控高斯梯度。</p><p data-pid=\"lQierBap\">本章将从头到尾讲解3DGS的全流程，包括SFM算法的构建，3DGS的初始化，3DGS的渲染及3DGS的自适应密度调节。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-168d91590a411b27a3adac6d6e90f91c_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"835\" data-rawheight=\"160\" data-original-token=\"v2-a4b40b13d542cd6f28131deef19a9539\" class=\"origin_image zh-lightbox-thumb\" width=\"835\" data-original=\"https://pica.zhimg.com/v2-168d91590a411b27a3adac6d6e90f91c_r.jpg\"/><figcaption>图1 3DGS构建全流程</figcaption></figure><h3>SFM算法</h3><p data-pid=\"WW18k6PX\"><b>运动结构恢复</b>(Structure from motion)，即给出多幅图像及其图像特征的一个稀疏对应集合，估计3D点的位置，这个求解过程通常涉及3D几何（结构）和摄像机姿态（运动）的同时估计。由于图像之间可能是无序的，并且图像数据量大，数据来源广而丰富。针对以上特点，出现了三种SFM策略，包括增量式、层级式以及全局式。增量式即先选出两张图像进行初始化，接着一张张图像进行配准及点的三角化；全局式即一次性将所有的影像进行配准与重建；层级式先将影像进行分组，每组进行配准，再对上一步的结果进行配准重建。其中，增量式在无序图像数据集重建中应用最广泛，其应用包括Bundler、VisualSfM、COLMAP等优秀的SFM开源软件，为SFM的应用与研究提供了基础。</p><p data-pid=\"VqSXIL0c\">3DGS的初始化流程中，严格遵循COLMAP增量式SFM的点云初始化流程。COLMAP是当今最受关注的主流SFM开源软件，其为3DGS提供稀疏点云，相机位姿以及对应图像参数。COLMAP的整体流程如图2所示，可简单归纳为以下环节：特征提取，特征匹配，相机位姿估计，三角测量，场景重建。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-33c7ad7383f1d4feb1cff9dfe5fdc9dd_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"830\" data-rawheight=\"155\" data-original-token=\"v2-98352eab93a2d10bddfdeb47e1bec192\" class=\"origin_image zh-lightbox-thumb\" width=\"830\" data-original=\"https://pic4.zhimg.com/v2-33c7ad7383f1d4feb1cff9dfe5fdc9dd_r.jpg\"/><figcaption>图2 COLMAP构建全流程</figcaption></figure><p data-pid=\"u1Lt76eB\"><b>特征提取</b>：这里的特征提取环节并不是神经网络中所提到的图像特征，而是特征描述子。在该环节中，需要提取图像间的共有特征点，该特征点需具备克服跨模态、跨尺度、跨视角、跨帧的能力，同时可以有效规避非线性辐射干扰、大气辐射干扰、运动模糊干扰、形变干扰等。在COLMAP中一般会采取经典的ORB、SIFT、SURF等特征点描述获取作为优先选项。针对不同的情况，<b>我本人推荐</b>HOPC、SuperGlue、SuperPoint、D2-Net等全新的特征点获取方式，并加入神经网格规整特征描述子的形状及X-Y坐标。</p><p data-pid=\"stlxx0xn\"><b>特征匹配</b>：由于COLMAP采取的是增量式SFM算法流程，整体以视频的时间帧进行图像间的特征点匹配。匹配环节符合SIFT、SURF的暴力匹配模型。在整体匹配中，为了规避无关点和误匹配的出现，采用KNN、FLANN等优化算法进行点与点间的连线，并加入RANSAC、LMEDS进行GOOD MATCHING（最佳匹配点）提纯。在这个环节中，<b>我本人推荐</b>使用更加优渥的SuperPoint和TEASER++的提纯环节，有效获取最佳匹配点。</p><p data-pid=\"hls4rVW6\"><b>图像对优选</b>：在获取了逐帧图像间的特征匹配关系后，利用特征点间的描述向量得到重叠图像对（≥2张）。该环节的在于对时间复杂度下的多重叠图像进行优化，去除不必要的图像，寻找相似度及图像信息囊括最多的图像帧。该环节在COLMAP中仍以RANSAC作为常用手段，但其本身的优化算法非常多。<b>我本人推荐</b>MatchMiner、VocMatch、PAIGE等算法进一步优化重叠图像对，并估计出影像对之间的变换矩阵，判断是否有足够多的特征点满足映射关系并最终获得需要的场景图。</p><p data-pid=\"AfXSNbSj\"><b>相机位姿估计及空间点坐标获取</b>：这个环节非常重要。首先，针对场景重建的问题。队医单一范围内的场景，我们选取上述操作中获取的重叠图像，并进行图像间的双目初始化。由于重叠图像的数量大多为两张以上，因此，我们获取的数据冗余非常高，这会使得重建的精度和细节更加丰富。接下来，我们进行图像的空间配准。通过特征点匹配所获取的变换矩阵，我们求解出图像与图像间特征点所在的相对三维空间坐标，并利用2D和3D关系，利用RANSAC算法及最小位姿解算器，求解计算机视觉中常见的PNP（Perspective-n-Point）问题，进而求解出多图像间的相机位姿。在该环节，<b>我本人推荐</b>使用更加快速的稀疏视图相机位姿求解方法，以规避在BEV视觉中存在的图像间夹角过小的问题，例如微软提出的VoxelPose或RootNet。</p><p data-pid=\"OSmEsLuB\"><b>三角化确立新图像地图点</b>：该环节是COLMAP的主要创新点之一。三角化过程即前方图像交汇，通过配准已有场景的强特征点，获取已存在的空间三维点（即单一场景里的相对三维坐标）并校准新的空间三维点（即选取单一场景的XY坐标为原点，校准合成三维场景的坐标）。在COLMAP中，使用了光束法平差进行该环节的优化。通过校准配准场景间的反向投影误差，提高三角化过程的准确性，并在训练过程中，增加了Huber、Tukey等损失函数。</p><p data-pid=\"2WNtYf_s\">该环节中，为了进一步选择新的视图，COLMAP提出了一种全新的试图选择策略。如图3所示。COLMAP将图像划分为了不同尺度的网格，对网格中的特征点分布状况进行打分。点的分布越多且越均匀，分就越高，得分最高的图像将会作为优先的图像配准选择。</p><p data-pid=\"M0Le4FOD\">该环节可能产生局部歧义，因此，<b>我本人推荐</b>使用其原始成员的文章《SFM disambiguation with COLMAP》对该环节进行进一步优化。消除SFM重建过程中的歧义。上述新地图点确立流程如图4所示。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-2cc6850761dbb7538428f2ce52d5a5f1_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"831\" data-rawheight=\"337\" data-original-token=\"v2-4f10be5e0ee533e5331077f50d729580\" class=\"origin_image zh-lightbox-thumb\" width=\"831\" data-original=\"https://pic4.zhimg.com/v2-2cc6850761dbb7538428f2ce52d5a5f1_r.jpg\"/><figcaption>图3 最佳试图选择策略</figcaption></figure><p data-pid=\"daz_vRih\"><b>重建场景及其约束</b>：在完成了所有的地图点获取后，此时所有的场景点云已经全部获取完毕。该场景点云全部由图与图之间的特征点所构成，点与点之间存在明确的空间对应关系。同时，单一点对应多个相机位姿和图像信息。为了进一步优化相机位姿和三维坐标点。COLMAP提出使用BA（Bundle Adjustment）算法对生成的稀疏点云和相机位姿进行监督优化。该算法可有效减少重投影误差和累积误差。BA问题是机器视觉中的经典求解问题，可选取严格方式和不严格方式进行求解。严格方式即通过相机稀疏矩阵与特征点稀疏矩阵优化参数；非严格方式即通过简单的迭代求解器，估算参数并与当前参数比对。在COLMAP中，提出了一种BA优化方法，即针对场景与场景间的图像配准关系，都只进行局部图像块的BA求解。当整体的场景重建完成后，再进行一次全局BA求解，从而减少计算代价。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-9e8cb03bbf2220f82b4ed6494f74a2ef_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"448\" data-rawheight=\"353\" data-original-token=\"v2-61b59cc4627ee5ee9f549cf0e4774b87\" class=\"origin_image zh-lightbox-thumb\" width=\"448\" data-original=\"https://pic4.zhimg.com/v2-9e8cb03bbf2220f82b4ed6494f74a2ef_r.jpg\"/><figcaption>图4 新地图点确立</figcaption></figure><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-1411b21d05ac329ca8e9be089b4ab39d_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"554\" data-rawheight=\"310\" data-qrcode-action=\"none\" data-original-token=\"v2-88604f7e0dcdf8ecfa1e36508498e90d\" class=\"origin_image zh-lightbox-thumb\" width=\"554\" data-original=\"https://pic2.zhimg.com/v2-1411b21d05ac329ca8e9be089b4ab39d_r.jpg\"/><figcaption>图5 SFM最终重建点云及相机位姿</figcaption></figure><p data-pid=\"k1JYa7Ur\"><b>重建场景再优化</b>：整体重建场景会存在两类问题：1）完整度缺失；2）点漂移或错误的位姿估计。为了进一步优化模型，COLMAP提出使用再三角化（RT）和多次迭代精化模型予以解决。首先，对于初次模型重建中，被提纯剔除的点特征进行再三角化；其次，由于很大一部分的三维点云会在多次提纯环节中被错误滤除。因此，使用多次的BA、RT后再提纯，可以有效提高点云数量同时提高精度。如图5所示，为最终的SFM重建场景。</p><h3>三维高斯初始化</h3><p data-pid=\"0XgfbV1R\">对于可微点渲染的应用历史，最早可追溯到1991年。在这里我们不再过多的赘述该环节及其发展历程。首先，3DGS的初始化，是为了使得新颖视图合成的场景存在完整的填充体积。为了做到有体积的填充，3DGS必须选择一个能够继承可微体积表示的基元，同时该基元是显式且非结构化的。因此，3DGS选择了三维高斯分布，一个在三维空间中可微，且极其容易投影到二维平面中的表示形式。3DGS接收SFM所提供的系列数据用于初始化，包括：SFM稀疏点云文件（ply格式输入），每一个点的相机位姿数据（坐标数据），每一个相机对应的图像数据（Json文件）等。</p><p data-pid=\"FQwgVTYj\"><b>第一个问题：</b>为何使用三维高斯分布（类似于空间中的一个椭球）作为最佳的填充基座呢？首先，椭球集表示三维模型并不是一个新鲜事，如图6所示，最早于1994年的3D游戏《魔城迷踪ecstatica》中就已经开始使用椭球集作为填充基元。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-252ec4dfc418f695c0c037eba10f947b_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"504\" data-rawheight=\"276\" data-original-token=\"v2-beda904ebfb7ed07037ba8fc3c4254b2\" class=\"origin_image zh-lightbox-thumb\" width=\"504\" data-original=\"https://pic2.zhimg.com/v2-252ec4dfc418f695c0c037eba10f947b_r.jpg\"/><figcaption>图6 SFM最终重建点云及相机位姿</figcaption></figure><p data-pid=\"RchTnddz\"><b>但是！</b>如果只使用实心椭球对SFM系数点进行图像填充的化，就会变成《魔城迷踪ecstatica》一样，无法覆盖足够多的图像信息，对整个场景的精细化结构进行优化了。因此，3DGS使用的并不是实心椭球，而是概率分布，它允许两个高斯椭球间相互融合重叠！</p><p data-pid=\"xtBZMqvn\">这个思想最早出自Heckbert在1989年的硕士毕业论文中所提出的“椭球加权平均滤波器”。随后基于该思想，2001年，Matthias Zwicker发表文章《<b>EWA Volume Splatting</b>》，提出了一种使用椭圆高斯核进行泼溅的体素渲染方法。</p><p data-pid=\"7MUxjCnk\">椭球高斯核被选择作为重建核和低通滤波器，<b>存在以下的性质</b>：<b>1）</b>高斯核在非对称映射和共振下是封闭的，沿着一个核心的任一轴对三维高斯进行积分时，会得到一个二维高斯。<b>2）</b>沿任一方向积分后的二维高斯可以使用重采样滤波器进行分析计算，并集成高斯分布的原始特征。上述特征都可以在渲染和编辑过程中被利用。</p><p data-pid=\"mrhVUsqp\">这些概念早在机器学习中可见一斑，如图7所示，为三维高斯各个变量间的关系。其中，中心点控制高斯分布的位置，协方差矩阵控制缩放因子。这一点与3DGS中高斯椭球的特征是相同的。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-4aac5e77b3d8e2e63734cd9a32ee5ed5_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"846\" data-rawheight=\"949\" data-original-token=\"v2-8f6e56a01308bc1241c2cdfd96e1b28e\" class=\"origin_image zh-lightbox-thumb\" width=\"846\" data-original=\"https://pic4.zhimg.com/v2-4aac5e77b3d8e2e63734cd9a32ee5ed5_r.jpg\"/><figcaption>图7 三维高斯各个变量间的关系</figcaption></figure><p data-pid=\"RbWwhBQe\">同时，三维高斯因其本质是概率分布，存在可混叠的特性。如下图8所示，多个三维高斯是可以混合重叠的，并且仍保持高斯分布所具有的性质，这在数学上有着深厚的研究历史。因此，选择高斯椭球作为渲染基元，代表着更自由的可编辑能力。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-023dd2d57fd1c2c853f6bf6021daf708_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1696\" data-rawheight=\"450\" data-original-token=\"v2-2085b63c9d2592ee199b15cc9c67abd6\" class=\"origin_image zh-lightbox-thumb\" width=\"1696\" data-original=\"https://pica.zhimg.com/v2-023dd2d57fd1c2c853f6bf6021daf708_r.jpg\"/><figcaption>图8 多三维高斯混叠</figcaption></figure><p data-pid=\"7tmC0p79\">接下来，看看如何将三维高斯在空间中进行表示。在《EWA Volume Splatting》中。三维高斯的空间表示方式比较简单粗暴。首先，在初始化了任一高斯椭球核后，我们首先进行视角变换（转换物体和摄像机的坐标），随后使用投影变换将相机坐标转换为射线坐标，如图9所示。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-1a96146d42438e41d0fe35361fcefd3b_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"348\" data-rawheight=\"281\" data-original-token=\"v2-fd6d9212804b661574b69930457ab88a\" class=\"content_image\" width=\"348\"/><figcaption>图9 相机坐标转换为射线坐标</figcaption></figure><p data-pid=\"gmaC-fZl\">在这一过程中，我们需要将重建核转移到射线空间中。但传统的体积映射方案利用“足迹图像”会消耗大量的算力。因此，这里非常巧妙的使用体积映射到光线空间，然后高效执行透视投影（仅需计算雅可比因子和两次3×3矩阵乘法）。如图10所示。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-5f9f7e387d7ef25b26cfebd73df10475_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"342\" data-rawheight=\"333\" data-original-token=\"v2-2d5cbf450971b351fddefaddf90dd7df\" class=\"content_image\" width=\"342\"/><figcaption>图10 重建核从相机映射到光线空间</figcaption></figure><p data-pid=\"c8HtzaQ4\">接下来就是体积内核的重建。EWA重采样滤波器可以处理任一体积的高斯内核如何选择高斯重构核的形状呢？在<b>2D Gaussian Splatting</b>中，其选择了一个沿表面法线方向平铺的重建核来提高等值面的渲染精度；在<b>EWA</b>论文中，将体积重构核转换为了曲面重构核，直接从体积数据中提取和渲染曲面表示；在<b>3D Gaussian Splatting</b>中，直接选择了不估计法线的三维高斯分布，只有中心点、全协方差矩阵控制整个高斯核的尺度旋转及位置。其映射方式如图11所示。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-a502c92e29935138c3dacb8dcd01effd_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"562\" data-rawheight=\"459\" data-original-token=\"v2-98628bce0e6548863973783d3594b326\" class=\"origin_image zh-lightbox-thumb\" width=\"562\" data-original=\"https://pic2.zhimg.com/v2-a502c92e29935138c3dacb8dcd01effd_r.jpg\"/><figcaption>图11 渲染体积核（上）；渲染表面内核（下）</figcaption></figure><p data-pid=\"8xaEOkZ2\">纹理如何映射到高斯核上呢？如图12所示更为直观。首先，三位平面被参数化。它将一个圆形的二维纹理映射到由三维空间中两个向量确定的平面上，形成一个椭圆。然后，利用斜平面投影映射椭球，将椭球与低通滤波器结合得到纹理滤波。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-d6f2afa37b6dbc2579677de7dd19739b_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"622\" data-rawheight=\"385\" data-original-token=\"v2-a836c3ca680ced97dd67f03a88338f08\" class=\"origin_image zh-lightbox-thumb\" width=\"622\" data-original=\"https://pic4.zhimg.com/v2-d6f2afa37b6dbc2579677de7dd19739b_r.jpg\"/><figcaption>图12 表面内核渲染</figcaption></figure><p data-pid=\"IDSOj7LL\">在整个高斯核的渲染中。EWA使用的方法很简单，即Splatting（飞溅）。飞溅的概念即对于一个相机位姿的图像，将图像的像素信息像抛雪球一样，抛射到高斯分布的一个面上，形成混叠的高斯球，如图13所示。对于单一的高斯核所对应的多个相机图像，采用累积飞溅，即混叠的方式，将像素信息进行累加（如图8）。然后使用NVIDIA自带的合成片材计算和照明模型进行初步着色。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-fcfa5404c3ad954c337be47b19443d08_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"303\" data-rawheight=\"303\" data-original-token=\"v2-6ac532888fa11ecedf07fb151164b10a\" class=\"content_image\" width=\"303\"/><figcaption>图13 抛雪球的概念（Splatting）</figcaption></figure><p data-pid=\"cvwdncii\"><b>注</b>：在3DGS中，该除初始位置，初始协方差矩阵，初始色彩以外，<b>还加入的透明度α和用于拟合外观的球谐函数</b>。在此我们做以下解释：</p><p data-pid=\"VsBUqYbc\">1）<b>透明度</b>：复杂光照变换作为真实场景重建中最大的难题之一，3DGS给出的方案很简单。一个服从概率分布的透明度α。用于表示单个高斯核的透明度。</p><p data-pid=\"RYCO9Cls\">2）<b>球谐函数</b>：球谐函数的概念和傅里叶级数是类似的。在获取了单个相机的位姿后，我们根据位姿信息计算出Splatting过程中，其抛射到高斯椭球上的色彩分布。球谐函数是Splatting的优化方法，其定义为相机视角参数<img src=\"https://www.zhihu.com/equation?tex=%EF%BC%88%5Ctheta%EF%BC%8C%5Cphi%EF%BC%89\" alt=\"（\\theta，\\phi）\" eeimg=\"1\"/>，输出为球面上的RGB数据。</p><p data-pid=\"EvxP1P6b\">3）<b>为啥需要高斯椭球完全可微</b>？很大一部分原因是为了2D投影调控RGB色彩和透明度时，配合使用球谐函数。同时方便整体网络训练反向传播时的梯度计算和回归。这里类似于深度学习里的概念。深度学习在设计Zero-Shot / Zero-Reference的时候，同样强调于迭代函数/模块的简单、可微。</p><h3>三维高斯渲染优化</h3><p data-pid=\"N6lGLZ69\">上文中，我们知道了，对于单个高斯核所对应的相机及图像信息。我们通过Splatting的方式，将其已高斯混叠累加在了图像空间中。那么如何多整个映射的三维场景进行进一步的优化呢？整体流程如图14所示，最终单个图像由数倍的3D高斯椭球所构成。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-21ed2fa796e8d6f8dd9b3100169b1cb2_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"831\" data-rawheight=\"223\" data-original-token=\"v2-dda4df6aa9bb3487c4c0438bb7e9d4b9\" class=\"origin_image zh-lightbox-thumb\" width=\"831\" data-original=\"https://pica.zhimg.com/v2-21ed2fa796e8d6f8dd9b3100169b1cb2_r.jpg\"/><figcaption>图13 抛雪球的概念（Splatting）</figcaption></figure><p data-pid=\"oHHvXpF9\">对于单个图像，在初始化时，只有一个或少个高斯椭球。这肯定是不行的。因为对于单一位姿的相机视角来说。极少数量的的高斯椭球肯定效果非常差。从相机视角来看，如图14所示。此时的是一个一个的椭球所构成的三维分布。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-13da08eb6ba34ed9d4f5fccecc6af96b_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1674\" data-rawheight=\"580\" data-original-token=\"v2-9edf10c337abbc1622d0495309b574c9\" class=\"origin_image zh-lightbox-thumb\" width=\"1674\" data-original=\"https://pic4.zhimg.com/v2-13da08eb6ba34ed9d4f5fccecc6af96b_r.jpg\"/><figcaption>图14 3DGS初始化概念</figcaption></figure><p data-pid=\"0_ZzkmkC\">因此，3DGS赋予了高斯椭球一个全新的能力<b>克隆、分裂和删除。</b>参考神经网络的有监督学习。3DGS会自适应的对局部图像的高斯椭球数量进行<b>繁殖</b>或<b>分裂</b>，并根据阈值<b>删除</b>多出的高斯椭球。如图15所示，3DGS将其命名为：<b>自适应高斯密度化方案。</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-af454d90b89fba7b909ba44ea3642c33_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"539\" data-rawheight=\"304\" data-original-token=\"v2-7050d97d55a54b43b8e5940b0ae82ac9\" class=\"origin_image zh-lightbox-thumb\" width=\"539\" data-original=\"https://pic4.zhimg.com/v2-af454d90b89fba7b909ba44ea3642c33_r.jpg\"/><figcaption>图15 当小尺度几何图形没有被完全覆盖时，3DGS克隆其高斯分布（上图）；当初始化高斯椭球过大，我们采用分裂的形式将大高斯核一分为二（下图）</figcaption></figure><p data-pid=\"7v_Jy01X\">同时，由于训练过程中，会产生大量不必要的高斯椭球。我们设置透明度阈值 <img src=\"https://www.zhihu.com/equation?tex=%5Cvarepsilon_%7B%5Calpha%7D\" alt=\"\\varepsilon_{\\alpha}\" eeimg=\"1\"/> 。对于局部图像中，透明度明显低于/高于图像平均值的部分，我们会进行删除。在训练过程中，这个操作会每1000次迭代进行一次。</p><p data-pid=\"HwwBjGRc\">接下来就是如何比对图像了。在COLMAP中，我们获取了所有图像的相机位姿及其对应编号的视角图像。我们将该图像认为是我图像的<b>真值</b>。同时，在重建的场景中，我们也有对应的相机位姿。此时，我们调取3DGS场景中的相机视角，并把当前的相机视角认为是<b>监督值</b>。我们使用以下损失函数，对两者的误差进行评估。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-2b25349e439d62761f72a59347fad7a2_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"748\" data-rawheight=\"114\" data-original-token=\"v2-2b25349e439d62761f72a59347fad7a2\" class=\"origin_image zh-lightbox-thumb\" width=\"748\" data-original=\"https://pic1.zhimg.com/v2-2b25349e439d62761f72a59347fad7a2_r.jpg\"/><figcaption>最经典损失</figcaption></figure><p data-pid=\"6tzIGmv6\">这个损失函数是图像重建中<b>最经典的损失函数之一</b>。其评判了真值图像和监督图像在结构相似性及L1损失上的误差。</p><p data-pid=\"u6rw7MX2\">通过该损失函数的监督。3DGS会自适应的调节高斯椭球的数量，同时调节每个椭球的大小、位置、旋转、透明度和球谐光照色彩分布。如图15所示，随着迭代次数的上升，我们可以明显看到出现了高保真的重建场景。同时，极高密度的高斯椭球也构成了真实的空间体积。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-61498d59c1adc6e3ce447cda3137bc08_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1255\" data-rawheight=\"955\" data-original-token=\"v2-e247e29a401435ac286fcd13f8b89204\" class=\"origin_image zh-lightbox-thumb\" width=\"1255\" data-original=\"https://pic3.zhimg.com/v2-61498d59c1adc6e3ce447cda3137bc08_r.jpg\"/><figcaption>图15 3DGS训练阶段图</figcaption></figure><p data-pid=\"7MHUEPef\"><b>注</b>：在3DGS中，还设计了有一个快速的光栅化渲染器。这里我们不再作过多的赘述。3DGS的光栅化渲染器具备两个不同于传统光栅化渲染的特点：1）允许近似的透明度混合渲染，包括各向异性Splatting，允许在任意数量的混叠高斯椭球上反向传播。2）避开了NeRF渲染中严格遵循像素排序的阻碍，打破了可以接受Splatting的数量限制。并且光栅化管道完全可微，方便进行2D投影。</p><h2><b>动态场景重建(4D)</b></h2><h3>总体流程</h3><blockquote data-pid=\"TuSXqQHb\">该部分由于方案仍在迭代更新，且本人显存受限，仅为<b>细微略讲，未详细拆分</b></blockquote><p data-pid=\"ZvSLxQcA\">对于4D场景的重建（单帧3D场景加入时间帧构建动态场景），自视觉SLAM技术诞生开始便成为了计算机视觉研究中的重要课题之一。在本章中我们将粗略介绍基于3DGS的4D场景的构建方法。</p><p data-pid=\"vSrnWtZD\">如下图16所示。我们采用“<b>动静分离</b>”的策略构建4D场景。其全流程大致可分为三步：</p><p data-pid=\"0lGwPuzA\">首先，对于输入的连续视频帧，我们采取了图像的预处理手段：<b>分割、跟踪、补全</b>。对于分割出的多个无模态物体，我们使用了<b>蒸馏采样</b>（SDS）和<b>基于Diffusion model的先验重建</b>，获取静态物体和动态物体的3DGS表示。</p><p data-pid=\"HWTBuoFV\">随后，为处理场景中的动态物体，我们将该部分的3DGS分解为三个部分：<b>相机运动</b>（物体运动轨迹）、<b>物体中心</b>（可形变）、<b>物体中心到世界坐标的转换</b>。并将三个参数保存为单一的节点动态模型。</p><p data-pid=\"LqvcBxVK\">最后，利用COLMAP中获取的<b>深度估计</b>，我们将单独优化后的静态场景和动态场景进行拼接，获取完整的动态场景。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-0b263ff966d8e93c2d178b461f4e282e_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"830\" data-rawheight=\"289\" data-original-token=\"v2-c8281973bd84263bba482879396bf765\" class=\"origin_image zh-lightbox-thumb\" width=\"830\" data-original=\"https://pica.zhimg.com/v2-0b263ff966d8e93c2d178b461f4e282e_r.jpg\"/><figcaption>图16 总体流程图</figcaption></figure><h3>图像预处理</h3><p data-pid=\"_FeeP8Cl\">整个图像预处理的环节包括以下四个步骤：<b>跟踪分割、天空分割、深度估计、图像补全</b>。我们将分别进行逐一讲解。</p><p data-pid=\"F0XcNKbf\">① <b>跟踪分割：</b>跟踪分割不同于常用的逐帧图像分割，而是在确立了单一动态物体运动轨迹的情况下，对物体进行逐帧的分割。PDF中不方便展示其效果。为了达到更好的视频补全效果。跟踪分割追求0漏帧的分割精度。在4D场景重建中，<b>我本人推荐</b>使用Xmem、Ground-SAM + YOLOV10 + 记忆模块这两种最先进的跟踪分割方式进行工作。如图17所示。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-1eaa1e60d776149b341aa2704a82c77b_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"830\" data-rawheight=\"406\" data-original-token=\"v2-3f2503a5defbfe8dd1cb2f58d405c202\" class=\"origin_image zh-lightbox-thumb\" width=\"830\" data-original=\"https://pic4.zhimg.com/v2-1eaa1e60d776149b341aa2704a82c77b_r.jpg\"/><figcaption>图17 Xmem流程图</figcaption></figure><p data-pid=\"Lj3pFTZH\">② <b>天空分割：</b>在3DGS的重建过程中，因为天空的光影随时间帧变化往往变幻极快，且其透明度和色彩难以被捕获。同时，为使得重建的大规模场景可以被用于进一步的光照生成。根据Wayve、Waymo和Tesla等公司的开源方案，天空是需要被分割的。在这里使用了常见的天空分割数据集做监督学习即可。<b>我本人推荐</b>使用阿里达摩院提供的一站式天空分割线上模型即可批量处理，效果如图18所示。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-e3ad5d8382df6b6dd8ddd27a592559f9_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1432\" data-rawheight=\"951\" data-original-token=\"v2-67490e9b5c4c57c947e0e4b4481dfef5\" class=\"origin_image zh-lightbox-thumb\" width=\"1432\" data-original=\"https://pic2.zhimg.com/v2-e3ad5d8382df6b6dd8ddd27a592559f9_r.jpg\"/><figcaption>图18 天空分割效果</figcaption></figure><p data-pid=\"-0JEEmyn\">③ <b>深度估计</b>：在3DGS的重建过程中，深度估计用于后期动态物体和静态场景进行有效拼接。因此，一开始在视频输入时，就需要高精度的动态视频深度估计方案。这个方法我们已经根据Depth-Anything v2及MaskFormer v2进行了优化。室内场景误差≤5cm，室外大场景误差≤5m。其展示效果如下图19所示。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-0a6b30a102182324ea6306504c9e433d_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1479\" data-rawheight=\"534\" data-original-token=\"v2-7cf5244a164ed6ac8374d78579b4a66f\" class=\"origin_image zh-lightbox-thumb\" width=\"1479\" data-original=\"https://pic2.zhimg.com/v2-0a6b30a102182324ea6306504c9e433d_r.jpg\"/><figcaption>图19 深度估计效果</figcaption></figure><p data-pid=\"cveigIKR\"><b>④ 图像补全</b>：在3DGS的重建过程中，静态场景如何进行重建呢？很简单！在分割动态物体后，使用扩散模型对缺失的街景进行补全即可。该方法我们也已经实现，并且效果不错。如下图20所示。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-736bdc1b3ec5942d01e28f491b57f884_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"830\" data-rawheight=\"162\" data-original-token=\"v2-d31da7babc7ce8985b774c857e9767fb\" class=\"origin_image zh-lightbox-thumb\" width=\"830\" data-original=\"https://pic1.zhimg.com/v2-736bdc1b3ec5942d01e28f491b57f884_r.jpg\"/><figcaption>图20 图像补全效果</figcaption></figure><p data-pid=\"4MlDXRqY\">在完成上述的图像处理步骤后，我们得到了两类数据：<b>静态场景图像</b>（补全街道后没有天空的静态场景图像，静态场景的深度估计图），<b>动态物体图像</b>（分割后的单一物体图像，动态物体的深度图）。上述所有的图像信息，都拥有<b>对应的时间帧</b>、<b>相机位姿</b>等信息，如图21所示。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-6949ead735722ba076efa128e8e27462_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"495\" data-rawheight=\"340\" data-original-token=\"v2-4c5aa0ec734727a16b43eead7efaafc9\" class=\"origin_image zh-lightbox-thumb\" width=\"495\" data-original=\"https://pic1.zhimg.com/v2-6949ead735722ba076efa128e8e27462_r.jpg\"/><figcaption>图20 获取的预处理图像数据</figcaption></figure><h3>动态物体孪生</h3><p data-pid=\"wLp7zNhv\">3DGS的动态物体孪生，旨在基于先验结构，利用扩散模型，生成完整的3DGS模型。在我们捕获的摄像头数据中。动态车辆往往只有一面或稀疏面。这难以有效对动态车辆进行3DGS模型重建。为了实现更好的动态物体重建。我们使用了基于Diffusion model的3DGS重建算法。</p><p data-pid=\"e_2kX-qf\">算法流程十分简单。根据3DGS的单面，由扩散模型生成该物体的其他面。并利用生成面和已知面直接进行3DGS重建即可。重建方法参考第二节的静态物体3DGS重建方法。如图21为单图3DGS孪生方案。图22为展示效果。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-2fa737a77543cdfbf0aa50468cbb566d_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"871\" data-rawheight=\"194\" data-original-token=\"v2-a55895b1580dadff46ed6ee4981dacbd\" class=\"origin_image zh-lightbox-thumb\" width=\"871\" data-original=\"https://picx.zhimg.com/v2-2fa737a77543cdfbf0aa50468cbb566d_r.jpg\"/><figcaption>图21 单图3DGS孪生方案</figcaption></figure><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-97c88ee32b379b2b20facc2a12164d0e_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"879\" data-rawheight=\"621\" data-original-token=\"v2-58651a9ff2a78d7796185a685445a1e4\" class=\"origin_image zh-lightbox-thumb\" width=\"879\" data-original=\"https://pica.zhimg.com/v2-97c88ee32b379b2b20facc2a12164d0e_r.jpg\"/><figcaption>图22 单图3DGS孪生展示效果</figcaption></figure><h3>复杂3D运动表示</h3><p data-pid=\"WsE1AdUU\">3DGS虽然实时渲染效率很高。但是在表示动态物体时，数以万计的高斯椭球同时运动会消耗大量的算力。因此，对单帧点云数据进行连续显示是不可取的。为了解决这个问题，将动态物体分解为三个部分：1）以对象为中心的运动，使用深度网络搭建形变模型；2）以对象为中心到世界坐标的转换，使用3D位移矢量和旋转因子表示；3）相机运动估计，由COLMAP所提供的相机轨迹表示。</p><p data-pid=\"elf5844o\">三部分在整体场景训练过程中，被作为单独的因子进行优化。并在<b>最终合称为一个动态节点模型</b>。</p><p data-pid=\"YoYd-vMn\">1）<b>以对象为中心的3DGS运动表示</b>：我们首先已经获取了动态物体的单一图像帮对单一图像初始化高斯表示。对于初始化后的高斯椭球，我们以对象为中心，进行高斯椭球的分组。对于帧与帧之间的高斯球运动，我们采用K平面的变形网络预测下一时间的中每个分组对象的变形参数（即高斯椭球的旋转、尺度、透明度等参数），并在训练过程中，利用SDS损失对其进行优化。其具体形式如下图23（第一条分支）所示。</p><p data-pid=\"eSJQU3Kg\">同时，为了使得重建出来的4DGS（动态高斯）符合物理学运动定律，我们引入了物理先验对形变进行验证.利用尺度正规则化损失，对高斯尺度变化误差进行惩罚。利用Rigid损失，局部刚性重建进行惩罚。</p><p data-pid=\"wAg8r5UF\">2）<b>以对象为中心到世界坐标的转换：</b>高斯坐标系从对象中心坐标系扭曲到世界坐标系，如图23（第三个分支）所示。</p><p data-pid=\"NNRDHHEM\">3）<b>相机运动估计：</b>COLMAP所提供的相机位姿在物理学上并不连续。因此，我们需要对相机运动估计的位姿进行进一步优化。我们利用可微的3DGS渲染来联合重建3D静态视频背景用于估计摄像机运动。该环节可以使用现成的算法进行优化。我本人推荐使用《Robust Consistent Video Depth Estimation》用于优化相机位姿运动。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-0f3dc8aa2a23d6503cd654018d08dcfe_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"589\" data-rawheight=\"336\" data-original-token=\"v2-b5a15ac29de81cc6c41da273e752060a\" class=\"origin_image zh-lightbox-thumb\" width=\"589\" data-original=\"https://pic3.zhimg.com/v2-0f3dc8aa2a23d6503cd654018d08dcfe_r.jpg\"/><figcaption>图23 运动表示流程</figcaption></figure><h3>动态场景合成</h3><p data-pid=\"Pb84Efre\">根据深度估计的确切深度，将动态物体和静态物体合并即可。</p><h2>结尾</h2><p data-pid=\"FX_pY9ts\">当前所有环节已经逐步更新，且SFM环节已经出现端到端模型。</p><p data-pid=\"FqGjUuSs\">欢迎关注本人最新文章。虽然更新异常缓慢。</p><p data-pid=\"40diuvj2\">关注车辆感知及弱光图像任务</p><p data-pid=\"bYHTWlhD\">推荐阅读：</p><a href=\"https://zhuanlan.zhihu.com/p/708372232\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic2.zhimg.com/v2-6b232e75db0aca998aaaaf6f0b8fe5f9_qhd.jpg\" data-image-width=\"645\" data-image-height=\"348\" class=\"internal\">PhD YuhanChen：2D Gaussian Splatting 文章 + 代码串读（无敌详细/引经据典/疯狂解读）</a><p></p>", "is_labeled": false, "visited_count": 11221, "thumbnails": ["https://pic1.zhimg.com/v2-51f45b4a3b0d397c0687ddb8051bca0b.jpg?source=7e7ef6e2&needBackground=1", "https://pic1.zhimg.com/50/v2-afdbcabedd1b834a6ec1ecb64c14c671_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-1c827cddc7c7bf850e676f8141e85d47_720w.jpg?source=b6762063", "https://pic1.zhimg.com/50/v2-494fa3316847b86ab584c2a7f9acd2ce_720w.jpg?source=b6762063", "https://picx.zhimg.com/50/v2-bb4956bc6644ca1a624e460378774ac7_720w.jpg?source=b6762063"], "favorite_count": 710, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": 11477776099}", "attached_info": "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", "action_card": false}, {"id": "74_1750898471.979", "type": "feed", "offset": 74, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898471, "updated_time": 1750898471, "target": {"id": "1920502314805993980", "type": "article", "url": "https://api.zhihu.com/articles/1920502314805993980", "author": {"id": "62764e1d641aa4c9026f735702ad1d73", "url": "https://api.zhihu.com/people/62764e1d641aa4c9026f735702ad1d73", "user_type": "people", "url_token": "xuelaoban", "name": "薛老板", "headline": "辅导求职AIGC和新能源 xuelaoban678", "avatar_url": "https://pic1.zhimg.com/50/v2-6236b55a25cd12d3637853acbf68c1e4_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 52366, "is_following": false, "is_followed": false}, "title": "【最后提醒一次】AIGC产品经理面试要多背业务题", "comment_permission": "all", "created": 1750663614, "updated": 1750663614, "voteup_count": 2, "voting": 0, "comment_count": 0, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "背景：今天面试了一个求职AI产品经理的女生，连Transformer由什么组成都没回答上来，真的是离了大谱。 AIGC是很火，但真的不是所有人都能上岸，真心建议每一个求职AI产品的小伙伴一定要多背业务面试题，下面给大家整理的这46道是一定要背的。 一、产品理解篇 Q1 如何定义AI产品的MVP（最小可行产品）？ Q2 什么是AI产品的&#34;数据闭环&#34;（Data Flywheel）？ Q3 AI产品经理和传统产品经理的核心区别？ Q4 为什么AI产品需要持续迭代模…", "excerpt_new": "背景：今天面试了一个求职AI产品经理的女生，连Transformer由什么组成都没回答上来，真的是离了大谱。 AIGC是很火，但真的不是所有人都能上岸，真心建议每一个求职AI产品的小伙伴一定要多背业务面试题，下面给大家整理的这46道是一定要背的。 一、产品理解篇 Q1 如何定义AI产品的MVP（最小可行产品）？ Q2 什么是AI产品的&#34;数据闭环&#34;（Data Flywheel）？ Q3 AI产品经理和传统产品经理的核心区别？ Q4 为什么AI产品需要持续迭代模…", "preview_type": "default", "preview_text": "", "content": "<p data-pid=\"TWG-amvZ\"><b>背景：</b></p><p data-pid=\"CqHWdf8P\">今天面试了一个求职AI产品经理的女生，连Transformer由什么组成都没回答上来，真的是离了大谱。</p><p data-pid=\"LlT8LzRk\">AIGC是很火，但真的不是所有人都能上岸，真心建议每一个求职AI产品的小伙伴一定要多背业务面试题，下面给大家整理的这46道是一定要背的。</p><h2><b>一、产品理解篇</b></h2><p data-pid=\"azYF3kea\"><b>Q1 如何定义AI产品的MVP（最小可行产品）？</b> </p><p data-pid=\"CaGNi2Pb\"><b>Q2 什么是AI产品的&#34;数据闭环&#34;（Data Flywheel）？</b></p><p data-pid=\"bu5yq5az\"><b>Q3 AI产品经理和传统产品经理的核心区别？</b></p><p data-pid=\"Kuk6kSBP\"><b>Q4 为什么AI产品需要持续迭代模型？</b></p><p data-pid=\"A6tkB5lR\"><b>Q5 AI产品的冷启动（Cold Start）问题如何解决？</b></p><p data-pid=\"ngENUO50\"><b>Q6 AI 产品经理的工作流程和工作职责是什么？</b></p><p data-pid=\"AfBa8Os0\"><b>Q7 什么样的AI产品算是成功的产品？</b></p><p data-pid=\"f8KZULNW\"><b>Q8 AI产品的核心三要素是什么？</b></p><p class=\"ztext-empty-paragraph\"><br/></p><h2><b>二、技术认知篇</b></h2><p data-pid=\"GUJbqDHb\"><b>Q9 大语言模型有哪些的优势/挑战/局限性？</b></p><p data-pid=\"H3g061oF\"><b>Q10 什么是特征工程（Feature Engineering）？</b></p><p data-pid=\"juEk9cSg\"><b>Q11 如何评估AI模型的业务价值/商业价值？</b></p><p data-pid=\"rA6lbA7h\"><b>Q12 机器学习模型的训练、验证、测试集的作用？</b></p><p data-pid=\"NKVC2E2V\"><b>Q13 什么是A/B测试？如何设计AI产品的A/B实验？</b></p><p data-pid=\"J4WaIbeA\"><b>Q14 模型准确率（Accuracy）和召回率（Recall）哪个更重要？</b></p><p data-pid=\"aokRvF9Z\"><b>Q15 为什么AI产品需要监控数据漂移（Data Drift）？</b></p><p data-pid=\"lTowb0MW\"><b>Q16 你之前负责产品中使用的最核心的算法是什么？这种算法有哪些优缺点？ </b></p><p data-pid=\"h0_tUlsH\"><b>Q17 什么是大语言模型？实现原理是什么？跟之前的算法模型有什么区别？</b></p><p class=\"ztext-empty-paragraph\"><br/></p><h2><b>三、业务场景篇</b></h2><p data-pid=\"Kh_2lrNG\"><b>Q18 AI算法工程师说你的需求实现不了怎么办？</b></p><p data-pid=\"YVtslBZp\"><b>Q19 如何向非技术背景的老板解释AI产品的技术限制？</b></p><p data-pid=\"iDeXmqSt\"><b>Q20 设计一个智能客服产品的核心模块？</b></p><p data-pid=\"_pejH-ev\"><b>Q21 如果模型准确率很高但用户不满意，怎么分析？</b></p><p data-pid=\"cAwq29KO\"><b>Q22 AI产品如何平衡自动化与人工干预？</b></p><p data-pid=\"dDgln-nZ\"><b>Q23 如何优化推荐系统的用户体验？ </b></p><p data-pid=\"5LSE5ilb\"><b>Q24 如何设计AI产品的用户反馈机制？ </b></p><p class=\"ztext-empty-paragraph\"><br/></p><h2><b>四、工程落地篇</b></h2><p data-pid=\"nEsHY9-o\"><b>Q25 模型构建流程是怎么样的？ </b> </p><p data-pid=\"LAiyxj2M\"><b>Q26 模型部署（Model Serving）的常见方式？</b></p><p data-pid=\"EtQNVfKh\"><b>Q27 工作中，用什么样的方法清洗和整理数据？</b></p><p data-pid=\"b3iIMUz9\"><b>Q28 什么是模型版本控制（Model Versioning）？</b></p><p data-pid=\"DIhuDZpr\"><b>Q29 对于机器人出现的幻觉问题你们是怎么避免的？</b></p><p data-pid=\"pKlx9LDc\"><b>Q30 如何优化AI产品的推理速度？</b></p><p data-pid=\"LYA_k0Op\"><b>Q31 如何评估AI产品的计算资源成本？</b></p><p data-pid=\"jDMpEpQU\"><b>Q32 你是怎么做微调的？常用的微调方式有哪些？</b> </p><p data-pid=\"mjvpzzze\"><b>Q33 什么是模型量化（Quantization）？对产品有什么影响？ </b></p><p data-pid=\"eIAOcuvv\"><b>Q34 如何管理AI产品的数据隐私合规（如GDPR）？ </b></p><h2><b>五、行业认知篇</b></h2><p data-pid=\"PpDYmmVT\"><b>Q35 你怎么看待 AI 行业？ 对于整个AI行业有哪些认知？</b></p><p data-pid=\"YMZe_KrI\"><b>Q36 结合我们公司的业务场景，通过 AI 技术可以做哪些工作来提升用户体验？</b></p><p data-pid=\"GQuRZkI6\"><b>Q37 AIGC当前在各个行业都有哪些落地场景和应用？</b></p><p data-pid=\"XNyCeGMH\"><b>Q38 如何看待AI Agent?</b></p><h2><b>六、商业与伦理篇</b></h2><p data-pid=\"Mk6ZEqHp\"><b>Q39 AI产品的商业模式有哪些？（如SaaS、API收费）</b></p><p data-pid=\"1u6kkn2e\"><b>Q40 AI产品经理应如何确保产品的合规性？</b> </p><p data-pid=\"IKm7eMsf\"><b>Q41 如何制定AI产品的定价策略？</b></p><p data-pid=\"teGKAKF6\"><b>Q42 如何衡量AI产品的ROI（投资回报率）？</b></p><p data-pid=\"zE4lAZmg\"><b>Q43 AI伦理（AI Ethics）对产品设计的影响？</b></p><p data-pid=\"yBzKX6Ua\"><b>Q44 AI产品如何应对监管政策变化？</b> </p><p data-pid=\"Ay_dC2iR\"><b>Q45 过往是否有遇到哪些因数据安全问题产生重大隐患和实际影响，这些问题如果以现在来看是否可以规避</b></p><p data-pid=\"RnwjwB-O\"><b>Q46 如何避免AI产品中的算法偏见（Bias）？</b> </p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Z2j04j8K\">篇幅有限，大多数问题我都整理了答案，需要的找薛老板（微信：xuelaoban678）要就可以，最后，祝大家都能收获满意的offer~</p><p></p>", "is_labeled": false, "visited_count": 113, "favorite_count": 9, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": 1920502314805993980}", "attached_info": "Cv8FCPu2tt6834aghgEQBxoJMjU5NDQ0MDcyIL6D5MIGKAIwAEBKSkEKLFRTX1NPVVJDRV9UV09UT1dFUl9TSE9SVElOVEVSRVNUX1JFQ0FMTF9URVhUEgEwGAAgADoKeyJyYXciOiIifUooCh1UU19TT1VSQ0VfTkVBUkxJTkVfQ09OVEVOVF9WMhIBMBgAIAA6AGIgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyEzE5MjA1MDIzMTQ4MDU5OTM5ODCqAQlyZWNvbW1lbmTCASA2Mjc2NGUxZDY0MWFhNGM5MDI2ZjczNTcwMmFkMWQ3M/IBCggMEgZOb3JtYWzyASgIChIkODEzZjEzMTktZmJjZS00ZWI4LTgzMmItYTUzN2ZhYTM0NTI18gEGCAsSAjEzggIAiAKt57nN+jKSAiA2Mjc2NGUxZDY0MWFhNGM5MDI2ZjczNTcwMmFkMWQ3M5oCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhhQZXJpb2RJbnRlcmVzdFdlaWdodFJ1bGXKAhVVc2VyTGNuRXhpdFdlaWdodFJ1bGXaAixUU19TT1VSQ0VfVFdPVE9XRVJfU0hPUlRJTlRFUkVTVF9SRUNBTExfVEVYVOgCA/oCC05PUk1BTF9GTE9XigMgYzg0NWMyY2FlOTI4NDJiNjgyOWI5ZDllZjVkNTMzNDiaAw0KAnYyEAAaBW90aGVyqANx2AMA6gMaZmVlZF9hdHRtX3R3b3Rvd2VyX3YyX3RleHT6Ax8SDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFgAQAiAQAkgQGTm9ybWFsmgQBM6AEAKgEALAEALoEAmFpwgQDNDAwyAQA0gQP5o6o6I2Q5bey5pu05paw2AQA8AQA+QQAAABguMGyP4EFAAAAAAAAAACJBcV5ggHpudI/kgUAmgUDZGZ0ogUDZGZ0sgUBMbkFAAAAAAAAAADQBQDgBQDoBQDwBQ2QBgCgBk6oBgCSAi4KCTI1OTQ0NDA3MhITMTkyMDUwMjMxNDgwNTk5Mzk4MBgHIgpJTUFHRV9URVhU", "action_card": false}, {"id": "75_1750898471.410", "type": "feed", "offset": 75, "verb": "TOPIC_ACKNOWLEDGED_ARTICLE", "created_time": 1750898471, "updated_time": 1750898471, "target": {"id": "1921168254644225312", "type": "article", "url": "https://api.zhihu.com/articles/1921168254644225312", "author": {"id": "0db55b89c8a0a0bd46168d158f8e7350", "url": "https://api.zhihu.com/people/0db55b89c8a0a0bd46168d158f8e7350", "user_type": "people", "url_token": "--14-29-86-2", "name": "硬盘毁灭者", "headline": "", "avatar_url": "https://picx.zhimg.com/50/v2-713ee14561610c9069de2defe9e4ab62_l.jpg?source=b6762063", "is_org": false, "gender": 0, "followers_count": 0, "is_following": false, "is_followed": false}, "title": "《全美经典学习指导系列丛书40本》【336.5MB】", "comment_permission": "all", "created": 1750822273, "updated": 1750822273, "voteup_count": 8, "voting": 0, "comment_count": 0, "linkbox": {"category": "", "pic": "", "title": "", "url": ""}, "excerpt": "资源目录详情：|─ID：19580_全美经典学习指导系列丛书40本 | |─01【全美经典】概率与统计（第二版）.pdf | |─02【全美经典】统计学（第三版） .pdf | |─03【全美经典】离散数学.pdf | |─04【全美经典】Mathematica使用指南.pdf | |─05【全美经典】数理金融引论（第三版）.pdf | |─06【全美经典】机械振动.pdf | |─07【全美经典】微分方程（第二版）.pdf | |─08【全美经典】统计学原理（上）.pdf | |─08【全美经典】统…", "excerpt_new": "资源目录详情：|─ID：19580_全美经典学习指导系列丛书40本 | |─01【全美经典】概率与统计（第二版）.pdf | |─02【全美经典】统计学（第三版） .pdf | |─03【全美经典】离散数学.pdf | |─04【全美经典】Mathematica使用指南.pdf | |─05【全美经典】数理金融引论（第三版）.pdf | |─06【全美经典】机械振动.pdf | |─07【全美经典】微分方程（第二版）.pdf | |─08【全美经典】统计学原理（上）.pdf | |─08【全美经典】统…", "preview_type": "default", "preview_text": "", "content": "<p></p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-61775d3d6c4d7eb14cfaf88f1ae534f0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"536\" data-rawheight=\"481\" data-original-token=\"v2-34d38b76baf782b8efacbb7870a13dc0\" class=\"origin_image zh-lightbox-thumb\" width=\"536\" data-original=\"https://pic1.zhimg.com/v2-61775d3d6c4d7eb14cfaf88f1ae534f0_r.jpg\"/></figure><h2>资源目录详情：</h2><p data-pid=\"DG8uLxgI\">|─ID：19580_全美经典学习指导系列丛书40本<br/>| |─01【全美经典】概率与统计（第二版）.pdf<br/>| |─02【全美经典】统计学（第三版） .pdf<br/>| |─03【全美经典】离散数学.pdf<br/>| |─04【全美经典】Mathematica使用指南.pdf<br/>| |─05【全美经典】数理金融引论（第三版）.pdf<br/>| |─06【全美经典】机械振动.pdf<br/>| |─07【全美经典】微分方程（第二版）.pdf<br/>| |─08【全美经典】统计学原理（上）.pdf<br/>| |─08【全美经典】统计学原理（下）.pdf<br/>| |─09【全美经典】微积分.pdf<br/>| |─10【全美经典】静力学与材料力学.pdf<br/>| |─11【全美经典】有限元分析.pdf<br/>| |─12【全美经典】传热学（原第二版）.pdf<br/>| |─13【全美经典】近代物理学（原第二版）.pdf<br/>| |─14【全美经典】2000工程热力学习题精解.pdf<br/>| |─15【全美经典】工程力学，静力学与动力学（原第五版）.pdf<br/>| |─16【全美经典】3000物理习题精解.pdf<br/>| |─17【全美经典】流体动力学.pdf<br/>| |─18【全美经典】物理学基础.pdf<br/>| |─19【全美经典】材料力学（第四版）.pdf<br/>| |─20【全美经典】2000离散数学习题精解.pdf<br/>| |─21【全美经典】工程热力学.pdf<br/>| |─22【全美经典】数值分析（第二版）.pdf<br/>| |─23【全美经典】量子力学 .pdf<br/>| |─24【全美经典】有机化学习题精解.pdf<br/>| |─25【全美经典】3000化学习题精解.pdf<br/>| |─26【全美经典】大学化学习题精解.pdf<br/>| |─27【全美经典】电路.pdf<br/>| |─28【全美经典】电气工程基础.pdf<br/>| |─29【全美经典】工程电磁场基础.pdf<br/>| |─30【全美经典】数字信号处理.pdf<br/>| |─31【全美经典】数字系统导论.pdf<br/>| |─32【全美经典】数字原理 .pdf<br/>| |─33【全美经典】电机与机电学.pdf<br/>| |─34【全美经典】基本电路分析.pdf<br/>| |─35【全美经典】信号与系统.pdf<br/>| |─36【全美经典】微生物学.pdf<br/>| |─37【全美经典】生物化学（第二版）.pdf<br/>| |─38【全美经典】生物学（第二版）.pdf<br/>| |─39【全美经典】分子和细胞生物学.pdf<br/>| |─40【全美经典】人体解剖与生理学（第二版）.pdf</p><p data-pid=\"A7jObE9h\">我用夸克网盘分享了《全美经典学习指导系列丛书40本》【336.5MB】，点击链接即可保存。打开「夸克APP」，无需下载在线播放视频，畅享原画5倍速，支持电视投屏。</p><p data-pid=\"DDsSqN64\"><b>夸克网盘</b>：<a href=\"https://link.zhihu.com/?target=https%3A//pan.quark.cn/s/31edfeac6ac1\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">pan.quark.cn/s/31edfeac</span><span class=\"invisible\">6ac1</span><span class=\"ellipsis\"></span></a></p>", "is_labeled": false, "visited_count": 418, "favorite_count": 58, "article_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"article\", \"id\": 1921168254644225312}", "attached_info": "CtEFCPu2tt6834aghgEQBxoJMjU5NTI5MjU3IIHb7cIGKAgwAEBLSigKE1RTX1NPVVJDRV9GRUVEUkVfVjkSATAYACAAOgp7InJhdyI6IiJ9SigKHVRTX1NPVVJDRV9ORUFSTElORV9DT05URU5UX1YyEgEwGAAgADoAYiA1OTAxOTkzODk3YjJiNjcxNTM0YjQxZGZlMTcwMzk0N3ITMTkyMTE2ODI1NDY0NDIyNTMxMqoBCXJlY29tbWVuZMIBIDBkYjU1Yjg5YzhhMGEwYmQ0NjE2OGQxNThmOGU3MzUw8gEKCAwSBk5vcm1hbPIBKAgKEiRjN2VmMGRlMS1kMDZhLTQ0MTgtYmQyNy1jNTRhZGE2NDIxYznyAQYICxICMTOCAgCIAq3nuc36MpICIDBkYjU1Yjg5YzhhMGEwYmQ0NjE2OGQxNThmOGU3MzUwmgIAygIWU2hvckludGVyZXN0V2VpZ2h0UnVsZcoCFVVzZXJMY25FeGl0V2VpZ2h0UnVsZdoCE1RTX1NPVVJDRV9GRUVEUkVfVjnoAgL6AgtOT1JNQUxfRkxPV4oDIGM4NDVjMmNhZTkyODQyYjY4MjliOWQ5ZWY1ZDUzMzQ4mgMNCgJ2MhAAGgVvdGhlcqgDogPYAwDqAwlmZWVkcmVfdjn6A04SDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFOi0IAhCYBBjhAyIjdjItMzRkMzhiNzZiYWY3ODJiOGVmYWNiYjc4NzBhMTNkYzCABACIBACSBAZOb3JtYWyaBAEyoAQAqAQAsAQAugQCYWnCBAM0MDDIBADSBA/mjqjojZDlt7Lmm7TmlrDYBADwBAD5BAAAAGDUjaY/gQUAAAAAAAAAAIkFxXmCAem50j+SBQCaBQNkZnSiBQNkZnSyBQExuQUAAAAAAAAAANAFAOAFAOgFAPAFDZAGAKAGT6gGAJICLgoJMjU5NTI5MjU3EhMxOTIxMTY4MjU0NjQ0MjI1MzEyGAciCklNQUdFX1RFWFQ=", "action_card": false}, {"id": "76_1750898471.541", "type": "feed", "offset": 76, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898471, "updated_time": 1750898471, "target": {"id": "1921367315041194478", "type": "answer", "url": "https://api.zhihu.com/answers/1921367315041194478", "author": {"id": "0a76dc3efc0eb0cd388d18d377dffe5f", "url": "https://api.zhihu.com/people/0a76dc3efc0eb0cd388d18d377dffe5f", "user_type": "people", "url_token": "ghjb-37", "name": "我是多肉", "headline": "想过比电影还精彩的人生", "avatar_url": "https://picx.zhimg.com/50/v2-505c381c856cc3bb8b3e28075267e63d_l.jpg?source=b6762063", "is_org": false, "gender": 1, "followers_count": 8, "is_following": false, "is_followed": false}, "created_time": 1750869665, "updated_time": 1750869665, "voteup_count": 0, "thanks_count": 0, "comment_count": 0, "is_copyable": true, "question": {"id": "652075168", "type": "question", "url": "https://api.zhihu.com/questions/652075168", "author": {"id": "b5602df3b463d649676fd3a8c7b4852b", "url": "https://api.zhihu.com/people/b5602df3b463d649676fd3a8c7b4852b", "user_type": "people", "url_token": "someregret", "name": "<PERSON><PERSON><PERSON><PERSON>", "headline": "", "avatar_url": "https://pic1.zhimg.com/50/v2-b4feb386f32e2e8a88b5d466470b98de_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 41, "is_following": false, "is_followed": false}, "title": "做自媒体真的像看起来那样轻松吗？什么样的人适合做自媒体？", "created": 1712476199, "answer_count": 0, "follower_count": 0, "comment_count": 2, "bound_topic_ids": [649, 2566, 119106, 32797, 1365426], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "现在的职业几乎没有“轻松”的说法了，你所理解的轻松只限于一些博主告诉你的，他们或许需要你报名他们的课程，又或许只是需要一个你的关注或者点赞，总之，他们做这类内容一定是有目的性的。 对于轻松的理解，短视频时代很多视频都是15s左右，而且看起来拍摄剪辑并不存在什么难度，姑且我们可以认为它是轻松的。但是你做了之后会发现：拍摄前做妆造，布置场景，调试灯光，调试美颜参数，研究发布文案……这些都是无法呈现在成片…", "excerpt_new": "现在的职业几乎没有“轻松”的说法了，你所理解的轻松只限于一些博主告诉你的，他们或许需要你报名他们的课程，又或许只是需要一个你的关注或者点赞，总之，他们做这类内容一定是有目的性的。 对于轻松的理解，短视频时代很多视频都是15s左右，而且看起来拍摄剪辑并不存在什么难度，姑且我们可以认为它是轻松的。但是你做了之后会发现：拍摄前做妆造，布置场景，调试灯光，调试美颜参数，研究发布文案……这些都是无法呈现在成片…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"ktgROktj\">现在的职业几乎没有“轻松”的说法了，你所理解的轻松只限于一些博主告诉你的，他们或许需要你报名他们的课程，又或许只是需要一个你的关注或者点赞，总之，他们做这类内容一定是有目的性的。</p><p data-pid=\"QGD6J6hJ\">对于轻松的理解，短视频时代很多视频都是15s左右，而且看起来拍摄剪辑并不存在什么难度，姑且我们可以认为它是轻松的。但是你做了之后会发现：拍摄前做妆造，布置场景，调试灯光，调试美颜参数，研究发布文案……这些都是无法呈现在成片中的，而且我以上的设想只是一个简单的15秒变装视频或者跳舞视频，又或者是个简单的段子类。</p><p data-pid=\"7L159uOd\">如果涉及到vlog ，口播，轻剧情，难度会增加数倍。至少从我在行业里的6年中，我看不到自媒体轻松的展现。</p><hr/><p data-pid=\"rkIq4Pqa\">至于什么人适合做自媒体</p><p data-pid=\"MtrloBlh\">首先第一个重要的特质：从来不奢望较小的付出就能得到回报的人。机会主义者不适合，长期主义者相对好一点，就像题主问：自媒体真的那么简单吗？这个背后有一层意思就是：真的这么简单就能涨粉，就能赚钱吗？如果你带着自媒体简单的想法进去这个行业，大概率几个月就放弃了。所以，最重要的是心态正确，做长期主义者。</p><p data-pid=\"fGA1_uF7\">第二个比较重要的特质是分享欲，旺盛的分享欲。这个很好理解，做自媒体首先得能输出，会输出，如果你生活中就特别喜欢分享，把分享对象从朋友换成粉丝就可以了。</p><p data-pid=\"uhW5G8hu\">第三个我觉得是敏感。敏感的人会看到更多的世界，也会有更多输出的素材和角度，就像有些人可以把恋爱经历写成歌，但是有些人只会说：我们不合适。充足的情绪调动能力，强共情力，会让你有更好的创作空间，也更容易取得好的数据效果。</p><p data-pid=\"65yzIgIy\">最后一个我想留给专业的人。如果你本身有一定的自媒体专业技能，比如拍摄，比如剪辑，比如脚本，那么你在发展负责的角度上可以先考虑自媒体，这也是离你比较近的副业。另外就是其他的专业技能足够突出的人，只要你能真正的给人带来价值，在哪都可以表变现的。如果你拥有一项在线下已经实现了变现的技能，又在线上见过类似的人实现变现，那么你只要跟着她的路径走下去，大概率也是可以成功的。</p><p data-pid=\"0HRRtltP\">其实自媒体没那么神奇，也没那么神秘，把他当作一个职业去看待，不要当作赌博去看待</p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": true, "visited_count": 15, "favorite_count": 0, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1921367315041194478}", "attached_info": "CoYGCPu2tt6834aghgEQBBoJNzM0MDI0OTI1IKHN8MIGKAAwAEBMSiQKGVRTX1NPVVJDRV9XQVJNX1VQX05PUk1BTDISATAYACAAOgBKIgoXVFNfU09VUkNFX1dBUk1VUF9SVUNFTkUSATAYACAAOgBaCTEwNzE4MzAzN2IgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyEzE5MjEzNjczMTUwNDExOTQ0NziKAQk2NTIwNzUxNjiqAQlyZWNvbW1lbmTCASAwYTc2ZGMzZWZjMGViMGNkMzg4ZDE4ZDM3N2RmZmU1ZvIBCggMEgZOb3JtYWzyASgIChIkNzBiNDU0NTUtODA4Ni00NzJiLWI4MDItYjM1NzVhNzkxMWNm8gEGCAsSAjEzggIAiAKt57nN+jKSAiAwYTc2ZGMzZWZjMGViMGNkMzg4ZDE4ZDM3N2RmZmU1ZpoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhhQZXJpb2RJbnRlcmVzdFdlaWdodFJ1bGXKAhVVc2VyTGNuRXhpdFdlaWdodFJ1bGXKAhhDb250ZW50V2FybVVwQnJlYWtJblJ1bGXaAhlUU19TT1VSQ0VfV0FSTV9VUF9OT1JNQUwy6AID+gILTk9STUFMX0ZMT1eKAyBjODQ1YzJjYWU5Mjg0MmI2ODI5YjlkOWVmNWQ1MzM0OJIDF3poaWh1Oi8vdG9waWNzLzIzNjE5MzA5mgMNCgJ2MhAAGgVvdGhlcqgDD9gDAOoDC3RleHRfcnVjZW5l+gMfEgxVTktOT1dOX01PREUgACoNTk9fSU1BR0VfTU9ERYAEAIgEAJIEBk5vcm1hbJoEATOgBACoBACwBAC6BAJhacIEAzQwMMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAIAp6sz+BBQAAAAAAAAAAiQXFeYIB6bnSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUNkAYAoAZQqAYBkgIuCgk3MzQwMjQ5MjUSEzE5MjEzNjczMTUwNDExOTQ0NzgYBCIKSU1BR0VfVEVYVA==", "action_card": false}, {"id": "77_1750898471.246", "type": "feed", "offset": 77, "verb": "TOPIC_ACKNOWLEDGED_ANSWER", "created_time": 1750898471, "updated_time": 1750898471, "target": {"id": "1919543341739124013", "type": "answer", "url": "https://api.zhihu.com/answers/1919543341739124013", "author": {"id": "074e572410de15d0459bb75745c47d85", "url": "https://api.zhihu.com/people/074e572410de15d0459bb75745c47d85", "user_type": "people", "url_token": "71-84-75-72", "name": "煮酒当鸽", "headline": "", "avatar_url": "https://picx.zhimg.com/50/v2-f1be5ed24936a7311e75da3884b2bd6d_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 416, "is_following": false, "is_followed": false}, "created_time": 1750434796, "updated_time": 1750532467, "voteup_count": 1362, "thanks_count": 77, "comment_count": 22, "is_copyable": true, "question": {"id": "618697518", "type": "question", "url": "https://api.zhihu.com/questions/618697518", "author": {"id": "d451853ddea8d4dcdce300210adc7084", "url": "https://api.zhihu.com/people/d451853ddea8d4dcdce300210adc7084", "user_type": "people", "url_token": "jiang-jun-liang-97-90", "name": "歌秋宝宝", "headline": "", "avatar_url": "https://picx.zhimg.com/50/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=b6762063", "is_org": false, "gender": -1, "followers_count": 65, "is_following": false, "is_followed": false}, "title": "你是否亲眼见过性格懦弱胆小怕事的人蜕变成刚强老练、天不怕地不怕的人？", "created": 1692748923, "answer_count": 0, "follower_count": 0, "comment_count": 4, "bound_topic_ids": [740, 994, 1576, 2566, 16208], "is_following": false, "excerpt": "", "relationship": {"is_author": false}, "detail": "", "question_type": "normal"}, "excerpt": "建议去看看天涯神贴《不要害怕任何人和任何事》 原出处天涯神贴：《不要害怕任何人和任何事》 原作者：佚名 天涯神贴合集：天涯神贴合集 以下是原文内容： 你所谓的害怕，本质上是对后果的担忧，害怕生命中出现一些让自己无法应付的意外。正是这种对未知的不战而降，磨灭了你所有的勇气。但换个角度想，恐惧其实是人类刻在基因里的保护机制：在面对猛兽或自然灾害时，正是恐惧促使早期人类逃跑、躲藏，从而提高了生存几率。可在现…", "excerpt_new": "建议去看看天涯神贴《不要害怕任何人和任何事》 原出处天涯神贴：《不要害怕任何人和任何事》 原作者：佚名 天涯神贴合集：天涯神贴合集 以下是原文内容： 你所谓的害怕，本质上是对后果的担忧，害怕生命中出现一些让自己无法应付的意外。正是这种对未知的不战而降，磨灭了你所有的勇气。但换个角度想，恐惧其实是人类刻在基因里的保护机制：在面对猛兽或自然灾害时，正是恐惧促使早期人类逃跑、躲藏，从而提高了生存几率。可在现…", "preview_type": "default", "preview_text": "", "reshipment_settings": "allowed", "content": "<p data-pid=\"YVHhvz-V\"><b>建议去看看天涯神贴《不要害怕任何人和任何事》</b></p><p data-pid=\"RBbyQr9C\"><b>原出处天涯神贴：《不要害怕任何人和任何事》</b></p><p data-pid=\"7pibXDyz\"><b>原作者：佚名</b></p><p data-pid=\"zE1x32Oh\"><b>天涯神贴合集：</b></p><a data-draft-node=\"block\" data-draft-type=\"link-card\" href=\"https://link.zhihu.com/?target=http%3A//suo.im/tylt\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">天涯神贴合集</a><p data-pid=\"VDHmXoru\">以下是原文内容：</p><p data-pid=\"KTPJDfSO\">你所谓的害怕，本质上是对后果的担忧，害怕生命中出现一些让自己无法应付的意外。正是这种对未知的不战而降，磨灭了你所有的勇气。但换个角度想，恐惧其实是人类刻在基因里的保护机制：在面对猛兽或自然灾害时，正是恐惧促使早期人类逃跑、躲藏，从而提高了生存几率。可在现代社会，过度谨慎的生存策略早已不再适用。只有敢于横冲直撞、无视权威的勇者，才更有机会闯出自己的一片天地。</p><p data-pid=\"30hdJCvD\">这个世界的游戏规则是，强者在“通关”后为后来者设置重重障碍，以巩固自己的胜利果实。一旦看清了这一点，你就会明白：要变得强大，首先要将勇气拉满，勇敢破除规则；同时，也要搞清楚失败的风险。如果风险在自己承受范围之内，就大胆去做。多做几次，一切都会变成经历；经历铸就阅历，阅历带来格局。这便是一个人由弱变强的必经之路。</p><p data-pid=\"cZ1Vm5po\">你要对这个世界保持一丝“虚伪”——别在弱者的自我贬低里无形流失能量。任何人、任何成就，都并非凭一己之力。马云再厉害，也不过是“时代造就的马云”，从无“马云时代”一说。你的仰望投射，正是虚化了对方的价值，也阉割了你自己内心的光芒。只有当你停止把光芒投向别人，才会重新点燃自己。</p><p data-pid=\"EfTF8NFQ\">要想摆脱恐惧，不要将精力放在情绪本身。在事未开始前，别总盯着失败的后果不放，而应思考步骤、策略和实操方法。大多数人一辈子浑浑噩噩，正因为他们把情绪看得比行动更重要。一个能摒弃情绪的人，理性永远占据上风；而目光如炬的人，会接纳风险，将混沌化为惊险，将迷茫化为清晰，最终蜕变为强者。</p><p data-pid=\"KxxFyWbB\">至于赚钱，虽与人脉、资源、行业有关，但归根结底，你自己对成功的渴望才是驱动力。富二代若无冲劲，再多资源也难成事；一穷二白却极度渴望成功的普通人，即便资源匮乏，也会拼命去做，直到拿到结果。真正阻碍你成功的，不是天赋、能力，也不是他人的目光，而是你内心的自我设限。</p><p data-pid=\"qxszYEMq\">95%的人总想着准备更多条件才行动，结果拖延数年仍未开始；而那5%的人，一旦想做就立刻行动、试错、优化、再试，直到精通极致。因为他们坚信：在做事的路上，会找到所需的一切资源。打工人不屑于摸鱼，只为积累经验；创业者主动学习同行打法，积攒真实口碑。只有这样，才能拉开与他人的差距。</p><p data-pid=\"rbJABL20\">最后，不要害怕任何人、任何事。保持自己的节奏，在自我修炼的道路上乐此不疲，用脚步丈量人生。既不要高估世界，也不要低估自己。这既是强者的起点，也是他们最终的归宿。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"8HvjEztR\"><b>天涯神贴合集：</b></p><a data-draft-node=\"block\" data-draft-type=\"link-card\" href=\"https://link.zhihu.com/?target=http%3A//suo.im/tylt\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">天涯合集</a><p></p>", "relationship": {"is_thanked": false, "is_nothelp": false, "voting": 0}, "is_labeled": false, "visited_count": 95037, "favorite_count": 5980, "answer_type": "normal", "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "brief": "{\"source\": \"TS\", \"type\": \"answer\", \"id\": 1919543341739124013}", "attached_info": "CpgGCPu2tt6834aghgEQBBoJNzMzMjE4Nzg4IOyH1sIGKNIKMBZATUooChNUU19TT1VSQ0VfRkVFRFJFX1Y5EgEwGAAgADoKeyJyYXciOiIifUo/CipUU19TT1VSQ0VfWlJFQ0FMTF9GRUVEUkVfTkVXQklFX0hPVVJMWV9SVU0SATAYACAAOgp7InJhdyI6IiJ9SigKHVRTX1NPVVJDRV9ORUFSTElORV9DT05URU5UX1YyEgEwGAAgADoAWgg5OTc2ODYwMWIgNTkwMTk5Mzg5N2IyYjY3MTUzNGI0MWRmZTE3MDM5NDdyEzE5MTk1NDMzNDE3MzkxMjQwMTOKAQk2MTg2OTc1MTiqAQlyZWNvbW1lbmTCASAwNzRlNTcyNDEwZGUxNWQwNDU5YmI3NTc0NWM0N2Q4NfIBCggMEgZOb3JtYWzyASgIChIkM2I4YzJiNzMtZDE2ZC00YWM0LWIyZmMtZmVlMTY2Y2M4ODNl8gEGCAsSAjEzggIAiAKt57nN+jKSAiAwNzRlNTcyNDEwZGUxNWQwNDU5YmI3NTc0NWM0N2Q4NZoCAMoCFlNob3JJbnRlcmVzdFdlaWdodFJ1bGXKAhZSZXZpc2l0VmFsdWVXZWlnaHRSdWxlygIVVXNlckxjbkV4aXRXZWlnaHRSdWxl2gITVFNfU09VUkNFX0ZFRURSRV9WOegCAvoCC05PUk1BTF9GTE9XigMgYzg0NWMyY2FlOTI4NDJiNjgyOWI5ZDllZjVkNTMzNDiaAw0KAnYyEAAaBW90aGVyqAO95gXYAwDqAwlmZWVkcmVfdjn6Ax8SDFVOS05PV05fTU9ERSAAKg1OT19JTUFHRV9NT0RFgAQAiAQAkgQGTm9ybWFsmgQBMqAEAKgEALAEALoEBm1hbnVhbMIEAzE2MMgEANIED+aOqOiNkOW3suabtOaWsNgEAPAEAPkEAAAAYO5Grj+BBQAAAAAAAAAAiQXFeYIB6bnSP5IFAJoFA2RmdKIFA2RmdLIFATG5BQAAAAAAAAAA0AUA4AUA6AUA8AUNkAYAoAZRqAYAkgIuCgk3MzMyMTg3ODgSEzE5MTk1NDMzNDE3MzkxMjQwMTMYBCIKSU1BR0VfVEVYVA==", "action_card": false}], "paging": {"is_end": false, "is_start": false, "next": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=down&ad_interval=-10&after_id=77&desktop=true&end_offset=81&page_number=14&session_token=5901993897b2b671534b41dfe1703947", "previous": "https://www.zhihu.com/api/v3/feed/topstory/recommend?action=pull&ad_interval=-10&before_id=77&desktop=true&end_offset=81&page_number=14&session_token=5901993897b2b671534b41dfe1703947", "totals": 0}, "fresh_text": "推荐已更新"}