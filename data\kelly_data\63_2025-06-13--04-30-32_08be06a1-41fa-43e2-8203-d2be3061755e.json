{"data": [{"id": "1527466576089", "type": "feed", "target": {"id": "310076435", "type": "answer", "url": "https://api.zhihu.com/answers/310076435", "voteup_count": 1156, "thanks_count": 143, "question": {"id": "22860487", "title": "为什么谷歌、微软等美国顶尖企业会有那么多印度裔高管？", "url": "https://api.zhihu.com/questions/22860487", "type": "question", "question_type": "normal", "created": 1393398525, "answer_count": 597, "comment_count": 37, "follower_count": 10950, "detail": "据统计硅谷三分之一的企业高管是印度人，谷歌十三位高管中五位是印度人，还都是安卓、chrome等这些核心业务。原来只知道印度人it很牛，但现在已经全面进入美国it公司的管理层了，这里面有什么原因？", "excerpt": "据统计硅谷三分之一的企业高管是印度人，谷歌十三位高管中五位是印度人，还都是安卓…", "bound_topic_ids": [484, 1018, 5231, 10705], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1517586039, "created_time": 1517581172, "author": {"id": "58f9e8123cc5268824b5134d5eea5862", "name": "放浪者", "headline": "下乡锻炼中", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/thewarrior", "url_token": "thewarrior", "avatar_url": "https://picx.zhimg.com/v2-fd34f50f4658887560f1363c868cb3ed_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 93, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"67BFS8BC\">最近几个事情让我觉得，即便机会放在国人面前，他们也会退缩回到自己习惯的埋头苦干中去。比如下面的场景，是和美国的朋友谈国内机会的时候很常见的：</p><p data-pid=\"2cWiQ6lR\">一般是湾区或雅图FLAG等公司的朋友说最近去面了阿里啊或者跟腾讯聊过啊，感觉怀才不遇，对方一般给个阿里P8最多P9就把自己打发了啊，感觉自己在美帝已经是技术专家了能力秒杀国内各种忽悠，怎么能给这样呢。然后通常会说想到国内换成管理岗。</p><p data-pid=\"27R095Kx\">好啊，我是很支持的，然后一般对方会说自己在美帝也开始带人带项目了有经验，我说嗯嗯，那就太好了，既然你目标是往国内发展，那就还是先要有铺垫，好歹跟国内先有来往吧。朋友都说对对。</p><p data-pid=\"PcBVA27c\">然后我说那我们看有没有什么可以发起的点，当然最好是能发起公司层面的某些合作；这种时候朋友们就打退堂鼓了，常见的说法是“哎，这些事情都是我们的市场部们负责，我们不管的“～～～～额，不是刚还在说想走管理岗，想往上爬么。。。这。。。</p><p data-pid=\"qpLqnsSO\">好吧，我也理解，毕竟人在美帝，要发起商业合作的难度比较高，对马工不现实（但是从对方的回答和态度，我基本上会判断啥“管理经验”纯属瞎掰，因为一个真的做过manager的人，必然会有基本的business sense，不会一句市场部门负责就把可能的商业机会也是自己往上爬的机会丢掉）。</p><p data-pid=\"bNg8eLpI\">但是没关系，我觉得马工要回国还是先从技术专家路线开始比较合适，我有时候就会说那帮你安排回来搞几个tech talk什么的嘛，混个脸熟。。。朋友们又会面露难色：没有vacation呀。。。额，当年我决定了回国，是周五中午走（三番飞北京的飞机中午2点那班），这样周五可以不请假，然后请周一的假来个wfh啥的，一个月连飞这么两次，然后再利用年底的假期。。。我看我那些朋友带娃或者爬山多high的，喊他们回个国来接触一下就不行了。。。</p><p data-pid=\"2OfVib8W\">从一个回国发展的例子，就可以看出马工们是多么的畏手畏脚循规蹈矩，完全没有为了自己的目标去搏一把的干劲。富贵险中求，难道真以为美国的公司让你坐着就升职加薪了？哪里没有争斗？你要么回国争，要么在美国抢。但是马工都做不到，要和国内合作他们怕风险或者觉得国内合作看不上，要在美国去抢他们又说没有资源没有支持。</p><p data-pid=\"Y4HCAs_I\">刷题，只有刷题他们才安心，才觉得回到了妈妈的怀抱！</p>", "excerpt": "最近几个事情让我觉得，即便机会放在国人面前，他们也会退缩回到自己习惯的埋头苦干中去。比如下面的场景，是和美国的朋友谈国内机会的时候很常见的： 一般是湾区或雅图FLAG等公司的朋友说最近去面了阿里啊或者跟腾讯聊过啊，感觉怀才不遇，对方一般给个阿里P8最多P9就把自己打发了啊，感觉自己在美帝已经是技术专家了能力秒杀国内各种忽悠，怎么能给这样呢。然后通常会说想到国内换成管理岗。 好啊，我是很支持的，然后一般对方…", "excerpt_new": "最近几个事情让我觉得，即便机会放在国人面前，他们也会退缩回到自己习惯的埋头苦干中去。比如下面的场景，是和美国的朋友谈国内机会的时候很常见的： 一般是湾区或雅图FLAG等公司的朋友说最近去面了阿里啊或者跟腾讯聊过啊，感觉怀才不遇，对方一般给个阿里P8最多P9就把自己打发了啊，感觉自己在美帝已经是技术专家了能力秒杀国内各种忽悠，怎么能给这样呢。然后通常会说想到国内换成管理岗。 好啊，我是很支持的，然后一般对方…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1527466576, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1527250074360", "type": "feed", "target": {"id": "274156509", "type": "answer", "url": "https://api.zhihu.com/answers/274156509", "voteup_count": 1850, "thanks_count": 559, "question": {"id": "57662817", "title": "你身边的精英都有什么样的特质？", "url": "https://api.zhihu.com/questions/57662817", "type": "question", "question_type": "normal", "created": 1490572360, "answer_count": 632, "comment_count": 33, "follower_count": 41389, "detail": "如题", "excerpt": "如题", "bound_topic_ids": [405, 2566, 27237, 91364], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "26af8c8f0d0412cae2413448fd11bbbd", "name": "<PERSON>", "headline": "一蓑烟雨任平生", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/yun-duo-li-de-re-dai-yu", "url_token": "yun-duo-li-de-re-dai-yu", "avatar_url": "https://picx.zhimg.com/v2-ed125c3fff90a1ab388be0a6c7a07c1d_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1518377205, "created_time": 1512926105, "author": {"id": "358d09b70962094ba73ea8d3f76d14cc", "name": "晚晚大人吃掉你", "headline": "公众号：她在河流下面", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/sunjiaxi", "url_token": "sunjiaxi", "avatar_url": "https://picx.zhimg.com/v2-898222fbf316959095e5e1ca545c3fd1_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 173, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"irSFNK3h\">我指的是top领域的精英大牛，并非中等偏上的潜力股。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"1_xlfPSj\">1 不好面子。不会的，开口就问，能求助身边人快速到达途径的，快速寻求合作机会。</p><p data-pid=\"OyslwPCI\">2 自己能独立完成的，自己做。没有懒癌。</p><p data-pid=\"3i0c8AB1\">3 分清主次，步步为营，冲刺不怕输。无所谓完美主义，在实践中修改完善。</p><p data-pid=\"R8BdYiP9\">4 反应速度极快，快速把能把握的资源窝在手里，能垄断则垄断。</p><p data-pid=\"78c-fjd5\">5 极强的抗压能力。有一套自己抗压的办法。沉得住气。</p><p data-pid=\"eubKLwpj\">6 优柔寡断这种特质，绝不会出现在他们身上。直接，该拒绝立刻拒绝，该低头立刻低头。</p><p data-pid=\"l1n1eb43\">7 事业与感情，事业靠前。</p><p data-pid=\"Mt5geMnO\">8 自我，想要的决不放弃。</p><p data-pid=\"8biTsr2T\">9 完成梦想后，立刻呈现高情商洗白自己。</p><p data-pid=\"JbHV3hns\">洗白这条，不少人问。没有特殊的意思，我意思是，我们每个人爬一步本来就是要杀破千军万马的，到了高位难免或多或少要裁决掉一些阻碍自己的因素。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"13STSaLS\">所以，我也是通过这套方法，来判断哪些人可能会成为精英。</p><p data-pid=\"qIC7i_v7\">很多人说，会做人的人可能会成功，但是很不幸，我身边这些年有可能成为精英的人，都是这样的人群。</p><p data-pid=\"7fKGEsUQ\">而会做人的，心不够狠的，往往是后面会被吃掉的。</p>", "excerpt": "我指的是top领域的精英大牛，并非中等偏上的潜力股。 1 不好面子。不会的，开口就问，能求助身边人快速到达途径的，快速寻求合作机会。 2 自己能独立完成的，自己做。没有懒癌。 3 分清主次，步步为营，冲刺不怕输。无所谓完美主义，在实践中修改完善。 4 反应速度极快，快速把能把握的资源窝在手里，能垄断则垄断。 5 极强的抗压能力。有一套自己抗压的办法。沉得住气。 6 优柔寡断这种特质，绝不会出现在他们身上。直接，该拒…", "excerpt_new": "我指的是top领域的精英大牛，并非中等偏上的潜力股。 1 不好面子。不会的，开口就问，能求助身边人快速到达途径的，快速寻求合作机会。 2 自己能独立完成的，自己做。没有懒癌。 3 分清主次，步步为营，冲刺不怕输。无所谓完美主义，在实践中修改完善。 4 反应速度极快，快速把能把握的资源窝在手里，能垄断则垄断。 5 极强的抗压能力。有一套自己抗压的办法。沉得住气。 6 优柔寡断这种特质，绝不会出现在他们身上。直接，该拒…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1527250074, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1527249853132", "type": "feed", "target": {"id": "391657429", "type": "answer", "url": "https://api.zhihu.com/answers/391657429", "voteup_count": 928, "thanks_count": 196, "question": {"id": "57662817", "title": "你身边的精英都有什么样的特质？", "url": "https://api.zhihu.com/questions/57662817", "type": "question", "question_type": "normal", "created": 1490572360, "answer_count": 632, "comment_count": 33, "follower_count": 41389, "detail": "如题", "excerpt": "如题", "bound_topic_ids": [405, 2566, 27237, 91364], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "26af8c8f0d0412cae2413448fd11bbbd", "name": "<PERSON>", "headline": "一蓑烟雨任平生", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/yun-duo-li-de-re-dai-yu", "url_token": "yun-duo-li-de-re-dai-yu", "avatar_url": "https://picx.zhimg.com/v2-ed125c3fff90a1ab388be0a6c7a07c1d_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1526305988, "created_time": 1526305582, "author": {"id": "c91e7874ab227d51f83bd79c3db59440", "name": "灏泽", "headline": "落魄世家何足挂齿？", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/hao-ze-yin-shi", "url_token": "hao-ze-yin-shi", "avatar_url": "https://picx.zhimg.com/v2-a93f9399f589e0b62813c4d1a1756103_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 62, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"3G617yjo\"><b>三个字，忍 韧 毅，</b></p><p data-pid=\"XCUuw3A_\"><b>是能忍人所不能忍，</b></p><p data-pid=\"QQnfMJsP\"><b>是能承人所不能承，</b></p><p data-pid=\"uZglB3Nw\"><b>是能扛人所不能抗。</b></p><p data-pid=\"AjyoW1CV\"><b>哪怕被逼入绝境，也绝不自暴自弃，</b></p><p data-pid=\"eU23kyLR\"><b>只要我活着，我就能他娘的卷土重来。</b></p><p data-pid=\"-rPDbAnw\">这种特质，正是穷人逆袭，富人保级的最大秘诀。</p><p data-pid=\"EdlBAgAu\">案例往大了说，有毛老爷，初期的派系争权，中期带军求生，后期的的国共大战，</p><p data-pid=\"BHYVt6Rw\"><b>绝对的潜龙之相，只求风雨便化天。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"XXtbPfG7\">往中了说，我国的史玉柱、马云、王健林，灯塔国的乔布斯、马斯克、还有那不死鸟特朗普。</p><p data-pid=\"RYB3Rguq\">各个都有在深渊中苦苦求生的历史，</p><p data-pid=\"dxqMwZEO\">但这几个家伙可都是拼了老命，扒烂了指甲盖在挣扎，最终鲜血淋漓的爬回人间。</p><p data-pid=\"5jkDtz-x\"><b>恰如猛虎卧荒丘,潜伏爪牙忍受。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"tYnr3RDZ\">往小了讲，你身边的励志发小，同事里的拼命三郎，朋友中的逆袭典型。</p><p data-pid=\"WuZLePaX\">没一个是靠纯粹的精明和小智慧成功的，</p><p data-pid=\"6p7K4kAJ\">都是饱经历练，社会阶级每上升一层，</p><p data-pid=\"sM7TxXCM\">就要被活脱脱的剥一层皮。</p><p data-pid=\"c-ft7In1\"><b>犹如蛇蝎百虫进阶，留的满地骨血。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"l_VK4l2A\">那些罗列出来的一条条繁复所谓<b>精英特质，</b></p><p data-pid=\"UZz3ULSt\">其实都是属于锦上添花的加分项，</p><p data-pid=\"1GgcV00k\">我看过无数人的命格和运势，</p><p data-pid=\"NRIPbPuI\">有的最终富可敌国，有的只能略有小成，</p><p data-pid=\"XO04--ph\">命兮运兮，但都不阻碍他们担得精英之名。</p><p data-pid=\"aRDcGLtz\"><b>精英是什么？</b></p><p data-pid=\"zXeHvF-8\"><b>就是当凡人被挫折打垮在地上，举起双手时，</b></p><p data-pid=\"0XABNNlJ\"><b>他们却把嘴里的牙齿和鲜血吐出，然后朝敌人继续下一次冲锋。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-0285b5b31b13483ca8c5cb8de0c45745_b.jpg\" data-rawwidth=\"1080\" data-rawheight=\"1080\" data-size=\"normal\" data-original-token=\"v2-e8ebecb2de9b1ce56054c2e1b5d45f16\" data-default-watermark-src=\"https://pica.zhimg.com/v2-e29a9a02d5c7e0de289953cfb83314ec_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-0285b5b31b13483ca8c5cb8de0c45745_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;1080&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1080\" data-rawheight=\"1080\" data-size=\"normal\" data-original-token=\"v2-e8ebecb2de9b1ce56054c2e1b5d45f16\" data-default-watermark-src=\"https://pica.zhimg.com/v2-e29a9a02d5c7e0de289953cfb83314ec_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-0285b5b31b13483ca8c5cb8de0c45745_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-0285b5b31b13483ca8c5cb8de0c45745_b.jpg\"/></figure>", "excerpt": "<b>三个字，忍 韧 毅，</b> <b>是能忍人所不能忍，</b> <b>是能承人所不能承，</b> <b>是能扛人所不能抗。</b> <b>哪怕被逼入绝境，也绝不自暴自弃，</b> <b>只要我活着，我就能他娘的卷土重来。</b>这种特质，正是穷人逆袭，富人保级的最大秘诀。 案例往大了说，有毛老爷，初期的派系争权，中期带军求生，后期的的国共大战， <b>绝对的潜龙之相，只求风雨便化天。</b> 往中了说，我国的史玉柱、马云、王健林，灯塔国的乔布斯、马斯克、还有那不死鸟特朗普。 各个都有在深渊中苦苦…", "excerpt_new": "<b>三个字，忍 韧 毅，</b> <b>是能忍人所不能忍，</b> <b>是能承人所不能承，</b> <b>是能扛人所不能抗。</b> <b>哪怕被逼入绝境，也绝不自暴自弃，</b> <b>只要我活着，我就能他娘的卷土重来。</b>这种特质，正是穷人逆袭，富人保级的最大秘诀。 案例往大了说，有毛老爷，初期的派系争权，中期带军求生，后期的的国共大战， <b>绝对的潜龙之相，只求风雨便化天。</b> 往中了说，我国的史玉柱、马云、王健林，灯塔国的乔布斯、马斯克、还有那不死鸟特朗普。 各个都有在深渊中苦苦…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1527249853, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1527182371214", "type": "feed", "target": {"id": "27936550", "title": "年收入 15 万元左右的年轻人如何理财？", "url": "https://api.zhihu.com/questions/27936550", "type": "question", "question_type": "normal", "created": 1422868041, "answer_count": 1655, "comment_count": 285, "follower_count": 70474, "detail": "", "excerpt": "", "bound_topic_ids": [395, 1895, 12706, 19800, 637155], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1527182371, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1527083078815", "type": "feed", "target": {"id": "112562642", "type": "answer", "url": "https://api.zhihu.com/answers/112562642", "voteup_count": 440, "thanks_count": 110, "question": {"id": "31837066", "title": "刚入职北美 IT 公司，如何提高？", "url": "https://api.zhihu.com/questions/31837066", "type": "question", "question_type": "normal", "created": 1435778088, "answer_count": 22, "comment_count": 6, "follower_count": 1009, "detail": "楼主刚刚从美国某校CS master毕业，即将入职某知名IT公司，感觉FLAG里的同事都是各种算法ACM大牛，自己只是一个在学校做过一些课程项目的master，纯粹刷题准备面试然后才能拿到offer的，很担心入职后被鄙视水平，请问各位在北美企业就职的程序员们，怎么样才能在三个月之内提高自己的水平从entry level变成至少不拖后腿的senior的水平？", "excerpt": "楼主刚刚从美国某校CS master毕业，即将入职某知名IT公司，感觉FLAG里的同事都是各…", "bound_topic_ids": [40, 99, 707, 1354, 2084], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1469175294, "created_time": 1469173643, "author": {"id": "eef16ca7df4d4cb380f5f6ae41eefe84", "name": "卢毅luis", "headline": "曾孵化过安全领域的独角兽公司", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/svjoke", "url_token": "sv<PERSON><PERSON>", "avatar_url": "https://picx.zhimg.com/v2-82c7ae57295255f700a32c061f7befa1_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["创业", "人工智能"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "创业等 2 个话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "v2-af063bec99e4decd9aa90c38d709e46f", "avatar_url": "https://pic1.zhimg.com/v2-af063bec99e4decd9aa90c38d709e46f_720w.jpg?source=32738c0c", "description": "", "id": "19550560", "name": "创业", "priority": 0, "token": "19550560", "type": "topic", "url": "https://www.zhihu.com/topic/19550560"}, {"avatar_path": "v2-c41d10d22173d515740c43c70f885705", "avatar_url": "https://pica.zhimg.com/v2-c41d10d22173d515740c43c70f885705_720w.jpg?source=32738c0c", "description": "", "id": "19551275", "name": "人工智能", "priority": 0, "token": "19551275", "type": "topic", "url": "https://www.zhihu.com/topic/19551275"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}], "title": "创业等 2 个话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 26, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"3hPHCkUM\">如何提高的方法论其实都摆在哪里，问题是知易行难。</p><p data-pid=\"_oPsMhu4\">大公司和小公司的职场逻辑其实不太一样，说一些通用的常识吧。</p><p data-pid=\"cH9oSRJ2\"><b>不要怕被鄙视水平<br/></b></p><p data-pid=\"_TWyz9hW\">新人入职都要有一段学习期，在这段时间你可能要接触各个方面的新知识，学习各种技术栈，学习各种内部工具，也会有Mentor带你。</p><p data-pid=\"aQ-i1PnW\">你自己什么水平其实是掩饰不了的，有些人就是天赋异禀，但是每个人都有自己的特点。程序员是个技术工种，本质上做一个合格的程序员只要你足够努力就好了。</p><p data-pid=\"OyzgX_-I\">在这段学习期间，你要真的去掌握你各种需要掌握的东西，不懂就去问，如果问了第一遍你还不懂，那你就问第二遍。</p><p data-pid=\"JZtiHEIs\">你需要读很多的代码，关于读代码，可能有必要先找你的Mentor解释一下架构和大概的代码惯例conventions。</p><p data-pid=\"UB1Gq8pv\">问问题也是个学问，在问问题前，自己要做好功课，很多问题先去搜索文档，先去自己读代码，不要问一些通过Google搜索就能查到的问题。</p><p data-pid=\"omvQZEjw\">简单说来，如果一个问题，如果你认为你需要其他人N倍(比如N&gt;3)以上的时间还搞不明白，那就可能要去问mentor或者其他人了。</p><p data-pid=\"lHaI38ct\">然后在学习期间，多做笔记，真正的消化这些知识。</p><p data-pid=\"mR6u2hHL\"><b>多写Wiki是一个非常好的习惯</b>，把你学习到的东西记录下来然后内化为自己的知识，还可以分享给其他人。</p><p data-pid=\"rI_KgWzA\">真的不要怕问问题被鄙视，从来就没有愚蠢的问题。There&#39;s no such thing as a dumb question。</p><p data-pid=\"jF-tgPHp\">有mentor的期间，特别是最开始的几个月的，真的是快速学习的好机会，一定要多和mentor预约一些时间来了解整个code base。</p><p data-pid=\"fnaSlwIG\">一个好的习惯就是加入一个新的组后，和组里的其他人都预约好一两个会议来了解别人在做什么，这样开组会的时候就不至于什么都不知道。</p><p data-pid=\"pGdJTW-A\"><b>多问为什么(Ask Why)<br/></b></p><p data-pid=\"qNXPHDpC\">这个习惯其实很重要，体现在方方面面。</p><p data-pid=\"JSp6gNlF\">开组会的时候，如果遇到不懂的地方，可以提出来。</p><p data-pid=\"lEA9OLo1\">还有很多时候，特别是写代码的时候，有不少人习惯从代码库里复制然后粘贴一部分代码。</p><p data-pid=\"ac7ZN3lg\">从代码库里学习是一个很好的方式，因为学习大家常用的代码写法一定是比较高效的。但即使是复制粘贴过来的。一定要理解自己为什么要这么写？</p><p data-pid=\"ZbSUxCpQ\">”别人这么写，我也这么写“这从来都不是一个合理的理由。</p><p data-pid=\"kDsLe9Ox\">很多时候，多问为什么这么做是快速学习的方式，特别看代码的时候，还有帮别人做Code Review的时候。</p><p data-pid=\"gMTjiQ74\">在涉及到数字的时候，也要特别的小心，为什么要设置参数为这个值，为什么要初始化为某个值。</p><p data-pid=\"rLC3fx5e\">为什么要这么命名，为什么要使用某个函数，etc...</p><p data-pid=\"Q5TQtU_A\">要真正的懂和理解自己的做法，而不能是想当然。</p><p data-pid=\"SDqgXCf1\"><b>大部分时候，想当然是必然犯错误。</b></p><p data-pid=\"yZ4HiNWX\">Ask Why另一个很重要的地方就是当老板给你任务时，你也要多问为什么。</p><p data-pid=\"qVWjCNGG\"><b>老板可能会让你做A做B，但是你要问清楚这些任务的背后的目的是什么，然后可能实际上你应该做的是C。</b>很多时候不问清楚目的而只是为了完成任务而完成任务可能会很低效。</p><p data-pid=\"88WTYV3h\"><b>关于Code Review</b></p><p data-pid=\"sxglI6CK\">前面提到了Code Review其实是一个很好的学习机会，请一定要完善好自己的代码后再提交给其他人做Code Review，就是完完全全的理解自己每一行代码后（对每一行代码ask why，想想能不能写的更好，想想能不能替换成其他的方式），完完全全的对自己的代码有信心后再让对方Code Review。</p><p data-pid=\"niRQsXyb\">Code Review前要<b>测试好</b>，另一方面要符合最佳实践，从命名到coding style，自己多读读Best coding practices之类的指南。</p><p data-pid=\"bPmWFdPN\">有时会出现在对方提出修改意见后，然后再改，然后再Code Review一次，然后再改。</p><p data-pid=\"BIM3_I5s\">这种back and forth是最浪费时间的，要避免这种情况就是完善好自己的代码，避免多次back and forth。</p><p data-pid=\"mIKUbV7-\">当然Code Review也是一个很好的学习过程。</p><p data-pid=\"h7QJv-F3\">Code Review别人的代码也是一个很好的学习过程。</p><p data-pid=\"BpnaY2d1\"><b>关于犯错误</b></p><p data-pid=\"UcwvQi4O\">刚刚入职总是会犯一些错误，最重要的一点就是不要重复自己的错误。</p><p data-pid=\"ig9ekEje\">要按照完整的流程来部署代码，一定要各个环节测试好自己的代码，从Code Review拿到thumb up，再到unit test,到staging上测试 再到canary上测试，再到production。</p><p data-pid=\"fNgqqA7k\">在大公司相对好一些，但小公司新人经常直接有机会break the production，这是很危险的，有些公司真的会因为这个开除人。</p><p data-pid=\"Iyjbg9BK\"><b>如果对自己代码不自信，一定不要部署。</b></p><p data-pid=\"Y0Q-JpyF\">而对自己代码的自信建立在了解整个环境和代码库上。</p><p data-pid=\"oZ61pS1R\"><b>要有ownership, take the initiative <br/></b></p><p data-pid=\"f6lGk6-a\">这点上很多国人做的不好，经常是为了完成任务而完成任务。</p><p data-pid=\"ecqGMyLY\">最好是把自己从事的那部分工作当成自己的，积极主动的去改进它，时刻想着如何能做的更好。</p><p data-pid=\"4f6i7-F7\">这个和Ask Why是相辅相成的，Ask Why能帮你发现有些地方过去的方式方法可以改进，就应该尝试去改善。</p><p data-pid=\"A8rIKGQ3\">其实每个人的成绩大家都看在眼里，你如果积极主动的去做事情，还是会被认可。</p><p data-pid=\"20tJyKQo\">当然积极的让大家知道你在做的事情和你的贡献也很重要。</p><p data-pid=\"-orJNNzR\">有好的想法，应该主动的去做，和老板和同事要资源去完成这个任务，不过新手阶段对这个要求可能比较低。</p><p data-pid=\"9Qk5lWGc\">如果你own a product/own a service/own a code base，那你基本就无法被替代了。 </p><p data-pid=\"zeW3JHxI\"><b>和Manager保持良好的关系</b></p><p data-pid=\"mtp4t0LX\">这个其实是一件很麻烦的事情，一个人工作的开心不开心，其实和manager有非常大的关系，也见过牛人跟老板闹翻的。</p><p data-pid=\"s6ndDy9-\">一般而言，能遇上一个你喜欢的老板，非常重要，如果你不喜欢你的老板，很大可能TA也不会喜欢你。</p><p data-pid=\"Dy8c1mZ9\">和老板保持沟通非常重要，一方面时刻让他知道你在做什么，有什么进展，遇到了什么问题，这些都非常重要。另一方面也要保持一定的私人关系。多关心关心你老板，一起多吃吃饭。</p><p data-pid=\"ZNlMHVgv\">一般每个公司对每个level的工程师都有明确的要求和任务( an engineering ladder, the job descriptions and levels of an engineering organization)。要了解自己的能力边界，要明确老板对你的期望(Expectation)，不要给老板错误的期望(Expectation)，如果老板对你期望太高而你完成不了，这个是非常耽误事情的。老板给你的有些事情，如果你觉得做不了，你要明确的把你认为可能遇到困难说出来，如果这些困难的确解决不了，自然应该让别人来做或者放弃。</p><p data-pid=\"KcfEvHPK\">在和老板1 on 1的时候，如果老板暗示你什么可以做的更好，你一定要小心。一般而言，在美国老板说你某些方面做的一般，那说明这方面你做的比较差。如果老板明确指出你应该改进什么问题，那你一定要非常严肃的对待了，如果不能及时改进，是非常危险的。</p><p data-pid=\"UfhgmqB9\">总体而言，就是要Get things Done,不要耽误事。在比较Push的创业公司，还要快速的Get things Done。</p><p data-pid=\"NcgG1Qgl\">如果和老板处不好，如果条件允许的话，是要慎重考虑换组的。</p><p data-pid=\"vEiNteLv\">如果一个事情不是很自信你是否能做，又想挑战自己，这个就要看公司文化和你老板是否鼓励了。</p><p data-pid=\"B-cWUpB1\"><b>要时刻有进展(making progress)</b></p><p data-pid=\"E9_OCRRB\">在工作中，最重要的是时刻有进展，否则就是在浪费时间。</p><p data-pid=\"z5uyJ0wI\">一个任务完成了当然是进展。</p><p data-pid=\"9lJafGCa\">一个任务，如果你遇到了困难，一定要把你的尝试的解决方案给老板说，为什么你的方案失败了，可以寻求帮助，而不是困在那里什么都不做。</p><p data-pid=\"DnDbc1sX\">寻求帮助的时候一定要aggressive一点。</p><p data-pid=\"bo7-QnQP\">发现一个不可行的方案也是一种进步，但是千万不要卡在那里，什么也不做，这个是非常危险的。</p><p data-pid=\"FozcyHXf\"><b>You should try harder</b></p><p data-pid=\"ifSMa8F0\">最好来句鸡汤吧，世界上大部分事情要做好总是靠努力就可以足够的，你不够好，一定是不够努力。</p><p data-pid=\"tzd4pLw6\">从好到伟大(from good to great)，那才需要天赋。</p><p data-pid=\"D84y7qmH\">----</p><p data-pid=\"IwGCwY1o\">摔桌，有人居然认为我只是个段子手，随便甩两个回答打脸</p><a href=\"https://www.zhihu.com/question/19878052/answer/32361868\" class=\"internal\">JavaScript 中，定义函数时用 var foo = function () {} 和 function foo() 有什么区别？ - 严肃的回答</a><a href=\"https://www.zhihu.com/question/19732473/answer/20851256\" class=\"internal\">怎样理解阻塞非阻塞与同步异步的区别？ - 严肃的回答</a><br/><br/><p data-pid=\"XWMT3SZh\">当然我承认我段子编的也很好（逃</p><a href=\"https://www.zhihu.com/question/33474046/answer/94874265\" class=\"internal\">销售最厉害的招数都有哪些？ - 严肃的回答</a><a href=\"https://www.zhihu.com/question/20681465/answer/17970621\" class=\"internal\">中国古代名妓的生活和工作是怎样的？ - 严肃的回答</a>", "excerpt": "如何提高的方法论其实都摆在哪里，问题是知易行难。 大公司和小公司的职场逻辑其实不太一样，说一些通用的常识吧。 <b>不要怕被鄙视水平 </b>新人入职都要有一段学习期，在这段时间你可能要接触各个方面的新知识，学习各种技术栈，学习各种内部工具，也会有Mentor带你。 你自己什么水平其实是掩饰不了的，有些人就是天赋异禀，但是每个人都有自己的特点。程序员是个技术工种，本质上做一个合格的程序员只要你足够努力就好了。 在这段学…", "excerpt_new": "如何提高的方法论其实都摆在哪里，问题是知易行难。 大公司和小公司的职场逻辑其实不太一样，说一些通用的常识吧。 <b>不要怕被鄙视水平 </b>新人入职都要有一段学习期，在这段时间你可能要接触各个方面的新知识，学习各种技术栈，学习各种内部工具，也会有Mentor带你。 你自己什么水平其实是掩饰不了的，有些人就是天赋异禀，但是每个人都有自己的特点。程序员是个技术工种，本质上做一个合格的程序员只要你足够努力就好了。 在这段学…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1527083078, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1527082747260", "type": "feed", "target": {"id": "23386750", "title": "对刚入职场几年的新人有哪些建议和忠告？", "url": "https://api.zhihu.com/questions/23386750", "type": "question", "question_type": "normal", "created": 1397315146, "answer_count": 4574, "comment_count": 17, "follower_count": 52483, "detail": "<p><b>本题已收录至知乎圆桌：<a href=\"https://www.zhihu.com/roundtable/longlife\" class=\"internal\">漫长人生告慰书</a>，欢迎关注我们，一同分享探讨面对困境的经验与解决办法：）</b></p>", "excerpt": "<b>本题已收录至知乎圆桌：<a href=\"https://www.zhihu.com/roundtable/longlife\" class=\"internal\">漫长人生告慰书</a>，欢迎关注我们，一同分享探讨面对困境的经验…</b>", "bound_topic_ids": [1537, 2566, 3479, 7583], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "1e6f8c8327fbaca7d74bbb88dfe72066", "name": "张帆", "headline": "懒", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/zhang-fan-11-60", "url_token": "zhang-fan-11-60", "avatar_url": "https://pica.zhimg.com/2617f8fdc_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1527082747, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1527082706100", "type": "feed", "target": {"id": "26859452", "title": "你有什么经验一定要分享给初入职场的新人？", "url": "https://api.zhihu.com/questions/26859452", "type": "question", "question_type": "normal", "created": 1417358075, "answer_count": 3235, "comment_count": 10, "follower_count": 24626, "detail": "<p><b>本题已加入圆桌 » </b><a href=\"https://www.zhihu.com/roundtable/zhichangxinren\" class=\"internal\">职场新人须知</a><b>，更多「职场新人」、「职场」的相关话题欢迎关注讨论。</b></p>", "excerpt": "<b>本题已加入圆桌 » </b><a href=\"https://www.zhihu.com/roundtable/zhichangxinren\" class=\"internal\">职场新人须知</a><b>，更多「职场新人」、「职场」的相关话题欢迎关注…</b>", "bound_topic_ids": [1537, 2566, 3479, 7583, 118995], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "5d0d550e64820c3183a125e35c39a3dc", "name": "安瑾年", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/love_liu_jing", "url_token": "love_liu_jing", "avatar_url": "https://pica.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1527082706, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1527082706100&page_num=63"}}