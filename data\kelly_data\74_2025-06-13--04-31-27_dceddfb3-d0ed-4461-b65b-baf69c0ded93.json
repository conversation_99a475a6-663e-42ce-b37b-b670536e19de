{"data": [{"id": "1515265809660", "type": "feed", "target": {"id": "290714800", "type": "answer", "url": "https://api.zhihu.com/answers/290714800", "voteup_count": 0, "thanks_count": 1, "question": {"id": "264853521", "title": "你读过哪些一生都值得读的书？", "url": "https://api.zhihu.com/questions/264853521", "type": "question", "question_type": "normal", "created": 1514632771, "answer_count": 46, "comment_count": 0, "follower_count": 59, "detail": "读了又读，能运用到实际中。或者是涨知识见闻，一生受益匪浅的书。", "excerpt": "读了又读，能运用到实际中。或者是涨知识见闻，一生受益匪浅的书。", "bound_topic_ids": [113, 508, 2058], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "3907f9f46d9f59b8e38b6dbd479c8fcc", "name": "瑾瑜JH", "headline": "家里蹲", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/chen-jia-hui-18-97", "url_token": "chen-jia-hui-18-97", "avatar_url": "https://picx.zhimg.com/v2-7fd93812dd3c5ff356a27a5464f93eb2_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1515273893, "created_time": 1515265809, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"v7eRuyhV\">史铁生的书。《我与地坛》、《病隙碎笔》等等。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"VDeW-_PR\">高中语文课文里收录了《我与地坛》节选，大四挣扎迷茫的时候遇到了全书，才发现这本书是真宝藏。时隔五年的今天，在面对坎坷时依然想到读读史铁生老师的书。有时睡前坐床上看看他对人生与信仰的思考、“命运的局限尽可永在，不屈的挑战却不可须臾或缺”的积极态度、“爱命运”的豁达精神，焦虑的心情瞬间平静下来。挫折困难，那都不是事儿呀！</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"dcqLm7yA\">如果你在人生的任何时候感到路有些难走，推荐你看看史铁生。</p>", "excerpt": "史铁生的书。《我与地坛》、《病隙碎笔》等等。 高中语文课文里收录了《我与地坛》节选，大四挣扎迷茫的时候遇到了全书，才发现这本书是真宝藏。时隔五年的今天，在面对坎坷时依然想到读读史铁生老师的书。有时睡前坐床上看看他对人生与信仰的思考、“命运的局限尽可永在，不屈的挑战却不可须臾或缺”的积极态度、“爱命运”的豁达精神，焦虑的心情瞬间平静下来。挫折困难，那都不是事儿呀！ 如果你在人生的任何时候感到路有些难…", "excerpt_new": "史铁生的书。《我与地坛》、《病隙碎笔》等等。 高中语文课文里收录了《我与地坛》节选，大四挣扎迷茫的时候遇到了全书，才发现这本书是真宝藏。时隔五年的今天，在面对坎坷时依然想到读读史铁生老师的书。有时睡前坐床上看看他对人生与信仰的思考、“命运的局限尽可永在，不屈的挑战却不可须臾或缺”的积极态度、“爱命运”的豁达精神，焦虑的心情瞬间平静下来。挫折困难，那都不是事儿呀！ 如果你在人生的任何时候感到路有些难…", "preview_type": "expand", "preview_text": "<p data-pid=\"v7eRuyhV\">史铁生的书。《我与地坛》、《病隙碎笔》等等。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"VDeW-_PR\">高中语文课文里收录了《我与地坛》节选，大四挣扎迷茫的时候遇到了全书，才发现这本书是真宝藏。时隔五年的今天，在面对坎坷时依然想到读读史铁生老师的书。有时睡前坐床上看看他对人生与信仰的思考、“命运的局限尽可永在，不屈的挑战却不可须臾或缺”的积极态度、“爱命运”的豁达精神，焦虑的心情瞬间平静下来。挫折困难，那都不是事儿呀！</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"dcqLm7yA\">如果你在人生的任何时候感到路有些难走，推荐你看看史铁生。</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1515265809, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}, {"id": "1515232839290", "type": "feed", "target": {"id": "30161567", "type": "article", "author": {"id": "64cf10f2eda8f01223d087e200afa406", "name": "<PERSON><PERSON><PERSON><PERSON>", "headline": "专业 Morale Support", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/r0rschach", "url_token": "r0rschach", "avatar_url": "https://picx.zhimg.com/v2-eddd5be30de96ac5d81e3a728c0cd933_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "created": 1508231080, "updated": 1523132627, "title": "你该如何准备旧金山热门 startup 的面试", "excerpt_title": "", "content": "<p data-pid=\"OHAot0D1\"><i><b>Disclamer</b>：下面这篇文章由我写给正在准备面试 Opendoor 的工程师和数据科学职位的应聘者的材料改写扩展而来。 这半年来我司面试的同学们应该都收到过 recruiter 发来的本文的英文版。 严格来说这里讲的内容只对应 Opendoor 这一家公司，但是据我了解我司的面试在旧金山的创业公司里也还是挺有代表性的。 考虑到过了我司面试的同学一般也会拿着一堆 FLAGUAP, Robinhood, Affirm, Lyft 等等等等的其他公司offer来谈合同，所以达到本文要求的同学应该大概律能刷到个不错的工作。 </i></p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"9sdyfyvU\">公司的面试流程一般包括下面几种类型的面试，下面我会把每轮面试会分成『面试过程』，『准备和注意事项』， 『FAQ』三部分逐个讲解。 我会根据大家提出的问题逐渐扩展 FAQ 部分。 </p><ul><li data-pid=\"cavhtxbt\">电话面试/远程编码面试 （Technical Phone Interview)</li><li data-pid=\"QIlar3OZ\">Onsite 面试</li><ul><li data-pid=\"N-Pegerz\">结对编程 (Pair Programming)</li><li data-pid=\"2MO3mlHZ\">结对数据分析建模 (Pair Data Science Programming)</li><li data-pid=\"P2PWCYIv\">软件系统设计 (System Design)</li></ul></ul><p class=\"ztext-empty-paragraph\"><br/></p><h2>电话面试</h2><p data-pid=\"dbEwhbvX\">面试者首先会简单自我介绍下，名字叫什么，做什么项目，在公司工作多久了等等。 之后很快进入编码环节。 </p><p data-pid=\"cIw35qn0\">这轮的考察内容通常都是几道不太难的算法题（最多leetcode medium吧）。 写代码一般都是用个在线的协同编辑工具，比较流行的是<a href=\"https://link.zhihu.com/?target=https%3A//coderpad.io/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">CoderPad</a>。 尽量用你最熟悉的编程语言回答问题，做好准备详细解释你的代码，尤其是碰上你的面试官不熟悉你用的语言的情况。 这轮电面的目标是了解你是如何思考并且解决问题的，所以如果对任何东西不确定的话请尽可能奔放的提出各种问题澄清理解。 一边写代码一边说，解释清楚你代码背后的逻辑和你正在做的和接下来要做的事情。 </p><p data-pid=\"jesQePca\">面试的最后五分钟是留给你的， 我们希望你能多多提问。 不妨提前拉个单子列一下你对对方公司最感兴趣的事情。 这是个蛮好的互相了解的机会，我们很乐意分享我们团队的情况，以及我们正在解决的问题。 </p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"4T_z3KyV\">如何准备呢？</p><ul><li data-pid=\"k_ICUs4Z\"><b>大量练习回答类似的问题</b>。 没错我说的就是刷题……一些很好的资源包括：<a href=\"https://link.zhihu.com/?target=https%3A//leetcode.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">LeetCode</a>, <a href=\"https://link.zhihu.com/?target=https%3A//www.hackerrank.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">HackerRank</a>, <a href=\"https://link.zhihu.com/?target=https%3A//www.careercup.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">CareerCup</a>, <a href=\"https://link.zhihu.com/?target=https%3A//www.interviewbit.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">InterviewBit</a>. 注意不要过度刷题，因为初创公司一般都不会特别在意你解题本身的娴熟程度， 也不会问太难的题。 （起码我没太见过有人叫写 DP 的）</li><li data-pid=\"XYpkAPzW\"><b>练习在相似的环境下回答问题</b>。即使你是个很有经验的工程师了，面试的环境也会和你正常工作的情况很不一样。 练习在时间压力下解决问题，用简单的 coderpad 这类编辑器写代码体验下没有全套 IDE 的感觉，试着一边写东西一边不停的说话。 </li><li data-pid=\"mTSH8kvr\"><b>复习下基础知识。 </b>基本的数据结构，算法等等内容。 你应该起码能轻松的跟对方聊聊 big-O 复杂度。 </li></ul><p data-pid=\"sADWre5N\">FAQ</p><ol><li data-pid=\"nQ0vEPEP\">Q：我用什么编程语言好？<br/><i>A：用你最能打的语言回答问题。 如果你什么都写得一样好， 那用 Python 之类比较『高级』的『动态』语言面试会略占一点点便宜。 相比起 C++之类语言你可能犯的错误会少很多， 比起 Java 或者 C# 来又没有那么啰嗦。 但是面试者也会根据你用什么语言写调整预期。 同样是30分钟解决问题，用 Java 写的会比用 Python 的评价稍微高一点。 </i></li><li data-pid=\"Z3VLtAau\">Q：最后五分钟我应该问什么问题呢？<br/><i>A：自由发挥。一般来说问对方团队的情况总是很安全的（多少人，都是什么背景的等等）。 问问团队正在做什么项目也不错，公司的工作方式企业文化这些也都可以聊聊。  </i></li></ol><p class=\"ztext-empty-paragraph\"><br/></p><h2>结对编程面试 (Pair Programming)</h2><p data-pid=\"7Rlxctib\">现在很多创业公司已经不太喜欢考纸上或者白板上编程了。多数情况都是直接上机编程，然后更激进一点的（比如 Opendoor）会考察结对编程。 面试一般会用专门的结对编程工作台， 你和你的面试者坐在一起解决问题。 面试者会扮演观察者(observer)的角色，而你来扮演驾驶员(driver)的角色。 对方会解释问题，然后在你编程的过程里提供很多建议，并且有时候会修改问题的需求。 </p><p data-pid=\"dm2xcJPR\">建议尽可能用自己的笔记本来面试，保证你的电脑上安装有你最喜欢最熟悉的开发环境。 面试的公司通常也可以提供电脑，但是往往不如你自己的顺手。 </p><p data-pid=\"4-dxq-_4\">结对编程一般只会做一道题，会比你电面上碰到的题目更难更复杂一些。 有时候是算法问题，有时候是写个简单系统。尽管问题有时候会非常的开放， 最好还是希望你能在面试结束的时候提供一个能从头到尾运行的程序。 </p><p data-pid=\"reIg3yn0\">面试的注意事项：</p><ul><li data-pid=\"TvlVOkdA\"><b>时间管理</b>：你只有一个小时的时间，要注意好好规划。 不要直接闷头写暴力解法。花点时间想想尽可能理想的解决方案。 但是如果想不出来完美快速的方法， 用比较基本的方式开始再在此这个基础上改善也是不错的稳扎稳打型思路。 能运行但是比较慢的答案，永远比完美的但是没写完的答案好。 </li><li data-pid=\"lBB9ZZXZ\"><b>表达能力是关键</b>：结对编程考察的不仅是写代码的能力。 要记得我们在模拟你们两个人一起工作的情形。 相信你一定也不想你工作的同事把你晾在一边围观你一声不吭写完一整段代码然后双击运行666吧？结对编程应该是一个你来我往的交互过程。 先澄清目标， 然后在纸上写写画画你的整个解决方案，等到你和你的面试者对方案都满意了再开工。 </li><li data-pid=\"acqsVdP9\"><b>别留 Bug</b>：上机编程的好处就是提供了全套的真实开发环境。 你可以用你喜欢的任何工具保证代码质量。 你可以反复运行，当然更应该写几个 unit test。 每个人都会写出 bug，但是要确保你能发现并解决它们。 当然最棒的情况是你的面试者还没看出来 bug 你已经自己发现然后搞定了。 </li><li data-pid=\"JAug7sd1\"><b>模拟面试</b>：对于没真有结对编程经验的同学来说，这个过程可能有点太过紧张。 找几个朋友和你试试看，就当是模拟面试。 然后如果你找不到能和你 pair 的人的话，搞个<a href=\"https://link.zhihu.com/?target=https%3A//en.wikipedia.org/wiki/Rubber_duck_debugging\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">橡皮鸭</a>子也蛮好的。 </li></ul><figure><img data-rawheight=\"440\" src=\"https://pic1.zhimg.com/v2-499324c6dcbf3fb86791e8ab3069411c_1440w.jpg\" data-rawwidth=\"440\" class=\"origin_image zh-lightbox-thumb\" width=\"440\" data-original=\"https://pic1.zhimg.com/v2-499324c6dcbf3fb86791e8ab3069411c_r.jpg\" data-original-token=\"v2-499324c6dcbf3fb86791e8ab3069411c\"/></figure><p data-pid=\"y-fkthHz\">FAQ</p><ol><li data-pid=\"o1LAM6Ft\">Q：为什么不用白板写？为什么一定要上机？<br/>A：<i>因为白板只能看看会不会做算法题， 好处是对于面试者的经验和能量需求比较低， 也就比较容易保证面试标准的一致性。 而实际上机 pairing 能阅读出多得多的东西。 我们可以很容易观察到你的编程习惯(engineering practice)，你对你偏好工具的熟练度(craftsmanship)，你的代码风格(coding style)， 你的测试习惯 (unit test)。</i></li><li data-pid=\"T15ERHGH\">Q：我题做出来了/面试官的要求我都实现了/我程序能跑， 为啥还是杯具了？<br/><i>A：因为很多来面试的同学，特别是我们中国出来的同学， 虽然编码能力不错但是往往很不注意沟通。 光闷头写 code 是不行的， 要尽量多和面试者交流你的想法。 被动让面试者告诉你写这个测那个不可能拿 strong hire 的， 要尽量在理解了问题的基础上主动引导解决问题。 很多时候面试官对你的那些看起来是『找茬给我增加难度』的要求，其实是他等你主动跟他澄清，或者等你自己想起来要做但是实在等不到只好直接给提示。</i></li><li data-pid=\"pvF6QaHv\">Q: 这轮面试有什么窍门？<br/><i>A：我们组里有个同事的上机面试祖传秘诀。 就是你在试图理解问题聊需求的时候，一边说一边想几个测试用例写成简单的 assertion。『如果输入是这个，这个函数的输出应该是这个对吧』。这样一方面给人你沟通能力很强很严谨的印象，顺便还写了几个 unit test。 这招使出来印象分往往暴涨。 </i></li></ol><h2>结对数据分析建模 (Pair Data Science Programming)</h2><p data-pid=\"Cfc1xyCy\">如果你是面试数据科学家（Data Scientist），数据分析师（BI Analyst）或者 机器学习工程师（Machine Learning Engineer）职位的话。有些时候会有上机的数据分析面试。 过程和上面的结对编程差不多， 区别是可能不会让你写个简单系统或者做算法题，而是会给你个数据要求你做些数据分析或者用这个数据建个机器学习模型。 </p><p data-pid=\"BNtREXI9\">数据规模都不会很大，普通配置的笔记本都能轻松搞定。多数来面试的同学会用 R 或者Python。 当然也有用 Java 硬写分析的强者通过面试的。和其他面试一样，最重要的是到面试结束的时候应该有个清楚的结论，或者有个能跑能用的模型。  </p><p data-pid=\"6K6yz46o\">面试的注意事项：</p><ul><li data-pid=\"n7FsUQG3\"><b>工欲善其事必先利其器</b>：如果觉得有需要，你可以随时查各种在线资源，用 google 也好 stackoverflow 也好都没关系。 但是面试时间宝贵，你对 R/dplyr 和 Python/Pandas/ScikitLearn 之类的工具熟练度越高你就越有优势。 </li><li data-pid=\"oMZEAEja\"><b>熟悉一般的数据分析流程</b>：</li><ul><li data-pid=\"Dgv-E9ua\">数据处理 (data munging)：不要在数据输入上浪费时间。 你应该很熟悉如何用个 csv 之类文件建立数据表。 知道怎么做基本的 aggregation, filter, transformation, date parsing, string manipulation 等等操作。 </li><li data-pid=\"OhkpPElq\">数据验证(data validation/skepticism)：真实世界的数据往往是很『脏』的。 工业界面试的一个特点就是会拿出包含各种常见错误的数据来看看你能不能有好的习惯做很多验证来保证数据正确性。 我甚至会主动加入不少噪声，制造一些空行之类的障碍。 有经验的分析师会明确的提出对数据的假设， 然后仔细验证这些假设。</li><li data-pid=\"Z_Sz0hy7\">数据可视化（data visualization）：你应该能用最有效的方式呈现你分析的结果。 如何能尽量避免冗杂的信息，如何选择最明显最清楚的图像表格类型是分析师的基本功。 </li></ul><li data-pid=\"9u8g0VcR\"><b>深入理解你用的模型</b>：准备好跟人聊聊各种常见的统计机器学习模型。 你为什么选择用某个特定的模型来解决这个问题？你准备用什么样的 feature，选择什么样的 training target？你的选择有什么pros and cons？如果训练结果显示是情况 A，你会怎么改进模型，情况 BCD 呢？假设我们觉得你选择的模型是正确的，接下来三个星期里你会怎么改进它？三个月呢？</li><li data-pid=\"ziC-KQXM\"><b>了解你面试公司的商业模式</b>：你不需要成为对方领域的行业专家（起码入职前你不需要）。 但是起码应该在常识的水平上理解公司处于什么行业， 这个行业的典型商业模式是什么样的。这些对于你选择分析的重点，和模型的优化目标很有帮助。 </li></ul><p data-pid=\"rG-us-6z\">FAQ</p><ol><li data-pid=\"mukMYhNX\">Q：我挺有经验的，但是我之前一直在 Microsoft/Google 工作不太熟悉外面这些工具怎么办？<br/><i>A：其实很多毕业就近大公司的同学都会面临这种只会用公司自己发明的轮子的问题。 建议面试之前花点时间刷几个项目。 Kaggle 是个很好的项目来源。 另外多投几个比较新的公司做做他们的 take home 也是个好办法。 （Airbnb 和 uber 的 takehome problem 就挺有意思）</i></li><li data-pid=\"j35JBu8f\">Q：统计模型，数学方面要准备多深？要准备手推公式么？<br/><i>A：这个很难一概而论。 要看公司寻找的是做哪方面事情的人。 一般来说对算法研究本身有需求的 researcher 类职位会对数学有高些的要求。 或者你本身是相关领域的 phd，面试者也会特意深入问一下你『预期』的强项。 </i></li></ol><p class=\"ztext-empty-paragraph\"><br/></p><h2>系统设计面试（System Design）</h2><p data-pid=\"vbIHuswc\">根据你之前的工作经验，通常在工程师面试里会安排一到两轮系统设计面试。 其中有半轮会用来聊聊过去的项目经历，面试者希望了解的是你如何设计一个完整的系统，你是如何做出很多技术决定的。 通常面试者会选择某几个点深挖下项目细节好判断当时项目的深度。 </p><p data-pid=\"J9gRWAOS\">其他时间的面试内容通常都会是设计某个具体的系统。 面试者会尽量创造一个彼此合作的环境，假设你刚刚加入公司即将担纲设计一个重要的系统， 而你的面试者是公司的资深成员帮助你了解项目。 这一轮面试首先是一次对话，所以不要怕提问题，别怕提出各种天马行空的思路，甚至激烈（但是态度礼貌谦和）的争论也是受欢迎的。 </p><p data-pid=\"EOwcWk3i\"><b>项目经历面试的注意事项</b>：</p><ul><li data-pid=\"-xX2PR11\"><b>项目经历面试请先大概总结项目要解决的问题</b>：面试者和你来自不同的公司，很多你觉得已经反复思考过的尝试对方可能根本没有想过。 所以在切入你的设计和方案之前解释下项目的目标和面临的挑战对帮助对方理解非常有帮助。 </li><li data-pid=\"LjwzL6sF\"><b>准备解释项目的各种限制（constraints）</b>：在项目过程里面不可避免的会因为你公司的环境对你的方案产生种种限制。 这些限制是如何影响你的设计的？你当时考虑了哪些 trade-off？项目需求和限制在执行过程种发生过变化么？你是如何调整设计适应这些变化的？</li></ul><p data-pid=\"PWlQwutY\"><b>系统设计面试的注意事项</b></p><ul><li data-pid=\"u8lzDU3I\"><b>尽量深刻理解系统需求：</b>有些时候面试者会故意在描述问题时候模棱两可，语焉不详的目的是要考察你是否有能力主动地理解清楚系统的设计需求是什么样的。即使你问了几次面试者还是没解释清楚，也不要简单放弃。 解释清楚为什么需要问这些问题，并且这些问题的答案会如何影响你的设计。 </li><li data-pid=\"uu9y649g\"><b>如何定义问题是关键的第一步：</b>理解了问题描述之后，尽量精确地用自己的语言重复一下问题，确保你和面试者在要解决什么问题上达成一致。 之后开始讨论 trade-off 并且定义好项目的关键指标（metrics）。 选对这些方向性的东西，项目就成功了一半。 </li><li data-pid=\"knNYY8O5\"><b>尽量话唠</b>：把思路讲出来。 别怕提出很傻的笨法子， 毕竟你可以有言在先『just think out loud a dumb simple solution』</li><li data-pid=\"kehQN2Ch\"><b>引导对话：</b>不要把系统设计面试当成问答题。 想象你自己是个项目负责人，正在和项目经理开会。 面试者了解要解决的问题是什么，而你的任务是搞明白这个问题然后提出个系统设计把这个问题解决掉。 如果没有明确的解决方法， 想出一个解决问题的思路框架来也很不错。 </li></ul><p data-pid=\"BHik7sY-\">FAQ</p><ol><li data-pid=\"pAk8kDcA\">Q：英语不好怎么办？感觉自己没法跟母语是英语的同事一样侃侃而谈？<br/><i>A：系统设计这个『对话』和其他的场合一样， 聊天并不是交流的全部。 你需要做到的是提高你交流的『带宽』。至于你传递信息的方式是靠嘴说，靠手写还是画图并没什么关系。 很多时候快速上手画个流程图比连比带划的说半天有效很多。 站着面试，一边听人讲需求一边就开始往白板上记录是个很好的习惯。 </i></li></ol>", "excerpt": "<i><b>Disclamer</b>：下面这篇文章由我写给正在准备面试 Opendoor 的工程师和数据科学职位的应聘者的材料改写扩展而来。 这半年来我司面试的同学们应该都收到过 recruiter 发来的本文的英文版。 严格来说这里讲的内容只对应 Opendoor 这一家公司，但是据我了解我司的面试在旧金山的创业公司里也还是挺有代表性的。 考虑到过了我司面试的同学一般也会拿着一堆 FLAGUAP, Robinhood, Affirm, Lyft 等等等等的其他公司offer来谈合同，所以达到…</i>", "excerpt_new": "<i><b>Disclamer</b>：下面这篇文章由我写给正在准备面试 Opendoor 的工程师和数据科学职位的应聘者的材料改写扩展而来。 这半年来我司面试的同学们应该都收到过 recruiter 发来的本文的英文版。 严格来说这里讲的内容只对应 Opendoor 这一家公司，但是据我了解我司的面试在旧金山的创业公司里也还是挺有代表性的。 考虑到过了我司面试的同学一般也会拿着一堆 FLAGUAP, Robinhood, Affirm, Lyft 等等等等的其他公司offer来谈合同，所以达到…</i>", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/30161567", "comment_permission": "all", "voteup_count": 287, "comment_count": 14, "image_url": "https://picx.zhimg.com/v2-a4241a71ca49695137657640d9b32758_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1515232839, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1515232104021", "type": "feed", "target": {"id": "19859548", "title": "有哪些职场套路是同事不愿意告诉你的？", "url": "https://api.zhihu.com/questions/19859548", "type": "question", "question_type": "normal", "created": 1316767223, "answer_count": 1340, "comment_count": 29, "follower_count": 95484, "detail": "", "excerpt": "", "bound_topic_ids": [2566, 3479, 3946, 24043, 33261], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "d131aab20f1c81c54cb9195b8e0aee5c", "name": "韩轶闻", "headline": "产品&amp;用户体验", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/han-yi-wen", "url_token": "han-yi-wen", "avatar_url": "https://pic1.zhimg.com/32b32913a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1515232104, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1515231654940", "type": "feed", "target": {"id": "270931024", "type": "answer", "url": "https://api.zhihu.com/answers/270931024", "voteup_count": 312, "thanks_count": 61, "question": {"id": "21248859", "title": "程序员如何有效、愉快的使用 GitHub？", "url": "https://api.zhihu.com/questions/21248859", "type": "question", "question_type": "normal", "created": 1372077216, "answer_count": 26, "comment_count": 2, "follower_count": 1806, "detail": "一般github上的开源项目都基本看不懂的！！那么如何有效的利用github呢？？传了一些自己敲的C Primer Plus 的源码上去，不知是否有意义？？", "excerpt": "一般github上的开源项目都基本看不懂的！！那么如何有效的利用github呢？？传了一些…", "bound_topic_ids": [707, 1354, 2517, 5288, 86116], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1512431686, "created_time": 1512431686, "author": {"id": "740b694e0a4af1e61765bb16773fb631", "name": "Summer", "headline": "摈弃世俗浮躁，追求技术精湛", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/summerblue", "url_token": "summerblue", "avatar_url": "https://pic1.zhimg.com/v2-0ee9d32581c6fea2a6723b597f53d19d_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "nobody", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "因作者设置，评论已关闭"}, "content": "<blockquote data-pid=\"zdwXbLlX\">除了一些常用功能，我们看看大家都是怎么样在使用 Github 的。</blockquote><h2>博客</h2><ul><li data-pid=\"bWRT6rka\">有人直接在上面写博客，例如： <a href=\"https://link.zhihu.com/?target=https%3A//github.com/dwqs/blog\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">dwqs/blog</a> </li><li data-pid=\"-jBnekxT\">有人利用 Github 来搭建独立博客，例如 <a href=\"https://link.zhihu.com/?target=https%3A//github.com/tmallfe/tmallfe.github.io\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">tmallfe/tmallfe.github.io</a></li><li data-pid=\"NeiQMMY4\">独立博客搭建教程请见 —— <a href=\"https://link.zhihu.com/?target=http%3A//www.jianshu.com/p/05289a4bc8b2\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">如何搭建一个独立博客——简明Github Pages与Hexo教程</a> </li></ul><h2>书籍</h2><ul><li data-pid=\"YVrACPzo\">有人用来写书，<a href=\"https://link.zhihu.com/?target=https%3A//github.com/eastlakeside/interpy-zh\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">eastlakeside/interpy-zh</a> ，《Python进阶》（Intermediate Python 中文版） <a href=\"https://link.zhihu.com/?target=https%3A//eastlakeside.gitbooks.io/interpy-zh/content/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">简介 · Python进阶</a> </li><li data-pid=\"D3xJY7jF\">这里还有个例子 <a href=\"https://link.zhihu.com/?target=https%3A//github.com/wangjiegulu/kotlin-for-android-developers-zh\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">wangjiegulu/kotlin-for-android-developers-zh</a> </li></ul><h2>朋友圈</h2><ul><li data-pid=\"_45U-gjQ\">有人把他当成朋友圈 —— <a href=\"https://link.zhihu.com/?target=https%3A//juejin.im/post/5953785b5188250d95761e5f\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">如何高效利用 GitHub 提升自己</a> </li></ul><h2>微博</h2><p data-pid=\"SBx9uWqU\">有人把他当微博，follow 大 V ，以下这几个大 V 可以follow 下，这样就可以在首页 timeline 里看到他们的最新动态：</p><ul><li data-pid=\"czpnpV4e\">Linux 的创始人 —— <a href=\"https://link.zhihu.com/?target=https%3A//github.com/torvalds\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">torvalds (Linus Torvalds)</a></li><li data-pid=\"l57AMr95\">Ruby on Rails 的创始人 —— <a href=\"https://link.zhihu.com/?target=https%3A//github.com/dhh\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">dhh (David Heinemeier Hansson)</a> </li><li data-pid=\"kU47kshw\">Android之神 —— <a href=\"https://link.zhihu.com/?target=https%3A//github.com/JakeWharton\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">JakeWharton (Jake Wharton)</a> </li><li data-pid=\"h6tSvxT2\">Google Chrome 开发者 —— <a href=\"https://link.zhihu.com/?target=https%3A//github.com/paulirish\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">paulirish (Paul Irish)</a> ，<a href=\"https://link.zhihu.com/?target=https%3A//github.com/addyosmani\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">addyosmani (Addy Osmani)</a> </li></ul><h2>资讯</h2><ul><li data-pid=\"WuDBuGca\">了解最新的编程项目 ——  <a href=\"https://link.zhihu.com/?target=https%3A//github.com/explore\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Explore GitHub</a>， <a href=\"https://link.zhihu.com/?target=https%3A//github.com/trending\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Github Trending</a> </li><li data-pid=\"WV0uicBN\">定位最受欢迎的 PHP 项目 —— <a href=\"https://link.zhihu.com/?target=https%3A//github.com/search%3Fl%3DPHP%26o%3Ddesc%26q%3Dstars%253A%253E1%26ref%3Dsimplesearch%26s%3Dstars%26type%3DRepositories%26utf8%3D%25E2%259C%2593\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Github 排行</a>  </li></ul><h2>个人简历</h2><ul><li data-pid=\"eGA9YcyU\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/gwuhaolin/resume\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">gwuhaolin/resume</a></li><li data-pid=\"Btjog0s-\"><a href=\"https://link.zhihu.com/?target=https%3A//www.v2ex.com/t/336477\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Github 账号数据分析生成简历</a>  </li></ul><h2>任务管理</h2><ul><li data-pid=\"NgtMPXGD\"><a href=\"https://link.zhihu.com/?target=https%3A//dotblogs.com.tw/wellwind/2016/09/16/github-projects-using-kanban\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">[GitHub Projects]在GitHub上使用看板功能 | 全端開發人員天梯</a> </li></ul><h2>团队协作</h2><ul><li data-pid=\"e47NW3y5\"><a href=\"https://link.zhihu.com/?target=http%3A//gitbeijing.com/github_flow.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">团队合作流程</a>  </li></ul><h2>阅读代码</h2><ul><li data-pid=\"ieCubcV-\">极力推荐 —— <a href=\"https://link.zhihu.com/?target=https%3A//github.com/buunguyen/octotree\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">buunguyen/octotree</a> </li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"EzxKkupa\">-EOF-</p><p></p><p></p>", "excerpt": "除了一些常用功能，我们看看大家都是怎么样在使用 Github 的。博客有人直接在上面写博客，例如： <a href=\"https://link.zhihu.com/?target=https%3A//github.com/dwqs/blog\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">dwqs/blog</a> 有人利用 Github 来搭建独立博客，例如 <a href=\"https://link.zhihu.com/?target=https%3A//github.com/tmallfe/tmallfe.github.io\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">tmallfe/tmallfe.github.io</a>独立博客搭建教程请见 —— <a href=\"https://link.zhihu.com/?target=http%3A//www.jianshu.com/p/05289a4bc8b2\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">如何搭建一个独立博客——简明Github Pages与Hexo教程</a> 书籍有人用来写书，<a href=\"https://link.zhihu.com/?target=https%3A//github.com/eastlakeside/interpy-zh\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">eastlakeside/interpy-zh</a> ，《Python进阶》（Intermediate Python 中文版） <a href=\"https://link.zhihu.com/?target=https%3A//eastlakeside.gitbooks.io/interpy-zh/content/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">简介 · Python进阶</a> 这里还有个例子 <a href=\"https://link.zhihu.com/?target=https%3A//github.com/wangjiegulu/kotlin-for-android-developers-zh\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">wangjiegulu/kotlin-for-android-deve…</a>", "excerpt_new": "除了一些常用功能，我们看看大家都是怎么样在使用 Github 的。博客有人直接在上面写博客，例如： <a href=\"https://link.zhihu.com/?target=https%3A//github.com/dwqs/blog\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">dwqs/blog</a> 有人利用 Github 来搭建独立博客，例如 <a href=\"https://link.zhihu.com/?target=https%3A//github.com/tmallfe/tmallfe.github.io\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">tmallfe/tmallfe.github.io</a>独立博客搭建教程请见 —— <a href=\"https://link.zhihu.com/?target=http%3A//www.jianshu.com/p/05289a4bc8b2\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">如何搭建一个独立博客——简明Github Pages与Hexo教程</a> 书籍有人用来写书，<a href=\"https://link.zhihu.com/?target=https%3A//github.com/eastlakeside/interpy-zh\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">eastlakeside/interpy-zh</a> ，《Python进阶》（Intermediate Python 中文版） <a href=\"https://link.zhihu.com/?target=https%3A//eastlakeside.gitbooks.io/interpy-zh/content/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">简介 · Python进阶</a> 这里还有个例子 <a href=\"https://link.zhihu.com/?target=https%3A//github.com/wangjiegulu/kotlin-for-android-developers-zh\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">wangjiegulu/kotlin-for-android-deve…</a>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_COLLECT_ANSWER", "created_time": 1515231654, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "收藏了回答", "is_sticky": false}, {"id": "1515208852093", "type": "feed", "target": {"id": "225965731", "type": "answer", "url": "https://api.zhihu.com/answers/225965731", "voteup_count": 18334, "thanks_count": 2651, "question": {"id": "64028409", "title": "面试的时候，销售经理让你把一瓶矿泉水卖 300 元，你会怎么应对？", "url": "https://api.zhihu.com/questions/64028409", "type": "question", "question_type": "normal", "created": 1502937504, "answer_count": 3258, "comment_count": 160, "follower_count": 23524, "detail": "面试者面试销售岗位，这是销售经理的面试题，同时还有几个面试人<br/>----------------------------<br/>我想知道如果真遇到这样的事，该有什么样合理的方法做到把一瓶水卖到300块。那些说是骗子，忽悠，要怼面试官的请不要再回答了。谢谢。", "excerpt": "面试者面试销售岗位，这是销售经理的面试题，同时还有几个面试人 -----------------…", "bound_topic_ids": [196, 622, 1740, 3324, 4640], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "b81eaf79b6c265ec4d36590b8aac7d43", "name": "李雷霆", "headline": "十年互联网从业者，可\\/:muzileiting", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/lileiting0011", "url_token": "lileiting0011", "avatar_url": "https://pica.zhimg.com/v2-1e639a9649076eda74fa7c1e1697fdd6_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1505301813, "created_time": 1504633947, "author": {"id": "89c1c1296516dda9ca5b10a53372a03d", "name": "陈凌轩", "headline": "自由创业者，瞎bbking，手办潮玩音响收集癖患者", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/chen-fu-48", "url_token": "chen-fu-48", "avatar_url": "https://pic1.zhimg.com/v2-e066d7970b734ef5fe9fbd880116f802_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 705, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"WB20Ea3s\">1.你买我一瓶水，我告诉你一个重要的信息，这叫卖的是附加值。<br/>2.你买我一瓶水，我记得你的好，这叫卖的是人情。<br/>3.你买我一瓶水，我可以引荐你去见我当局长的老舅，这叫卖的是关系。<br/>4.你买我一瓶水，我可以帮你来一次全身保健，这叫卖的是服务。<br/>5.你买我一瓶水，我可以帮你告诉隔离老王，这叫卖的是面子。<br/>6.你买我一瓶水，我一年以后400元回购，这叫卖的是增值。<br/>7.你买我一瓶水，喝水期间所有意外我负责，这叫卖的是保险。<br/>8.你买我一瓶水，让你加入我们企业家俱乐部，这叫卖的是身份。<br/>9.你买我一瓶水，我捐出去299元，这叫卖的是爱心。<br/>10.你买我一瓶水，给你一顶白帽子，这叫卖的是特权。</p>", "excerpt": "1.你买我一瓶水，我告诉你一个重要的信息，这叫卖的是附加值。 2.你买我一瓶水，我记得你的好，这叫卖的是人情。 3.你买我一瓶水，我可以引荐你去见我当局长的老舅，这叫卖的是关系。 4.你买我一瓶水，我可以帮你来一次全身保健，这叫卖的是服务。 5.你买我一瓶水，我可以帮你告诉隔离老王，这叫卖的是面子。 6.你买我一瓶水，我一年以后400元回购，这叫卖的是增值。 7.你买我一瓶水，喝水期间所有意外我负责，这叫卖的是保险。 …", "excerpt_new": "1.你买我一瓶水，我告诉你一个重要的信息，这叫卖的是附加值。 2.你买我一瓶水，我记得你的好，这叫卖的是人情。 3.你买我一瓶水，我可以引荐你去见我当局长的老舅，这叫卖的是关系。 4.你买我一瓶水，我可以帮你来一次全身保健，这叫卖的是服务。 5.你买我一瓶水，我可以帮你告诉隔离老王，这叫卖的是面子。 6.你买我一瓶水，我一年以后400元回购，这叫卖的是增值。 7.你买我一瓶水，喝水期间所有意外我负责，这叫卖的是保险。 …", "preview_type": "expand", "preview_text": "<p data-pid=\"WB20Ea3s\">1.你买我一瓶水，我告诉你一个重要的信息，这叫卖的是附加值。<br/>2.你买我一瓶水，我记得你的好，这叫卖的是人情。<br/>3.你买我一瓶水，我可以引荐你去见我当局长的老舅，这叫卖的是关系。<br/>4.你买我一瓶水，我可以帮你来一次全身保健，这叫卖的是服务。<br/>5.你买我一瓶水，我可以帮你告诉隔离老王，这叫卖的是面子。<br/>6.你买我一瓶水，我一年以后400元回购，这叫卖的是增值。<br/>7.你买我一瓶水，喝水期间所有意外我负责，这叫卖的是保险。<br/>8.你买我一瓶水，让你加入我们企业家俱乐部，这叫卖的是身份。<br/>9.你买我一瓶水，我捐出去299元，这叫卖的是爱心。<br/>10.你买我一瓶水，给你一顶白帽子，这叫卖的是特权。</p>", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1515208852, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1515130269221", "type": "feed", "target": {"id": "21757507", "title": "你平时在哪找免费的可商业使用的图片素材？", "url": "https://api.zhihu.com/questions/21757507", "type": "question", "question_type": "normal", "created": 1381063803, "answer_count": 387, "comment_count": 28, "follower_count": 48344, "detail": "", "excerpt": "", "bound_topic_ids": [99, 445, 1761, 2055, 8021], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1515130269, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1515128687823", "type": "feed", "target": {"id": "25852838", "title": "如何高效使用和整理印象笔记（Evernote）？", "url": "https://api.zhihu.com/questions/25852838", "type": "question", "question_type": "normal", "created": 1412784647, "answer_count": 117, "comment_count": 8, "follower_count": 19446, "detail": "每天接触大量的信息，一直想看看大神怎么整理笔记。", "excerpt": "每天接触大量的信息，一直想看看大神怎么整理笔记。", "bound_topic_ids": [233, 2179, 2369, 10110, 51286], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "f0171f402357bfc0314e283bb3472953", "name": "EP wu", "headline": "大学生", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/ep-wu", "url_token": "ep-wu", "avatar_url": "https://pic1.zhimg.com/1a1a46e79c1990019d3aee01c9fbfc37_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1515128687, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1515128687823&page_num=74"}}