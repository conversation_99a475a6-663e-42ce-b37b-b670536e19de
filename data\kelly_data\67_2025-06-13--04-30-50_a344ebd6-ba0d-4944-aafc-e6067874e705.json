{"data": [{"id": "1524961138648", "type": "feed", "target": {"id": "25308051", "type": "article", "author": {"id": "dc0595f8589072b3274c9fc1096fd1d8", "name": "猩猩点灯", "headline": "AGIGA", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/da-bao-33-23", "url_token": "da-<PERSON>ao-33-23", "avatar_url": "https://picx.zhimg.com/3a2ee9df1223cc0adde52570f2c03b98_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1487425128, "updated": 1487468420, "title": "比XGBOOST更快--LightGBM介绍", "excerpt_title": "", "content": "<p data-pid=\"G3pNLWTl\"> xgboost的出现，让数据民工们告别了传统的机器学习算法们：RF、GBM、SVM、LASSO........。现在，微软推出了一个新的boosting框架，想要挑战xgboost的江湖地位。笔者尝试了一下，下面请看来自第一线的报告。</p><p data-pid=\"LchEBaVo\">包含以下几个部分：</p><p data-pid=\"P1VsIBx5\">一. 基本介绍</p><p data-pid=\"EmyqGV_f\">二.  XGBOOST原理及缺点</p><p data-pid=\"vuZNW7bD\">三. LightGBM的优化</p><p data-pid=\"i0sDB5pB\">四. 建模过程（python）</p><p data-pid=\"C_qpid5v\">五. 调参</p><p data-pid=\"85KvgrEm\">一. 基本介绍</p><p data-pid=\"L9hF7QOa\">LightGBM 是一个梯度 boosting 框架，使用基于学习算法的决策树。它可以说是分布式的，高效的，它有以下优势：</p><p data-pid=\"H616fpSW\">- 更快的训练效率</p><p data-pid=\"78WYd5HH\">- 低内存使用</p><p data-pid=\"tjMFhKvc\">- 更好的准确率</p><p data-pid=\"UfU7ugnt\">- 支持并行学习</p><p data-pid=\"yhWAWcVP\">- 可处理大规模数据</p><p data-pid=\"QkgauGIu\">与常用的机器学习算法进行比较：</p><p data-pid=\"lu1rJJbM\">· 速度飞起</p><br/><figure><img src=\"https://pic3.zhimg.com/v2-d1d269324108c58ee25252e0305ffd76_1440w.png\" data-rawwidth=\"541\" data-rawheight=\"284\" class=\"origin_image zh-lightbox-thumb\" width=\"541\" data-original=\"https://pic3.zhimg.com/v2-d1d269324108c58ee25252e0305ffd76_r.jpg\" data-original-token=\"v2-d1d269324108c58ee25252e0305ffd76\"/></figure><br/><p data-pid=\"VunwdPdU\">二.  XGBOOST原理及缺点</p><p data-pid=\"k4X2CA4H\">1. 原理</p><p data-pid=\"E58LjyR_\">1 ) 有监督学习</p><p data-pid=\"4enn8bUN\">有监督学习的目标函数是下面这个东东：</p><figure><img src=\"https://pica.zhimg.com/v2-b2f6b2d06516026679630c2257fd88c8_1440w.png\" data-rawwidth=\"442\" data-rawheight=\"62\" class=\"origin_image zh-lightbox-thumb\" width=\"442\" data-original=\"https://pica.zhimg.com/v2-b2f6b2d06516026679630c2257fd88c8_r.jpg\" data-original-token=\"v2-b2f6b2d06516026679630c2257fd88c8\"/></figure><p data-pid=\"yQqpLH6c\">其中，第一项称为误差函数，常见的误差函数有平方误差，logistic误差等等，第二项称为正则项，常见的有L1正则和L2正则，神经网络里面的dropout等等</p><p data-pid=\"JhU0idun\">2）Boosted Tree</p><p data-pid=\"AwKWw-Hd\">i）基学习器：分类树和回归树（CART）</p><p data-pid=\"MY-yIaxu\">ii ) Tree Ensemble</p><p data-pid=\"PHbtXh8f\">一个CART往往过于简单无法有效地预测，因此一个更加强力的模型叫做tree ensemble。<figure><img src=\"https://pica.zhimg.com/v2-57de88286f83850ef5136eff89bf7960_1440w.png\" data-rawwidth=\"702\" data-rawheight=\"343\" class=\"origin_image zh-lightbox-thumb\" width=\"702\" data-original=\"https://pica.zhimg.com/v2-57de88286f83850ef5136eff89bf7960_r.jpg\" data-original-token=\"v2-57de88286f83850ef5136eff89bf7960\"/></figure><br/></p><p data-pid=\"uV_HubHG\">简而言之，Boosted Tree 就是一种 Tree Ensemble的方法，和RF一样，只是构造（学习）模型参数的方法不同。</p><p data-pid=\"4LHVa1zW\">iii）模型学习：additive training</p><p data-pid=\"W3wun-BZ\">每一次保留原来的模型不变，加入一个新的函数f到我们的模型中。</p><p data-pid=\"v1qBUV19\"><figure><img src=\"https://picx.zhimg.com/v2-d60f105a79be94c229c7444f82056165_1440w.png\" data-rawwidth=\"992\" data-rawheight=\"311\" class=\"origin_image zh-lightbox-thumb\" width=\"992\" data-original=\"https://picx.zhimg.com/v2-d60f105a79be94c229c7444f82056165_r.jpg\" data-original-token=\"v2-d60f105a79be94c229c7444f82056165\"/></figure><br/>f 的选择标准---最小化目标函数！</p><p data-pid=\"pFdrlWXn\">通过二阶泰勒展开，以及（中间省略N步），我们得到了最终的目标函数：</p><figure><img src=\"https://pic3.zhimg.com/v2-2023c43e50dd939427fa1006afd2ed0c_1440w.png\" data-rawwidth=\"482\" data-rawheight=\"106\" class=\"origin_image zh-lightbox-thumb\" width=\"482\" data-original=\"https://pic3.zhimg.com/v2-2023c43e50dd939427fa1006afd2ed0c_r.jpg\" data-original-token=\"v2-2023c43e50dd939427fa1006afd2ed0c\"/></figure><br/><p data-pid=\"2NZx-hmt\">G、H：与数据点在误差函数上的一阶、二阶导数有关，T：叶子的个数</p><p data-pid=\"nqGrovWo\">iv ) 枚举所有不同树结构的贪心算法</p><p data-pid=\"Po7lD0Xg\">不断地枚举不同树的结构，根据目标函数来寻找出一个最优结构的树，加入到我们的模型中，再重复这样的操作。不过枚举所有树结构这个操作不太可行，所以常用的方法是贪心法，每一次尝试去对已有的叶子加入一个分割。对于一个具体的分割方案，我们可以获得的增益可以由如下公式计算。</p><figure><img src=\"https://pica.zhimg.com/v2-232156cfd7cbb325d2d5d765f7faf02a_1440w.png\" data-rawwidth=\"626\" data-rawheight=\"156\" class=\"origin_image zh-lightbox-thumb\" width=\"626\" data-original=\"https://pica.zhimg.com/v2-232156cfd7cbb325d2d5d765f7faf02a_r.jpg\" data-original-token=\"v2-232156cfd7cbb325d2d5d765f7faf02a\"/></figure><br/><p data-pid=\"1kwCTjzw\">对于每次扩展，我们还是要枚举所有可能的分割方案，如何高效地枚举所有的分割呢？我假设我们要枚举所有 x&lt;a 这样的条件，对于某个特定的分割a我们要计算a左边和右边的导数和。</p><figure><img src=\"https://pica.zhimg.com/v2-2de52c6116d7abd532d90242ba30c5e6_1440w.png\" data-rawwidth=\"618\" data-rawheight=\"231\" class=\"origin_image zh-lightbox-thumb\" width=\"618\" data-original=\"https://pica.zhimg.com/v2-2de52c6116d7abd532d90242ba30c5e6_r.jpg\" data-original-token=\"v2-2de52c6116d7abd532d90242ba30c5e6\"/></figure><br/><p data-pid=\"K7T8GY63\">我们可以发现对于所有的a，我们只要做一遍从左到右的扫描就可以枚举出所有分割的梯度和GL和GR。然后用上面的公式计算每个分割方案的分数就可以了。</p><blockquote data-pid=\"PdUB2rnY\"><p data-pid=\"03JZiBls\">详细的内容可以看陈天奇大神的文章【3】</p></blockquote><p data-pid=\"AWdQMyWD\">2. 缺点</p><p data-pid=\"x36Xrm7N\">-- 在每一次迭代的时候，都需要遍历整个训练数据多次。如果把整个训练数据装进内存则会限制训练数据的大小；如果不装进内存，反复地读写训练数据又会消耗非常大的时间。</p><p data-pid=\"25HamnG8\">-- 预排序方法（pre-sorted）：</p><p data-pid=\"KyIDDB_M\">首先，空间消耗大。这样的算法需要保存数据的特征值，还保存了特征排序的结果（例如排序后的索引，为了后续快速的计算分割点），这里需要消耗训练数据两倍的内存。</p><p data-pid=\"tKHXpPmH\">其次，时间上也有较大的开销，在遍历每一个分割点的时候，都需要进行分裂增益的计算，消耗的代价大。</p><p data-pid=\"5xE7WYS9\">最后，对cache优化不友好。在预排序后，特征对梯度的访问是一种随机访问，并且不同的特征访问的顺序不一样，无法对cache进行优化。同时，在每一层长树的时候，需要随机访问一个行索引到叶子索引的数组，并且不同特征访问的顺序也不一样，也会造成较大的cache miss。</p><p data-pid=\"DbUEq8Tu\">三. LightGBM的优化</p><ul><li data-pid=\"ZLZKqsi3\"><p data-pid=\"S3hSPCMO\">基于Histogram的决策树算法</p></li><li data-pid=\"r5jTf3ih\"><p data-pid=\"GEQ9LbtB\">带深度限制的Leaf-wise的叶子生长策略</p></li><li data-pid=\"Tz-kCOCp\"><p data-pid=\"-W6sCK5q\">直方图做差加速</p></li><li data-pid=\"kOUkqg4l\"><p data-pid=\"BfRxnarE\">直接支持类别特征(Categorical Feature)</p></li><li data-pid=\"6sPRbA4u\"><p data-pid=\"Y1NRiMlf\">Cache命中率优化</p></li><li data-pid=\"YV8S9v_i\"><p data-pid=\"Y6hy89-p\">基于直方图的稀疏特征优化</p></li><li data-pid=\"6iYrgdjm\"><p data-pid=\"r7PmlXAK\">多线程优化</p></li></ul><p data-pid=\"T_jAxIMx\">下面主要介绍Histogram算法、带深度限制的Leaf-wise的叶子生长策略。</p><p data-pid=\"NGW-MSLK\">&gt;&gt;&gt;&gt;</p><p data-pid=\"QdppEw2U\">Histogram算法</p><p data-pid=\"dsukP3dP\">直方图算法的基本思想是先把连续的浮点特征值离散化成k个整数，同时构造一个宽度为k的直方图。在遍历数据的时候，根据离散化后的值作为索引在直方图中累积统计量，当遍历一次数据后，直方图累积了需要的统计量，然后根据直方图的离散值，遍历寻找最优的分割点。</p><p data-pid=\"doWM8-vu\">图：直方图算法</p><p data-pid=\"fH0CbqHv\">&gt;&gt;&gt;&gt;</p><p data-pid=\"2_i6A5aj\">带深度限制的Leaf-wise的叶子生长策略</p><p data-pid=\"dn_8m4tt\">Level-wise过一次数据可以同时分裂同一层的叶子，容易进行多线程优化，也好控制模型复杂度，不容易过拟合。但实际上Level-wise是一种低效的算法，因为它不加区分的对待同一层的叶子，带来了很多没必要的开销，因为实际上很多叶子的分裂增益较低，没必要进行搜索和分裂。</p><p data-pid=\"EnmXi7Pt\">Leaf-wise则是一种更为高效的策略，每次从当前所有叶子中，找到分裂增益最大的一个叶子，然后分裂，如此循环。因此同Level-wise相比，在分裂次数相同的情况下，Leaf-wise可以降低更多的误差，得到更好的精度。Leaf-wise的缺点是可能会长出比较深的决策树，产生过拟合。因此LightGBM在Leaf-wise之上增加了一个最大深度的限制，在保证高效率的同时防止过拟合。</p><p data-pid=\"Mh8iyscw\">四. 建模过程（python）</p><ol><li data-pid=\"DAAZrwXY\"><p data-pid=\"5dj7XkWo\">数据导入</p><p data-pid=\"01T698-Q\"># 接受：libsvm/tsv/csv 、Numpy 2D array、pandas object（dataframe）、LightGBM binary file</p><p data-pid=\"Ebb-8YgL\"># 需要指定 feature names and categorical features</p><p data-pid=\"HgPMejL-\">train_data = lgb.Dataset(dtrain[predictors],label=dtrain[target],feature_name=list(dtrain[predictors].columns), categorical_feature=dummies)</p><p data-pid=\"BbWtCU3X\">test_data = lgb.Dataset(dtest[predictors],label=dtest[target],feature_name=list(dtest[predictors].columns), categorical_feature=dummies)</p></li><li data-pid=\"QYjXlzaJ\"><p data-pid=\"dePMYWu5\">设置参数</p><p data-pid=\"fAYg-V3m\">param = {&#39;max_depth&#39;:6,&#39;num_leaves&#39;:64,&#39;learning_rate&#39;:0.03,&#39;scale_pos_weight&#39;:1,&#39;num_threads&#39;:40,&#39;objective&#39;:&#39;binary&#39;,         &#39;bagging_fraction&#39;:0.7,&#39;bagging_freq&#39;:1,&#39;min_sum_hessian_in_leaf&#39;:100}</p><p data-pid=\"QekUweR0\">param[&#39;is_unbalance&#39;]=&#39;true&#39;</p></li></ol><p data-pid=\"2pVqE_QG\">       param[&#39;metric&#39;] = &#39;auc&#39;</p><p data-pid=\"zVyKPj_w\">  3. CV</p><p data-pid=\"cFKU3ZAm\">      bst=lgb.cv(param,train_data,num_boost_round=1000,nfold=3,early_stopping_rounds=30)</p><p data-pid=\"Fy4uxrAo\">estimators = lgb.train(param,train_data,num_boost_round=len(bst[&#39;auc-mean&#39;]))</p><p data-pid=\"8aNjdpcH\">  4. 预测</p><p data-pid=\"FuszfDOj\">ypred = estimators.predict(dtest[predictors])</p><p data-pid=\"OhnZHHd2\">四. 实测效果</p><p data-pid=\"kdFnskWe\">试了一下90W条记录*130维的样本，num_threads设置为40</p><ol><li data-pid=\"HbD6xGNv\"><p data-pid=\"8Qk9DWua\">时间：</p><br/>Train(num_boost_round=100)cv(early_stopping_rounds=30)XGBOOST45s809sLightGBM11s129s</li></ol><p data-pid=\"wYi5QE6c\"> 2. 准确率：</p><br/><p data-pid=\"e5jL7C0i\">RecallPrecisionXGBOOST35%3.30%LightGBM35%3.10%</p><p data-pid=\"R-PLtkRl\">五. 调参</p><p data-pid=\"J7LBOzos\">1. 使用num_leaves</p><p data-pid=\"DuAQaAp8\">     因为LightGBM使用的是leaf-wise的算法，因此在调节树的复杂程度时，使用的是num_leaves而不是max_depth</p><p data-pid=\"1-MWmQjC\">      大致换算关系：num_leaves = 2^(max_depth)</p><p data-pid=\"8d-uh16y\">max_depthnum_leaves1224387128101024</p><p data-pid=\"1f3Ta9Li\">2.对于非平衡数据集：可以param[&#39;is_unbalance&#39;]=&#39;true’</p><p data-pid=\"HwQcok_L\">3. Bagging参数：bagging_fraction+bagging_freq（必须同时设置）、feature_fraction</p><p data-pid=\"3Z27MYeJ\">4. min_data_in_leaf、min_sum_hessian_in_leaf</p><p data-pid=\"x9AZEPDc\">参考文献</p><ol><li data-pid=\"ukGkwcVb\"><p data-pid=\"E6CX-Kys\"><a href=\"https://link.zhihu.com/?target=https%3A//github.com/Microsoft/LightGBM/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">https://</span><span class=\"visible\">github.com/Microsoft/Li</span><span class=\"invisible\">ghtGBM/</span><span class=\"ellipsis\"></span></a></p></li><li data-pid=\"44HjhPlD\"><p data-pid=\"vXA5tQnt\">关于LightGBM： <a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzA3MzI4MjgzMw%3D%3D%26mid%3D2650719786%26idx%3D3%26sn%3Dab1c5a77237dc4b2ee5ae12c7a68ff87%26chksm%3D871b0254b06c8b42d5a4fdf3327f7284c9ffbe72fe7911301d368b157024b32923d88401c2a8%26scene%3D0%26open_source%3Dweibo_search\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">mp.weixin.qq.com/s?</span><span class=\"invisible\">__biz=MzA3MzI4MjgzMw==&amp;mid=2650719786&amp;idx=3&amp;sn=ab1c5a77237dc4b2ee5ae12c7a68ff87&amp;chksm=871b0254b06c8b42d5a4fdf3327f7284c9ffbe72fe7911301d368b157024b32923d88401c2a8&amp;scene=0&amp;open_source=weibo_search</span><span class=\"ellipsis\"></span></a></p></li><li data-pid=\"GxKBItEt\"><p data-pid=\"WUJgaNe1\">关于XGBOOST：<a href=\"https://link.zhihu.com/?target=http%3A//www.52cs.org/%3Fp%3D429\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">52cs.org/?</span><span class=\"invisible\">p=429</span><span class=\"ellipsis\"></span></a></p></li></ol><br/><p data-pid=\"cL2zOkx_\">对数据感兴趣的小伙伴，欢迎交流，微信公共号:一白侃数</p>", "excerpt": "xgboost的出现，让数据民工们告别了传统的机器学习算法们：RF、GBM、SVM、LASSO........。现在，微软推出了一个新的boosting框架，想要挑战xgboost的江湖地位。笔者尝试了一下，下面请看来自第一线的报告。 包含以下几个部分： 一. 基本介绍 二. XGBOOST原理及缺点 三. LightGBM的优化 四. 建模过程（python） 五. 调参 一. 基本介绍 LightGBM 是一个梯度 boosting 框架，使用基于学习算法的决策树。它可以说是分布式的，高效的…", "excerpt_new": "xgboost的出现，让数据民工们告别了传统的机器学习算法们：RF、GBM、SVM、LASSO........。现在，微软推出了一个新的boosting框架，想要挑战xgboost的江湖地位。笔者尝试了一下，下面请看来自第一线的报告。 包含以下几个部分： 一. 基本介绍 二. XGBOOST原理及缺点 三. LightGBM的优化 四. 建模过程（python） 五. 调参 一. 基本介绍 LightGBM 是一个梯度 boosting 框架，使用基于学习算法的决策树。它可以说是分布式的，高效的…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/25308051", "comment_permission": "all", "voteup_count": 395, "comment_count": 14, "image_url": "https://pica.zhimg.com/v2-ab4c601c0f320f1fa5b99a5faf940508_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1524961138, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1524888573906", "type": "feed", "target": {"id": "34173739", "type": "answer", "url": "https://api.zhihu.com/answers/34173739", "voteup_count": 33215, "thanks_count": 14449, "question": {"id": "26677313", "title": "你有什么相见恨晚的英语学习方法？", "url": "https://api.zhihu.com/questions/26677313", "type": "question", "question_type": "normal", "created": 1416325881, "answer_count": 2725, "comment_count": 52, "follower_count": 143604, "detail": "在英语这方面你有什么好的学习方法要告诉大家的？比如学了很久才摸索出来的学习方法。<br/><br/>相关问题：<a href=\"https://www.zhihu.com/question/51534501\" class=\"internal\">有什么相见恨晚的小知识？ - 健康</a>", "excerpt": "在英语这方面你有什么好的学习方法要告诉大家的？比如学了很久才摸索出来的学习方法…", "bound_topic_ids": [988, 1100, 2748, 6493, 173580], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "e6f7a2beb4ff107211f454c476e20f65", "name": "极客涨姿势", "headline": "公众号:极客涨姿势", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/qiang-sen-neo", "url_token": "qiang-sen-neo", "avatar_url": "https://pic1.zhimg.com/v2-1f12a1774f1700a3f152be4fe3d2c847_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1488353517, "created_time": 1417164799, "author": {"id": "13ba78a859eaf6b9a5b27c5c56ee8419", "name": "ze ran", "headline": "less is more", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/ze.ran", "url_token": "ze.ran", "avatar_url": "https://picx.zhimg.com/v2-2ef7f1bdfcf4fd26e7c7e715b7e6b8ad_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["编程"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "新知答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": null, "title": "新知答主", "type": "best", "url": "https://zhuanlan.zhihu.com/p/344234033"}], "title": "新知答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": false, "comment_count": 1753, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"Ihls-zIq\">我这个应当够晚，晚了十几年。</p><p data-pid=\"X74U9NjS\">对于一个把学习方法看的比学习更重要的人来说，英语是我学的最失败的学科。虽然考试成绩还可以，但直到大学毕业，也没找到一个合适的方法。在国外找工作的时候，本来担心的是口语，可是话唠到哪里都是话唠，英语说起来也没什么，不会的词我就绕开，顶多啰嗦点罢了，工作没有问题。但是阅读不行，一段话有几个词不懂，就看不下去了，图书馆里那么多小说，我都没法看，词汇量不够，这是硬伤。</p><p data-pid=\"9EeV4bAu\">曾相信过词汇不重要，阅读技巧重要的观点，但问题是，读不懂就是读不懂，一个词不认识，看十遍还是不认识。所以曾尝试了各种不同的方法背单词：</p><p data-pid=\"96lwf7jN\">单词书，精背，A-Z，背到B，放弃。</p><p data-pid=\"mVghqEdw\">单词书，精背，Z-A，背到 T，放弃。</p><p data-pid=\"O-DLe4fd\">单词书，浏览，看了3遍，一个词没记住，放弃。</p><p data-pid=\"Z71iOUy8\">单词书，音义联想，想出了一堆乱七八糟的的东西，每次想到的还不一样，放弃。</p><p data-pid=\"JRCxSBNu\">单词书，词根法，那么多词根，每个还有变形，一个词根只能对应几个词，效果不大，放弃。</p><p data-pid=\"n0dfaLvz\">单词书，词源法，希腊来的，法语来的，拉丁语来的... 开玩笑，这么多，放弃。</p><p data-pid=\"9h15LXCK\">总结法，形近词，音近词，同义词，反义词，记住了几个，然后都混了，放弃。</p><p data-pid=\"UDunoJ3Z\">文曲星法，试过里边的猜单词的学法，但失败几次就去打俄罗斯方块了，放弃。</p><p data-pid=\"0nCML9yY\">阅读法，读书，不会的就查字典，二十分钟看一页，下次再看，之前查过的词都没印象了，放弃。</p><p data-pid=\"FqVoUOlF\">阅读法2，写一个软件，提取出电子版中所有我不认识的单词，先背完生词，再读书。提取出来一看，一两千生词，这么多，放弃。</p><p data-pid=\"Z3EMeSK-\">软件法，我爱背单词的单机版，当时这个软件有了复习的概念，但还没有学习计划，背了几千个词，虽然没记住，但混了个脸熟，再遇到，想不起来也不觉得面生了。</p><p data-pid=\"pu7Vn8Pf\">软件法2，囧记单词，图片助记，很轻松，但没图我就想不起来，放弃。</p><p data-pid=\"p7alfGzs\">软件法3，做选择题，看词选释义，我太擅长做选择题了，不认识也能猜出来，只看词还是不会。</p><p data-pid=\"mqh8jvnw\">至此，我已经知道我要的是什么了，我要一个软件，输入要背的词，自动安排每天我要背什么，复习什么，不要有选择题，只是词，让我选择认识还是不认识，软件决定如何复习。</p><p data-pid=\"E_uBDbWq\">我就决定自己写一个，在写之前我打算先看看同类软件都是怎么做的。诧异的发现，扇贝已经做到了，做到了我想要的80%左右的功能，在复习算法上有一些缺憾，我不喜欢打卡，不喜欢每天固定单词量，也不喜欢什么学习小组，不喜欢必须要登录才能用。但它可以通过不同的终端学习，手机，PC之间可以同步，有较标准的发音，这是我自己写软件做不到的地方。</p><p data-pid=\"tKriT17h\">于是开始用它来背，具体过程在此说过，</p><a href=\"http://www.zhihu.com/question/26487679/answer/33103305\" class=\"internal\">为什么游戏容易上瘾而学习通常不会？</a><p data-pid=\"r66BL0ga\">，大概几个月后，词汇量到了2万。</p><p data-pid=\"OmyfgqxK\">词汇量超过8k的时候，可以体会到词根的作用，超过15k的时候就可以大概分出它的词源，到20k就已经可以较流畅的阅读小说，并通过阅读增加词汇。</p><p data-pid=\"NmMHnja8\">所以，不是方法没用，而是词汇量不够。不过我还是觉得联想法意义不大。</p><p data-pid=\"jpVuWdtW\">以下是经历各种挫折后总结出的经验，</p><p data-pid=\"Q0RATz0k\">1，单词发音很重要。即使你不读，你也要听。与中文不同，英文的发音是与拼写联系在一起的，知道怎么读，就知道怎么写了。</p><p data-pid=\"t1Y2j8eC\">2，老老实实的背基础词汇。对于前6k个基础词汇，各种方法帮助不大。等到词汇量多了，自然而然的你就可以应用词根词源，同义反义了。不要浪费时间研究方法。</p><p data-pid=\"8gWO76vX\">3，大量，重复。一天背200，比四天背50效率要高很多。</p><p data-pid=\"PwFgiITp\">4，使用软件。很多人说有书就好了，但背书需要的毅力比用软件大多了。每人习惯不同，背单词的软件很多，可以都试试，找到最合适自己的。最好软件可以安排学习，复习任务，不用操心以前学的忘了没，该不该复习之类的事。</p><p data-pid=\"CtxMUdlJ\">现在，我已经不背单词了，而是用kindle看书，不认识的词查不查都没有关系，或者可以猜出来，或者当它是个生僻字，下次见到再说，这就是阅读法。但前提是，我已经有了两万的词汇量。</p><p data-pid=\"1rs4gVcU\">最后，单词很重要，很多说词汇量不重要的人，往往都已经有一两万的词汇了。</p><p data-pid=\"-A_slENL\">update：许多英语的学习方法都是学得已经很好的人，回过头来帮学习较弱的同学总结的，往往体会不到基础较弱的同学的难处。作为一个英语基础较差的人，我试过各种方法和捷径，但都不了了之。当我花了几个月把词汇量从 5k 提到 25k 时，我才知道原因，词汇量 5k 和 25k 是不同的世界，5k 的时候，我不会想要多看一眼不认识的词，因为太多词都不认识了，果汁里的成分，杂志里的八卦，不认识的词太多，就不会想看，也不会记到脑子里，25k 的时候，遇到不认识的词会想，居然有我不认识的，仔细瞧瞧，然后可能可以根据词根什么的猜出来，或者干脆查字典，然后就记住了。这个时候我才能体会到以前试过的那些学习方法的用处，但前提是，我已经有相当的词汇量了。</p><p data-pid=\"mqMmgSS_\">这也是我写这个答案的原因，从一个英语较差的人的角度来说下如何学英语，想强调的就是，词汇量很重要，不可以忽略。很多同学是要应付考试，但我并没有说过太多的考试技巧，其实我有研究过一些考试技巧，但那些只能让你比自己的实力多考十几分，关键是能力本身，过多的关注技巧会让人忽视了学习的重点，把做试卷仅仅当成训练题感的途径。</p><p data-pid=\"OX9fxkRf\">高考的时候，题目稍难，做到阅读理解发现时间不够，而且文章看不懂，很难形容当时的心情，我算了一下时间，不可能做完阅读理解了，后边还有作文，我就完全放弃阅读文章，只看题目，根据逻辑关系猜答案。结果正确率是80%，英语成绩比英语专业的要求还高3分。</p><p data-pid=\"lIesp_jB\">厉害吗？很厉害，但厉害的不是英语，是心理素质，是猜出题老师心思的能力。这种技巧对英语学习没有任何好处，考试技巧强，掩盖了英语基础差的事实，导致我花更多时间在研究方法和捷径上。</p><p data-pid=\"SsTp3WtD\">有时候，一个人的长处，可能会掩盖自己的短板，从而形成更大的弱点。如果你有和我类似的经历，这篇文章就是写给你的。</p><a data-hash=\"4c9a877ad46a00b0a2b012482f939565\" href=\"https://www.zhihu.com/people/4c9a877ad46a00b0a2b012482f939565\" class=\"member_mention\" data-tip=\"p$b$4c9a877ad46a00b0a2b012482f939565\" data-hovercard=\"p$b$4c9a877ad46a00b0a2b012482f939565\">@王豪豪</a><p data-pid=\"p8hQEo4n\"> ，百词斩我也有试过，有段时间是和扇贝一起用的，用扇贝背，用百词斩复习。个人来说，初始记忆的时候，我倾向于简单的音，词，义的对应。百词斩的图片或者是四个释义选项，会让我对新词的第一印象乱掉，所以，我只用它复习。</p><a data-hash=\"a5b591e494b462bc2f013461fd6ef886\" href=\"https://www.zhihu.com/people/a5b591e494b462bc2f013461fd6ef886\" class=\"member_mention\" data-editable=\"true\" data-title=\"@徐婧培\" data-tip=\"p$b$a5b591e494b462bc2f013461fd6ef886\" data-hovercard=\"p$b$a5b591e494b462bc2f013461fd6ef886\">@徐婧培</a><p data-pid=\"nm7SEHji\">，这是我刷过的书，看到最后就能看到那些还没开始背得书的进度都已经是90%以上了。</p><figure><noscript><img src=\"https://pica.zhimg.com/8aef0c42ce4b51268b86fa375bc7f29a_b.jpg\" data-rawwidth=\"800\" data-rawheight=\"1070\" data-original-token=\"8aef0c42ce4b51268b86fa375bc7f29a\" class=\"origin_image zh-lightbox-thumb\" width=\"800\" data-original=\"https://pica.zhimg.com/8aef0c42ce4b51268b86fa375bc7f29a_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;800&#39; height=&#39;1070&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"800\" data-rawheight=\"1070\" data-original-token=\"8aef0c42ce4b51268b86fa375bc7f29a\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"800\" data-original=\"https://pica.zhimg.com/8aef0c42ce4b51268b86fa375bc7f29a_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/8aef0c42ce4b51268b86fa375bc7f29a_b.jpg\"/></figure><figure><noscript><img src=\"https://pic4.zhimg.com/68de57aae538956e326c78bc8949ae69_b.jpg\" data-rawwidth=\"837\" data-rawheight=\"1073\" data-original-token=\"68de57aae538956e326c78bc8949ae69\" class=\"origin_image zh-lightbox-thumb\" width=\"837\" data-original=\"https://pic4.zhimg.com/68de57aae538956e326c78bc8949ae69_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;837&#39; height=&#39;1073&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"837\" data-rawheight=\"1073\" data-original-token=\"68de57aae538956e326c78bc8949ae69\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"837\" data-original=\"https://pic4.zhimg.com/68de57aae538956e326c78bc8949ae69_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/68de57aae538956e326c78bc8949ae69_b.jpg\"/></figure><figure><noscript><img src=\"https://picx.zhimg.com/d605b9a3b53554bac766bde5e43f7661_b.jpg\" data-rawwidth=\"806\" data-rawheight=\"989\" data-original-token=\"d605b9a3b53554bac766bde5e43f7661\" class=\"origin_image zh-lightbox-thumb\" width=\"806\" data-original=\"https://picx.zhimg.com/d605b9a3b53554bac766bde5e43f7661_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;806&#39; height=&#39;989&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"806\" data-rawheight=\"989\" data-original-token=\"d605b9a3b53554bac766bde5e43f7661\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"806\" data-original=\"https://picx.zhimg.com/d605b9a3b53554bac766bde5e43f7661_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/d605b9a3b53554bac766bde5e43f7661_b.jpg\"/></figure><figure><noscript><img src=\"https://pic2.zhimg.com/3a79dc7355f636ffb3fa02f788ba7b03_b.jpg\" data-rawwidth=\"822\" data-rawheight=\"1080\" data-original-token=\"3a79dc7355f636ffb3fa02f788ba7b03\" class=\"origin_image zh-lightbox-thumb\" width=\"822\" data-original=\"https://pic2.zhimg.com/3a79dc7355f636ffb3fa02f788ba7b03_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;822&#39; height=&#39;1080&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"822\" data-rawheight=\"1080\" data-original-token=\"3a79dc7355f636ffb3fa02f788ba7b03\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"822\" data-original=\"https://pic2.zhimg.com/3a79dc7355f636ffb3fa02f788ba7b03_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/3a79dc7355f636ffb3fa02f788ba7b03_b.jpg\"/></figure><figure><noscript><img src=\"https://pic4.zhimg.com/10bc315027d5d7682f3957d5fb80f903_b.jpg\" data-rawwidth=\"814\" data-rawheight=\"1073\" data-original-token=\"10bc315027d5d7682f3957d5fb80f903\" class=\"origin_image zh-lightbox-thumb\" width=\"814\" data-original=\"https://pic4.zhimg.com/10bc315027d5d7682f3957d5fb80f903_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;814&#39; height=&#39;1073&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"814\" data-rawheight=\"1073\" data-original-token=\"10bc315027d5d7682f3957d5fb80f903\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"814\" data-original=\"https://pic4.zhimg.com/10bc315027d5d7682f3957d5fb80f903_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/10bc315027d5d7682f3957d5fb80f903_b.jpg\"/></figure><figure><noscript><img src=\"https://pic2.zhimg.com/b8a4190b79b52172cc35bc06299c9af3_b.jpg\" data-rawwidth=\"807\" data-rawheight=\"1068\" data-original-token=\"b8a4190b79b52172cc35bc06299c9af3\" class=\"origin_image zh-lightbox-thumb\" width=\"807\" data-original=\"https://pic2.zhimg.com/b8a4190b79b52172cc35bc06299c9af3_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;807&#39; height=&#39;1068&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"807\" data-rawheight=\"1068\" data-original-token=\"b8a4190b79b52172cc35bc06299c9af3\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"807\" data-original=\"https://pic2.zhimg.com/b8a4190b79b52172cc35bc06299c9af3_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/b8a4190b79b52172cc35bc06299c9af3_b.jpg\"/></figure><figure><noscript><img src=\"https://pic3.zhimg.com/fa294f8e23c8ae1a06d821a9f28cfc14_b.jpg\" data-rawwidth=\"806\" data-rawheight=\"1072\" data-original-token=\"fa294f8e23c8ae1a06d821a9f28cfc14\" class=\"origin_image zh-lightbox-thumb\" width=\"806\" data-original=\"https://pic3.zhimg.com/fa294f8e23c8ae1a06d821a9f28cfc14_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;806&#39; height=&#39;1072&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"806\" data-rawheight=\"1072\" data-original-token=\"fa294f8e23c8ae1a06d821a9f28cfc14\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"806\" data-original=\"https://pic3.zhimg.com/fa294f8e23c8ae1a06d821a9f28cfc14_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/fa294f8e23c8ae1a06d821a9f28cfc14_b.jpg\"/></figure><figure><noscript><img src=\"https://pic1.zhimg.com/5b2a7528397d580412a60e811a41629c_b.jpg\" data-rawwidth=\"826\" data-rawheight=\"1073\" data-original-token=\"5b2a7528397d580412a60e811a41629c\" class=\"origin_image zh-lightbox-thumb\" width=\"826\" data-original=\"https://pic1.zhimg.com/5b2a7528397d580412a60e811a41629c_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;826&#39; height=&#39;1073&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"826\" data-rawheight=\"1073\" data-original-token=\"5b2a7528397d580412a60e811a41629c\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"826\" data-original=\"https://pic1.zhimg.com/5b2a7528397d580412a60e811a41629c_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/5b2a7528397d580412a60e811a41629c_b.jpg\"/></figure><figure><noscript><img src=\"https://picx.zhimg.com/d4defe49736c75cfb4de08094de5a713_b.jpg\" data-rawwidth=\"765\" data-rawheight=\"1071\" data-original-token=\"d4defe49736c75cfb4de08094de5a713\" class=\"origin_image zh-lightbox-thumb\" width=\"765\" data-original=\"https://picx.zhimg.com/d4defe49736c75cfb4de08094de5a713_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;765&#39; height=&#39;1071&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"765\" data-rawheight=\"1071\" data-original-token=\"d4defe49736c75cfb4de08094de5a713\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"765\" data-original=\"https://picx.zhimg.com/d4defe49736c75cfb4de08094de5a713_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/d4defe49736c75cfb4de08094de5a713_b.jpg\"/></figure><figure><noscript><img src=\"https://pic2.zhimg.com/1b3fe533eea6b0dab5ef928280fabbb3_b.jpg\" data-rawwidth=\"851\" data-rawheight=\"1066\" data-original-token=\"1b3fe533eea6b0dab5ef928280fabbb3\" class=\"origin_image zh-lightbox-thumb\" width=\"851\" data-original=\"https://pic2.zhimg.com/1b3fe533eea6b0dab5ef928280fabbb3_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;851&#39; height=&#39;1066&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"851\" data-rawheight=\"1066\" data-original-token=\"1b3fe533eea6b0dab5ef928280fabbb3\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"851\" data-original=\"https://pic2.zhimg.com/1b3fe533eea6b0dab5ef928280fabbb3_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/1b3fe533eea6b0dab5ef928280fabbb3_b.jpg\"/></figure><br/><br/><p data-pid=\"kCtYELhe\">我也是太闲了。</p>", "excerpt": "我这个应当够晚，晚了十几年。 对于一个把学习方法看的比学习更重要的人来说，英语是我学的最失败的学科。虽然考试成绩还可以，但直到大学毕业，也没找到一个合适的方法。在国外找工作的时候，本来担心的是口语，可是话唠到哪里都是话唠，英语说起来也没什么，不会的词我就绕开，顶多啰嗦点罢了，工作没有问题。但是阅读不行，一段话有几个词不懂，就看不下去了，图书馆里那么多小说，我都没法看，词汇量不够，这是硬伤。 曾相信…", "excerpt_new": "我这个应当够晚，晚了十几年。 对于一个把学习方法看的比学习更重要的人来说，英语是我学的最失败的学科。虽然考试成绩还可以，但直到大学毕业，也没找到一个合适的方法。在国外找工作的时候，本来担心的是口语，可是话唠到哪里都是话唠，英语说起来也没什么，不会的词我就绕开，顶多啰嗦点罢了，工作没有问题。但是阅读不行，一段话有几个词不懂，就看不下去了，图书馆里那么多小说，我都没法看，词汇量不够，这是硬伤。 曾相信…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "need_payment", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1524888573, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1524733670934", "type": "feed", "target": {"id": "35914525", "type": "article", "author": {"id": "13c73543898a2e258130bc5221bf3761", "name": "富叔", "headline": "出版新书《屏蔽力》，卖出3个版权：繁体版+韩国版+越南版", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/newbacon", "url_token": "newbacon", "avatar_url": "https://picx.zhimg.com/v2-a820ee0fb4a991971decfbfb6c3504c2_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1524207616, "updated": 1524207615, "title": "《北京女子图鉴》：人生下半场，拼的是什么？", "excerpt_title": "", "content": "<p data-pid=\"JiHmU_qb\"><a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzA4OTQxODczNA%3D%3D%26mid%3D2650731702%26idx%3D2%26sn%3D0e2f054cb82c14da4cdee410bf6fae27%26chksm%3D88116a37bf66e321ee7f9474c260ff0355174cb6aa0fc543405e22f484bcc36213de6bea4c8c%23rd\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《北京女子图鉴》：人生下半场，拼的是什么？</a></p><p data-pid=\"4mchPBRr\">作者：韩老白（富书签约作者）</p><p data-pid=\"F-MY4P7i\">最近《北京女子图鉴》朋友圈刷屏，里面有句话瞬间戳痛了我：“<b>人生到了下半场，敌人就只剩下自己了</b>。”</p><p data-pid=\"PcVGods1\">女主陈可，大学毕业后，不甘蜗居在四川小县城里过差不多的生活，孤身奔去北京发展。</p><p data-pid=\"DqwLDvIP\">她从小公司前台，外企小白领，商务代理再到自媒体，一路摸爬滚打，给观众展示了一个北漂姑娘真实的恋爱与生活：</p><p data-pid=\"wjzGYM40\">陈可在视频里这样独白：</p><blockquote data-pid=\"3KV7OjLY\">我叫陈可，我来自四川，为什么要来北京发展？为了梦想！北京，遍地都是金子，生活上根本不可能有交集的人，都在那等着你。</blockquote><p data-pid=\"ozG6FSr6\">同事指点她：“你自己要什么标准，你就按照什么标准努力。”</p><p data-pid=\"oUOwt2mG\">男友劝诫她：“女人欲望过强会很辛苦，你不明白吗？”</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-bb3265940708bb9cc224d7dbcbf90957_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"800\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic4.zhimg.com/v2-bb3265940708bb9cc224d7dbcbf90957_r.jpg\" data-original-token=\"v2-bb3265940708bb9cc224d7dbcbf90957\"/></figure><p data-pid=\"HXfIuTBK\">女上司为她解惑：“如果有一天，你找到了一个跟你有同样欲望的男人，那就不是问题了。”</p><p data-pid=\"8ubipyIY\">最后，她说：“<b>前面是什么已经不重要了，重要的是，我可以一直走下去</b>。”</p><p data-pid=\"g-JZz2U2\">她的人生下半场，是在纸醉金迷、灯红酒绿的北京，她不断寻找对与错，不断探求那属于自己的幸福，去找到自己的位置。</p><p data-pid=\"oD3MNQzl\">人生下半场打得漂亮的人，都是凭着心口一个“勇”字跟自己死磕。</p><p data-pid=\"2nAH58l2\">那么，当我们踏上征途，我们到底拼的是什么？</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-d5c48d8ec9d8371ca4c29e955110f98e_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"348\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pica.zhimg.com/v2-d5c48d8ec9d8371ca4c29e955110f98e_r.jpg\" data-original-token=\"v2-d5c48d8ec9d8371ca4c29e955110f98e\"/></figure><p data-pid=\"Vj5t-9rc\"><b>一、 拼逆商，才有绝地反击的资本</b></p><p data-pid=\"0cNwK_Fn\">陈可怀揣一腔热血和梦想，孤身一人来到北京闯荡。</p><p data-pid=\"icRPvB39\">初来乍到的她，就像被生活接二连三地扇了一套连环巴掌。</p><p data-pid=\"H1eXIA7j\">充满脚臭味的长途大巴，满是泡面味的车厢，人挤人挤破头的北京大巴......这一切让初到北京的陈可，感受到一股扑面而来的浓重逼仄味儿。</p><p data-pid=\"dvBGWzpe\">她投了很多简历，要求从月收入6000到“5000，最终到按你们的要求来，只有生活才会让一个人认清现实，认清自己几斤几两。</p><p data-pid=\"2DAut-cs\">她投靠在北京的男同学，住了几天，帮人家洗衣服、分摊水电费，但换来的是什么？</p><p data-pid=\"lXWWj559\">是深夜当她筋疲力尽下班回家后，却突如其来被袭胸：“<b>我们就处处吧，每天你在那晃啊，晃得我真难受</b>。”</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-bc90e7168055fdf9598d18065c3873ec_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"369\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic3.zhimg.com/v2-bc90e7168055fdf9598d18065c3873ec_r.jpg\" data-original-token=\"v2-bc90e7168055fdf9598d18065c3873ec\"/></figure><p data-pid=\"yCOF1rJA\">她恼羞成怒，连夜搬出了男同学家，流落街头，肚子饿了，但兜里的钱却只能买得起半根玉米。她央求了老伯好半天，人家才愿意卖给她半根。</p><p data-pid=\"2_7XXPRO\">蹲在街边吃着几乎是讨来的半根玉米，她泪如雨下。</p><p data-pid=\"-RZzB8Dd\">但即便如此，她还是顽强的死磕在北京，凭着逆商，一步步走下来。</p><p data-pid=\"X0eh6fNZ\">作家余华说：“中国的年轻人里面，优秀者很多，但扛得住事儿的太少。”</p><p data-pid=\"t8NQSCnI\"><b>扛事儿，就是心理学上的逆商。</b></p><p data-pid=\"grO_PZGY\">逆商，是美国职业培训大师保罗·斯托茨提出的概念，它的英文全称是Adversity Quotient ，简称AQ，指人面对挫折，摆脱困境和超越困难的能力。</p><ul><li data-pid=\"TSZi_Mj4\"><b>逆商爆表的人，下半场的人生，才是他们真正的舞台。</b></li></ul><p data-pid=\"606ZzXlw\">李嘉诚不到15岁就失去了父亲，自己还得了肺病，不得不挑起照顾一家老小的重担。</p><p data-pid=\"ZWOxs5Jj\">年少时举家迁往香港投奔亲戚，很长一段时间都寄人篱下，看近人情冷暖。</p><p data-pid=\"Jhf-VD3Q\">但他始终坚信自己一定能够让家人过上更好的生活，从厂子里的小工一步步成为想搞商界标志性人物。</p><p data-pid=\"KLB6AnQX\">前不久逝世的霍金也是如此，21岁不幸全身瘫痪、不能言语，终生都在于病魔作斗争，但仍旧笔耕不辍，探索科学的真谛，成为当代伟大的天文学家、物理学家。</p><p data-pid=\"dQYbUUvB\">苏轼被贬的时候，写了一句诗：“竹杖芒鞋轻声马，谁怕？一蓑烟雨任平生。”</p><p data-pid=\"rTcOQhBm\">还有他在《贾谊论》中的一句话令我至今印象深刻：“<b>夫君子之所取者远，则必有所待；所就者大，则必有所忍</b>。”</p><p data-pid=\"oAK2JPKU\">苏轼最大的才华，就是用足够的逆商，去抵挡命运无情的碾压。</p><p data-pid=\"Hvu_pXG_\">有人说，这种“逆商”指数就是一个人取得成功和幸福的重要因子，正是摆脱苦难和超越困境的超高逆商，让他们能够气定神闲地度过自己的人生。</p><p data-pid=\"UWd2Rlij\">就像李嘉诚说的：“<b>你想过普通的生活，就会遇到普通的挫折。你想过最好的生活，就一定会遇上最强的伤害。这世界很公平，想要最好，就一定会给你最痛</b>。”</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-0a5b8b52bb42ccc9e4a489b3f98eec76_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"367\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic3.zhimg.com/v2-0a5b8b52bb42ccc9e4a489b3f98eec76_r.jpg\" data-original-token=\"v2-0a5b8b52bb42ccc9e4a489b3f98eec76\"/></figure><p data-pid=\"dd50kwVc\"><b>二、拼人脉，才能拿到精英阶层的通行证</b></p><p data-pid=\"y5Gb9Jpt\">陈可通过同学王佳佳，认识了吴昊吴总，并受邀参加饭局，在饭局上认识了仅有一面之缘的陈总，因此得到了一个去外企工作的机会。</p><p data-pid=\"F_rJob-9\">她偶遇富婆顾映真，并递了一张创可贴，又因为HR笨笨告诉她“北京，就是一个标准不一的地方”，所以，她提出更高的薪酬，跳槽到了富婆的公司。</p><p data-pid=\"NXHMsZr3\">说实话，陈可的前三份工作，都是靠“贵人”相助得到的。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-ef79cc6d0e1590ef07b2fcc1bd5d93bb_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"370\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://picx.zhimg.com/v2-ef79cc6d0e1590ef07b2fcc1bd5d93bb_r.jpg\" data-original-token=\"v2-ef79cc6d0e1590ef07b2fcc1bd5d93bb\"/></figure><p data-pid=\"AREnf9Sr\">美国著名的成功学大师卡耐基曾经说：</p><blockquote data-pid=\"EEtzqqUe\">专业知识在一个人的成功中起到的作用只占15％，而其余的85％则取决于你的人际关系。</blockquote><p data-pid=\"QHbw_Vui\">朱元璋是中国历史上少有的强悍君主，他以一个人人看不起的流浪汉的身份，一手建立起繁荣昌盛的大明王朝，凌驾于万人之上，其丰功伟绩不可谓不伟大。</p><p data-pid=\"liZ4moWB\">朱元璋凭什么达到这一辉煌之位？</p><p data-pid=\"5I7SJ3p7\">就是因为他充分利用了身边人脉。</p><p data-pid=\"rOCym7Ay\">那些明史上所谓的名将、开国功臣们，大多是朱元璋的发小、儿时的玩伴，比如：</p><blockquote data-pid=\"kc_-7zrt\">带领朱元璋走上起义道路，最后被封为信国公，死后追封襄武东欧王的汤和；<br/>跟常遇春技艺不分高低的胡大海；<br/>大明朝开国第一功臣，连朱元璋都以“万里长城”评价他的中山王（死后追封）徐达。</blockquote><p data-pid=\"C4dinI81\">可以说，朱元璋能够功成名就，有相当一部分原因是依靠他这些从小就聚集在身边的人脉。</p><p data-pid=\"jAo4uJfX\"><b>拼人脉这件事，不分年代、不分国籍、不分行业。</b></p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-553275abe269b0ef10fb8c9f604c4325_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"324\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://picx.zhimg.com/v2-553275abe269b0ef10fb8c9f604c4325_r.jpg\" data-original-token=\"v2-553275abe269b0ef10fb8c9f604c4325\"/></figure><p data-pid=\"venkkwrj\">美国老牌影星寇克·道格拉斯曾经非常穷困，包括许多知名大导演在内，没有人认为他会成为明星。</p><p data-pid=\"TV14CG8g\">有一天，寇克·道格拉斯乘火车去某地，一位女士同他相邻。</p><p data-pid=\"iwSSbeZb\">由于旅途漫漫，时间难以打发，他就同女士交流起来，没想到这一聊就聊出了一个重大机会。</p><p data-pid=\"YXLAhhOH\">没过几天，寇克·道格拉斯被邀请到制片厂报到。</p><p data-pid=\"MQKTEQMA\">原来，这位女士是一位非常有名的制片人。</p><p data-pid=\"ixvamOIf\">从此，道格拉斯的人生开始发生转折。</p><p data-pid=\"5iRtdWlB\">在这位女制片人的帮助和提携下，他的演绎事业很快获得了更好的发展。</p><p data-pid=\"tOKV37WX\">不久之后，他因在《冠军》一片中出色的演出而一举成名，之后接拍的《生活的欲望》《光荣之路》等电影更是让他家喻户晓。</p><p data-pid=\"YHdhW31B\">罗振宇曾说过：“<b>在社交红利时代，谁懂得社交，懂得传播，就能够掌握商业的先机。未来的一切交易都将是社交</b>。”</p><ul><li data-pid=\"wHkfD5sw\"><b>社交的本质，就是建立核心人脉，用人脉来进行彼此互惠互利的资源置换，为人生打江山。</b></li></ul><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-345805679e7243c732b8b6e7ba8ea76d_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"388\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://picx.zhimg.com/v2-345805679e7243c732b8b6e7ba8ea76d_r.jpg\" data-original-token=\"v2-345805679e7243c732b8b6e7ba8ea76d\"/></figure><p data-pid=\"INVk7dgW\"><b>三、 拼野心，才能逆袭走上人生巅峰</b></p><p data-pid=\"SubbR6M5\">有些姑娘，注定是要出来闯荡的。</p><p data-pid=\"JnFmj7Hm\">电视剧第一集，只用了3个镜头，就给出了陈可离家奔赴北京的原因：</p><blockquote data-pid=\"Kcfez8kK\">大家打破头争抢一份2000块的单位工作；<br/>相亲对象抽烟、抖脚；<br/>闺蜜结婚，双方父母算计份子钱。</blockquote><p data-pid=\"DtAOx0XW\">骨子里有野心的姑娘，是不允许自己过这种没有格调的生活的，因为她们人生的字典里，没有“平庸”这两个字，有的只是野心和欲望。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-4a1197a9a74377f2b1ba666fd87c54e0_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"358\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic3.zhimg.com/v2-4a1197a9a74377f2b1ba666fd87c54e0_r.jpg\" data-original-token=\"v2-4a1197a9a74377f2b1ba666fd87c54e0\"/></figure><p data-pid=\"BRO0aHoQ\">陈可新到一个公司，老总带她出差，却意图将她潜规则，她聪明躲开，但同行的女同事却去了老总的房间，结果很快被提拔了。</p><p data-pid=\"u8YWcwZz\">别人告诉她“女人干得好，不如睡得好。”陈可这才意识到这不是自己想呆的公司。</p><p data-pid=\"yH3it1eK\">她凭着快速增长的面试技巧，果断跳槽，拿到了7000块的高薪。</p><p data-pid=\"1hgD6o9E\">但是，当她把这个好消息告诉曾是自己上司的男友时，男友明确甩给她一句：“有欲望的女人，让男人不喜欢。”</p><p data-pid=\"aUz9IYo0\">男友踏实、有才，却总是跟她描述“28岁回老家一起结婚生孩子”的生活，吃自助餐也要做攻略，给她买299元的仿LV吊带作为生日礼物，还要强调“盒子还要单算钱”。</p><p data-pid=\"Ckoaw37f\">陈可最终选择分手，并说出了国产剧里女主很少说的一句话：“张超，我爱你，但对不起，我好像更爱我自己。”</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-c1c8dd88c5f9fd7bbc1a166a16b3e386_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"307\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pica.zhimg.com/v2-c1c8dd88c5f9fd7bbc1a166a16b3e386_r.jpg\" data-original-token=\"v2-c1c8dd88c5f9fd7bbc1a166a16b3e386\"/></figure><p data-pid=\"E4SE23Ru\">是啊，对于陈可们而言，张超们的地图上没有她们想要的风景。</p><p data-pid=\"Jj2miIru\">毫不夸张地说：</p><blockquote data-pid=\"V0JD70Py\">野心，或者说目标，就是一个人问鼎金字塔尖的核心驱动力。你的野心有多大，未来就有多宽广。</blockquote><p data-pid=\"nThFdTu9\">杨澜曾说：“<b>我的心中有个模糊的梦想，要去探索一个更大的世界。”</b></p><p data-pid=\"k660AB1i\">而现在，她正在享受她那个更大的世界。</p><p data-pid=\"IEHD1dOG\">美国管理学兼心理学教授洛克和著名管理学家休斯在研究中发现，外来的刺激（如奖励、工作反馈、监督的压力）都是通过目标来影响动机的。</p><p data-pid=\"RUqjzbnV\">目标能引导活动指向与目标有关的行为，使人们根据难度的大小来调整努力的程度，并影响行为的持久性。</p><p data-pid=\"FF4-Ugxw\">他们于1967年最先提出“<b>目标设定理论</b>”（Goal Setting Theory），认为目标本身就具有激励作用。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-7f31da0e1f39aed94d7359fa2f354168_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"335\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic1.zhimg.com/v2-7f31da0e1f39aed94d7359fa2f354168_r.jpg\" data-original-token=\"v2-7f31da0e1f39aed94d7359fa2f354168\"/></figure><p data-pid=\"1D3glCYW\">目标能把人的需要转变为动机，使人们的行为朝着一定的方向努力，并将自己的行为结果与既定的目标相对照，及时进行调整和修正，从而能实现目标。</p><p data-pid=\"dWeSZh1O\">这种使需要转化为动机，再由动机支配行动以达成目标的过程就是目标激励。</p><p data-pid=\"-T3e9XTq\"><b>野心，其实就是为自己所想要的生活设定目标，然后憋着一口气去达到它。它包含了目标感、进取心、适度竞争和意志力。</b></p><p data-pid=\"YU4xlt5x\">当陈可和男友分手后，她做的第一件事情，就是申请信用卡，给自己买了一个LV包包。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-e0fc1aecc2cb60abba307420bf5109a9_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"356\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://picx.zhimg.com/v2-e0fc1aecc2cb60abba307420bf5109a9_r.jpg\" data-original-token=\"v2-e0fc1aecc2cb60abba307420bf5109a9\"/></figure><p data-pid=\"MWIVPmCU\">很多人看着这一幕，都觉得陈可虚荣。但我却特别能太理解她。因为她不是想要那个LV，而是想要让这个LV提醒自己，不要失掉最初的野心，不要忘记自己想去的远方。</p><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-e0fc1aecc2cb60abba307420bf5109a9_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"356\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://picx.zhimg.com/v2-e0fc1aecc2cb60abba307420bf5109a9_r.jpg\" data-original-token=\"v2-e0fc1aecc2cb60abba307420bf5109a9\"/></figure><p data-pid=\"DFY_uuUz\">如果你想在欲望都市有立足之地，没有野心，是在生活面前过不了几招的。只有野心，才能撑起你在低谷中匍匐向前的生活，只有野心，才能让你突破困局，迎来曙光。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-870390bd5443d5102e5593affbefdf06_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"358\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pica.zhimg.com/v2-870390bd5443d5102e5593affbefdf06_r.jpg\" data-original-token=\"v2-870390bd5443d5102e5593affbefdf06\"/></figure><p data-pid=\"KSYH1DVy\">被美国媒体誉为“<b>硅谷最有影响力女人</b>”的Facebook首席运营官谢丽尔·桑德伯格，在《向前一步》中主张：</p><blockquote data-pid=\"WIXY2v6M\">无论是男人还是女人，都应拥有自由的选择权，实现自己的潜能，创造出最大的自我价值。</blockquote><ul><li data-pid=\"jsatLHw3\"><b>如果说人生上半场是拼爹、拼长相、拼教育，那么下半场才是真正的拼逆商、拼人脉，拼野心。</b></li></ul><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-fee42b18601da9f4ec41968509eb42ff_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"790\" data-rawheight=\"86\" class=\"origin_image zh-lightbox-thumb\" width=\"790\" data-original=\"https://picx.zhimg.com/v2-fee42b18601da9f4ec41968509eb42ff_r.jpg\" data-original-token=\"v2-fee42b18601da9f4ec41968509eb42ff\"/></figure><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-26f106b7254b929848bacd6d674e72b6_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"320\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pica.zhimg.com/v2-26f106b7254b929848bacd6d674e72b6_r.jpg\" data-original-token=\"v2-26f106b7254b929848bacd6d674e72b6\"/></figure><p data-pid=\"7EFLx31b\"><i>图片来源北京女女子图鉴官微</i></p><p data-pid=\"26lwS7sX\">作者简介：韩老白，富书签约作者，狮子座正能量辣妈，小清新文艺设计师，读研时担任校报主编，毕业后工作五年从设计院裸辞，懂点心理学，专注塑造女性职场、婚姻幸福力，愿我的文字，陪伴万千女性共同成长，简书、微博@韩老白。 本文首发富兰克林读书俱乐部（ID：FranklinReadingClub），百万新中产生活学院</p>", "excerpt": "<a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzA4OTQxODczNA%3D%3D%26mid%3D2650731702%26idx%3D2%26sn%3D0e2f054cb82c14da4cdee410bf6fae27%26chksm%3D88116a37bf66e321ee7f9474c260ff0355174cb6aa0fc543405e22f484bcc36213de6bea4c8c%23rd\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《北京女子图鉴》：人生下半场，拼的是什么？</a>作者：韩老白（富书签约作者） 最近《北京女子图鉴》朋友圈刷屏，里面有句话瞬间戳痛了我：“ <b>人生到了下半场，敌人就只剩下自己了</b>。”女主陈可，大学毕业后，不甘蜗居在四川小县城里过差不多的生活，孤身奔去北京发展。 她从小公司前台，外企小白领，商务代理再到自媒体，一路摸爬滚打，给观众展示了一个北漂姑娘真实的恋爱与生活： 陈可在视频里这样独白： 我叫陈可，我来自四川，…", "excerpt_new": "<a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzA4OTQxODczNA%3D%3D%26mid%3D2650731702%26idx%3D2%26sn%3D0e2f054cb82c14da4cdee410bf6fae27%26chksm%3D88116a37bf66e321ee7f9474c260ff0355174cb6aa0fc543405e22f484bcc36213de6bea4c8c%23rd\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《北京女子图鉴》：人生下半场，拼的是什么？</a>作者：韩老白（富书签约作者） 最近《北京女子图鉴》朋友圈刷屏，里面有句话瞬间戳痛了我：“ <b>人生到了下半场，敌人就只剩下自己了</b>。”女主陈可，大学毕业后，不甘蜗居在四川小县城里过差不多的生活，孤身奔去北京发展。 她从小公司前台，外企小白领，商务代理再到自媒体，一路摸爬滚打，给观众展示了一个北漂姑娘真实的恋爱与生活： 陈可在视频里这样独白： 我叫陈可，我来自四川，…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/35914525", "comment_permission": "censor", "voteup_count": 970, "comment_count": 0, "image_url": "https://picx.zhimg.com/v2-db71e9762b41268016e613e52374ac24_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_VOTEUP_ARTICLE", "created_time": 1524733670, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了文章", "is_sticky": false}, {"id": "1524733424112", "type": "feed", "target": {"id": "375450897", "type": "answer", "url": "https://api.zhihu.com/answers/375450897", "voteup_count": 6261, "thanks_count": 763, "question": {"id": "31537241", "title": "有哪些你看过五遍以上的电影？", "url": "https://api.zhihu.com/questions/31537241", "type": "question", "question_type": "normal", "created": 1435023864, "answer_count": 39851, "comment_count": 756, "follower_count": 3091618, "detail": "<p>类似问题：<a href=\"https://www.zhihu.com/question/53488083\" class=\"internal\">你有哪些看过五遍以上的书籍？ - 文学 - 知乎</a></p><p class=\"ztext-empty-paragraph\"><br/></p><p><b>欢迎参与知乎想法 </b><a href=\"https://www.zhihu.com/pin/special/895649867853094912\" class=\"internal\">#我有八天假期#</a><b> 讨论，用视频分享你的精彩假期~</b></p>", "excerpt": "类似问题：<a href=\"https://www.zhihu.com/question/53488083\" class=\"internal\">你有哪些看过五遍以上的书籍？ - 文学 - 知乎</a> <b>欢迎参与知乎想法 </b><a href=\"https://www.zhihu.com/pin/special/895649867853094912\" class=\"internal\">#我有八…</a>", "bound_topic_ids": [68, 316, 1309, 1497, 221108], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "cc2728f79a133ed52a0aefb436dd8835", "name": "笑春风", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/chen-peng-25-19", "url_token": "chen-peng-25-19", "avatar_url": "https://pic1.zhimg.com/fffba338cdb753397bac957d7282ca1a_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1525803257, "created_time": 1524597683, "author": {"id": "4775dce9f750b57856b5b90f15a339f7", "name": "王哥测评", "headline": "小家电测评  数码测评  男士护肤  时尚穿搭", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/shu-you-68", "url_token": "shu-you-68", "avatar_url": "https://picx.zhimg.com/v2-4189d965104488b358a59b311568b457_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 478, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"4_ZTsIug\">《穿普拉达的女王》<br/>《The Devil Wears Prada》<br/><br/>此部电影中 女主华丽变身的桥段 一直是国内时尚电影的必模仿桥段 可谓是开创先河了 <br/><br/>转眼来看 时尚是个轮回 永不过时 <br/>同时也感受到了 精致起来能让别人多害怕</p><a class=\"video-box\" href=\"https://link.zhihu.com/?target=https%3A//www.zhihu.com/video/972313247688458240\" target=\"_blank\" data-video-id=\"\" data-video-playable=\"\" data-name=\"\" data-poster=\"https://pic1.zhimg.com/v2-804034d8e7e2ddd2d094730798d1df11.jpg\" data-lens-id=\"972313247688458240\"><img class=\"thumbnail\" src=\"https://pic1.zhimg.com/v2-804034d8e7e2ddd2d094730798d1df11.jpg\"/><span class=\"content\"><span class=\"title\"><span class=\"z-ico-extern-gray\"></span><span class=\"z-ico-extern-blue\"></span></span><span class=\"url\"><span class=\"z-ico-video\"></span>https://www.zhihu.com/video/972313247688458240</span></span></a>", "excerpt": "《穿普拉达的女王》 《The Devil Wears Prada》 此部电影中 女主华丽变身的桥段 一直是国内时尚电影的必模仿桥段 可谓是开创先河了 转眼来看 时尚是个轮回 永不过时 同时也感受到了 精致起来能让别人多害怕", "excerpt_new": "《穿普拉达的女王》 《The Devil Wears Prada》 此部电影中 女主华丽变身的桥段 一直是国内时尚电影的必模仿桥段 可谓是开创先河了 转眼来看 时尚是个轮回 永不过时 同时也感受到了 精致起来能让别人多害怕", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1524733424, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1524323762432", "type": "feed", "target": {"id": "371497071", "type": "answer", "url": "https://api.zhihu.com/answers/371497071", "voteup_count": 2848, "thanks_count": 259, "question": {"id": "273991528", "title": "如何看待阿里巴巴全资收购中天微？将产生哪些影响？", "url": "https://api.zhihu.com/questions/273991528", "type": "question", "question_type": "normal", "created": 1524194837, "answer_count": 167, "comment_count": 9, "follower_count": 2427, "detail": "<p>4月20日，阿里巴巴集团宣布全资收购杭州中天微系统有限公司(下称中天微)，后者是中国大陆唯一的自主嵌入式CPU IP Core公司。不过对于收购金额，阿里并未对外透露。</p><p>相关问题</p><p><a href=\"https://www.zhihu.com/question/273910173\" class=\"internal\">如何评价阿里达摩院自主研发的 AI 芯片?</a> </p>", "excerpt": "4月20日，阿里巴巴集团宣布全资收购杭州中天微系统有限公司(下称中天微)，后者是中…", "bound_topic_ids": [454, 1740, 9754, 12230, 186280], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "2fb92dab7d9e52c09f17dfe44368f9c8", "name": "1 号玩家", "headline": "创业者", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xun-rui-zhen", "url_token": "xun-rui-zhen", "avatar_url": "https://picx.zhimg.com/v2-615af6b886f05518d7be5ba6cf6f7633_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1524242095, "created_time": 1524196079, "author": {"id": "68955d03626e460576a433e54836a0d8", "name": "罗超频道", "headline": "关注跟科技有关的一切。商洽：luochaozhuli", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/luochaotmt", "url_token": "luochaotmt", "avatar_url": "https://pica.zhimg.com/v2-528610a41128dd29cb935585eec2b653_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://pica.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 389, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"zsNNjaP5\">中天微成立于2001年，总部位于杭州，在上海浦东新区设有分支机构。“中华芯，天下行”的前两个字是中天名字的由来，也是中天微系统的使命与目标。从名字可以看到，中天微的成立就是要振兴中国国产芯片，而从成立时间看，它跟BAT等互联网巨头几乎在同一时期诞生，虽然相对于Intel等芯片巨头有些迟缓，不过也算走在了前面。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-d0fc31509f2440c439b4a2c411a83f3a_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"552\" data-rawheight=\"307\" data-original-token=\"v2-d0fc31509f2440c439b4a2c411a83f3a\" class=\"origin_image zh-lightbox-thumb\" width=\"552\" data-original=\"https://pic1.zhimg.com/v2-d0fc31509f2440c439b4a2c411a83f3a_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;552&#39; height=&#39;307&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"552\" data-rawheight=\"307\" data-original-token=\"v2-d0fc31509f2440c439b4a2c411a83f3a\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"552\" data-original=\"https://pic1.zhimg.com/v2-d0fc31509f2440c439b4a2c411a83f3a_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-d0fc31509f2440c439b4a2c411a83f3a_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"w23f4NAY\">就在昨天，阿里巴巴宣布，旗下达摩院正在研发一款神经网络芯片——Ali-NPU，该芯片是一款AI芯片，将重点运用于图像视频分析、机器学习等AI推理计算。阿里方面透露这个芯片计算能力会是同类产品40倍。不过后来根据阿里达摩院公关的解释，这里的同类产品是指CPU或者GPU，而不是NPU。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-64cd5a74f6f7cb0adab434bd8d210c94_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"415\" data-rawheight=\"240\" data-original-token=\"v2-64cd5a74f6f7cb0adab434bd8d210c94\" class=\"content_image\" width=\"415\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;415&#39; height=&#39;240&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"415\" data-rawheight=\"240\" data-original-token=\"v2-64cd5a74f6f7cb0adab434bd8d210c94\" class=\"content_image lazy\" width=\"415\" data-actualsrc=\"https://pic3.zhimg.com/v2-64cd5a74f6f7cb0adab434bd8d210c94_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"8bxMyKie\">正如我昨天所言，比40倍更值得的关注的是阿里巴巴开始亲自做芯片这件事情的意义。此前阿里巴巴投资了大量芯片公司，马云被媒体称为要买下大半个芯片产业，比如阿里投资了寒武纪、Barefoot Networks、深鉴、耐能（Kneron）、翱捷科技（ASR）、中天微等多家家芯片公司，其中寒武纪为华为麒麟970处理器提供了NPU。</p><p data-pid=\"jdNzQNLl\">今天全资收购中天微则表明阿里巴巴将进一步加强芯片自主研发能力，鉴于中天微主要是做CPU的公司，应该与阿里达摩院的Ali-NPU没有直接关系。从投资到宣布自主研发芯片再到全资收购芯片公司，可以看到，阿里巴巴在芯片这件事情上一环扣一环，开始动真格。目前因为中兴被制裁，国产芯片崛起再一次引爆了舆论，显得十分迫切，看上去阿里巴巴此时宣布芯片动作是蹭热点，实际上却是在对的时间做对的事情。</p><p data-pid=\"eovozY4l\">此前，阿里云在系统层面已经实现了去IOE——IBM、Oracel和EMC，即数据库存储等系统已不再依赖国外巨头，此前国内软件产业特别是金融行业基本都是依赖IOE，现在百度金融等玩家已经意识到去IOE的重要性，正在积极在软件层面国产化或者开源化。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-4724a9e4336f2ab01dae91b3abbb051a_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"540\" data-rawheight=\"352\" data-original-token=\"v2-4724a9e4336f2ab01dae91b3abbb051a\" class=\"origin_image zh-lightbox-thumb\" width=\"540\" data-original=\"https://pic1.zhimg.com/v2-4724a9e4336f2ab01dae91b3abbb051a_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;540&#39; height=&#39;352&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"540\" data-rawheight=\"352\" data-original-token=\"v2-4724a9e4336f2ab01dae91b3abbb051a\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"540\" data-original=\"https://pic1.zhimg.com/v2-4724a9e4336f2ab01dae91b3abbb051a_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-4724a9e4336f2ab01dae91b3abbb051a_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"K5hwx1KW\">不过这是不够的，软件层面的去IOE做完后，硬件层面国产化就成为必须，而硬件层面要做到国产化，最核心的自然是芯片，阿里巴巴未来很可能会将其云计算机房的服务器的芯片逐步替换为自主研发的芯片，此后再对联想的X86服务器业务展开收购也不是没有可能，其终极目标是在云计算业务上实现从硬件到软件的全部国产化。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-a718150ea501a97e4a44c6d4bec63a86_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"427\" data-original-token=\"v2-a718150ea501a97e4a44c6d4bec63a86\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic1.zhimg.com/v2-a718150ea501a97e4a44c6d4bec63a86_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;640&#39; height=&#39;427&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"427\" data-original-token=\"v2-a718150ea501a97e4a44c6d4bec63a86\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"640\" data-original=\"https://pic1.zhimg.com/v2-a718150ea501a97e4a44c6d4bec63a86_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-a718150ea501a97e4a44c6d4bec63a86_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"VAKTRJEO\">芯片的布局不只是可以左右云计算市场的格局，阿里巴巴正在大力推进AI战略、IoT战略，未来其智能汽车等等智能设备整合自有芯片，也是一个必然的结果。所以，你看到阿里巴巴在蹭热点，我看到阿里步步为营，在强化自主技术实力的同时，为国产技术做出一份贡献。</p><p data-pid=\"hu4tMcfs\">事实上，不论是研发自有操作系统AliOS，还是云计算去IoE，以及现在布局自有芯片，都体现出阿里巴巴对底层技术的重视，去年阿里巴巴投入1000亿成立达摩院广纳技术人才，同样是要强化底层技术研究。马云是英语教师出身，是互联网大佬中最不懂技术的（一个说法是马云不大会用电脑打字），不过马云却对外说过：“正因为我不懂技术，我比谁都支持技术，我老是觉得不懂的东西要出事。” 去年在成立达摩院时，马云表示，“达摩院一定也必须要超越英特尔，必须超越微软，必须超越IBM，因为我们生于二十一世纪。”</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-2a3bf13b0f6514876df087aa67f10d94_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"786\" data-original-token=\"v2-2a3bf13b0f6514876df087aa67f10d94\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://pic1.zhimg.com/v2-2a3bf13b0f6514876df087aa67f10d94_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;640&#39; height=&#39;786&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"786\" data-original-token=\"v2-2a3bf13b0f6514876df087aa67f10d94\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"640\" data-original=\"https://pic1.zhimg.com/v2-2a3bf13b0f6514876df087aa67f10d94_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-2a3bf13b0f6514876df087aa67f10d94_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"unU3cA8F\">可以看到，当时马云就有超越英特尔的野心。“达摩院”首批公布的研究领域包括包括量子计算、机器学习、基础算法、网络安全、视觉计算、自然语言处理、下一代人机交互、芯片技术、传感器技术、嵌入式系统等，涵盖机器智能、智联网、金融科技等多个产业领域。现在看来，芯片只是一个开始。</p><p data-pid=\"JG_nNGhP\">在国内科技产业普遍缺乏底层技术的情况下，对芯片、AI、数据库、5G等等底层技术布局已十分迫切。目前不只是阿里在行动，华为的移动芯片实力已经被证明，而百度在AI算法、无人车上的能力同样可与世界巨头竞争。所以我想阿里巴巴的蹭热点行为，可以更多一些。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"qsWAt9c7\">4月21日凌晨更新：</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"kThWPKEE\">第一个没想到，评论中还有这么多人质疑AliOS（前身YunOS）不是真国产操作系统——就因为它可以兼容Android，可以兼容就不是自主研发吗？YunOS跟Android，除了可以兼容后者外没任何关系，这个科技行业的悬案早弄清楚了。如果国产手机不用Android，切换到AliOS（YunOS）也没什么问题。有些人啊，看到别人做什么事儿只会风言风语。不做吧，说中国科技公司不思进取；做吧，又说不是真原创，你们究竟如何才满意呢……</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"hJtfSdf_\">第二个意料之中的是，有人说，华为当年做海思，其实也没这么高调！确实，华为、腾讯等南方企业做事儿，向来是做了再说，闷声发财。阿里不同，阿里从来是说了再做，或者说边做边说。阿里重视公关是一回事，更重要的是马云看事情看得远，而且有高调做事的习惯，大家回想一下，不论是B2B、淘宝、支付宝、余额宝、菜鸟、阿里云，哪一个不是做之前就高调地宣传？更重要的是吹的牛逼基本都实现了，而且很多都是开始不被认可（如阿里云），后来做大做强的，你不得不服，当然，如果每个人都能认同，马云还是马云吗？马云也说过：“有些人因为看见而相信，有些人因为相信而看见”，大多数人是前者，少部分人是后者。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"N9CpB0By\">当然，马云也有吹了牛没实现的事儿，比如社交，当年十分高调地做来往怼微信，号称要火烧南极，发生了什么大家都知道了，不过有人说阿里此举是暗度陈仓，因为社交没做成，淘宝天猫支付宝移动化，做成了，同时顺便搞了个菜鸟，阿里云也悄然做大，已经是世界三大云计算巨头之一（Aliyun，仅次于AWS和Azure）。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wAA14RY-\">高调有高调的好处，比如阿里这个时候放出要做芯片的消息，对于品牌提升是一回事儿，更重要的是可以提升影响力（是平时的十倍），进而可以吸引大量的高端人才资源和政策、产业、产业资源，更多半导体人才会关注阿里做芯片吧？更多做终端的厂商会关注到阿里做芯片成为其潜在客户吧？</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"nQABluSl\">我建议啊，很多事情，还是不要上来就喷，只看负能量的一面，多看看积极的一面。</p>", "excerpt": "中天微成立于2001年，总部位于杭州，在上海浦东新区设有分支机构。“中华芯，天下行”的前两个字是中天名字的由来，也是中天微系统的使命与目标。从名字可以看到，中天微的成立就是要振兴中国国产芯片，而从成立时间看，它跟BAT等互联网巨头几乎在同一时期诞生，虽然相对于Intel等芯片巨头有些迟缓，不过也算走在了前面。 就在昨天，阿里巴巴宣布，旗下达摩院正在研发一款神经网络芯片——Ali-NPU，该芯片是一款AI芯片，将重点运…", "excerpt_new": "中天微成立于2001年，总部位于杭州，在上海浦东新区设有分支机构。“中华芯，天下行”的前两个字是中天名字的由来，也是中天微系统的使命与目标。从名字可以看到，中天微的成立就是要振兴中国国产芯片，而从成立时间看，它跟BAT等互联网巨头几乎在同一时期诞生，虽然相对于Intel等芯片巨头有些迟缓，不过也算走在了前面。 就在昨天，阿里巴巴宣布，旗下达摩院正在研发一款神经网络芯片——Ali-NPU，该芯片是一款AI芯片，将重点运…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1524323762, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1523508010308", "type": "feed", "target": {"id": "228988259", "type": "answer", "url": "https://api.zhihu.com/answers/228988259", "voteup_count": 5, "thanks_count": 0, "question": {"id": "55520342", "title": "为什么感觉硅谷的科技公司总在反对特朗普（无论当选前后）？", "url": "https://api.zhihu.com/questions/55520342", "type": "question", "question_type": "normal", "created": 1486454331, "answer_count": 35, "comment_count": 0, "follower_count": 575, "detail": "比如最近很多科技公司反对特朗普针对特定国家的移民新政，在特朗普当选前也有很多科技公司 CEO 和雇员公开表明了立场。<br/>是否跟他们所从事的业务有关系？（比如 Uber CEO 受到来自用户和员工的压力退出特朗普的政策讨论组）<br/>还是主要出于价值观因素？", "excerpt": "比如最近很多科技公司反对特朗普针对特定国家的移民新政，在特朗普当选前也有很多科…", "bound_topic_ids": [54, 153388, 159978, 162644, 176851], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1505207109, "created_time": 1505207108, "author": {"id": "e4b4cee7b40937dd5b3c9afd105e527d", "name": "W-Pwn", "headline": "科技，信息安全，好玩", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/wtt-leaks", "url_token": "wtt-leaks", "avatar_url": "https://pica.zhimg.com/v2-b1eedf2b0d876fd4b8f7f083a415a0b0_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"5KGLMFrp\">因为<b>不论华盛顿承认与否，硅谷现在已经日渐成为另一个政治中心。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://picx.zhimg.com/v2-482053db8be1dcb1c167d49b6768649b_b.jpg\" data-rawwidth=\"800\" data-rawheight=\"546\" data-original-token=\"v2-482053db8be1dcb1c167d49b6768649b\" class=\"origin_image zh-lightbox-thumb\" width=\"800\" data-original=\"https://picx.zhimg.com/v2-482053db8be1dcb1c167d49b6768649b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;800&#39; height=&#39;546&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"800\" data-rawheight=\"546\" data-original-token=\"v2-482053db8be1dcb1c167d49b6768649b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"800\" data-original=\"https://picx.zhimg.com/v2-482053db8be1dcb1c167d49b6768649b_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-482053db8be1dcb1c167d49b6768649b_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"IfclDZxm\">9月5日，特朗普宣布废除童年入境暂缓遣返DACA计划（DACA是一项奥巴马时代的计划，旨在保护儿童时期就被带到美国的非法移民被驱逐出境）。被媒体称为“Facebook的第一夫人”，现任Facebook首席运营官的雪莉·桑德伯格（Sheryl Sandberg）立即回应：十分难过，也十分担忧。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ANN0Xk4b\">科技产业是美国最伟大的产业之一。</p><p data-pid=\"Jv0xPuGm\">世界各国领导人在访问美国时，都会去参观谷歌、苹果、Facebook、英特尔等大型科技公司的总部，会见这些科技企业的CEO们。</p><figure><noscript><img src=\"https://picx.zhimg.com/v2-d55cf3cec2d54603129d088109cbb2d7_b.jpg\" data-rawwidth=\"970\" data-rawheight=\"622\" data-original-token=\"v2-d55cf3cec2d54603129d088109cbb2d7\" class=\"origin_image zh-lightbox-thumb\" width=\"970\" data-original=\"https://picx.zhimg.com/v2-d55cf3cec2d54603129d088109cbb2d7_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;970&#39; height=&#39;622&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"970\" data-rawheight=\"622\" data-original-token=\"v2-d55cf3cec2d54603129d088109cbb2d7\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"970\" data-original=\"https://picx.zhimg.com/v2-d55cf3cec2d54603129d088109cbb2d7_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-d55cf3cec2d54603129d088109cbb2d7_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"KD4F_bLZ\">而美国总统，比如之前的巴拉克·奥巴马，也经常和这些科技企业老板见面，或者共进晚餐。</p><p data-pid=\"9HQEf7WZ\">跟其他行业一样，科技行业也极大地推动了行业内的发展，比如网络安全问题，或是企业税收问题。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://picx.zhimg.com/v2-88baddc637a20af782d32749afcaa70b_b.jpg\" data-rawwidth=\"848\" data-rawheight=\"548\" data-original-token=\"v2-88baddc637a20af782d32749afcaa70b\" class=\"origin_image zh-lightbox-thumb\" width=\"848\" data-original=\"https://picx.zhimg.com/v2-88baddc637a20af782d32749afcaa70b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;848&#39; height=&#39;548&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"848\" data-rawheight=\"548\" data-original-token=\"v2-88baddc637a20af782d32749afcaa70b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"848\" data-original=\"https://picx.zhimg.com/v2-88baddc637a20af782d32749afcaa70b_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-88baddc637a20af782d32749afcaa70b_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"1uRs7Y7S\">现在除了这些问题以外，在其他许多社会热点上，比如女权问题、移民问题、同性恋权力等问题上，科技CEO们也积极为弱势群体发声，希望能做出一些改变（不过，比较讽刺的一点就是，虽然许多科技CEO在女权和种族问题上积极发声，但是硅谷实际上一直被称为“白人/男人的地盘”）。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"kZgYt_GR\"><b>不论华盛顿承认与否，硅谷现在已经日渐成为另一个政治中心。</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"Ud-qXwQz\">在特朗普宣誓成为美国总统之前，曾得到过美国科技行业的支持。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://picx.zhimg.com/v2-ae6ab5e362610f2381cbd3ab6c21fca9_b.gif\" data-rawwidth=\"463\" data-rawheight=\"322\" data-original-token=\"v2-ae6ab5e362610f2381cbd3ab6c21fca9\" data-thumbnail=\"https://picx.zhimg.com/v2-ae6ab5e362610f2381cbd3ab6c21fca9_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"463\" data-original=\"https://picx.zhimg.com/v2-ae6ab5e362610f2381cbd3ab6c21fca9_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;463&#39; height=&#39;322&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"463\" data-rawheight=\"322\" data-original-token=\"v2-ae6ab5e362610f2381cbd3ab6c21fca9\" data-thumbnail=\"https://picx.zhimg.com/v2-ae6ab5e362610f2381cbd3ab6c21fca9_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"463\" data-original=\"https://picx.zhimg.com/v2-ae6ab5e362610f2381cbd3ab6c21fca9_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-ae6ab5e362610f2381cbd3ab6c21fca9_b.gif\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"7vTKUWVj\">不过现在看来，双方的关系并不稳固，因为许多科技公司CEO频繁地高声反对特朗普的政策，比如以下事件中，总统并没有获得他们的支持。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"ZZfetHmw\"><b>1. 特朗普首次发布入境禁令</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"GY_F_qWZ\">在特朗普正式上任仅两周后，立即发出了“入境禁令”。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"x0NfumMp\">因为特朗普认为当时恐怖袭击对美国造成了极大的威胁，越是特朗普签署了“新的审查措施，意图使激进的伊斯兰恐怖分子离开美国境内”。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://pica.zhimg.com/v2-8f5a95b65b8394a900a6ddae7e901846_b.jpg\" data-rawwidth=\"908\" data-rawheight=\"654\" data-original-token=\"v2-8f5a95b65b8394a900a6ddae7e901846\" class=\"origin_image zh-lightbox-thumb\" width=\"908\" data-original=\"https://pica.zhimg.com/v2-8f5a95b65b8394a900a6ddae7e901846_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;908&#39; height=&#39;654&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"908\" data-rawheight=\"654\" data-original-token=\"v2-8f5a95b65b8394a900a6ddae7e901846\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"908\" data-original=\"https://pica.zhimg.com/v2-8f5a95b65b8394a900a6ddae7e901846_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-8f5a95b65b8394a900a6ddae7e901846_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"g7uZtjjh\">不过，这一举动极大地激怒了他的政治对手。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"88KHk5zd\">几乎在禁令发布的同时，美国的主要大机场就开始了大规模的抗议活动，甚至有些美国地方法院没有立即执行这个禁令。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://pic3.zhimg.com/v2-d4fdae387937d012fd0bf66a8263ed5c_b.jpg\" data-rawwidth=\"986\" data-rawheight=\"604\" data-original-token=\"v2-d4fdae387937d012fd0bf66a8263ed5c\" class=\"origin_image zh-lightbox-thumb\" width=\"986\" data-original=\"https://pic3.zhimg.com/v2-d4fdae387937d012fd0bf66a8263ed5c_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;986&#39; height=&#39;604&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"986\" data-rawheight=\"604\" data-original-token=\"v2-d4fdae387937d012fd0bf66a8263ed5c\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"986\" data-original=\"https://pic3.zhimg.com/v2-d4fdae387937d012fd0bf66a8263ed5c_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-d4fdae387937d012fd0bf66a8263ed5c_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"InVK6X-g\">该禁令也激起了科技界对特朗普的反对声音，科技公司的董事们，包括苹果、谷歌、Facebook、英特尔和Expedia的领导人、普遍反对这一举动，并发文指责，希望特朗普重新考虑。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"aT47iq9B\"><b>2. 特朗普第二次发布入境禁令</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"iL3mO40C\">特朗普第二次发布入境禁令时，科技CEO们回以了更加猛烈的批评。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"JAZNOiCT\">Salesforce的CEO马克·贝尼奥夫（Mark Benioff）在推特发推文，配以祖父的照片，因为他的祖父曾经也是以难民的身份来到了美国。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://pic2.zhimg.com/v2-ab9cd6d88bb0897b7629de98b5d44089_b.jpg\" data-rawwidth=\"720\" data-rawheight=\"460\" data-original-token=\"v2-ab9cd6d88bb0897b7629de98b5d44089\" class=\"origin_image zh-lightbox-thumb\" width=\"720\" data-original=\"https://pic2.zhimg.com/v2-ab9cd6d88bb0897b7629de98b5d44089_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;720&#39; height=&#39;460&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"720\" data-rawheight=\"460\" data-original-token=\"v2-ab9cd6d88bb0897b7629de98b5d44089\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"720\" data-original=\"https://pic2.zhimg.com/v2-ab9cd6d88bb0897b7629de98b5d44089_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-ab9cd6d88bb0897b7629de98b5d44089_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"9retDzss\">Airbnb首席执行官布莱恩·切斯基（Brian Chesky）表示，“因为一个人的出生地而禁止他来到美国，这样的做法十分荒谬。”</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"s3F5T0xq\"><b>3. 白人种族主义和夏洛特维尔集会事件</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"nkFDoY0-\">特朗普对于夏洛特维尔集会事件的回应是让许多原本较为中立的科技企业高管开始发声指责特朗普的原因。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wc9ouXo-\">八月份的“Unite the Right”集会表面上是为了支持Robert E. Lee将军，实际上却集合了许多白人种族主义者和新纳粹势力。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://pic4.zhimg.com/v2-5187d077263e56fb8e76e66a6e7956eb_b.jpg\" data-rawwidth=\"838\" data-rawheight=\"516\" data-original-token=\"v2-5187d077263e56fb8e76e66a6e7956eb\" class=\"origin_image zh-lightbox-thumb\" width=\"838\" data-original=\"https://pic4.zhimg.com/v2-5187d077263e56fb8e76e66a6e7956eb_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;838&#39; height=&#39;516&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"838\" data-rawheight=\"516\" data-original-token=\"v2-5187d077263e56fb8e76e66a6e7956eb\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"838\" data-original=\"https://pic4.zhimg.com/v2-5187d077263e56fb8e76e66a6e7956eb_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-5187d077263e56fb8e76e66a6e7956eb_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"07yzCHdO\">这些人高喊反犹太的口号，并与集会群众发生肢体冲突，其中一名新纳粹主义者驾车冲入人群，导致一人当场死亡。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"qm6gLAx8\">对于此事件，特朗普并没有直接批评袭击者，而是指责了双方，认为冲突的双方都有错，后来还表示白人种族主义者和新纳粹中也有“非常好的人“，这些言论引起了全美上下的大声讨。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://picx.zhimg.com/v2-2d149cf3229bc4523237964883b26501_b.jpg\" data-rawwidth=\"1000\" data-rawheight=\"638\" data-original-token=\"v2-2d149cf3229bc4523237964883b26501\" class=\"origin_image zh-lightbox-thumb\" width=\"1000\" data-original=\"https://picx.zhimg.com/v2-2d149cf3229bc4523237964883b26501_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1000&#39; height=&#39;638&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1000\" data-rawheight=\"638\" data-original-token=\"v2-2d149cf3229bc4523237964883b26501\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1000\" data-original=\"https://picx.zhimg.com/v2-2d149cf3229bc4523237964883b26501_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-2d149cf3229bc4523237964883b26501_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"lwYzyZGJ\">特朗普自己团队里的人，比如制造业和基础设施委员会的管理层纷纷辞职，或是谴责总统的言论，最终特朗普不得不解散了该组织。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"RQGGQ9Pt\">该事件也促使许多科技公司共同行动，抵制新纳粹势力用于宣传的网站“The DailyStormer”，最终迫使该网站转到了暗网。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"YmNvpdJd\"><b>4. 特朗普颁布“变性军人兵役禁令”</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"KV7O1NYz\">7月，科技行业又再次团结以来，共同反对总统。因为特朗普在推特上宣布，禁止变性人参军。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"xAdTgDNt\">许多科技公司高管，比如Facebook CEO马克·扎克伯格（Mark Zuckerberg），在网络上发文，谴责总统的举动，并表示“人人都有为自己的国家奉献的权力 – 无关身份。”</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://picx.zhimg.com/v2-23934e4b6f852de5a97921a8c5c39dbb_b.jpg\" data-rawwidth=\"1006\" data-rawheight=\"678\" data-original-token=\"v2-23934e4b6f852de5a97921a8c5c39dbb\" class=\"origin_image zh-lightbox-thumb\" width=\"1006\" data-original=\"https://picx.zhimg.com/v2-23934e4b6f852de5a97921a8c5c39dbb_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1006&#39; height=&#39;678&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1006\" data-rawheight=\"678\" data-original-token=\"v2-23934e4b6f852de5a97921a8c5c39dbb\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1006\" data-original=\"https://picx.zhimg.com/v2-23934e4b6f852de5a97921a8c5c39dbb_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-23934e4b6f852de5a97921a8c5c39dbb_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"z1QfN26P\">苹果首席执行官蒂姆·库克（TimCook），他是一名同性恋者，他曾通过推特表示：“我们应当感激所有履行公民职责的人。任何歧视只会让社会退步。”</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"n5PfHwkO\"><b>5. 特朗普终止DACA计划</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"PFvbfGr2\">9月刚开始，就有谣言称特朗普希望废除DACA，并已计划向法院提出申请，该举动将可能导致美国的60万人被遣返。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://pic2.zhimg.com/v2-6538c63b613e7c16d7c789986a8c16b7_b.jpg\" data-rawwidth=\"1058\" data-rawheight=\"618\" data-original-token=\"v2-6538c63b613e7c16d7c789986a8c16b7\" class=\"origin_image zh-lightbox-thumb\" width=\"1058\" data-original=\"https://pic2.zhimg.com/v2-6538c63b613e7c16d7c789986a8c16b7_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1058&#39; height=&#39;618&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1058\" data-rawheight=\"618\" data-original-token=\"v2-6538c63b613e7c16d7c789986a8c16b7\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1058\" data-original=\"https://pic2.zhimg.com/v2-6538c63b613e7c16d7c789986a8c16b7_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-6538c63b613e7c16d7c789986a8c16b7_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"BS_M-6AK\">该举动成为了另一突破点，硅谷高管们已经在计划游行，希望将几个游说组织聚集在一起，以推动移民改革。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"wh1YslBV\">目前，包括微软在内的许多科技公司已经开始采取行动。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"a2A5O3J_\">————————</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"CEnHYXWe\">看了这么多例子，特朗普总统，</p><p data-pid=\"hXjYORaE\">为啥老被怼，现在你心里有点b数了吗？</p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://pic2.zhimg.com/v2-c4082f7167b2d45ddad44e051c26bff5_b.jpg\" data-rawwidth=\"856\" data-rawheight=\"584\" data-original-token=\"v2-c4082f7167b2d45ddad44e051c26bff5\" class=\"origin_image zh-lightbox-thumb\" width=\"856\" data-original=\"https://pic2.zhimg.com/v2-c4082f7167b2d45ddad44e051c26bff5_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;856&#39; height=&#39;584&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"856\" data-rawheight=\"584\" data-original-token=\"v2-c4082f7167b2d45ddad44e051c26bff5\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"856\" data-original=\"https://pic2.zhimg.com/v2-c4082f7167b2d45ddad44e051c26bff5_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-c4082f7167b2d45ddad44e051c26bff5_b.jpg\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><figure><noscript><img src=\"https://pica.zhimg.com/v2-72dcc1b744bbfbcffe7343aae1618166_b.jpg\" data-rawwidth=\"630\" data-rawheight=\"386\" data-original-token=\"v2-72dcc1b744bbfbcffe7343aae1618166\" class=\"origin_image zh-lightbox-thumb\" width=\"630\" data-original=\"https://pica.zhimg.com/v2-72dcc1b744bbfbcffe7343aae1618166_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;630&#39; height=&#39;386&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"630\" data-rawheight=\"386\" data-original-token=\"v2-72dcc1b744bbfbcffe7343aae1618166\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"630\" data-original=\"https://pica.zhimg.com/v2-72dcc1b744bbfbcffe7343aae1618166_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-72dcc1b744bbfbcffe7343aae1618166_b.jpg\"/></figure><p data-pid=\"k2F8w0Q2\">欢迎关注我们的微信公众号和网站：WTT资讯（微信号：wttechorg）</p><p data-pid=\"gxZ7hmVN\"><a href=\"https://link.zhihu.com/?target=http%3A//www.wttech.org/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">WTT资讯-最新科技资讯，实时网安信息</a></p><p data-pid=\"wuqylzdo\"><a href=\"https://link.zhihu.com/?target=http%3A//leaks.wttech.org/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">WttLeaks</a></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>", "excerpt": "因为 <b>不论华盛顿承认与否，硅谷现在已经日渐成为另一个政治中心。</b> 9月5日，特朗普宣布废除童年入境暂缓遣返DACA计划（DACA是一项奥巴马时代的计划，旨在保护儿童时期就被带到美国的非法移民被驱逐出境）。被媒体称为“Facebook的第一夫人”，现任Facebook首席运营官的雪莉·桑德伯格（Sheryl Sandberg）立即回应：十分难过，也十分担忧。 科技产业是美国最伟大的产业之一。 世界各国领导人在访问美国时，都会去参观谷歌、苹果、F…", "excerpt_new": "因为 <b>不论华盛顿承认与否，硅谷现在已经日渐成为另一个政治中心。</b> 9月5日，特朗普宣布废除童年入境暂缓遣返DACA计划（DACA是一项奥巴马时代的计划，旨在保护儿童时期就被带到美国的非法移民被驱逐出境）。被媒体称为“Facebook的第一夫人”，现任Facebook首席运营官的雪莉·桑德伯格（Sheryl Sandberg）立即回应：十分难过，也十分担忧。 科技产业是美国最伟大的产业之一。 世界各国领导人在访问美国时，都会去参观谷歌、苹果、F…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1523508010, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1523507606697", "type": "feed", "target": {"id": "145200143", "type": "answer", "url": "https://api.zhihu.com/answers/145200143", "voteup_count": 21, "thanks_count": 5, "question": {"id": "55520342", "title": "为什么感觉硅谷的科技公司总在反对特朗普（无论当选前后）？", "url": "https://api.zhihu.com/questions/55520342", "type": "question", "question_type": "normal", "created": 1486454331, "answer_count": 35, "comment_count": 0, "follower_count": 575, "detail": "比如最近很多科技公司反对特朗普针对特定国家的移民新政，在特朗普当选前也有很多科技公司 CEO 和雇员公开表明了立场。<br/>是否跟他们所从事的业务有关系？（比如 Uber CEO 受到来自用户和员工的压力退出特朗普的政策讨论组）<br/>还是主要出于价值观因素？", "excerpt": "比如最近很多科技公司反对特朗普针对特定国家的移民新政，在特朗普当选前也有很多科…", "bound_topic_ids": [54, 153388, 159978, 162644, 176851], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1486545975, "created_time": 1486545975, "author": {"id": "bd4924d4dd6322117d5e6b11ab977ac5", "name": "川烧红脖", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/cf-cf-84", "url_token": "cf-cf-84", "avatar_url": "https://picx.zhimg.com/v2-e6ff35ee02adb662844e1505e6e5d1b2_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 4, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"volocN9R\">具体来说跟业务与价值观都有很大的关系。</p><p data-pid=\"kpXiof0s\">业务方面：</p><p data-pid=\"jeDjhyIT\">硅谷科技公司，尤其是互联网科技公司对全球化人才的依赖很大，很多硅谷科技公司现任大佬，或者创始人都是移民或其后代，还有大量持有工作签证的高级管理人员以及科研人员。川普推行的是孤立主义推动下的移民政策，单单是最近的移民禁令就不同程度影响到各科技公司的外籍人才，继而影响到公司正常运营以及业务。</p><a href=\"https://link.zhihu.com/?target=http%3A//www.huffingtonpost.com/entry/lloyd-blankfein-muslim-ban_us_588f6ce5e4b01763779592c2\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">连投行界里高科技部门比重很高的高盛也公开反对川普这次的移民政策。</a><br/><br/><p data-pid=\"jEcofPY4\">另外，乡间红脖由于保守的消费观念以及远离创新性的产品消费环境，比在城市左派更少接触到硅谷高科技企业的产品，这就导致硅谷科技企业的美国市场业务，尤其是消费者业务的主要群体、用户形成以城市为中心，左派为主体的状态。这就导致硅谷企业面对左派消费者压力更大（川普在大选期间说过要杯葛苹果也没什么卵用）。 </p><p data-pid=\"VT6PwyVP\">不过这次Uber在移民禁令浪潮中被杯葛应该是多方面的原因，包括企业本身发展过程中就与同行竞争对手、原出租车行业、Uber美国司机，以及多个地方政府有过激烈冲突，得罪人多，声名过于狼藉，导致这次杯葛迫使Uber CEO退出川普的圈子。但是同样反对川普移民政策的特斯拉CEO就依旧呆在里面，还公开向其他人收集意见反馈给川普。</p><p data-pid=\"PULcOEYo\">价值观方面：</p><p data-pid=\"jSI299CE\">能在硅谷成功的大多数人，就算没有很强烈的政治意识形态，也是骨子里看不起川普这个人，更不用说那些已经持有左派理念的人，与川普身后代表的保守、孤立主义势不两立。在反对川普那些人眼中，川普只不过是一个继承千万财产，整天靠招摇撞骗，欺凌弱小，压榨员工，供应商以及商业伙伴，逃避社会责任（逃税、逃役）来积累不义之财的混混，跟硅谷里白手起家，把一个公司从车库做到500强的硅谷企业家无法相提并论。</p><p data-pid=\"qMTFaclT\">硅谷崇尚自由、平等、开放、进步、博爱的环境既是多数硅谷企业成功的基础，这些理念帮助了很多企业获得世界各地高水平员工，也帮助企业在市场取得比其他地区的企业更大的成功。川普身后代表了反对社会进步，极力保留愚昧保守观念的势力（尤其是福音派原教旨主义那一套），与硅谷左派认同的人生来自由平等等基本权利（如LGBT，女性健康保障以及公民基本医疗，受教育权等现代社会公民权利）格格不入。</p>", "excerpt": "具体来说跟业务与价值观都有很大的关系。 业务方面： 硅谷科技公司，尤其是互联网科技公司对全球化人才的依赖很大，很多硅谷科技公司现任大佬，或者创始人都是移民或其后代，还有大量持有工作签证的高级管理人员以及科研人员。川普推行的是孤立主义推动下的移民政策，单单是最近的移民禁令就不同程度影响到各科技公司的外籍人才，继而影响到公司正常运营以及业务。 <a href=\"https://link.zhihu.com/?target=http%3A//www.huffingtonpost.com/entry/lloyd-blankfein-muslim-ban_us_588f6ce5e4b01763779592c2\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">连投行界里高科技部门比重很高的高盛也公开反对川普这次的移民…</a>", "excerpt_new": "具体来说跟业务与价值观都有很大的关系。 业务方面： 硅谷科技公司，尤其是互联网科技公司对全球化人才的依赖很大，很多硅谷科技公司现任大佬，或者创始人都是移民或其后代，还有大量持有工作签证的高级管理人员以及科研人员。川普推行的是孤立主义推动下的移民政策，单单是最近的移民禁令就不同程度影响到各科技公司的外籍人才，继而影响到公司正常运营以及业务。 <a href=\"https://link.zhihu.com/?target=http%3A//www.huffingtonpost.com/entry/lloyd-blankfein-muslim-ban_us_588f6ce5e4b01763779592c2\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">连投行界里高科技部门比重很高的高盛也公开反对川普这次的移民…</a>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1523507606, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1523507606697&page_num=67"}}