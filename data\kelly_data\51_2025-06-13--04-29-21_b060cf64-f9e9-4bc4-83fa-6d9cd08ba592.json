{"data": [{"id": "1642282499516", "type": "feed", "target": {"id": "1884691581", "type": "answer", "url": "https://api.zhihu.com/answers/1884691581", "voteup_count": 392, "thanks_count": 99, "question": {"id": "266194776", "title": "在币安工作是怎么样的？", "url": "https://api.zhihu.com/questions/266194776", "type": "question", "question_type": "normal", "created": 1516949370, "answer_count": 35, "comment_count": 0, "follower_count": 1265, "detail": "<p>感兴趣，但是网上资料甚少，求币安工作人员解答，希望尽可能全面</p>", "excerpt": "感兴趣，但是网上资料甚少，求币安工作人员解答，希望尽可能全面", "bound_topic_ids": [707, 2566, 34183, 207629], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "a5809bbc7b9660d85a9bd31bda1b63e1", "name": "七夜说钱", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/altcoiner", "url_token": "altcoiner", "avatar_url": "https://picx.zhimg.com/v2-063fdc9226d24f38bc96b0f845655784_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1668008724, "created_time": 1620915241, "author": {"id": "", "name": "", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/", "url_token": "", "avatar_url": "https://pic3.zhimg.com/da8e974dc.jpg", "gender": -1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 368, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"hN4C8fkw\">首次编辑 2021.7</p><p data-pid=\"rJ1Lrvki\">匿了。新入职韭菜一棵，目前体验极佳。</p><p data-pid=\"zXewWfPh\">同事上司人都超级奶思，而且大家又帅又美，技术大佬也很和蔼。公司也算有矿，福利待遇也很好。</p><p data-pid=\"-Yfl4LWv\">不足就是毕竟灰产，很多东西不能明面说，然后招聘方式一般还是内推的多。</p><p data-pid=\"WlTI0LBq\">内推一个奖金极高（修改：奖金不多了，基本内推用爱发电了），我问同事有把他们一个组都从别的公司端来的。</p><p data-pid=\"h1T7HvZX\">之后有啥体验再说吧。我目前现在超喜欢这个工作。至于想暴富还有担心被抓走的都可以省一省。天上不会掉馅饼，有的风险是你选择这工作的时候就要知道的。</p><hr/><p data-pid=\"Y8gNBFdX\">2021.11</p><p data-pid=\"332o2g7X\">我又把答案捞了出来，或许我有机会在某乎拿到第一笔内推奖金 。</p><p data-pid=\"52hVl97z\">大家可以评论内推～但是先说好：</p><p data-pid=\"EqcBB5J5\">1.接受面向监狱搬砖，这个风险我来解释或者任何人解释都是没有意义的。最终需要自己评估的。不要私聊再问什么风险这些的。</p><p data-pid=\"E0g8-6E4\">2.搬砖地点分布式，只有你想不到没有没有。这个flexible程度超乎想象。不需要纳入考虑范围，也不用问了。（但是特别热爱线下交流工作环境的要慎重，wfh节约时间成本，也会缺失一些沟通体验）</p><p data-pid=\"63UAvVV_\">3.具体什么岗位可以先看官网，有的不是很满足可能也会招Junior一点的人，觉得自己ok可以先冲。</p><p data-pid=\"d_AGY6vM\">4.薪资具体和hr谈。我只能说具体到个人，和互联网厂相比有竞争力的薪水，这个描述应该没问题。</p><p data-pid=\"koZN9reJ\">5.福利待遇一流。</p><hr/><p data-pid=\"uVN2-n5y\">更新一下，很多朋友要加我貌似不是为了内推，是好奇或者问东问西的？</p><p data-pid=\"H0dbIQ8h\">比较基础的可以谷歌，具体职位可以看官网。我不理解有的选手加我之后：</p><p data-pid=\"W34UkKJ4\">1. 我是学xxxx的，你们有没有适合的岗位？</p><p data-pid=\"xRhNQ7bH\">请问你是我要给你量身定制一个吗。是你让我内推，不是我让你内推。</p><p data-pid=\"W3Jr6XeA\">2. 我好好奇里面工作是什么样的呀，你能具体说说吗？</p><p data-pid=\"ITbWhN4X\">成年人时间都宝贵的，我没兴趣给你科普聊天。特别是你根本没有表现出投递欲望的情况下。</p><p data-pid=\"QmGJB3Aw\">最后，我只是一个普通员工，你可以理解为是一个信箱，你写好邮编发信件就可以了。你不要指望那个邮箱能给你什么信息。我真的不是hr。</p><p data-pid=\"qJEVF2r7\">ps：大家最好备注一下学历吧。就如果奇奇怪怪的话我也不太好意思推的。</p><p data-pid=\"CE040Wp9\">2021.12 更新</p><p data-pid=\"OqXDYEVE\">差不多工作半年啦，从新韭菜变成老韭菜。再来补充一些新想法。</p><p data-pid=\"WzPIW-FW\">注明这个只是底层员工视角，给大家提供这个信息也要结合我自己的地位立场。</p><p data-pid=\"P6Ib4EW5\">首先这个工作按照时薪计算，应该是同级别互联网大厂拍马都追不上的。我了解的情况是除了个别短缺人手的组，还有个别卷王，大家加班都是真的自觉主动想多做点事，不是没有意义虚耗。当然2022开年之后感觉工作量变多了，这也是经济大环境下没办法的事。富有的公司也不能幸免。</p><p data-pid=\"A5tTb-XS\">有个很有意思的事，昨天看到我们产品6:50显示离线俩小时。我说你咋就下班了？</p><p data-pid=\"nRMvqc-a\">产品：“怎么了？七点还不下班啊？”（理直气壮）</p><p data-pid=\"xWnLmstG\">同样我问字节小伙伴，“你们最近加班严重吗？”</p><p data-pid=\"pmzMa8w2\">答曰：“9点下班算加班吗？”（诚恳）</p><p data-pid=\"jsvwo2Ly\">我：“？？？不然呢？？”</p><p data-pid=\"qu5Dirf-\">再答曰：“互联网公司9点下班不是很正常吗？”</p><p data-pid=\"aZXBMAet\">这个可以自己感受。</p><p data-pid=\"d1OXAsXL\">能把9点下班视为正常的工作环境，我还是家里蹲算了。</p><p data-pid=\"nVFEjMS1\">就冲这一点，这个工作是值得做的。</p><p data-pid=\"FtVQ0CaX\">其他还有很多事情，比如其他业务线的英语登峰造极的大佬来 <b>友情</b> 听我讲ppt（真的这个ppt和他一毛钱关系也没有，纯友情出演），然后提出改进意见。（非技术组英语都相当极其好）其他部门的同事会给我发公告的小失误疯狂擦屁股。然后插队加需求啥的也是家常便饭了，大佬们不情不愿不情不愿，还是最后都做了。</p><p data-pid=\"ZyK6S5WB\">以及每周固定都有分享会，大家可以自己报名，可以讲自己最近工作成果分析，可以比如技术讲讲自己的专业术语，产品讲讲abtest，分析一下新的不知道啥币的技术。听不懂也可以学学英语。</p><p data-pid=\"5jcWkG-v\">老板们说话也会被别的大佬强行打断n次，每次我都有点忍不住不笑。</p><p data-pid=\"B09aP4aa\">可以从我的字里行间看出来是很喜欢这份工作的。</p><p data-pid=\"NkmRgG9t\">然后就回到风险问题。我不止一个韭菜朋友问，风险大不大，好想去啊，但是有点担心balabala。</p><p data-pid=\"iispytdZ\">这种问题是没有答案的。</p><p data-pid=\"4isQ4Jku\">当你问的时候，你内心其实就有了一个答案。</p><p data-pid=\"kIFquBn4\">你希望我的回答是：</p><p data-pid=\"ct5BY8Pd\">A：“风险超大，我每天在里面兢兢业业担心被抓走。”</p><p data-pid=\"2s4HBJzJ\">这样你可以得到心理上的平衡：切，说那么好有什么p用。到时候工资都要被收缴。</p><p data-pid=\"sxDe_JGP\">B：“没事啊啥事没有，你快来吧，我给你内推。”</p><p data-pid=\"ksr7l3Gi\">好的！我在里面工作的朋友都说没风险！这还不赶紧投！</p><p data-pid=\"zo3sOjvS\">但是我作为一个正常人，是不会给出上面任何一种回答的，不然就证明我脑子有问题。</p><p data-pid=\"WGT-Fy5i\">除了以上两种回答，其他回答有区别吗？</p><p data-pid=\"8tHhe5WH\">这样可以说明白了吗？</p><p data-pid=\"BLGM9Plo\">我这半年还有一个小感悟就是，如果有的人最开始就是完全否认态度，他一定不是目标群体。</p><p data-pid=\"CJwTg-r-\">比如上来就问风险的，是我朋友我一定不内推。我也不想搞半天被埋怨。</p><p data-pid=\"kNJH5_dW\">比如上来就是这种平台坑爹害人的，恕我直言，你这个智商进去也是被割。别玩了真的。</p><p data-pid=\"U2ihbNI_\">我之前问过我们产品，这个xxx用户看不懂咋整，产品：太笨了，这个不是我们目标用户。</p><p data-pid=\"fT94gGx1\">虽然听起来政治不正确，但是仔细想想，你要转化这样的人付出代价太高了。</p><p data-pid=\"qSP7fZTU\">同理，如果上来就揪着我问这问那的，然后表现出很强的不信任/打探感的，我转化你的成本是不是也太高了点？</p><p data-pid=\"gUvO-a3p\">ps：最近发现我的同事们都是宝藏同事。希望有一天我也能和他们一样强，有一样有趣的人生经历。</p><p data-pid=\"2NHGQ31k\">再ps：我略微搞了点理财，反正我们app里的收益比我zfb和某银行收益加起来还高，这我能说啥。连定投理财都省了。</p><p data-pid=\"M8A6RGXz\">再再ps：最后一句还是删掉吧。这个不严谨。我昨晚跌了一波一年收成回去了…啊这。</p><p data-pid=\"AzV70kya\">2022.2补充</p><p data-pid=\"2KNeU4b1\">【国内必须出国】出国package会涨（但是相当于招聘成本也高了，大家可以评估一下自己实力）。不招国内实习。</p><p data-pid=\"cuIRZw3g\">据我了解，【开发不卡学历】，卡经验值，我遇到的开发都是三年以上大厂经验，学历普通/专业不匹配的都有。</p><p data-pid=\"Vg83ByIP\">【非技术比较卡学历】，严格说是我看身边非技术的学历都比我，好得到的幸存者偏差结论。当然，我内推的也有学历很好的挂了的（比如NUS）。</p><p data-pid=\"xT7ymIhd\">如果ok的我3天内会私信回复，加tg备注内推。超过三天就可以不用等了。</p><p data-pid=\"XZOKiYZy\">（大家不要知乎设置陌生人禁发消息，我消息会发不出去…）</p><p data-pid=\"SVyOWUUw\">【2022.10 最新更新】</p><p data-pid=\"LIDIxqMy\">已润，关匿名。</p><p data-pid=\"IJzv9aQH\">优点新增：提供润的可能和非常好的福利待遇。</p><p data-pid=\"QxiM9fiK\">就这一条可以抵过千个万个其他优点。</p>", "excerpt": "首次编辑 2021.7 匿了。新入职韭菜一棵，目前体验极佳。 同事上司人都超级奶思，而且大家又帅又美，技术大佬也很和蔼。公司也算有矿，福利待遇也很好。 不足就是毕竟灰产，很多东西不能明面说，然后招聘方式一般还是内推的多。 内推一个奖金极高（修改：奖金不多了，基本内推用爱发电了），我问同事有把他们一个组都从别的公司端来的。 之后有啥体验再说吧。我目前现在超喜欢这个工作。至于想暴富还有担心被抓走的都可以省一省。…", "excerpt_new": "首次编辑 2021.7 匿了。新入职韭菜一棵，目前体验极佳。 同事上司人都超级奶思，而且大家又帅又美，技术大佬也很和蔼。公司也算有矿，福利待遇也很好。 不足就是毕竟灰产，很多东西不能明面说，然后招聘方式一般还是内推的多。 内推一个奖金极高（修改：奖金不多了，基本内推用爱发电了），我问同事有把他们一个组都从别的公司端来的。 之后有啥体验再说吧。我目前现在超喜欢这个工作。至于想暴富还有担心被抓走的都可以省一省。…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {"REACTION_CONTENT_SEGMENT_LIKE": "HIDE"}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1642282499, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1639542062386", "type": "feed", "target": {"id": "2271748187", "type": "answer", "url": "https://api.zhihu.com/answers/2271748187", "voteup_count": 1, "thanks_count": 0, "question": {"id": "420682401", "title": "健康和财富，你选择哪一个？", "url": "https://api.zhihu.com/questions/420682401", "type": "question", "question_type": "normal", "created": 1599870494, "answer_count": 13443, "comment_count": 16, "follower_count": 13445, "detail": "", "excerpt": "", "bound_topic_ids": [112, 237, 2896, 12674, 122537], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1639542860, "created_time": 1639542061, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<blockquote data-pid=\"y7cFkoYj\">“Easy choice, hard life. Hard Choice, easy life.” </blockquote><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-07b6e44eacf4578a677ee9775beb8fb1_b.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"720\" data-original-token=\"v2-66fe39d5b52fce391d844101cc9dc29d\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-07b6e44eacf4578a677ee9775beb8fb1_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;720&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"720\" data-original-token=\"v2-66fe39d5b52fce391d844101cc9dc29d\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-07b6e44eacf4578a677ee9775beb8fb1_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-07b6e44eacf4578a677ee9775beb8fb1_b.jpg\"/><figcaption>本期主人公：硅谷天使投资人, AngelList创始人Naval Ravikant</figcaption></figure><h2>关于Naval Ravikant</h2><p data-pid=\"o7woD3TB\">出生于印度，幼时移居美国，父母离异，和弟弟一起随母亲在纽约布鲁克林的黑人聚集区长大。今天要写的这位创业者/投资人，不像那些有家族从商传统、家境优越的创业者，他的起跑线远不如别人那么亮丽。</p><p data-pid=\"bmOMJRZx\">在小的时候，他只能隔着窗玻璃羡慕别人的漂亮房子和幸福生活，希望有一天他也能实现自己的美国梦。</p><p data-pid=\"zBHh68ae\">好在他还算聪明，凭着不错的成绩考进了不错的高中，后来顺利进入藤校达特茅斯学院，主修计算机和经济学。</p><p data-pid=\"jZq2wda9\">毕业后他连续创办了多家公司，现在他是知名天使投资平台AngelList的CEO和创始人，他自己也投资了超过200家公司，其中包括Twitter, Uber, Yammer, PostMates, Wish, Thumbtack等知名公司。他就是Naval Ravikant。</p><p data-pid=\"KtGq51vm\">知名公司那么多，创始人那么多，为什么会写他？</p><p data-pid=\"ODpyXSHb\">按照一位朋友的话来说：“他是一个真正慷慨分享自己成功经验的人。”</p><p data-pid=\"TPAYbV8T\">Naval 经常在Podcast、Twitter 上分享自己的思考，他在2018年发的 &#34;How to get rich without getting lucky&#34;（如何不靠运气致富）系列Tweets，朴素而充满智慧，至今被转发点赞二十多万次，被翻译成多种语言在全世界范围传播。</p><p data-pid=\"f4dWD6wG\">有人看到他的智慧四处散落在网络上，便把它们整理成了一本书：The Almanack of Naval Ravikant: A Guide to Wealth and Happiness。</p><p data-pid=\"JNrqLxho\">一看书名，嘿，和《穷查理宝典》（ Poor Charlie&#39;s Almanack ）怎么那么像。确实如此，在这本书以及 Naval 过去的访谈里，你会注意到他多次提到查理·芒格。在本书末尾的推荐书目里，《穷查理宝典》也赫然在列。芒格应该是 Naval 的榜样之一。</p><p data-pid=\"hQ8c3_DY\">这本书不难读，推荐有英语阅读能力的朋友读英文原版。这篇文章是我总结的一部分精华。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-5c674f9e2d54e1910befbe8e903e4f20_b.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"708\" data-original-token=\"v2-20bd6641293f5110e183d44765c785b2\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-5c674f9e2d54e1910befbe8e903e4f20_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;708&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"708\" data-original-token=\"v2-20bd6641293f5110e183d44765c785b2\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-5c674f9e2d54e1910befbe8e903e4f20_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-5c674f9e2d54e1910befbe8e903e4f20_b.jpg\"/><figcaption>The Almanack of Naval Ravikant</figcaption></figure><h2>一、如何不靠运气致富</h2><p data-pid=\"kptmpgcO\">你应该追求财富，而不是金钱或者地位。财富是能在你睡觉时自己增值的资产。</p><p data-pid=\"gR2P9o6v\">一个人完全可以以道德的方式获得财富。如果你鄙视财富，那么财富也会躲着你。</p><p data-pid=\"7AilK2xf\">你不会通过单纯出卖自己的时间致富。你必须通过拥有股权，也就是一家公司的一部分，来实现财富自由。这可能意味着自己创业、加入一家公司并拿到股权、或者购买一家公司的股票。</p><p data-pid=\"3KU4Ch_r\">如果你可以规模化的提供社会大众想要但是不知道如何获取的东西，你就会因此致富。</p><p data-pid=\"0J40YizV\">选择一个你愿意长期从事的行业，选择同样信奉长期主义的伙伴一起前行。</p><p data-pid=\"pMlzKQMS\">选择高智商、精力旺盛、正直的合作伙伴，其中最重要的一点是正直。</p><p data-pid=\"Wu8kGxIi\">无论是财富，人际关系，或者知识，所有你人生里获得的回报，都来自复利效应。</p><p data-pid=\"18lvKhX0\">学习如何销售，学习如何做产品。如果你既懂销售又能做产品，你将势不可挡。</p><p data-pid=\"JLivwvmd\"><b>要想获得财富，你需要用独到知识，责任，和杠杆来装备自己。</b></p><h3>1. 独到知识</h3><p data-pid=\"fa8W2ouL\">独到知识是那种不能轻易通过培训而获得的知识。如果别人可以通过培训很容易就获得这门知识，那么别人就可以轻松取代你。</p><p data-pid=\"JluLRzL-\"><b>如何找到自己的独到知识？</b></p><p data-pid=\"plk4fQRJ\"><b>跟随你的兴趣和好奇心，而不是追赶潮流和“风口”。</b></p><p data-pid=\"7I2DgB07\">独到知识通常极富技术性和创造性，不能被外包或自动实现。</p><p data-pid=\"xbQeAakH\">由于你本身有对这个领域有着强烈的兴趣和好奇心，积累独到知识对你来说会像玩儿一样轻松，而对别人来说却是一份乏味的工作。</p><p data-pid=\"dHtbay8N\">因为对你来说就像玩儿一样，你可以把除了睡觉之外的时间都用在钻研这个领域上而不觉得累，而别人只能朝九晚五。长期下来，在复利效应的作用下，你会拥有巨大的优势。</p><h3>2. 责任</h3><p data-pid=\"M4TpagRA\">拥抱责任，敢于摊上自己的名誉去承担风险，那么社会会以责任、股权和杠杆回报给你。</p><p data-pid=\"vNh8V868\">责任最大的人往往拥有独一无二、世人皆知、敢于冒险的个人品牌，比如：奥普拉·温弗里、特朗普、侃爷、埃隆·马斯克……</p><h3>3. 杠杆</h3><p data-pid=\"79BrnjEU\"><b>财富的增长需要杠杆。商业杠杆的主要来源有以下几类：资本，人力，复制起来边际成本为0的产品，比如代码和媒体。</b></p><p data-pid=\"Y3NTP2ua\">资本，也就是钱。想要融资，那就要运用你的独到知识，承担责任，并展示出你的判断力。</p><p data-pid=\"ijaqbFXQ\">人力，即为你干活的人。你的父母也许会因为你手下有很多人为你工作而感到骄傲，但是你不应该把时间浪费在追求人力杠杆上。</p><p data-pid=\"k2ycxPWn\"><b>资本和人力都是需要他人许可才能使用的杠杆。</b>每个人都追逐资本，但是得有个人愿意提供资本；每个人都想要领导其他人，但是总得有其他人愿意跟随。</p><p data-pid=\"PXuYhQvW\"><b>代码和媒体是无需许可即可使用的杠杆。</b>它们是新贵人群使用的杠杆，如果你可以写软件、创作自媒体作品，在你睡觉时他们依然可以为你赚钱。</p><p data-pid=\"scd-tYDT\">如果你不会写代码，那么就写书或者创作博客，录制视频或者播客。</p><p data-pid=\"8WxU-rCn\">你应该给自己设定一个有抱负的时薪。如果解决一个问题所能节省下来的成本低于你的时薪，那么就忽略这个问题。如果一项任务的外包成本低于你的个人时薪，那么就外包。</p><p data-pid=\"AANvkvcW\">你要能在你所在的领域做到顶尖水平。如果你不能，那么你需要不断重新定义你的领域，直到你找到可以做到顶尖水平的领域。</p><p data-pid=\"luniYpK-\">这个世界上没有一夜暴富的方法，如果你想找到一夜暴富的方法，那么你只会让别人从你身上赚钱致富。</p><p data-pid=\"PXtHwxg6\">运用你的独到知识，配合上杠杆（资本、人力、代码、媒体），最终你会得到你应该得到的东西。</p><p data-pid=\"G99a9I_N\">等到有一天你终于变得富有，你会发现财富并不是你起初想要追寻的东西。不过这都是后话了。</p><h2><b>二、如何提升判断力</b></h2><p data-pid=\"E4CWfNGL\">如果你想要赚尽可能多的钱，并且最大程度的提高你这辈子赚到大钱的可能性，那么你需要走在趋势的最前端，学习科技、设计、和艺术，争取做到特别擅长某一个领域。</p><p data-pid=\"QpaD4UaK\">你前进的方向比你前进的速度更重要，特别是在有杠杆作用的情况下，因为杠杆能够成倍地放大你的判断力。（之前的这篇文章《<a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247485428%26idx%3D1%26sn%3D1236afbcb6b3cc6b7f412f8b54853f42%26chksm%3Dfdb448dacac3c1cc61fcf70f36ba7406ee800baeb31e51a5f5038e5db324851734657100e1fa%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">职业规划，你只需要做对这两件事</a>》里写到过Sam Altman的类似说法。）</p><p data-pid=\"EuQXapC1\">判断力需要经验，但是可以通过学习基本技能快速建立起来。并不存在一种叫做“商业”的能力，但是你可以通过学习微观经济学、博弈论、心理学、说服术、伦理学、数学和计算机科学建立起你的判断力。</p><h3>1. 做一个头脑清醒的思考者</h3><p data-pid=\"G9kKq5Z9\">做一个头脑清醒的思考者意味着懂得事物的基本原理，能够用简单的语言解释复杂事物。</p><p data-pid=\"2m_MuY5K\">如果一个人讲话全是高深的概念和专业的词汇，那么八成他也不知道自己在说什么。你对事物的解释应该让小孩子都听得懂。如果你不能从最基本最底层的角度理解一个概念，那么你很可能只是在死记硬背。</p><p data-pid=\"8gVBbYap\">Naval 说自己从不浅尝辄止，了解新事物时总会问很多问题，从第一性原理思考，直到自己真正深入了解一个领域。</p><p data-pid=\"uZ1QGbVz\">想要让自己更善于思考，可以从阅读数学、科学、以及哲学领域的经典著作开始。忽略当代作品和新闻。</p><p data-pid=\"pv0c1f9D\">这些领域的经典著作可能不那么好读，但是就跟你需要去健身房锻炼肌肉一样，你也需要通过阅读来训练你的阅读肌肉，你应该练习如何阅读这类书。</p><h3>2. 建立多元的思维模型</h3><p data-pid=\"AyOJSEJH\">Naval 推崇的有着多元思维模型的人有：查理·芒格、纳西姆·塔勒布、以及本杰明·富兰克林。（有意思的是，查理·芒格的榜样是本杰明·富兰克林。）</p><p data-pid=\"gcfac0Hf\">书中提到的 Naval 的常用思维模型有：进化论、博弈论、经济学、复利效应、委托-代理问题（principal–agent problem）、复杂性理论（complexity theory）、基础数学、黑天鹅、微积分、可证伪性等等。感兴趣的朋友建议自行查询一下不熟悉的思维模型，以丰富自己的思维模型库。</p><p data-pid=\"xJ20snBT\"><b>怎么建立多元思维模型？</b></p><p data-pid=\"3cCoiEbm\"><b>阅读，大量的阅读。</b></p><p data-pid=\"XonnXSmM\">如果你能爱上阅读，这将成为你的一个超能力。读得多了，你会自然成为一位鉴赏家。你会更倾向于读理论、概念和非虚构类读物。</p><p data-pid=\"pEXJuACp\">阅读一本书、一篇文章不会给你带来很大的改变，但是在复利效应的作用下，长期下来会给你带来巨大的改变。</p><p data-pid=\"4HeqVeHC\">Naval 从小热爱读书，平均每天读书1-2小时，他说这足以使他处于人群的前0.00001%。他自称自己所有物质上的成功，和他可能拥有的任何智慧，都归功于他的大量阅读。</p><p data-pid=\"MyDi3ED0\">这跟他的两个榜样所提倡的十分相似——</p><p data-pid=\"cQF8xM1o\">查理芒格曾说：“只要我手里有本书，我就不觉得我在浪费时间。”</p><p data-pid=\"d2gFbucT\">富兰克林曾说：“读书是我让自己享受的唯一乐趣。我不在酒馆、赌场或任何游乐场合消磨时光。”</p><figure data-size=\"normal\"><noscript><img src=\"https://pica.zhimg.com/v2-c9a1a399fa0f63b12fcc5348dbffbf1a_b.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"810\" data-original-token=\"v2-336f17736b637135f66d39f10ab81f89\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-c9a1a399fa0f63b12fcc5348dbffbf1a_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;810&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"810\" data-original-token=\"v2-336f17736b637135f66d39f10ab81f89\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-c9a1a399fa0f63b12fcc5348dbffbf1a_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-c9a1a399fa0f63b12fcc5348dbffbf1a_b.jpg\"/><figcaption>Naval Ravikant</figcaption></figure><h2>三、如何过上幸福的生活</h2><p data-pid=\"akZafIi7\">你的每一个欲望都是你给自己选择的一个不幸福的理由</p><p data-pid=\"yA2EBTM8\">人之所以不幸福是因为欲望，人们想要这个、想要那个，很少能够活在当下，也就无法去感受当下的美好。</p><p data-pid=\"gHW6_VYp\">幸福就是你感到平静、满足，不觉得生活中缺少什么。你的欲望越少，你就越能够接受你当下的状态，你的思绪就会越少的去想过去或者将来。</p><p data-pid=\"WB04tsH0\">成功不会带来幸福。相反，成功和幸福往往是矛盾的。</p><p data-pid=\"7wLkKDdm\">幸福是对自己现状的满足，而成功往往来自于对现状的不满。那些想要取得成功的人，往往在取得了小的成功后，还想要取得更大的成功。他们对现状的不满是他们能够获得成功的动力，但这也就意味着他们常常感到不快乐。</p><p data-pid=\"hkjYnMXr\">怎样才能过的幸福？</p><h3>1. 降低欲望</h3><p data-pid=\"pbdqoAVq\">因为你的每一个欲望都是你给自己选择的一个不幸福的理由。</p><h3>2. 停止嫉妒</h3><p data-pid=\"5q-p0tw5\">嫉妒有毒。你的嫉妒不会对别人产生任何影响，你也不会因为嫉妒别人而变的更好，只会因为嫉妒变得更不快乐。所以，停止嫉妒。</p><h3>3. 培养习惯</h3><p data-pid=\"AcbnMgMZ\">不饮酒、不吃糖、不刷社交网络，可以使你的情绪更稳定。</p><p data-pid=\"o3yYHv2s\">玩游戏让你获得短暂的快乐，长期来说对你有害。咖啡因也是一样。</p><p data-pid=\"LGu2uiGL\">每天锻炼身体可以让你感到快乐。</p><p data-pid=\"Bp2Em1P6\">减少对这三个app的使用频率：电话、日程表、闹钟。</p><p data-pid=\"WxmNvZFD\">减少内心乱七八糟的想法。</p><p data-pid=\"Xj5nFJ0c\">不去在意那些无关紧要的事情。</p><p data-pid=\"tGH7A3cv\">和快乐的人交往。</p><p data-pid=\"AfZ36uFR\">阅读哲学，冥想。</p><p data-pid=\"FqDfdegQ\">珍惜自己的时间。</p><h3>3. 跳出游戏</h3><p data-pid=\"qVl3OFiE\">人们习惯于把人生过成一个多人游戏，总是去跟别人比较谁过的更好。然而事实是，人生是一场单机游戏，你独自来到这个世界，最后会独自离去。你的所有记忆、所有经历都是你自己一个人的。过了三代人以后，大概率没有人再记得你。</p><p data-pid=\"20d5dD2l\">那些跳出这场游戏、真正超脱的人，是 Naval 心目中最成功的人。</p><p data-pid=\"AINsDbPf\">你终有一天会离开这个世界，所有的一切都将变得不再重要。所以，趁现在，享受生活，做一些积极的事，散播一些爱，给一些人带去快乐，多笑一笑，感恩当下这一刻。</p><h2>四、如何自救</h2><h3>1. 做你自己</h3><p data-pid=\"ndnPIJ3k\">你的人生目标是找到这个世界上最需要你的人、生意、项目、或者艺术创作。这个世界上一定有一件事是为你准备的、最需要你的。</p><p data-pid=\"FqsbWteR\">你最不应该做的就是看见别人在做什么事，就去给自己的 to-do list 加上一笔。你永远无法成为别人，你应该成为自己，没有人能比你更“成为你”。</p><h3>2. 关爱自己</h3><p data-pid=\"uhNgLyqX\">生活中最最重要的事，比快乐、家庭、事业都更重要的事，是自己的健康。</p><p data-pid=\"SDE-I5aA\">首先是身体的健康，其次是精神健康，然后是家人的健康。在这些之后，才是其他我们要在这个世界上做的事。</p><p data-pid=\"v1Le8UgD\">因为自己的健康是最重要的事，所以 Naval 每天起床后第一件事一定是健身。不管有什么大事，都可以等到锻炼半小时以后再说。</p><p data-pid=\"wIih8iIG\">听起来很困难是吗，的确不容易，但是——“Easy choice, hard life. Hard Choice, easy life.” （轻松的选择，艰难的生活。艰难的选择，轻松的生活。）</p><h3>3. 自我成长</h3><p data-pid=\"x5dTMqbY\">每个人都有令自己感到有激情的领域，你需要找到你的领域是什么。</p><p data-pid=\"J7FHER8H\">每学习一个新领域，要掌握最根本的知识，把基础打结实。</p><p data-pid=\"n6YkpRcY\">Naval 认为对自己来说最重要的技能是：对阅读的热爱，数学思维，以及说服他人的能力。</p><p data-pid=\"XC1cmNQ8\">阅读是知识和智慧的来源，数学思维无论在商业还是科学领域都很有用，说服他人的能力可以帮助你在这个社会上更游刃有余。</p><h3>4. 解放自己</h3><p data-pid=\"eIekR63-\">Naval 说自己以前认为的自由是有自由去做想做的事，现在他所理解的自由是内在的自由，即选择不做什么的自由。</p><p data-pid=\"woZfOOvJ\">不要在意他人的看法。重视你的时间，不要浪费时间，没有什么东西比你的时间更重要。</p><p data-pid=\"dJYgZuXr\">不要把时间用在为了让别人开心上。别人开不开心是别人的问题，不是你的问题。</p><p data-pid=\"G3dwNTIC\">做你自己想做的事。只要你在做自己想做的事，你就没有在浪费时间。但是如果你没有在做自己想做的事，你也没有在学习新东西，你到底在干嘛呢？</p><p data-pid=\"gTpIR5Oi\">生活水平远低于自己收入水平的人拥有着忙于消费升级的人无法体会的自由。</p><p data-pid=\"hAnli0wS\">一旦你能够掌握自己的命运，能决定自己想做什么、不做什么，你就不会愿意再让别人来告诉你该做什么。</p><p data-pid=\"RTUqBjnc\">一旦你品尝过自由的滋味，你不会愿意再继续被他人雇佣。</p><h2><b>结语</b></h2><p data-pid=\"se9gthZF\">总结下来，Naval 给人们的建议是：</p><p data-pid=\"zgeu4U5A\">要想富有，你得用独到知识，责任，和杠杆来装备自己。</p><p data-pid=\"aFuaOMDP\">要想赚尽可能多的钱，方向比速度更重要，为了找准方向，你需要提升你的判断力。</p><p data-pid=\"6yHynEkZ\">即使真的富有了，你也不会因此而更幸福。要想过的幸福，你需要降低欲望、停止嫉妒、培养好习惯，或者干脆跳出游戏，不去和他人进行无意义的竞争。</p><p data-pid=\"Egol23S5\">你独自来到这个世上，最后也会独自离去。所以，照顾好你的健康，做你自己，找到这个世界最需要你的那个位置，去发光发热。</p><p data-pid=\"xeMugHxp\">不要在意他人的看法，不要浪费时间，不要被外物牵绊，努力去掌握自己的命运，获得真正的自由。</p><p data-pid=\"ecnMTCjS\">读完你有什么想法呢？欢迎留言与大家交流。</p><p data-pid=\"Yh1p8P-I\"><b>参考资料</b></p><ul><li data-pid=\"mL5Vu7-Z\"><a href=\"https://link.zhihu.com/?target=https%3A//book.douban.com/subject/35219944/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">The Almanack of Naval Ravikant</a></li><li data-pid=\"gBQVcdMb\"><a href=\"https://link.zhihu.com/?target=https%3A//tim.blog/2015/08/18/the-evolutionary-angel-naval-ravikant/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Naval Ravikant — The Person I Call Most for Startup Advice</a></li><li data-pid=\"Sq2Wwldv\"><a href=\"https://link.zhihu.com/?target=https%3A//www.fx896.com/a/10120210728002501275890\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">硅谷著名的投资人 | 如何不依靠运气变得富有？</a></li><li data-pid=\"s4CpeKgg\"><a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s/TfhBCbr8-IoHyPKtB3hTlw\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Naval：如何不靠运气致富</a></li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"KqpHOEhf\">本文作者：公众号「凯莉彭」，主要写创始人成长故事，欢迎关注。</p>", "excerpt": "“Easy choice, hard life. Hard Choice, easy life.” 关于Naval Ravikant出生于印度，幼时移居美国，父母离异，和弟弟一起随母亲在纽约布鲁克林的黑人聚集区长大。今天要写的这位创业者/投资人，不像那些有家族从商传统、家境优越的创业者，他的起跑线远不如别人那么亮丽。 在小的时候，他只能隔着窗玻璃羡慕别人的漂亮房子和幸福生活，希望有一天他也能实现自己的美国梦。 好在他还算聪明，凭着不错的成绩考进了不错的高中，…", "excerpt_new": "“Easy choice, hard life. Hard Choice, easy life.” 关于Naval Ravikant出生于印度，幼时移居美国，父母离异，和弟弟一起随母亲在纽约布鲁克林的黑人聚集区长大。今天要写的这位创业者/投资人，不像那些有家族从商传统、家境优越的创业者，他的起跑线远不如别人那么亮丽。 在小的时候，他只能隔着窗玻璃羡慕别人的漂亮房子和幸福生活，希望有一天他也能实现自己的美国梦。 好在他还算聪明，凭着不错的成绩考进了不错的高中，…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1639542062, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}, {"id": "1639542061018", "type": "feed", "target": {"id": "445589956", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1639542060, "updated": 1653279487, "title": "硅谷天使投资人Naval Ravikant：如何过上富有又幸福的一生", "excerpt_title": "", "content": "<blockquote data-pid=\"x7B21-9X\">“Easy choice, hard life. Hard Choice, easy life.” </blockquote><h2>关于Naval Ravikant</h2><p data-pid=\"ID-k0sMf\">出生于印度，幼时移居美国，父母离异，和弟弟一起随母亲在纽约布鲁克林的黑人聚集区长大。今天要写的这位创业者/投资人，不像那些有家族从商传统、家境优越的创业者，他的起跑线远不如别人那么亮丽。</p><p data-pid=\"rCJrDIjd\">在小的时候，他只能隔着窗玻璃羡慕别人的漂亮房子和幸福生活，希望有一天他也能实现自己的美国梦。</p><p data-pid=\"wvmG0bJ5\">好在他还算聪明，凭着不错的成绩考进了不错的高中，后来顺利进入藤校达特茅斯学院，主修计算机和经济学。</p><p data-pid=\"YYQZAKrw\">毕业后他连续创办了多家公司，现在他是知名天使投资平台AngelList的CEO和创始人，他自己也投资了超过200家公司，其中包括Twitter, Uber, Yammer, PostMates, Wish, Thumbtack等知名公司。他就是Naval Ravikant。</p><p data-pid=\"CNK5iPnm\">知名公司那么多，创始人那么多，为什么会写他？</p><p data-pid=\"klvmi4Tq\">按照一位朋友的话来说：“他是一个真正慷慨分享自己成功经验的人。”</p><p data-pid=\"MGzFAd5_\">Naval 经常在Podcast、Twitter 上分享自己的思考，他在2018年发的 &#34;How to get rich without getting lucky&#34;（如何不靠运气致富）系列Tweets，朴素而充满智慧，至今被转发点赞二十多万次，被翻译成多种语言在全世界范围传播。</p><p data-pid=\"j9AbP0ic\">有人看到他的智慧四处散落在网络上，便把它们整理成了一本书：The Almanack of Naval Ravikant: A Guide to Wealth and Happiness。</p><p data-pid=\"HH5dkN3F\">一看书名，嘿，和《穷查理宝典》（ Poor Charlie&#39;s Almanack ）怎么那么像。确实如此，在这本书以及 Naval 过去的访谈里，你会注意到他多次提到查理·芒格。在本书末尾的推荐书目里，《穷查理宝典》也赫然在列。芒格应该是 Naval 的榜样之一。</p><p data-pid=\"fvGrOpa2\">这本书不难读，推荐有英语阅读能力的朋友读英文原版。这篇文章是我总结的一部分精华。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-5c674f9e2d54e1910befbe8e903e4f20_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"708\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-5c674f9e2d54e1910befbe8e903e4f20_r.jpg\" data-original-token=\"v2-20bd6641293f5110e183d44765c785b2\"/><figcaption>The Almanack of Naval Ravikant</figcaption></figure><h2>一、如何不靠运气致富</h2><p data-pid=\"hTNclXHY\">你应该追求财富，而不是金钱或者地位。财富是能在你睡觉时自己增值的资产。</p><p data-pid=\"Yro-zqk1\">一个人完全可以以道德的方式获得财富。如果你鄙视财富，那么财富也会躲着你。</p><p data-pid=\"xCQPbwyG\">你不会通过单纯出卖自己的时间致富。你必须通过拥有股权，也就是一家公司的一部分，来实现财富自由。这可能意味着自己创业、加入一家公司并拿到股权、或者购买一家公司的股票。</p><p data-pid=\"YmqIpnX9\">如果你可以规模化的提供社会大众想要但是不知道如何获取的东西，你就会因此致富。</p><p data-pid=\"9iR_81Y_\">选择一个你愿意长期从事的行业，选择同样信奉长期主义的伙伴一起前行。</p><p data-pid=\"OS255bt5\">选择高智商、精力旺盛、正直的合作伙伴，其中最重要的一点是正直。</p><p data-pid=\"SpvMg66s\">无论是财富，人际关系，或者知识，所有你人生里获得的回报，都来自复利效应。</p><p data-pid=\"1JxwCR5J\">学习如何销售，学习如何做产品。如果你既懂销售又能做产品，你将势不可挡。</p><p data-pid=\"70fKfwic\"><b>要想获得财富，你需要用独到知识，责任，和杠杆来装备自己。</b></p><h3>1. 独到知识</h3><p data-pid=\"LACuXo5Q\">独到知识是那种不能轻易通过培训而获得的知识。如果别人可以通过培训很容易就获得这门知识，那么别人就可以轻松取代你。</p><p data-pid=\"N8ZecCZ8\"><b>如何找到自己的独到知识？</b></p><p data-pid=\"EstxK2pq\"><b>跟随你的兴趣和好奇心，而不是追赶潮流和“风口”。</b></p><p data-pid=\"tqzyfFoR\">独到知识通常极富技术性和创造性，不能被外包或自动实现。</p><p data-pid=\"v4NzFTu_\">由于你本身有对这个领域有着强烈的兴趣和好奇心，积累独到知识对你来说会像玩儿一样轻松，而对别人来说却是一份乏味的工作。</p><p data-pid=\"47UfJYMs\">因为对你来说就像玩儿一样，你可以把除了睡觉之外的时间都用在钻研这个领域上而不觉得累，而别人只能朝九晚五。长期下来，在复利效应的作用下，你会拥有巨大的优势。</p><h3>2. 责任</h3><p data-pid=\"_A5m-hBE\">拥抱责任，敢于摊上自己的名誉去承担风险，那么社会会以责任、股权和杠杆回报给你。</p><p data-pid=\"MVjBB7D-\">责任最大的人往往拥有独一无二、世人皆知、敢于冒险的个人品牌，比如：奥普拉·温弗里、特朗普、侃爷、埃隆·马斯克……</p><h3>3. 杠杆</h3><p data-pid=\"L06DKraU\"><b>财富的增长需要杠杆。商业杠杆的主要来源有以下几类：资本，人力，复制起来边际成本为0的产品，比如代码和媒体。</b></p><p data-pid=\"2whVhqrK\">资本，也就是钱。想要融资，那就要运用你的独到知识，承担责任，并展示出你的判断力。</p><p data-pid=\"zJimliAg\">人力，即为你干活的人。你的父母也许会因为你手下有很多人为你工作而感到骄傲，但是你不应该把时间浪费在追求人力杠杆上。</p><p data-pid=\"a7JxvLBg\"><b>资本和人力都是需要他人许可才能使用的杠杆。</b>每个人都追逐资本，但是得有个人愿意提供资本；每个人都想要领导其他人，但是总得有其他人愿意跟随。</p><p data-pid=\"i1KbOwKQ\"><b>代码和媒体是无需许可即可使用的杠杆。</b>它们是新贵人群使用的杠杆，如果你可以写软件、创作自媒体作品，在你睡觉时他们依然可以为你赚钱。</p><p data-pid=\"zCBN6xUl\">如果你不会写代码，那么就写书或者创作博客，录制视频或者播客。</p><p data-pid=\"MJ6pYuaR\">你应该给自己设定一个有抱负的时薪。如果解决一个问题所能节省下来的成本低于你的时薪，那么就忽略这个问题。如果一项任务的外包成本低于你的个人时薪，那么就外包。</p><p data-pid=\"rKa0Zeae\">你要能在你所在的领域做到顶尖水平。如果你不能，那么你需要不断重新定义你的领域，直到你找到可以做到顶尖水平的领域。</p><p data-pid=\"Pb5jfmOk\">这个世界上没有一夜暴富的方法，如果你想找到一夜暴富的方法，那么你只会让别人从你身上赚钱致富。</p><p data-pid=\"VCrhCa0A\">运用你的独到知识，配合上杠杆（资本、人力、代码、媒体），最终你会得到你应该得到的东西。</p><p data-pid=\"8OiojGPm\">等到有一天你终于变得富有，你会发现财富并不是你起初想要追寻的东西。不过这都是后话了。</p><h2><b>二、如何提升判断力</b></h2><p data-pid=\"65KNIAX7\">如果你想要赚尽可能多的钱，并且最大程度的提高你这辈子赚到大钱的可能性，那么你需要走在趋势的最前端，学习科技、设计、和艺术，争取做到特别擅长某一个领域。</p><p data-pid=\"KlDqJkH8\">你前进的方向比你前进的速度更重要，特别是在有杠杆作用的情况下，因为杠杆能够成倍地放大你的判断力。（之前的这篇文章《<a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247485428%26idx%3D1%26sn%3D1236afbcb6b3cc6b7f412f8b54853f42%26chksm%3Dfdb448dacac3c1cc61fcf70f36ba7406ee800baeb31e51a5f5038e5db324851734657100e1fa%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">职业规划，你只需要做对这两件事</a>》里写到过Sam Altman的类似说法。）</p><p data-pid=\"HAiuoWeQ\">判断力需要经验，但是可以通过学习基本技能快速建立起来。并不存在一种叫做“商业”的能力，但是你可以通过学习微观经济学、博弈论、心理学、说服术、伦理学、数学和计算机科学建立起你的判断力。</p><h3>1. 做一个头脑清醒的思考者</h3><p data-pid=\"pIkyGgrT\">做一个头脑清醒的思考者意味着懂得事物的基本原理，能够用简单的语言解释复杂事物。</p><p data-pid=\"XVLcPzIm\">如果一个人讲话全是高深的概念和专业的词汇，那么八成他也不知道自己在说什么。你对事物的解释应该让小孩子都听得懂。如果你不能从最基本最底层的角度理解一个概念，那么你很可能只是在死记硬背。</p><p data-pid=\"3oJ2yIJo\">Naval 说自己从不浅尝辄止，了解新事物时总会问很多问题，从第一性原理思考，直到自己真正深入了解一个领域。</p><p data-pid=\"G0P0XWBq\">想要让自己更善于思考，可以从阅读数学、科学、以及哲学领域的经典著作开始。忽略当代作品和新闻。</p><p data-pid=\"773bxZv2\">这些领域的经典著作可能不那么好读，但是就跟你需要去健身房锻炼肌肉一样，你也需要通过阅读来训练你的阅读肌肉，你应该练习如何阅读这类书。</p><h3>2. 建立多元的思维模型</h3><p data-pid=\"sPmOTpZD\">Naval 推崇的有着多元思维模型的人有：查理·芒格、纳西姆·塔勒布、以及本杰明·富兰克林。（有意思的是，查理·芒格的榜样是本杰明·富兰克林。）</p><p data-pid=\"P23R_9FX\">书中提到的 Naval 的常用思维模型有：进化论、博弈论、经济学、复利效应、委托-代理问题（principal–agent problem）、复杂性理论（complexity theory）、基础数学、黑天鹅、微积分、可证伪性等等。感兴趣的朋友建议自行查询一下不熟悉的思维模型，以丰富自己的思维模型库。</p><p data-pid=\"7VBzTT2t\"><b>怎么建立多元思维模型？</b></p><p data-pid=\"wbxO9ugW\"><b>阅读，大量的阅读。</b></p><p data-pid=\"5kaOK81G\">如果你能爱上阅读，这将成为你的一个超能力。读得多了，你会自然成为一位鉴赏家。你会更倾向于读理论、概念和非虚构类读物。</p><p data-pid=\"Q_NsjlYB\">阅读一本书、一篇文章不会给你带来很大的改变，但是在复利效应的作用下，长期下来会给你带来巨大的改变。</p><p data-pid=\"J1TZrp-u\">Naval 从小热爱读书，平均每天读书1-2小时，他说这足以使他处于人群的前0.00001%。他自称自己所有物质上的成功，和他可能拥有的任何智慧，都归功于他的大量阅读。</p><p data-pid=\"nopoP6s7\">这跟他的两个榜样所提倡的十分相似——</p><p data-pid=\"TijjEyrR\">查理芒格曾说：“只要我手里有本书，我就不觉得我在浪费时间。”</p><p data-pid=\"ZATOFJJi\">富兰克林曾说：“读书是我让自己享受的唯一乐趣。我不在酒馆、赌场或任何游乐场合消磨时光。”</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-c9a1a399fa0f63b12fcc5348dbffbf1a_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"810\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-c9a1a399fa0f63b12fcc5348dbffbf1a_r.jpg\" data-original-token=\"v2-336f17736b637135f66d39f10ab81f89\"/><figcaption>Naval Ravikant</figcaption></figure><h2>三、如何过上幸福的生活</h2><p data-pid=\"-6E9rMng\">你的每一个欲望都是你给自己选择的一个不幸福的理由</p><p data-pid=\"z3zNlEaM\">人之所以不幸福是因为欲望，人们想要这个、想要那个，很少能够活在当下，也就无法去感受当下的美好。</p><p data-pid=\"2jVKrmLS\">幸福就是你感到平静、满足，不觉得生活中缺少什么。你的欲望越少，你就越能够接受你当下的状态，你的思绪就会越少的去想过去或者将来。</p><p data-pid=\"X7MRbnK-\">成功不会带来幸福。相反，成功和幸福往往是矛盾的。</p><p data-pid=\"nBqmOl8D\">幸福是对自己现状的满足，而成功往往来自于对现状的不满。那些想要取得成功的人，往往在取得了小的成功后，还想要取得更大的成功。他们对现状的不满是他们能够获得成功的动力，但这也就意味着他们常常感到不快乐。</p><p data-pid=\"fpm5xd6v\">怎样才能过的幸福？</p><h3>1. 降低欲望</h3><p data-pid=\"VJDRAZSG\">因为你的每一个欲望都是你给自己选择的一个不幸福的理由。</p><h3>2. 停止嫉妒</h3><p data-pid=\"fOz0bNOo\">嫉妒有毒。你的嫉妒不会对别人产生任何影响，你也不会因为嫉妒别人而变的更好，只会因为嫉妒变得更不快乐。所以，停止嫉妒。</p><h3>3. 培养习惯</h3><p data-pid=\"LSL2IWRI\">不饮酒、不吃糖、不刷社交网络，可以使你的情绪更稳定。</p><p data-pid=\"U0KHcZGA\">玩游戏让你获得短暂的快乐，长期来说对你有害。咖啡因也是一样。</p><p data-pid=\"1UgmUHHk\">每天锻炼身体可以让你感到快乐。</p><p data-pid=\"DGMQ_5mv\">减少对这三个app的使用频率：电话、日程表、闹钟。</p><p data-pid=\"5RzJuZO2\">减少内心乱七八糟的想法。</p><p data-pid=\"nklNRihT\">不去在意那些无关紧要的事情。</p><p data-pid=\"nmDq0Ykv\">和快乐的人交往。</p><p data-pid=\"Dan5VL9J\">阅读哲学，冥想。</p><p data-pid=\"0fnA9TMz\">珍惜自己的时间。</p><h3>3. 跳出游戏</h3><p data-pid=\"vZsmZ7RJ\">人们习惯于把人生过成一个多人游戏，总是去跟别人比较谁过的更好。然而事实是，人生是一场单机游戏，你独自来到这个世界，最后会独自离去。你的所有记忆、所有经历都是你自己一个人的。过了三代人以后，大概率没有人再记得你。</p><p data-pid=\"VklpzPqp\">那些跳出这场游戏、真正超脱的人，是 Naval 心目中最成功的人。</p><p data-pid=\"O0Vh34wj\">你终有一天会离开这个世界，所有的一切都将变得不再重要。所以，趁现在，享受生活，做一些积极的事，散播一些爱，给一些人带去快乐，多笑一笑，感恩当下这一刻。</p><h2>四、如何自救</h2><h3>1. 做你自己</h3><p data-pid=\"a-JwtXdx\">你的人生目标是找到这个世界上最需要你的人、生意、项目、或者艺术创作。这个世界上一定有一件事是为你准备的、最需要你的。</p><p data-pid=\"cFGv8aQo\">你最不应该做的就是看见别人在做什么事，就去给自己的 to-do list 加上一笔。你永远无法成为别人，你应该成为自己，没有人能比你更“成为你”。</p><h3>2. 关爱自己</h3><p data-pid=\"ofeynpks\">生活中最最重要的事，比快乐、家庭、事业都更重要的事，是自己的健康。</p><p data-pid=\"sxdicKES\">首先是身体的健康，其次是精神健康，然后是家人的健康。在这些之后，才是其他我们要在这个世界上做的事。</p><p data-pid=\"w4gNUn8L\">因为自己的健康是最重要的事，所以 Naval 每天起床后第一件事一定是健身。不管有什么大事，都可以等到锻炼半小时以后再说。</p><p data-pid=\"iDAw_90q\">听起来很困难是吗，的确不容易，但是——“Easy choice, hard life. Hard Choice, easy life.” （轻松的选择，艰难的生活。艰难的选择，轻松的生活。）</p><h3>3. 自我成长</h3><p data-pid=\"vn3VPE-c\">每个人都有令自己感到有激情的领域，你需要找到你的领域是什么。</p><p data-pid=\"EKfgSUZE\">每学习一个新领域，要掌握最根本的知识，把基础打结实。</p><p data-pid=\"lDQlLyxD\">Naval 认为对自己来说最重要的技能是：对阅读的热爱，数学思维，以及说服他人的能力。</p><p data-pid=\"5aUuX7JN\">阅读是知识和智慧的来源，数学思维无论在商业还是科学领域都很有用，说服他人的能力可以帮助你在这个社会上更游刃有余。</p><h3>4. 解放自己</h3><p data-pid=\"5PDeb9Bf\">Naval 说自己以前认为的自由是有自由去做想做的事，现在他所理解的自由是内在的自由，即选择不做什么的自由。</p><p data-pid=\"CWgF9Aw6\">不要在意他人的看法。重视你的时间，不要浪费时间，没有什么东西比你的时间更重要。</p><p data-pid=\"cEwhPhSs\">不要把时间用在为了让别人开心上。别人开不开心是别人的问题，不是你的问题。</p><p data-pid=\"M-PzDQr6\">做你自己想做的事。只要你在做自己想做的事，你就没有在浪费时间。但是如果你没有在做自己想做的事，你也没有在学习新东西，你到底在干嘛呢？</p><p data-pid=\"2gl7qLvi\">生活水平远低于自己收入水平的人拥有着忙于消费升级的人无法体会的自由。</p><p data-pid=\"icvohwqd\">一旦你能够掌握自己的命运，能决定自己想做什么、不做什么，你就不会愿意再让别人来告诉你该做什么。</p><p data-pid=\"QwIQFygd\">一旦你品尝过自由的滋味，你不会愿意再继续被他人雇佣。</p><h2><b>结语</b></h2><p data-pid=\"min4aN_1\">总结下来，Naval 给人们的建议是：</p><p data-pid=\"VoH0e3Dx\">要想富有，你得用独到知识，责任，和杠杆来装备自己。</p><p data-pid=\"H1VEzZbU\">要想赚尽可能多的钱，方向比速度更重要，为了找准方向，你需要提升你的判断力。</p><p data-pid=\"CTqwRgxy\">即使真的富有了，你也不会因此而更幸福。要想过的幸福，你需要降低欲望、停止嫉妒、培养好习惯，或者干脆跳出游戏，不去和他人进行无意义的竞争。</p><p data-pid=\"_yetCv8Z\">你独自来到这个世上，最后也会独自离去。所以，照顾好你的健康，做你自己，找到这个世界最需要你的那个位置，去发光发热。</p><p data-pid=\"W_frZthh\">不要在意他人的看法，不要浪费时间，不要被外物牵绊，努力去掌握自己的命运，获得真正的自由。</p><p data-pid=\"keUfdutq\">读完你有什么想法呢？欢迎留言与大家交流。</p><p data-pid=\"E81hJ7Bz\"><b>参考资料</b></p><ul><li data-pid=\"t-ThU-on\"><a href=\"https://link.zhihu.com/?target=https%3A//book.douban.com/subject/35219944/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">The Almanack of Naval Ravikant</a></li><li data-pid=\"obz_2-DY\"><a href=\"https://link.zhihu.com/?target=https%3A//tim.blog/2015/08/18/the-evolutionary-angel-naval-ravikant/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Naval Ravikant — The Person I Call Most for Startup Advice</a></li><li data-pid=\"pA3zxdfu\"><a href=\"https://link.zhihu.com/?target=https%3A//www.fx896.com/a/10120210728002501275890\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">硅谷著名的投资人 | 如何不依靠运气变得富有？</a></li><li data-pid=\"UD5VNiLj\"><a href=\"https://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s/TfhBCbr8-IoHyPKtB3hTlw\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Naval：如何不靠运气致富</a></li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"VbqKogt6\">我是Kelly，全网同名“凯莉彭”自媒体账号主理人。前硅谷资深数据科学家，曾就职于Airbnb等公司。目前是全职自媒体人，关注创业和个人成长，每周末卫星视pin号直播访谈一位不走寻常路的创业者，欢迎来看我哦～</p>", "excerpt": "“Easy choice, hard life. Hard Choice, easy life.” 关于Naval Ravikant出生于印度，幼时移居美国，父母离异，和弟弟一起随母亲在纽约布鲁克林的黑人聚集区长大。今天要写的这位创业者/投资人，不像那些有家族从商传统、家境优越的创业者，他的起跑线远不如别人那么亮丽。 在小的时候，他只能隔着窗玻璃羡慕别人的漂亮房子和幸福生活，希望有一天他也能实现自己的美国梦。 好在他还算聪明，凭着不错的成绩考进了不错的高中，…", "excerpt_new": "“Easy choice, hard life. Hard Choice, easy life.” 关于Naval Ravikant出生于印度，幼时移居美国，父母离异，和弟弟一起随母亲在纽约布鲁克林的黑人聚集区长大。今天要写的这位创业者/投资人，不像那些有家族从商传统、家境优越的创业者，他的起跑线远不如别人那么亮丽。 在小的时候，他只能隔着窗玻璃羡慕别人的漂亮房子和幸福生活，希望有一天他也能实现自己的美国梦。 好在他还算聪明，凭着不错的成绩考进了不错的高中，…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/445589956", "comment_permission": "all", "voteup_count": 0, "comment_count": 0, "image_url": "https://pic1.zhimg.com/v2-6cfd5089062c1d387abd2e8194202d81_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1639542061, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1631763776359", "type": "feed", "target": {"id": "410942566", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1631763775, "updated": 1653200187, "title": "放弃Airbnb大厂高薪，我选择做职业导师", "excerpt_title": "", "content": "<p data-pid=\"kz86SzC4\">我和Emma认识于2020年8月。当时她从同事微信群里找到我，说自己想做一个帮助被裁员的人找工作的网站，而正好我之前写过一篇关于疫情期间裁员后再就业的文章，她说想跟我电话聊聊。</p><p data-pid=\"ziqFlae8\">现在回头看，我的回应有些冷漠。我说我之所以写文章，就是为了不重复回答相同的问题，由于我前不久在另一个活动中分享了疫情期间的求职经验，我直接把那次活动的视频链接发给了她。</p><p data-pid=\"EAgFFhGO\">还好她没有因此把我拉黑。后来我们一直保持着联系，我对她的了解也逐渐增多。</p><p data-pid=\"Af_1zWCv\">2020年9月时，可能当时她打算尝试做YouTube，就问我有没有想过做YouTube。我当时只做短视频，说感觉做长视频太费时间（后来我的确尝试了几次，实在是太费时间了）。</p><p data-pid=\"xX9P1Nhk\">这次聊天中，她提到希望自己在2021年可以辞职，虽然她似乎刚在Airbnb内部从Data Scientist转到Software Engineer不久。</p><p data-pid=\"72nU2JQg\">我当然会问，为什么要辞职？她说，<b>并不是工作不好，而是她不太能从工作中看到意义</b>——写一行代码，和写一百行代码，有什么本质区别呢？也没有对别人造成什么影响啊。</p><p data-pid=\"1FQ65hp6\">相较之下，给别人提供职业上的指导帮助，即使一开始是免费，即使做这件事不容易规模化，但是<b>能够实实在在的给另一个人带去积极影响，她觉得这样的人生会比较有意义</b>。</p><p data-pid=\"xC5HFAVO\">这可能是她和我最大的不同。我以前写过几篇关于数据科学的Medium博客，虽然也是帮助了别人，但更多的是为了一次性解答尽可能多的人的问题，避免自己将来重复回答相同的问题。</p><p data-pid=\"BVYYtrC0\">而Emma，她是发自内心的愿意帮助别人、给别人提供职业咨询，即使这意味着她要花费大量时间去跟陌生人进行一对一的谈话，甚至偶尔还会遇到让人愤懑的奇葩。</p><p data-pid=\"pxPwhDtp\">后来，她发了第一篇关于数据科学的Medium博客，也开始做YouTube视频。<b>每一条视频，她都会花6-8小时写文稿，2个小时录制视频，4-6小时后期剪辑，这一切，都是在下班之后的时间完成。</b></p><p data-pid=\"JFLh0Glt\">再后来，她发了很多篇数据科学内容的博客，以及很多条数据科学内容的YouTube视频，每一条都干货满满，看得出来，制作相当用心。</p><p data-pid=\"00HzPTaf\">渐渐的，我的同行朋友会把她的视频发给我，问我认不认识这位博主；有人问我面试相关的问题，我会直接把她的视频发过去，人家跟我说：&#34;Emma的视频我都看过了，内容很棒。&#34;</p><p data-pid=\"C1uIewwU\">这期间，我有试图介绍她和我身边想要找人开课的朋友合作，她都委婉拒绝了。她说暂时不考虑开课，要先踏实把内容做好。</p><p data-pid=\"C_4iYnYV\">后来，我们就继续各忙各的。她继续一边工作，一边做视频、写文章，一边给求职者提供免费咨询。我也继续一边工作，一边瞎折腾。</p><p data-pid=\"trh8X5tp\">直到今年7月份，她突然告诉我，她辞职了！</p><p data-pid=\"IrNW3UEy\">我一下子来了精神——这么给力，去年说今年辞职，这么快就说到做到了！</p><p data-pid=\"pAl_bprc\">她说自己在今年6月离开了Airbnb，开始全职做自己的事情。她已经建好了网站（<a href=\"https://link.zhihu.com/?target=http%3A//www.datainterviewpro.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">www.datainterviewpro.com</a>），发布了付费的服务项目。</p><p data-pid=\"7nYyRmJO\"><b>由于前期有充足的铺垫，积累了良好的口碑，报名她的付费服务项目的人很多，让她有些忙不过来。现在的她，每天比之前在Airbnb上班还要忙。</b></p><p data-pid=\"ub4QSTeY\">前两周和另一个同事一起，我们仨来了个视频通话。在视频里看到她的那一刹——哇，她的气色太好了，虽然当时已是晚上八九点，虽然她说她忙了一天有点累，但是我能感觉到她由内而外的快乐。</p><p data-pid=\"O2K_CblY\">是啊，<b>还有什么比做自己真正想做的事更快乐呢？还有什么比看到自己做的事有意义更快乐呢？</b></p><p data-pid=\"8zDskh38\">她调侃说，自己一直都是十分任性的性格，总是想干嘛就去干嘛了——之前学过五个专业，多次转行，现在又是说辞职就辞职了。</p><p data-pid=\"0LOXc5ej\">虽然辞职意味着收入暂时归零，还意味着放弃Airbnb的还没有vesting的大量股票，但她觉得这些都不重要——<b>“Airbnb的股票涨了，和我做的事有多大关系呢？”</b></p><p data-pid=\"g3g8pMeS\">我觉得这不是任性，是大胆尝试、勇于取舍、牢牢掌握自己的人生。</p><p data-pid=\"9d3ogdnm\">我记得之前看到过这样一句话：<b>“每当我发现我在对自己说‘可是我都因此赚了那么多钱了’的时候，这都是一种警示，表明我正在做错误的事。”</b>单纯为了金钱和所谓的稳定而主动被一份工作绑架，才是人生可悲的事。</p><p data-pid=\"vloVDI3m\">人们总是要通过不断的尝试而越来越了解自己真正想做什么，如果那件想做的事不是当下的工作，那么<b>时间比高薪宝贵。</b></p><p data-pid=\"BW7bx8Ng\">Emma找到了她想走的路，我很佩服她在找到了这条路之后，一直坚定的向前迈步着。我期待看到她在数据科学领域影响和帮助到更多的人，相信她的未来会更加精彩。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-ff3d99157869714a58b61f3ec0a66550_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"604\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-ff3d99157869714a58b61f3ec0a66550_r.jpg\" data-original-token=\"v2-0f0b15050500e03c8017296d9f9b4e86\"/><figcaption>图片来自Emma的网站：www.datainterviewpro.com</figcaption></figure><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"kAuF4SpZ\">我还问了她几个问题，也许对其他人会有启发，下面以Q&amp;A的形式写出来：</p><p data-pid=\"zpPCRf7-\"><b>Q:你是在内容做到什么阶段决定辞职的？辞职前靠副业赚钱了吗？</b></p><p data-pid=\"SVw-Bqpr\">A: 我辞职前一直是免费提供咨询，没有赚一分钱。有的人说要等副业赚钱赚到多少以后才辞职，但是我发现辞职以后开始全心投入做自己的事，进展速度飞快，远远超过我的想象。我觉得我做事就得要all in，要做就投入全身心去做，不然进展慢而且更累。这可能是我的个人风格吧。</p><p data-pid=\"QpqehfjH\"><b>Q: 现在每天的日程安排是什么样的？</b></p><p data-pid=\"tTZbWB-8\">A:现在我每天从早上9点工作到晚上10点，比之前上班辛苦很多。相似的是一天到晚在电脑前，跟我的员工（现在算是有两个兼职的员工）开会，跟客户开会。就时间安排上来说，每天60%时间在内容创作上，40%时间服务客户以及处理一些其他的事情。</p><p data-pid=\"lVog2ctZ\"><b>Q: 数据科学领域的内容创作打算做多长时间、做到什么程度，有给自己定目标吗？</b></p><p data-pid=\"rI5Kgshh\">A: 打算先把所有的课程做出来，做到自己满意的地步，然后把内容拓展到机器学习和数据工程领域的内容，服务除了程序员岗位以外的技术岗位的面试。</p><p data-pid=\"1pLj4kYN\"><b>Q: 对于有辞职创业打算的人，你有什么建议？</b></p><p data-pid=\"UPIgJmkD\">A: <b>一定要接受一定程度的教育，向成功的人学习。</b>现在网上有很多的课程，教创业的方方面面，可以去学习一下。不要自己去摸索，因为时间成本不值得。（p.s. 在对话中，Emma提到自己自2017年以来就在探索创业。她找过商业教练，也学过不少课程。可以想象她在个人创业方面下足了功夫，毕竟没有随随便便的成功。）</p><p data-pid=\"sZc304mi\"><b>Q: 是否有一句名言指引着你的人生？</b></p><p data-pid=\"4fgmGHi-\">A: 我特别相信<b>“Believe in yourself blindly”</b>。是在Tim Ferris的一本书里看到的，但是具体忘了是谁说的了。自信可以带来很多正面的东西，而且自信只能靠自己给自己 ，别人再怀疑你、打压你，只要你有足够的自信都可以坚持下来。</p><p data-pid=\"AFe5MIQj\"><b>Q: 对想要向你学习的人，你会推荐什么书，或者其他资料？</b></p><p data-pid=\"Qum_SBLi\">A: 学习创业的话，我觉得要<b>先找到适合自己的领域比较重要</b>。我觉得通用的建议没有什么意义，因为现在线上赚钱途径太多了 ——有人写书也很成功，有人做电商很成功，等等。每个途径需要的能力都不太一样，不要看别人赚钱就去模仿，找到适合自己的方式比较重要，比如，如果你不喜欢露脸，那就不要去做视频。等你找到适合自己的商业模型（或者至少是你感兴趣去尝试的商业模型）之后，去找这个领域做的比较好的人学习。你可以看他们的书，或者购买他们的课程，或者找个教练 （我找过商业教练&#34;business coach&#34;）。</p><p data-pid=\"wgvZLCLT\">另外，刚起步的时候，并不需要向业界最牛的人学习，因为你跟业界最牛差距太远，你能找到一个在你前面几步的就可以了，比如你一年赚6位数，你可以先像7位数8位数的学习，你一下学习10位数的大牛，可能并不能理解人家的战略。等你往前走了几步之后，再去追求更高的目标。</p><p data-pid=\"ozqHiMrI\">咦，你这个问题问的是学习创业，还是学习面试？后者的话，当然是我的YouTube频道啦，哈哈！</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"TG5QAifj\">最后，向大家隆重推荐Emma的YouTube频道（虽然我感觉我在美国的同行可能大部分都看过她的视频了）——<b>全网干货最多、质量高又易于理解的数据科学面试知识视频：Data Interview Pro</b>，记得关注订阅哦～</p><hr/><p data-pid=\"CmCjJg7r\">你可能还喜欢：</p><ul><li data-pid=\"avIIompj\"><a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247486300%26idx%3D1%26sn%3D0a43fd0cf05c355696dc572109e106de%26chksm%3Dfdb44472cac3cd64441ce2b18bb0fb67f7fa4fac0fa4db8d8f4f14ffeb8f6cf925901e6f0165%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">“我度过了非常精彩的一生”</a></li><li data-pid=\"HYfdRDBr\"><a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247485683%26idx%3D1%26sn%3D45d8d56b0033d6cfcd57f556769b6bba%26chksm%3Dfdb447ddcac3cecb60fc686c5f8dc727edd8a4d891c4730eb7c47f31f9f95f0f9b890c83d862%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">从一位创业者朋友身上看到的</a></li><li data-pid=\"edRVoYol\"><a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247485428%26idx%3D1%26sn%3D1236afbcb6b3cc6b7f412f8b54853f42%26chksm%3Dfdb448dacac3c1cc61fcf70f36ba7406ee800baeb31e51a5f5038e5db324851734657100e1fa%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">职业规划，你只需要做对这两件事</a></li><li data-pid=\"4wsl1Y8C\"><a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247484876%26idx%3D1%26sn%3D2b2cd45508ba3be725ac9a105098679d%26chksm%3Dfdb44ae2cac3c3f4a83f239a5c1e097c87b5e952c1d0ea2ff8033f5133188c1d0da8e61c775e%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">30岁，好戏刚刚开始</a></li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p></p><p></p>", "excerpt": "我和Emma认识于2020年8月。当时她从同事微信群里找到我，说自己想做一个帮助被裁员的人找工作的网站，而正好我之前写过一篇关于疫情期间裁员后再就业的文章，她说想跟我电话聊聊。 现在回头看，我的回应有些冷漠。我说我之所以写文章，就是为了不重复回答相同的问题，由于我前不久在另一个活动中分享了疫情期间的求职经验，我直接把那次活动的视频链接发给了她。 还好她没有因此把我拉黑。后来我们一直保持着联系，我对她的了解…", "excerpt_new": "我和Emma认识于2020年8月。当时她从同事微信群里找到我，说自己想做一个帮助被裁员的人找工作的网站，而正好我之前写过一篇关于疫情期间裁员后再就业的文章，她说想跟我电话聊聊。 现在回头看，我的回应有些冷漠。我说我之所以写文章，就是为了不重复回答相同的问题，由于我前不久在另一个活动中分享了疫情期间的求职经验，我直接把那次活动的视频链接发给了她。 还好她没有因此把我拉黑。后来我们一直保持着联系，我对她的了解…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/410942566", "comment_permission": "all", "voteup_count": 0, "comment_count": 0, "image_url": "https://picx.zhimg.com/v2-526e0067ac102dca7bc6ab1b08217be1_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1631763776, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1626739928357", "type": "feed", "target": {"id": "391281372", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1626739927, "updated": 1653200135, "title": "Telegram创始人：最“帅”的理想主义创业者", "excerpt_title": "", "content": "<h2>01 “俄罗斯的<PERSON>”</h2><p data-pid=\"h7TruXs2\">2020年，特朗普政府传出禁微信的行政禁令（后来并没有真正发生），很多在美国的华人纷纷在朋友圈发布自己的其他联系方式，其中一个是telegram。</p><p data-pid=\"sSUFunYk\">telegram是一个端对端加密的通讯软件，创办于2013年8月。截止2021年1月，该软件月活达到5千万。国内的朋友不用去应用商店找了，在我国它是被禁的。</p><p data-pid=\"bf_Bvziq\"><b>该软件的创始人是一位明明可以靠脸吃饭，却偏要靠才华的人。</b>接下来直接上图：</p><p data-pid=\"Nl5t3EAJ\">（友情提示：请注意控制目光停留在照片上的时间，以避免口水掉落。）</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-5715770a636caf2175b483d1b2d28d10_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1347\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-5715770a636caf2175b483d1b2d28d10_r.jpg\" data-original-token=\"v2-56a887a3767b26b08c29beff53837cbe\"/></figure><figure data-size=\"normal\"><img src=\"https://picx.zhimg.com/v2-633042da0cd818e5f683b96a693dacc3_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1068\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://picx.zhimg.com/v2-633042da0cd818e5f683b96a693dacc3_r.jpg\" data-original-token=\"v2-87dcdab4b77dbd925f72a1268d277075\"/></figure><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-a2055448d16f8bc49b3aeae5a86713ae_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1068\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-a2055448d16f8bc49b3aeae5a86713ae_r.jpg\" data-original-token=\"v2-e0bfa44ac3ae57ae395f13279eab824e\"/></figure><p data-pid=\"UBzgsk-Z\">telegram的创始人是Pavel Durov，而telegram并不是Pavel Durov做的第一个产品。他在此之前的一家公司VKontakte（简称VK），被称为俄语世界的Facebook，Pavel Durov也因此被称为“俄罗斯的Mark Zuckerberg（Facebook创始人）”。</p><p data-pid=\"g_Bay1LZ\">虽然称他为“俄罗斯的Mark Zuckerberg”能一下子让你明白他的主营业务，但是<b>Pavel Durov的支持者却认为拿Mark Zuckerberg和Pavel Durov相提并论，有些羞辱Pavel Durov。</b></p><p data-pid=\"2CvhSiW8\">要想明白其中的原因，我们得先了解Pavel Durov的故事。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-6d58fb8da172188b547f619920f41baa_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"554\" data-rawheight=\"242\" class=\"origin_image zh-lightbox-thumb\" width=\"554\" data-original=\"https://pic1.zhimg.com/v2-6d58fb8da172188b547f619920f41baa_r.jpg\" data-original-token=\"v2-063c686b65c242edc7848e6b55f745a8\"/><figcaption>介绍Pavel Durov的视频下的热门评论</figcaption></figure><h2>02 若为自由故，一切皆可抛</h2><p data-pid=\"r3Q_7qOB\">Pavel Durov，1984年10月10日出生于俄罗斯圣彼得堡。由于父亲在意大利工作， 他从小在意大利长大，到了上大学的年纪时，他回到俄罗斯，就读圣彼得堡国立大学语言学院。2006年，他从大学毕业，一毕业便和哥哥Nikolai Durov一起创办了VK。</p><p data-pid=\"fGdghtfr\">VK的页面设计跟Facebook十分相似，市场推广也同样是从俄罗斯几所著名大学开始的。Pavel Durov承认VK的设计是借鉴Facebook的，毕竟咱不是设计师啊，全都要重新设计的话得多花多长时间。</p><p data-pid=\"_b57NNda\"><b>虽然两个人一开始做的事相似，他们后来的轨道却截然相反。</b></p><p data-pid=\"YdxoTHql\">Pavel Durov通过VK的成功小赚了几十亿，但他似乎不太把钱当回事。</p><p data-pid=\"nKzCz-1c\">他和VK一位副总裁从办公室窗户往楼下扔用钱叠的纸飞机的视频，曾一度登上世界媒体头条。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-50bb80b394724257cdbf08e6db394c8c_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"630\" data-rawheight=\"474\" class=\"origin_image zh-lightbox-thumb\" width=\"630\" data-original=\"https://pic1.zhimg.com/v2-50bb80b394724257cdbf08e6db394c8c_r.jpg\" data-original-token=\"v2-e00d858179ad3b7afc187f53eb2dff4f\"/><figcaption>Pavel Durov和VK副总裁从办公室往楼下扔钱折成的飞机</figcaption></figure><p data-pid=\"H1FwokIx\">他不仅不怎么把钱当回事，还不怎么把国家领导人当回事。这直接导致了他得罪国家领导人，后来不得不放弃自己创建的公司，离开俄罗斯，成为“世界公民”。</p><p data-pid=\"vrokeywu\">2011年，时任俄罗斯总理的普京明确表示：明年我来当总统，现任总统梅德韦杰夫来当总理，大家意向如何？</p><p data-pid=\"mma-LI8o\">于是，反对普京的人开始抗议。2011年底至2012年初，俄罗斯爆发了一系列抗议活动，社交网站成了抗议者的集结地，各种讽刺普京的漫画在VK上流传，各种谣言和阴谋论也冒了出来。</p><p data-pid=\"h1k8y56B\"><b>俄罗斯政府要求Pavel Durov查封普京反对者的VK账号，Durov的回复却是一张吐着舌头的小狗</b>，并生怕对方看不懂，补充了一句：“这就是我的答复。”</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-1004420554880fce48f86ed4cfd07e96_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1857\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-1004420554880fce48f86ed4cfd07e96_r.jpg\" data-original-token=\"v2-5bb66c22eb2e3a3bd68951c25d121e5c\"/><figcaption>Pavel Durov对俄罗斯政府要求封号的回复</figcaption></figure><p data-pid=\"aO1yID6H\">随后，他不可避免的陷入麻烦之中。</p><p data-pid=\"qsdqXYXV\">他先是被指控一辆名下的白色奔驰交通事故肇事逃逸，尽管他从没碰过那辆车，他甚至压根不会开车。</p><p data-pid=\"QfMXbJNk\">他还渐渐失去了对VK的控制权。亲普京的投资者们不断购买VK的股份，直到最后，Pavel Durov和哥哥Nikolai Durov成了董事会里的孤家寡人，被迫卖出股份，离开了他们一手创办的公司。</p><p data-pid=\"sU6rNhdp\">有一天晚上，一支特警队突袭了他的公寓。他当时给哥哥打了一通电话，电话接通的瞬间，他突然想到这通电话一定在被监听，是不安全的。</p><p data-pid=\"TuitgqyN\">随后他意识到，现有的通讯方式没有任何一种可以保证他想要的隐私和安全——它们要么是有技术的缺陷，要么是工具的运营者会配合泄露用户隐私，导致这些工具毫无安全可言。</p><p data-pid=\"d3IVHYlX\"><b>“既然没有一个能提供隐私和安全的通讯工具，那就自己做一个好了。”</b></p><p data-pid=\"mayptsi1\">就这样，Pavel Durov和哥哥Nikolai Durov再一次合伙，要做一个端到端加密的通讯工具。</p><p data-pid=\"G-qmGIAl\">所谓“端到端”加密，就好比是你寄出一个加了锁的信件，对方收到之后可以用唯一的钥匙打开，中间没有任何人能偷看内容。</p><p data-pid=\"Ol9FDBoA\">端到端加密的关键在于加密通讯协议，也就是“那把锁”的牢固程度。</p><p data-pid=\"goLj7Ax4\">Pavel Durov的哥哥Nikolai Durov，带头研发了这个加密通讯协议。</p><p data-pid=\"cC1V0aJy\">这里需要多说几句Pavel Durov的哥哥。‍‍‍‍‍‍Nikolai Durov是VK的技术团队的老大，一位天才程序员、数学家，曾经3次参加国际奥数比赛，拿回三块金牌，又4次参加ACM竞赛（全球大学生计算机程序能力竞赛中最有影响力的赛事），砍下一金三银。</p><p data-pid=\"KmQnEhZk\">2013年8月，一个名叫Telegram的通讯工具正式上线。</p><h2>03 成为世界公民</h2><p data-pid=\"t7dD8FZy\">为了避免政府的干涉，在创建telegram的时候，Pavel Durov和团队已经离开俄罗斯。他们在分别在美国和英国秘密注册了公司，在美国纽约州水牛城（Buffalo）租了一个办公场地。</p><p data-pid=\"05JX9H3v\">但是来到美国后，他也并没有获得他想象中的自由——一周之内，美国政府机构就拜访了他们两次，先是利诱，然后威胁，企图收买他们。</p><p data-pid=\"oS5cISBo\">每次入境，Pavel Durov都会被扣下来，接受FBI特工的询问。2016年5月，Pavel Durov从欧洲飞往旧金山，参加谷歌年度开发者大会，早上八点，FBI特工竟突然出现他通过Airbnb租住的房子里。</p><p data-pid=\"m8EuQxor\">不堪美国特工们的骚扰，Pavel Durov带着团队离开了美国，满世界跑，隔三差五换个地点办公。所以人们会在他的instagram上看到他在不同国家工作的照片，像是一位旅行博主。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-2670af423bc8e3c741f1482ad4ddd1f8_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1497\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-2670af423bc8e3c741f1482ad4ddd1f8_r.jpg\" data-original-token=\"v2-a00f9f5aa707d7033b789b330fa99554\"/><figcaption>Pavel Durov在意大利罗马工作</figcaption></figure><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-8eefbb56b138584b054b4eba71eee47e_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1713\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-8eefbb56b138584b054b4eba71eee47e_r.jpg\" data-original-token=\"v2-4a7a5fae5b827f1129151e05b35b9c01\"/><figcaption>Pavel Durov在法国巴黎工作</figcaption></figure><h2>04 手握千万“流量”却不“变现”——“我对钱不感兴趣”</h2><p data-pid=\"BeEZ2WS1\">Telegram团队总共约15人，其中至少有6个人得过ACM竞赛大奖。又如前文所说，telegram于2013年创立，2021年初月活达到5千万。</p><p data-pid=\"TQ81Blyk\"><b>和大多数企业家不一样的是，虽然拥有强大的团队和庞大的“流量”，Pavel Durov似乎并不打算通过Telegram发财。</b></p><p data-pid=\"BEfRzqm-\">大概是从VK的经历中吃一堑长一智，Pavel Durov宣布telegram<b>永远不会出售，拒绝任何投资。</b>尽管每个月有上百万美元的服务器开销，但telegram<b>从不推送广告，维持经营主要靠捐助。</b></p><p data-pid=\"ioby2z1a\">当然，最主要的捐助者是Pavel Durov本人。他摸摸自己兜里的几亿美元，够撑很长一段时间了。</p><p data-pid=\"pPHGZXpO\">Pavel Durov还表示，telegram绝对不会对用户收钱，如果未来有别的收入，能维持盈亏平衡就行。</p><p data-pid=\"8Ab5258W\"><b>对于以赚钱、增长和赚更多钱为目标的人来说，这是超出他们认知范围的疯话。</b></p><p data-pid=\"4dtSLm8R\">Pavel Durov说，当他变得有钱时，他接触到很多其他有钱人。他看到那些人的豪宅和游艇，意识到这并不是他自己想要的生活。<b>对他来说，钱，是他在改变世界过程中的副产品和资源。而改变世界这件事，比赚钱有意思的多。</b></p><p data-pid=\"KHy_pUy-\">由于强调隐私和安全，拒绝与政府合作，telegram受到不少批评，认为它在为恐怖组织提供帮助。</p><p data-pid=\"6bnJBeaB\">恐怖组织会用Telegram的加密聊天功能来策划袭击，并且利用群聊功能来为自己做宣传，招募独狼式恐怖分子。</p><p data-pid=\"gx4xI1eK\">Pavel Durov认为，技术本身是中性的，好人利用技术做好事，坏人利用技术做坏事，这是由人的本性决定的。相对于恐怖主义这类坏事，他认为保护隐私更为重要，因为即使没有telegram，恐怖组织也总会找到其他方法来交流。</p><h2>05 这世界需要更多的理想主义者</h2><p data-pid=\"Yd2K6WHj\">我自己用了telegram两年左右，之前只知道使用起来流畅简洁，这几天才了解到创始人的故事，Pavel Durov的故事（和颜值）很难不给人留下深刻印象——</p><p data-pid=\"fPq9Ilvn\">不太把钱当回事，认为钱只是改变世界过程中的副产品和资源。</p><p data-pid=\"yt2ndPkW\">没有在强权面前妥协，始终坚持自己的信念。</p><p data-pid=\"BajAxd2J\">遇到突袭和监听，不是抱怨，而是找到了下一个要解决的大问题——用户隐私和安全问题。</p><p data-pid=\"EH_G3gY3\">被迫离开自己亲手创立的第一家公司，离开祖国，却因此建立起一家更伟大的公司，为千万用户提供有隐私保障的通讯服务。</p><p data-pid=\"qUecUCds\">……</p><p data-pid=\"7PUBSPci\">似乎为人们所钦佩的创业者都有些理想主义，能让员工心甘情愿卖命工作的企业往往有着伟大的使命。</p><p data-pid=\"IEBFtiid\">因为金钱能够产生的激励是有限的，人们不会仅因为低价的产品或者高工资而忠于你——市场上总会出现更便宜的产品、更高的薪资待遇。</p><p data-pid=\"WJcMFitn\">而<b>那个“为什么要做这件事”的使命，是黑暗中的灯塔，是超越金钱的意义，是人们追随、支持你的原因。</b></p><p data-pid=\"EBo-HQ52\">那么，<b>如果可以不为“搞钱”而奔命，你的使命是什么？</b></p><p class=\"ztext-empty-paragraph\"><br/></p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"SK_2IukV\">参考资料：</p><ul><li data-pid=\"abPxu7z6\"><a href=\"https://link.zhihu.com/?target=https%3A//www.tmtpost.com/1443098.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">《Telegram传奇：一个关于俄罗斯富豪、黑客、极权和阴谋的创业故事》</a></li><li data-pid=\"PEkA2jAe\"><a href=\"https://zhuanlan.zhihu.com/p/151425076\" class=\"internal\" target=\"_blank\">《自由与失控：Telegram创始人和他的“黑客帝国”》</a></li><li data-pid=\"foZ9CpEX\">维基百科：保羅·杜洛夫</li><li data-pid=\"T24_GBSm\"><a href=\"https://link.zhihu.com/?target=https%3A//www.cnbctv18.com/technology/meet-telegram-founder-pavel-durov-the-zuckerberg-of-russia-7998191.htm\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Meet Telegram founder Pavel Durov, the &#39;Zuckerberg of Russia&#39;</a></li></ul><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"mVyeC_7f\"><b>往期推荐：</b></p><ul><li data-pid=\"jKCDSKL1\"><a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247485799%26idx%3D1%26sn%3D322093d8e237c27d9277d474b8866a34%26chksm%3Dfdb44649cac3cf5f9c1e7a12017290b600f7bbe609d557f6e1ac32e1504e03cc3506198cbbb5%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">34岁怀孕期间创业，我在生孩子那天拿到了融资</a></li><li data-pid=\"jneU17VY\"><a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247486180%26idx%3D1%26sn%3D942ff9fe2b9a3d9656664145b5ea407b%26chksm%3Dfdb445cacac3ccdc3e11132f05f9d55777dd6eaecc5a60b526aab91c5b758ea4344b15ce860a%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">从传真机销售到最年轻女亿万富豪</a></li><li data-pid=\"mNy1HBGv\"><a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247485725%26idx%3D1%26sn%3D566ca636b90b68cd2a17498abc885b02%26chksm%3Dfdb44633cac3cf25ff349a9657f45e92ed6aa4aae347c64bf1a95157f0b160ee8042a7a50d48%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">残次蔬果不好卖？有家公司靠这个生意成为独角兽</a></li><li data-pid=\"ts1qBhdJ\"><a href=\"https://link.zhihu.com/?target=http%3A//mp.weixin.qq.com/s%3F__biz%3DMzU4MjY1NDUyOQ%3D%3D%26mid%3D2247486149%26idx%3D1%26sn%3D6224feba849b4404b90c22b9448a66b6%26chksm%3Dfdb445ebcac3ccfd88cab255d7137e11e6d9b620ecd5655d9f3d716c07f784bd8b122763ced2%26scene%3D21%23wechat_redirect\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">百万富翁快车道</a></li></ul><hr/><p data-pid=\"JdQ0guEL\">我是Kelly，全网同名“凯莉彭”自媒体账号主理人。前硅谷资深数据科学家，曾就职于Airbnb等公司。目前是全职自媒体人，关注创业和个人成长，每周末卫星视pin号直播访谈一位不走寻常路的创业者，欢迎来看我哦～</p>", "excerpt": "01 “俄罗斯的Mark Zuckberg”2020年，特朗普政府传出禁微信的行政禁令（后来并没有真正发生），很多在美国的华人纷纷在朋友圈发布自己的其他联系方式，其中一个是telegram。 telegram是一个端对端加密的通讯软件，创办于2013年8月。截止2021年1月，该软件月活达到5千万。国内的朋友不用去应用商店找了，在我国它是被禁的。 <b>该软件的创始人是一位明明可以靠脸吃饭，却偏要靠才华的人。</b>接下来直接上图：（友情提示：请注意控制目…", "excerpt_new": "01 “俄罗斯的Mark Zuckberg”2020年，特朗普政府传出禁微信的行政禁令（后来并没有真正发生），很多在美国的华人纷纷在朋友圈发布自己的其他联系方式，其中一个是telegram。 telegram是一个端对端加密的通讯软件，创办于2013年8月。截止2021年1月，该软件月活达到5千万。国内的朋友不用去应用商店找了，在我国它是被禁的。 <b>该软件的创始人是一位明明可以靠脸吃饭，却偏要靠才华的人。</b>接下来直接上图：（友情提示：请注意控制目…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/391281372", "comment_permission": "all", "voteup_count": 21, "comment_count": 3, "image_url": "https://pic1.zhimg.com/v2-67ed574a88477f3b5af2ad8ef314c5fc_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1626739928, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1623215018504", "type": "feed", "target": {"id": "379164822", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1623215017, "updated": 1653200153, "title": "残次蔬果不好卖？有公司靠这门生意成为独角兽", "excerpt_title": "", "content": "<p data-pid=\"Kg7HbW8H\">你有没有过这样的经历——你到农田去采摘水果，你会去摘树上的果子，你也许曾注意到，不少成熟的果子掉到了地上，外表有些损坏，面对地上那些水果，你是感叹食物被浪费很可惜呢，还是想到这个问题可能是机会呢？</p><p data-pid=\"3r9nn9h0\">Misfits Market，就是一家解决残次蔬果被浪费问题的公司。</p><p data-pid=\"U87veMXW\">Misfits Market公司的创始人Abhi Ramesh的顿悟瞬间，是2018年初他和女友在美国费城附近农田摘苹果的观察。他注意到很多苹果掉在地上，不会被前来摘苹果的顾客买走。于是他问了农场经营者一些问题——“掉在地上的水果这么多，你怎么处理他们？”，“这些水果会怎么卖、卖给谁？“……</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-1af202f939771230fe74cb20fa59fb4d_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2324\" data-rawheight=\"1085\" class=\"origin_image zh-lightbox-thumb\" width=\"2324\" data-original=\"https://pic2.zhimg.com/v2-1af202f939771230fe74cb20fa59fb4d_r.jpg\" data-original-token=\"v2-305aeb05d094e7969037671af35aefa9\"/><figcaption>图片来源于网络</figcaption></figure><p data-pid=\"EJ5U6jkT\">农场经营者告诉他，这些掉在地上的苹果是二级或者三级的苹果，没办法出售给超市，于是农场会将它们低价出售给周围的邻居，如果周围邻居买不完的话，他们只好把剩下的扔掉。</p><p data-pid=\"Mmjxyt7L\">Abhi Ramesh看到了机会。他找到了附近其他农场主聊天，了解到这个残次蔬果不好处理的问题普遍存在。为了和农场主们建立关系，他每次去拜访都会买一些待处理的残次蔬果，然后带回自己的公寓。他当然并不需要这么多蔬果，但是他想借此和农场主们建立起良好的关系，并表示对他们的感谢。</p><p data-pid=\"_efV4cZq\">知道这个市场的供给端没有问题之后，接下来他需要测试需求端——消费者们会对价格低但品相不佳的蔬果感兴趣吗？</p><p data-pid=\"xiqtKO7P\">虽然他的第六感告诉他这个市场是存在需求的，但是他还是需要验证一下他的想法。</p><p data-pid=\"Q-jULKag\">他花了$150美金找人设计了Misfits Market的公司logo，自己用shopify搭建了一个电商平台，挂上去一些长相不那么好看的蔬果照片，以低于市价25% 的价格出售这些蔬果。</p><p data-pid=\"DCOVfArt\">网站建好后，他花了$1000美金在社交网站上打广告。再后来，为了扩大公司知名度，他开了五张信用卡，把十五万美金的额度全部用来打广告。这是一个冒险的决定，因为万一事情不成的话，他会背上利息超过20%的大额信用卡债务。</p><p data-pid=\"guF9Ih1z\">为了降低成本，Misfits Market公司起初的一切都是租的。Abhi租来了仓库、冰箱、卡车，从Craigslist（美国大型免费分类广告网站）上招来几个人负责给食物打包，正式启动了这门生意。</p><p data-pid=\"lAvbmSv5\">2018年初，公司刚成立时，Misfits Market一天顶多接15单，后来需求不断增长，到2018年底，Misfits Market一天能接200单。随后，在不到一年的时间里，Misfits Market的仓库从800平方英尺扩张到了超过一万平方英尺。</p><p data-pid=\"TgfxcM7Y\">2021年4月，Misfits Market完成C轮融资，正式成为一家市值超10亿美金的独角兽。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-862886d516c629a0e8129d4e8eeaef78_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"3000\" data-rawheight=\"2000\" class=\"origin_image zh-lightbox-thumb\" width=\"3000\" data-original=\"https://pic1.zhimg.com/v2-862886d516c629a0e8129d4e8eeaef78_r.jpg\" data-original-token=\"v2-8290c774c1ca4d01eade1cfae7628f50\"/><figcaption>Misfits Market员工在仓库打包食物</figcaption></figure><p data-pid=\"RZODFJ4M\">这个创业故事听起来是不是太顺了？一个不怎么“改变世界”的点子，不仅帮助解决了食物浪费问题，还赚到了钱？</p><p data-pid=\"4z3SOc6h\">但是<b>我们看到的往往只是别人的高光时刻，而忽略了他们蛰伏的过程。</b></p><p data-pid=\"EGIq7J45\">Abhi Ramesh是一位90后、第二代印度裔美国移民。在他5岁时，他的父母为了让一家人过上安稳的好生活，全家移民来到了美国。他的父母没有机会去“创业“、当”企业家“，但是他们希望自己的孩子能够在这片土地上自由闯荡，实现他们没能实现的”美国梦“。</p><p data-pid=\"ympz0vVJ\">Abhi Ramesh从小就不断的尝试各种赚钱的途径，梦想有一天自己能够变得富有。高中时，他发现了Amazon Marketplace，想到了他的第一个创业机会——他注意到美国高中会要求学生读很多必读书目，但是学期结束后学生们就不会再用这些书，也不知道怎么处理他们。于是Abhi向他的同学们以$20、$30价格收购他们不再需要的书，然后他再通过Amazon Marketplace将这些书以$80、$90价格售出。这次创业经历让他赚了一笔小钱，更重要的是学会了做生意的道理。</p><p data-pid=\"zgCa_Z37\">然而，这并不是他高中唯一一次创业。美国高中生在申请大学时需要提供SAT成绩，Abhi的父母非常重视教育，要求Abhi一定要取得好的SAT分数、申请上好的大学。Abhi在SAT考试上找到了新的商机——由于他的SAT成绩非常好，并且看到了他的同学们有这个需求，在高中生活的后半段，他召集了几个SAT成绩很好的同学，一起开了家SAT辅导公司，为其他学生提供SAT教学辅导服务。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-bb713bfa54f755a7e7de617e520796b3_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1146\" data-rawheight=\"1322\" class=\"origin_image zh-lightbox-thumb\" width=\"1146\" data-original=\"https://pic4.zhimg.com/v2-bb713bfa54f755a7e7de617e520796b3_r.jpg\" data-original-token=\"v2-e35426738b70ae42bceec4405383eb94\"/><figcaption>Abhi Ramesh和同学在高中时开办的SAT培训机构</figcaption></figure><p data-pid=\"7Gwvbr2M\">在沃顿商学院读本科期间，他依然在琢磨别的创业想法，然而繁重的学业让他无法全身心投入“事业”。大二结束后，为了能够全身心的执行自己的想法，他向学校申请休学一年。当时“间隔年”这个概念并不流行，学校的管理处和他的家长都强烈反对，他不顾反对，坚持选择了休学。</p><p data-pid=\"YClZ_whn\">离开学校后，他来到加州，连续做了几家创业公司。</p><p data-pid=\"Qz1hnMrq\">2012年，他创立的第一家公司叫TrendBent，主营男性时装个性化推荐系统。在获得了大约五千名客户后，他和合伙人认为男性时尚这个领域缺少大规模扩张的机会，于是就放弃了这家公司。回头看，他觉得当初这个决定可能是错的，因为后来崛起的Stitch Fix公司，做的事和TrendBent当初所做的非常相似。</p><p data-pid=\"LO-dbZds\">2013年，他创立了一家叫StoreTok的公司，主营业务是社交购物，具体形式是使用户能够通过Facebook、Twitter、YouTube等社交平台的内容评论直接购买商品。现在听起来这依然是个很有意思的点子，但是他认为当年时机还未成熟，做这件事有点太早了。经营了两年多以后，他再次关掉了公司。</p><p data-pid=\"Id9ZNg18\">后来，他还尝试做了一家医疗领域的公司，依然没有取得成功。他甚至想：<b>“想要创业成功实在是太难了，可能我就不是那块料吧。“</b>他决定去积累一些业界经验，于是，他回到沃顿商学院完成了本科学业，毕业后进入了一家资产管理公司工作。</p><p data-pid=\"5RsxPJDE\">他在这家资产管理公司只工作了11个月，连工作满12个月就可以拿到的3.5万美金的年终奖都没有拿，就开始了下一段创业之旅。他说，这份工作并没有问题，选择离开是他自己的问题。在资产管理公司的投资部门工作时，他有机会见到很多创业公司的管理者。他看到很多创始人拼搏了三五年后取得了瞩目的成就，他心想：<b>“为什么我不能成为他们中的一个？”</b></p><p data-pid=\"W7ywrcbk\">辞职后，他再次来到加州，和两个合伙人一起开了家教编程的12周强化训练营。这家公司只存在了两年，毕业了大约700名学生，他再次关掉了公司。</p><p data-pid=\"Ickrct7N\">再后来，就是如今的Misfits Market。如今，Misfits Market估值11亿美金，公司依然年轻，依然在快速发展中。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-de64e158212467f8cb7dc2083b4a182c_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"2730\" data-rawheight=\"4096\" class=\"origin_image zh-lightbox-thumb\" width=\"2730\" data-original=\"https://pic1.zhimg.com/v2-de64e158212467f8cb7dc2083b4a182c_r.jpg\" data-original-token=\"v2-648e2653ec5c6efd570e9135e5c63e3e\"/><figcaption>Abhi Ramesh和Misfits Market</figcaption></figure><p data-pid=\"dunCbC6q\">读到这里，你能数得清楚他曾经尝试过多少次吗？</p><p data-pid=\"ZAOvONFR\">就如同股价曲线从右往左分析总是容易一样，当一个人已经取得引人关注的成就时，人们回头分析他的成长经历总是容易的，好像一切都是命中注定。只有身处在这个过程之中，或者经历过这个过程的人，才会明白成功道路上会遇到多少挫折，会经历多少自我怀疑的灰暗时刻。</p><p data-pid=\"y92MjA4U\"><b>不断尝试是关键，善于观察、发现机会是必需，解决问题、为社会创造价值是根本。</b>Abhi Ramesh和Misfits Market的这一课，值得每一个不甘平庸的人学习。</p><p class=\"ztext-empty-paragraph\"><br/></p><p data-pid=\"HzuVZ21z\">参考资料：</p><ul><li data-pid=\"oRbv3oQx\">Misfits Funding Round Brings Valuation to $1.1 Billion</li><li data-pid=\"YB8o3ymN\">Meet the CEO selling boxes of &#34;ugly&#34; produce to help solve our food waste crisis</li><li data-pid=\"d3sDQWfU\">How I Built a $1 Billion Start-Up Called Misfits Market</li></ul><hr/><p data-pid=\"j21R_9GZ\">我是Kelly，全网同名“凯莉彭”自媒体账号主理人。前硅谷资深数据科学家，曾就职于Airbnb等公司。目前是全职自媒体人，关注创业和个人成长，每周末卫星视pin号直播访谈一位不走寻常路的创业者，欢迎来看我哦～</p>", "excerpt": "你有没有过这样的经历——你到农田去采摘水果，你会去摘树上的果子，你也许曾注意到，不少成熟的果子掉到了地上，外表有些损坏，面对地上那些水果，你是感叹食物被浪费很可惜呢，还是想到这个问题可能是机会呢？ Misfits Market，就是一家解决残次蔬果被浪费问题的公司。 Misfits Market公司的创始人Abhi Ramesh的顿悟瞬间，是2018年初他和女友在美国费城附近农田摘苹果的观察。他注意到很多苹果掉在地上，不会被前来摘苹果的顾…", "excerpt_new": "你有没有过这样的经历——你到农田去采摘水果，你会去摘树上的果子，你也许曾注意到，不少成熟的果子掉到了地上，外表有些损坏，面对地上那些水果，你是感叹食物被浪费很可惜呢，还是想到这个问题可能是机会呢？ Misfits Market，就是一家解决残次蔬果被浪费问题的公司。 Misfits Market公司的创始人Abhi Ramesh的顿悟瞬间，是2018年初他和女友在美国费城附近农田摘苹果的观察。他注意到很多苹果掉在地上，不会被前来摘苹果的顾…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/379164822", "comment_permission": "all", "voteup_count": 0, "comment_count": 0, "image_url": "https://pica.zhimg.com/v2-e0257ffbeff04239f95491629cd896c0_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1623215018, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1609209410180", "type": "feed", "target": {"id": "1650903577", "type": "answer", "url": "https://api.zhihu.com/answers/1650903577", "voteup_count": 1, "thanks_count": 0, "question": {"id": "413497955", "title": "有哪些成长类的博主（公众号）值得推荐呢？", "url": "https://api.zhihu.com/questions/413497955", "type": "question", "question_type": "normal", "created": 1597137107, "answer_count": 14, "comment_count": 0, "follower_count": 24, "detail": "", "excerpt": "", "bound_topic_ids": [1575, 15971, 49441, 86626, 87465], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "14b62a34167307832d3ef44fe3263c76", "name": "爱浪的糖小小", "headline": "「暖心文字」「大学成长」", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/xiang-yang-sheng-chang-41-68", "url_token": "xiang-yang-sheng-chang-41-68", "avatar_url": "https://picx.zhimg.com/v2-008d130dbd6d53b01b1a5b3b80f3efd3_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1609209409, "created_time": 1609209409, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"6BmogZZT\">自荐一把～大家好，我是凯莉，在旧金山的互联网公司做数据科学家。不过我喜欢写的东西和数据科学专业知识没什么关系，大多是个人成长类型内容。主题包括但不限于：<b>投资理财，FIRE计划，好书分享，职场感悟，以及身边优秀的人的故事</b>。欢迎关注<b>「凯莉彭」</b>。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-8bdf8ae0a825414b78f0eecec7217424_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1125\" data-rawheight=\"2172\" data-original-token=\"v2-f4b195858412c03450be80200e95d26f\" data-default-watermark-src=\"https://pic1.zhimg.com/v2-77efdfaf2efdb870d5f2caa3b097d058_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1125\" data-original=\"https://pic1.zhimg.com/v2-8bdf8ae0a825414b78f0eecec7217424_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1125&#39; height=&#39;2172&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"1125\" data-rawheight=\"2172\" data-original-token=\"v2-f4b195858412c03450be80200e95d26f\" data-default-watermark-src=\"https://pic1.zhimg.com/v2-77efdfaf2efdb870d5f2caa3b097d058_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1125\" data-original=\"https://pic1.zhimg.com/v2-8bdf8ae0a825414b78f0eecec7217424_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-8bdf8ae0a825414b78f0eecec7217424_b.jpg\"/></figure><p></p>", "excerpt": "自荐一把～大家好，我是凯莉，在旧金山的互联网公司做数据科学家。不过我喜欢写的东西和数据科学专业知识没什么关系，大多是个人成长类型内容。主题包括但不限于： <b>投资理财，FIRE计划，好书分享，职场感悟，以及身边优秀的人的故事</b>。欢迎关注<b>「凯莉彭」</b>。 ", "excerpt_new": "自荐一把～大家好，我是凯莉，在旧金山的互联网公司做数据科学家。不过我喜欢写的东西和数据科学专业知识没什么关系，大多是个人成长类型内容。主题包括但不限于： <b>投资理财，FIRE计划，好书分享，职场感悟，以及身边优秀的人的故事</b>。欢迎关注<b>「凯莉彭」</b>。 ", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1609209410, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1609209410180&page_num=51"}}