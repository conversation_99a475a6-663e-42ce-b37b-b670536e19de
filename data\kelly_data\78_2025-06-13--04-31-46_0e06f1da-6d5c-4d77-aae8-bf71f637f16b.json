{"data": [{"id": "1491864150029", "type": "feed", "target": {"id": "20282222", "title": "怎样认识比你优秀的人并和他们成为朋友？", "url": "https://api.zhihu.com/questions/20282222", "type": "question", "question_type": "normal", "created": 1339047134, "answer_count": 1526, "comment_count": 51, "follower_count": 103850, "detail": "<p>可以通过哪些途径认识比你优秀或和你志同道合的人，无论是网络中的，素未谋面的还是现实中不熟悉的那些在某方面有能力的可以给你帮助的人。还有怎样能和他们深入交流成为朋友呢？有一句话说看你是什么样的人就要看你和什么样的人交往。我很希望能认识良师益友，即使在平凡的交流中也能激发我思考。</p>", "excerpt": "可以通过哪些途径认识比你优秀或和你志同道合的人，无论是网络中的，素未谋面的还是…", "bound_topic_ids": [404, 963, 3946, 5361, 7129], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "f06377de7926289ff3e527bb82f95f45", "name": "yuli<PERSON><PERSON>", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/liang-sa-sa", "url_token": "liang-sa-sa", "avatar_url": "https://picx.zhimg.com/b787d2330_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "verb": "", "created_time": 1491864150, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "关注了问题", "is_sticky": false}, {"id": "1491345096307", "type": "feed", "target": {"id": "16542956", "type": "answer", "url": "https://api.zhihu.com/answers/16542956", "voteup_count": 346, "thanks_count": 73, "question": {"id": "19725590", "title": "怎样用非数学语言讲解贝叶斯定理（<PERSON><PERSON>'s theorem）？", "url": "https://api.zhihu.com/questions/19725590", "type": "question", "question_type": "normal", "created": 1308615391, "answer_count": 117, "comment_count": 13, "follower_count": 3577, "detail": "<p>昨天看到这样一道题，感觉是可以用简单的逻辑推论出来的。但是总是差一点点。一机器在良好状态生产合格产品几率是 90%，在故障状态生产合格产品几率是 30%，机器良好的概率是 75%。若一日第一件产品是合格品，那么此日机器良好的概率是多少。  </p><p>求逻辑推论，我知道拿公式来解速度很快，但这终究是速成法，不能内化。这种逻辑上推导才是数学精神和乐趣所在，数学语言是人思维活动的一种表现形式，有达人能告诉我这种情况的思考方式吗？</p><p><b>本问题已经加入新闻专题 &gt;&gt; <a href=\"https://www.zhihu.com/special/19651932\" class=\"internal\">那些年，我们一起被「数学证明」支配过的恐惧</a></b> </p><p><b>更多与数学有关的事儿详见专题。</b></p>", "excerpt": "昨天看到这样一道题，感觉是可以用简单的逻辑推论出来的。但是总是差一点点。一机器…", "bound_topic_ids": [1291, 3074, 8745, 27412, 40314], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "9c03606cf175b72043dc498742655c13", "name": "曾举臣", "headline": "Good luck to you~", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/zengjuchen", "url_token": "<PERSON><PERSON><PERSON><PERSON>", "avatar_url": "https://picx.zhimg.com/5acfa84d9_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1364646536, "created_time": 1364646536, "author": {"id": "fb6339c1ca51e8098a7e644d07f2549e", "name": "山醒", "headline": "物理系的爱智慧男。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/rethink", "url_token": "rethink", "avatar_url": "https://pic1.zhimg.com/efe4d3762_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["物理学"], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "物理学话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "v2-6502f931cb98470c24111581d3f19d5d", "avatar_url": "https://pic1.zhimg.com/v2-6502f931cb98470c24111581d3f19d5d_720w.jpg?source=32738c0c", "description": "", "id": "19556950", "name": "物理学", "priority": 0, "token": "19556950", "type": "topic", "url": "https://www.zhihu.com/topic/19556950"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}], "title": "物理学话题下的优秀答主"}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "comment_permission": "all", "is_copyable": true, "comment_count": 40, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"-31Cxfkw\">与</p><a class=\"member_mention\" data-hash=\"828d10598f89ef5a5d68a41634c8c220\" href=\"https://www.zhihu.com/people/828d10598f89ef5a5d68a41634c8c220\" data-hovercard=\"p$b$828d10598f89ef5a5d68a41634c8c220\">@陳浩</a><p data-pid=\"lPw9z4Ya\"> 的表格一样，但是以前我才开始学的时候，更喜欢画图：</p><figure><noscript><img data-rawheight=\"368\" data-rawwidth=\"308\" src=\"https://pica.zhimg.com/fbf113ec8ae297393b64da39ed92788c_b.jpg\" data-original-token=\"fbf113ec8ae297393b64da39ed92788c\" class=\"content_image\" width=\"308\"/></noscript><img data-rawheight=\"368\" data-rawwidth=\"308\" src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;308&#39; height=&#39;368&#39;&gt;&lt;/svg&gt;\" data-original-token=\"fbf113ec8ae297393b64da39ed92788c\" class=\"content_image lazy\" width=\"308\" data-actualsrc=\"https://pica.zhimg.com/fbf113ec8ae297393b64da39ed92788c_b.jpg\"/></figure>", "excerpt": "与 <a class=\"member_mention\" data-hash=\"828d10598f89ef5a5d68a41634c8c220\" href=\"https://www.zhihu.com/people/828d10598f89ef5a5d68a41634c8c220\" data-hovercard=\"p$b$828d10598f89ef5a5d68a41634c8c220\">@陳浩</a> 的表格一样，但是以前我才开始学的时候，更喜欢画图： ", "excerpt_new": "与 <a class=\"member_mention\" data-hash=\"828d10598f89ef5a5d68a41634c8c220\" href=\"https://www.zhihu.com/people/828d10598f89ef5a5d68a41634c8c220\" data-hovercard=\"p$b$828d10598f89ef5a5d68a41634c8c220\">@陳浩</a> 的表格一样，但是以前我才开始学的时候，更喜欢画图： ", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1491345096, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1489219803864", "type": "feed", "target": {"id": "33980390", "type": "answer", "url": "https://api.zhihu.com/answers/33980390", "voteup_count": 67, "thanks_count": 47, "question": {"id": "20538465", "title": "项目管理方面有没有什么书推荐可以看一下?", "url": "https://api.zhihu.com/questions/20538465", "type": "question", "question_type": "normal", "created": 1350481559, "answer_count": 28, "comment_count": 0, "follower_count": 299, "detail": "", "excerpt": "", "bound_topic_ids": [53, 27802, 679503], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "2ac4e8b0865c5bda75f432e3cdef1bbd", "name": "<PERSON><PERSON>a", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/sunjia-28", "url_token": "sunjia-28", "avatar_url": "https://picx.zhimg.com/v2-a43763a14bc2a6d7798df142b91b7cfa_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1428590278, "created_time": 1416893099, "author": {"id": "6b3cd0aa761e28cc26121b6b464a908d", "name": "蓬莱山辉夜", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/peng-lai-shan-hui-ye-23", "url_token": "peng-lai-shan-hui-ye-23", "avatar_url": "https://pica.zhimg.com/5c0cbcd224c258880de428adfedc5727_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://pic1.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 5, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"h4ZW2oA3\">分用途回答：</p><p data-pid=\"Mk8Axlk1\">现在流行的项目管理理念已经从单纯的追求按时、不超支、保证质量的完成合同规定的工作范畴，变成了结合考虑企业整体的企业、事业战略方向，并且意欲通过项目的成功开展来达到战略目的。</p><p data-pid=\"PB020tLv\">因此，项目管理个人一向以为最重要的是首先要打好三观基础，首推的一本原汁原味的项目管理书籍就是：</p><p data-pid=\"xsblEzWz\">Harvery Maylor的Project Management（</p><a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/4378129/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Project Management (豆瓣)</a><p data-pid=\"quU-pfNv\">），该书并非着眼于PMBOK的逻辑架构，而是大量加入直观的感性拓展阅读资料来帮助读者理解项目管理的技术/工具，以及应用它们的场合、目的等等。</p><p data-pid=\"PLrNhKx-\">而更偏向于高层的项目集经理、PMO经理的一本书籍则是：</p><p data-pid=\"mDrZykdp\">Enzo Frigenti的Project Management Practice（</p><a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/2335480/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">The Practice of Project Management (豆瓣)</a><p data-pid=\"wv3ZQAc0\">），该书更是完全抛弃了Body of Knowledge的叙事逻辑体系，完全从实践的角度出发来考虑、讨论问题，是很好的了解项目、项目集、项目组合统合管理实践理念的书籍。</p><p data-pid=\"Mx-oD7rs\">另外，比较软一些的书籍还有：</p><p data-pid=\"HLr2DGrc\">1，</p><a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/26261028/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">三年後，你的工作還在嗎 (豆瓣)</a><p data-pid=\"IFf38mwH\">2，</p><a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/4050758/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">基业长青 (豆瓣)</a><br/><p data-pid=\"PnrCjW0K\">3，</p><a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/20261729/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">竞争战略论 (豆瓣)</a><br/><p data-pid=\"q4uzmhIn\">都是很值得参考，同时可读性较强，而又不会太过于专业导致阅读兴致提不起来。大推荐！</p><p data-pid=\"OmfLcsI1\">最后，如果在读完上面的几本书（起码要读完Harvey Maylor那本）之后，对项目管理依然兴趣不减的话，此时可以考虑感性的阅读一下PMBOK，不用强迫自己完全理解，大体感受以下PMBOK的逻辑架构即可。</p><a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/24697645/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">项目管理知识体系指南 (豆瓣)</a><br/><p data-pid=\"oe35383e\">————————————————20150408分割线————————————————</p><p data-pid=\"7GaZUkkj\">最近找到了一些比较好的关于Technical Writing方面的书籍，这里做一简要更新，供参考：</p><p data-pid=\"3xlgLkDY\">（Technical writing这门课程大陆这里尚未听闻有高校开设，这门课程专门用于解决如何书写各类实际工作中需要接触到的文档，虽然名字叫做“技术写作”，其实内容跟“技术”的关系不大，更多的是比如说RFI、RFQ、RFP怎么写？Proposal怎么起草？会议纪要怎样记录？商务信函的格式要求？甚至于简历的编纂？乃至于工作应聘的面试技巧都有所提及，个人认为是非常好的新人职场入职教育的一个知识组成部分！）</p><p data-pid=\"4Uygrrf8\">1，Handbook of technical writing：</p><a href=\"https://link.zhihu.com/?target=http%3A//www.amazon.com/Handbook-Technical-Writing-Gerald-Alred-ebook/dp/B00T95PSYK/ref%3Dsr_1_14%3Fie%3DUTF8%26qid%3D1428589977%26sr%3D8-14%26keywords%3Dtechnical%2Bwriting\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Amazon.com: Handbook of Technical Writing eBook: Gerald J. Alred, Charles T. Brusaw, Walter E. Oliu: Kindle Store</a><br/><p data-pid=\"dIr6yUtk\">2，The insider&#39;s guide to technical writing：</p><a href=\"https://link.zhihu.com/?target=http%3A//www.amazon.com/Insiders-Guide-Technical-Writing/dp/1937434036/ref%3Dsr_1_2%3Fie%3DUTF8%26qid%3D1428589977%26sr%3D8-2%26keywords%3Dtechnical%2Bwriting\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">The Insider&#39;s Guide to Technical Writing: Krista Van Laan, Krista Van Laan, Joann T. Hackos: 9781937434038: Amazon.com: Books</a><br/><p data-pid=\"4ePISI0r\">3，Elements of technical writing：</p><a href=\"https://link.zhihu.com/?target=http%3A//www.amazon.com/Elements-Technical-Writing-Gary-Blake/dp/0020130856/ref%3Dsr_1_7%3Fie%3DUTF8%26qid%3D1428589977%26sr%3D8-7%26keywords%3Dtechnical%2Bwriting\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Elements of Technical Writing: Gary Blake, Robert W. Bly: 0021898130853: Amazon.com: Books</a><br/><br/><p data-pid=\"fbd1Q1G5\">这三本是我看到Amazon上评价比较好，组织结构我个人觉得也很棒的书籍。其实也可以在Amazon上直接搜看看有没有自己喜欢的。</p><p data-pid=\"3m3EYtuA\">PS：不推荐买原版，一个是真.特么贵（我买的就是第一本，本体+运费85刀我简直肉疼），而且装订简直一坨屎…… 美帝的人工时费用高不说，劳动质量还差的吓人……</p><p data-pid=\"zxlsUzR6\">PS2：可以在X宝上按照这些书名搜索一下，应该是能搞到电子版的，撑死10块20块软妹币，然后自己在打印装订成书，便宜不说质量还好。（X宝上也有类似服务）</p>", "excerpt": "分用途回答： 现在流行的项目管理理念已经从单纯的追求按时、不超支、保证质量的完成合同规定的工作范畴，变成了结合考虑企业整体的企业、事业战略方向，并且意欲通过项目的成功开展来达到战略目的。 因此，项目管理个人一向以为最重要的是首先要打好三观基础，首推的一本原汁原味的项目管理书籍就是： Harvery Maylor的Project Management（ <a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/4378129/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Project Management (豆瓣)</a>），该书并非着眼于PMBOK的逻辑架构，而是大量加入直观的感…", "excerpt_new": "分用途回答： 现在流行的项目管理理念已经从单纯的追求按时、不超支、保证质量的完成合同规定的工作范畴，变成了结合考虑企业整体的企业、事业战略方向，并且意欲通过项目的成功开展来达到战略目的。 因此，项目管理个人一向以为最重要的是首先要打好三观基础，首推的一本原汁原味的项目管理书籍就是： Harvery Maylor的Project Management（ <a href=\"https://link.zhihu.com/?target=http%3A//book.douban.com/subject/4378129/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">Project Management (豆瓣)</a>），该书并非着眼于PMBOK的逻辑架构，而是大量加入直观的感…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1489219803, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1489162098456", "type": "feed", "target": {"id": "34714804", "type": "answer", "url": "https://api.zhihu.com/answers/34714804", "voteup_count": 4972, "thanks_count": 1235, "question": {"id": "26743347", "title": "网易云音乐的歌单推荐算法是怎样的？", "url": "https://api.zhihu.com/questions/26743347", "type": "question", "question_type": "normal", "created": 1416721706, "answer_count": 240, "comment_count": 56, "follower_count": 25864, "detail": "不是广告党，但我却成为网易云音乐的的重度患者，不管是黑红的用户界面，还是高质量音乐质量都用起来很舒服。我喜欢听歌，几乎每周不低于15小时，但其实听得不是特别多，并没有经常刻意地去搜歌名，所以曲目数量我并不是很在乎。但是比起其它，网音给我推荐的歌单几乎次次惊艳，而且大多都没听过，或者好久以前听过早就忘记了名字，或者之前不知道在哪听过 只是知道其中一部分旋律，根本不知道名字，等等，听起来整个人大有提升。<br/>——————————————————————————————————<br/>问题来了，我想知道网音的歌单推荐是网音项目团队精心挑选制作的，还是众多音乐达人的推荐？即：歌单是网音官方提供，还是UGC？才有如此对口味的歌单推荐？求研究过的大神给出详细解答。", "excerpt": "不是广告党，但我却成为网易云音乐的的重度患者，不管是黑红的用户界面，还是高质量…", "bound_topic_ids": [75, 368, 10096, 87983], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "ca18c03d322c001f819c8cedc95007ce", "name": "白胡子", "headline": "互联网 电子", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/lu-fei-7", "url_token": "lu-fei-7", "avatar_url": "https://picx.zhimg.com/e79c87446_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1418139509, "created_time": 1417955595, "author": {"id": "e8d59f0eb58bebd923751263db30d69c", "name": "nick li", "headline": "机器学习/统计/Ph.D.", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/nick-lee-60", "url_token": "nick-lee-60", "avatar_url": "https://pic1.zhimg.com/v2-0ed2090eb53a90f7e6f2db89f6b48d2b_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 252, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"iP06VGa-\">这里我想给大家介绍另外一种推荐系统，这种算法叫做潜在因子（Latent\nFactor）算法。这种算法是在NetFlix（没错，就是用大数据捧火《纸牌屋》的那家公司）的推荐算法竞赛中获奖的算法，最早被应用于电影推荐中。这种算法在实际应用中比现在排名第一的 <a data-hash=\"296727f65ac7121dce72e2424edbd552\" href=\"https://www.zhihu.com/people/296727f65ac7121dce72e2424edbd552\" class=\"member_mention\" data-editable=\"true\" data-title=\"@邰原朗\" data-tip=\"p$b$296727f65ac7121dce72e2424edbd552\" data-hovercard=\"p$b$296727f65ac7121dce72e2424edbd552\">@邰原朗</a> 所介绍的算法误差（RMSE）会小不少，效率更高。我下面仅利用基础的矩阵知识来介绍下这种算法。</p><p data-pid=\"fCJIazNg\">这种算法的思想是这样：每个用户（<b>user</b>）都有自己的偏好，比如A喜欢带有<b>小清新的</b>、<b>吉他伴奏的</b>、<b>王菲</b>等元素（<b>latent factor</b>），如果一首歌（<b>item</b>）带有这些元素，那么就将这首歌推荐给该用户，也就是用元素去连接用户和音乐。每个人对不同的元素偏好不同，而每首歌包含的元素也不一样。我们希望能找到这样两个矩阵：</p><p data-pid=\"fUUdjG8R\">一，<b>用户-潜在因子矩阵Q</b>，表示不同的用户对于不用元素的偏好程度，1代表很喜欢，0代表不喜欢。比如下面这样：</p><figure><noscript><img src=\"https://pic1.zhimg.com/6b9686e909e3e14fda19782426b1b88e_b.jpg\" data-rawwidth=\"543\" data-rawheight=\"194\" data-original-token=\"6b9686e909e3e14fda19782426b1b88e\" class=\"origin_image zh-lightbox-thumb\" width=\"543\" data-original=\"https://pic1.zhimg.com/6b9686e909e3e14fda19782426b1b88e_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;543&#39; height=&#39;194&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"543\" data-rawheight=\"194\" data-original-token=\"6b9686e909e3e14fda19782426b1b88e\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"543\" data-original=\"https://pic1.zhimg.com/6b9686e909e3e14fda19782426b1b88e_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/6b9686e909e3e14fda19782426b1b88e_b.jpg\"/></figure><p data-pid=\"fp9-qjhU\">二，<b>潜在因子-音乐矩阵P</b>，表示每种音乐含有各种元素的成分，比如下表中，音乐A是一个偏小清新的音乐，含有小清新这个Latent Factor的成分是0.9，重口味的成分是0.1，优雅的成分是0.2……</p><figure><noscript><img src=\"https://pica.zhimg.com/b37d2aea4a35d3f45e8f25fd121c4e52_b.jpg\" data-rawwidth=\"543\" data-rawheight=\"231\" data-original-token=\"b37d2aea4a35d3f45e8f25fd121c4e52\" class=\"origin_image zh-lightbox-thumb\" width=\"543\" data-original=\"https://pica.zhimg.com/b37d2aea4a35d3f45e8f25fd121c4e52_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;543&#39; height=&#39;231&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"543\" data-rawheight=\"231\" data-original-token=\"b37d2aea4a35d3f45e8f25fd121c4e52\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"543\" data-original=\"https://pica.zhimg.com/b37d2aea4a35d3f45e8f25fd121c4e52_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/b37d2aea4a35d3f45e8f25fd121c4e52_b.jpg\"/></figure><p data-pid=\"Fzl937nY\">利用这两个矩阵，我们能得出张三对音乐A的喜欢程度是：张三对<b>小清新</b>的偏好*音乐A含有<b>小清新</b>的成分+对<b>重口味</b>的偏好*音乐A含有<b>重口味</b>的成分+对<b>优雅</b>的偏好*音乐A含有<b>优雅</b>的成分+……</p><figure><noscript><img src=\"https://pica.zhimg.com/7a37d920fff8d307c6494ef03ca249e8_b.jpg\" data-rawwidth=\"543\" data-rawheight=\"116\" data-original-token=\"7a37d920fff8d307c6494ef03ca249e8\" class=\"origin_image zh-lightbox-thumb\" width=\"543\" data-original=\"https://pica.zhimg.com/7a37d920fff8d307c6494ef03ca249e8_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;543&#39; height=&#39;116&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"543\" data-rawheight=\"116\" data-original-token=\"7a37d920fff8d307c6494ef03ca249e8\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"543\" data-original=\"https://pica.zhimg.com/7a37d920fff8d307c6494ef03ca249e8_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/7a37d920fff8d307c6494ef03ca249e8_b.jpg\"/></figure><figure><noscript><img src=\"https://picx.zhimg.com/5cddc0bb594d8469625ea5e2b8bd3e47_b.jpg\" data-rawwidth=\"543\" data-rawheight=\"116\" data-original-token=\"5cddc0bb594d8469625ea5e2b8bd3e47\" class=\"origin_image zh-lightbox-thumb\" width=\"543\" data-original=\"https://picx.zhimg.com/5cddc0bb594d8469625ea5e2b8bd3e47_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;543&#39; height=&#39;116&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"543\" data-rawheight=\"116\" data-original-token=\"5cddc0bb594d8469625ea5e2b8bd3e47\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"543\" data-original=\"https://picx.zhimg.com/5cddc0bb594d8469625ea5e2b8bd3e47_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/5cddc0bb594d8469625ea5e2b8bd3e47_b.jpg\"/></figure><p data-pid=\"8pkmMfl5\">即：0.6*0.9+0.8*0.1+0.1*0.2+0.1*0.4+0.7*0=0.69</p><p data-pid=\"8_hPG_CQ\">每个用户对每首歌都这样计算可以得到不同用户对不同歌曲的评分矩阵<img src=\"https://www.zhihu.com/equation?tex=%5Ctilde%7BR%7D+\" alt=\"\\tilde{R} \" eeimg=\"1\"/>。（注，这里的破浪线表示的是估计的评分，接下来我们还会用到不带波浪线的R表示实际的评分）：</p><figure><noscript><img src=\"https://pic3.zhimg.com/0206a3b5a16ed64e2711e534dfb9bc4e_b.jpg\" data-rawwidth=\"459\" data-rawheight=\"194\" data-original-token=\"0206a3b5a16ed64e2711e534dfb9bc4e\" class=\"origin_image zh-lightbox-thumb\" width=\"459\" data-original=\"https://pic3.zhimg.com/0206a3b5a16ed64e2711e534dfb9bc4e_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;459&#39; height=&#39;194&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"459\" data-rawheight=\"194\" data-original-token=\"0206a3b5a16ed64e2711e534dfb9bc4e\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"459\" data-original=\"https://pic3.zhimg.com/0206a3b5a16ed64e2711e534dfb9bc4e_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/0206a3b5a16ed64e2711e534dfb9bc4e_b.jpg\"/></figure><p data-pid=\"MhbL6gAB\">因此我们队张三推荐四首歌中得分最高的B，对李四推荐得分最高的C，王五推荐B。</p><p data-pid=\"R4ijedzA\">如果用矩阵表示即为：</p><img src=\"https://www.zhihu.com/equation?tex=%5Ctilde%7BR%7D+%3DQP%5E%7BT%7D+\" alt=\"\\tilde{R} =QP^{T} \" eeimg=\"1\"/><br/><br/><p data-pid=\"Qe3RqlDN\">下面问题来了，<b>这个潜在因子（latent factor）</b><b>是怎么得到的呢？</b></p><p data-pid=\"0eCbUr46\">由于面对海量的让用户自己给音乐分类并告诉我们自己的偏好系数显然是不现实的，事实上我们能获得的数据只有用户行为数据。我们沿用 </p><a data-hash=\"296727f65ac7121dce72e2424edbd552\" href=\"https://www.zhihu.com/people/296727f65ac7121dce72e2424edbd552\" class=\"member_mention\" data-editable=\"true\" data-title=\"@邰原朗\" data-tip=\"p$b$296727f65ac7121dce72e2424edbd552\" data-hovercard=\"p$b$296727f65ac7121dce72e2424edbd552\">@邰原朗</a><p data-pid=\"AAG7_TnO\">的量化标准：单曲循环=5, 分享=4, 收藏=3, 主动播放=2 , 听完=1, 跳过=-2 , 拉黑=-5，在分析时能获得的实际评分矩阵<b>R</b>，也就是输入矩阵大概是这个样子：</p><figure><noscript><img src=\"https://pic2.zhimg.com/1a783eefd2beaa432faf2e20163ea835_b.jpg\" data-rawwidth=\"1079\" data-rawheight=\"298\" data-original-token=\"1a783eefd2beaa432faf2e20163ea835\" class=\"origin_image zh-lightbox-thumb\" width=\"1079\" data-original=\"https://pic2.zhimg.com/1a783eefd2beaa432faf2e20163ea835_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1079&#39; height=&#39;298&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1079\" data-rawheight=\"298\" data-original-token=\"1a783eefd2beaa432faf2e20163ea835\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1079\" data-original=\"https://pic2.zhimg.com/1a783eefd2beaa432faf2e20163ea835_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/1a783eefd2beaa432faf2e20163ea835_b.jpg\"/></figure><p data-pid=\"ysOZZ3Hl\">事实上这是个非常非常稀疏的矩阵，因为大部分用户只听过全部音乐中很少一部分。如何利用这个矩阵去找潜在因子呢？这里主要应用到的是矩阵的UV分解。也就是将上面的评分矩阵分解为两个低维度的矩阵，用Q和P两个矩阵的乘积去估计实际的评分矩阵，而且我们希望估计的评分矩阵</p><img src=\"https://www.zhihu.com/equation?tex=%5Ctilde%7BR%7D+\" alt=\"\\tilde{R} \" eeimg=\"1\"/><figure><noscript><img src=\"https://pic1.zhimg.com/59b28d6c857ece645400e43cb8a08a6c_b.jpg\" data-rawwidth=\"1082\" data-rawheight=\"259\" data-original-token=\"59b28d6c857ece645400e43cb8a08a6c\" class=\"origin_image zh-lightbox-thumb\" width=\"1082\" data-original=\"https://pic1.zhimg.com/59b28d6c857ece645400e43cb8a08a6c_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1082&#39; height=&#39;259&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1082\" data-rawheight=\"259\" data-original-token=\"59b28d6c857ece645400e43cb8a08a6c\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1082\" data-original=\"https://pic1.zhimg.com/59b28d6c857ece645400e43cb8a08a6c_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/59b28d6c857ece645400e43cb8a08a6c_b.jpg\"/></figure><br/><p data-pid=\"jqqy-vOR\">和实际的评分矩阵不要相差太多，也就是求解下面的目标函数：</p><img src=\"https://www.zhihu.com/equation?tex=min_%7BP%2CQ%7D+%5CSigma+%28r_%7Bui%7D-q_%7Bi%7Dp_%7Bu%7D%5E%7BT%7D%29%5E2\" alt=\"min_{P,Q} \\Sigma (r_{ui}-q_{i}p_{u}^{T})^2\" eeimg=\"1\"/><p data-pid=\"9LLs2CZy\">这里涉及到最优化理论，在实际应用中，往往还要在后面加上2范数的罚项，然后利用梯度下降法就可以求得这<b>P,Q</b>两个矩阵的估计值。这里我们就不展开说了。例如我们上面给出的那个例子可以分解成为这样两个矩阵：</p><figure><noscript><img src=\"https://pic2.zhimg.com/56d1d0861468cc5216d5abe403cb9371_b.jpg\" data-rawwidth=\"1483\" data-rawheight=\"298\" data-original-token=\"56d1d0861468cc5216d5abe403cb9371\" class=\"origin_image zh-lightbox-thumb\" width=\"1483\" data-original=\"https://pic2.zhimg.com/56d1d0861468cc5216d5abe403cb9371_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1483&#39; height=&#39;298&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1483\" data-rawheight=\"298\" data-original-token=\"56d1d0861468cc5216d5abe403cb9371\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1483\" data-original=\"https://pic2.zhimg.com/56d1d0861468cc5216d5abe403cb9371_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/56d1d0861468cc5216d5abe403cb9371_b.jpg\"/></figure><p data-pid=\"tHhvWlJc\">这两个矩阵相乘就可以得到估计的得分矩阵：</p><figure><noscript><img src=\"https://picx.zhimg.com/c3e70bdd45d67b49d81e4bd2112741f5_b.jpg\" data-rawwidth=\"1177\" data-rawheight=\"298\" data-original-token=\"c3e70bdd45d67b49d81e4bd2112741f5\" class=\"origin_image zh-lightbox-thumb\" width=\"1177\" data-original=\"https://picx.zhimg.com/c3e70bdd45d67b49d81e4bd2112741f5_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1177&#39; height=&#39;298&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1177\" data-rawheight=\"298\" data-original-token=\"c3e70bdd45d67b49d81e4bd2112741f5\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1177\" data-original=\"https://picx.zhimg.com/c3e70bdd45d67b49d81e4bd2112741f5_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/c3e70bdd45d67b49d81e4bd2112741f5_b.jpg\"/></figure><p data-pid=\"iLQ-Vm6i\">将用户已经听过的音乐剔除后，选择分数最高音乐的推荐给用户即可（红体字）。</p><p data-pid=\"gv9UenxO\">在这个例子里面用户7和用户8有强的相似性：</p><figure><noscript><img src=\"https://pic4.zhimg.com/78188eafd238f32321d4ae42eba2063d_b.jpg\" data-rawwidth=\"1079\" data-rawheight=\"67\" data-original-token=\"78188eafd238f32321d4ae42eba2063d\" class=\"origin_image zh-lightbox-thumb\" width=\"1079\" data-original=\"https://pic4.zhimg.com/78188eafd238f32321d4ae42eba2063d_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1079&#39; height=&#39;67&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1079\" data-rawheight=\"67\" data-original-token=\"78188eafd238f32321d4ae42eba2063d\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1079\" data-original=\"https://pic4.zhimg.com/78188eafd238f32321d4ae42eba2063d_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/78188eafd238f32321d4ae42eba2063d_b.jpg\"/></figure><p data-pid=\"aDGqgpcY\">从推荐的结果来看，正好推荐的是对方评分较高的音乐：</p><figure><noscript><img src=\"https://pic1.zhimg.com/ae603dd2c6626c43b32b19d4f01f42fc_b.jpg\" data-rawwidth=\"1079\" data-rawheight=\"67\" data-original-token=\"ae603dd2c6626c43b32b19d4f01f42fc\" class=\"origin_image zh-lightbox-thumb\" width=\"1079\" data-original=\"https://pic1.zhimg.com/ae603dd2c6626c43b32b19d4f01f42fc_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1079&#39; height=&#39;67&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1079\" data-rawheight=\"67\" data-original-token=\"ae603dd2c6626c43b32b19d4f01f42fc\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1079\" data-original=\"https://pic1.zhimg.com/ae603dd2c6626c43b32b19d4f01f42fc_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/ae603dd2c6626c43b32b19d4f01f42fc_b.jpg\"/></figure>", "excerpt": "这里我想给大家介绍另外一种推荐系统，这种算法叫做潜在因子（Latent Factor）算法。这种算法是在NetFlix（没错，就是用大数据捧火《纸牌屋》的那家公司）的推荐算法竞赛中获奖的算法，最早被应用于电影推荐中。这种算法在实际应用中比现在排名第一的 <a data-hash=\"296727f65ac7121dce72e2424edbd552\" href=\"https://www.zhihu.com/people/296727f65ac7121dce72e2424edbd552\" class=\"member_mention\" data-editable=\"true\" data-title=\"@邰原朗\" data-tip=\"p$b$296727f65ac7121dce72e2424edbd552\" data-hovercard=\"p$b$296727f65ac7121dce72e2424edbd552\">@邰原朗</a> 所介绍的算法误差（RMSE）会小不少，效率更高。我下面仅利用基础的矩阵知识来介绍下这种算法。这种算法的思想是这样：每个用户（ <b>user</b>）都有自己的偏好，比如A喜欢带有<b>…</b>", "excerpt_new": "这里我想给大家介绍另外一种推荐系统，这种算法叫做潜在因子（Latent Factor）算法。这种算法是在NetFlix（没错，就是用大数据捧火《纸牌屋》的那家公司）的推荐算法竞赛中获奖的算法，最早被应用于电影推荐中。这种算法在实际应用中比现在排名第一的 <a data-hash=\"296727f65ac7121dce72e2424edbd552\" href=\"https://www.zhihu.com/people/296727f65ac7121dce72e2424edbd552\" class=\"member_mention\" data-editable=\"true\" data-title=\"@邰原朗\" data-tip=\"p$b$296727f65ac7121dce72e2424edbd552\" data-hovercard=\"p$b$296727f65ac7121dce72e2424edbd552\">@邰原朗</a> 所介绍的算法误差（RMSE）会小不少，效率更高。我下面仅利用基础的矩阵知识来介绍下这种算法。这种算法的思想是这样：每个用户（ <b>user</b>）都有自己的偏好，比如A喜欢带有<b>…</b>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1489162098, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1487383850092", "type": "feed", "target": {"id": "15298428", "type": "answer", "url": "https://api.zhihu.com/answers/15298428", "voteup_count": 214, "thanks_count": 88, "question": {"id": "20498490", "title": "美股可以做 T+0 日内交易吗？", "url": "https://api.zhihu.com/questions/20498490", "type": "question", "question_type": "normal", "created": 1348558803, "answer_count": 42, "comment_count": 2, "follower_count": 573, "detail": "做日内交易有什么好处？", "excerpt": "做日内交易有什么好处？", "bound_topic_ids": [395, 65777, 69866], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "0", "name": "匿名用户", "headline": "", "type": "", "user_type": "", "url": "", "url_token": "", "avatar_url": "https://pic1.zhimg.com/aadd7b895.png", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": null, "badge_v2": null, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1549174321, "created_time": 1348592362, "author": {"id": "551679f0a111ce47094a53dc12b7784f", "name": "<PERSON><PERSON>", "headline": "摸石头过河", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/harfmoon", "url_token": "harfmoon", "avatar_url": "https://picx.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 18, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"TCWYBr_K\"><b>首先声明一点：炒股有风险</b></p><p data-pid=\"LDqg0ym4\">美股不像A股，股票买了不能当天卖，而是可以做T+0。所以有一种交易方式就是当天买卖，迅速进出，持股时间以分钟计有时候甚至是秒计，原则上不持股过夜。有些交易者可能偶然的买了股票当天就卖出，是intraday trading，但是不算day trade的交易方式。</p><p data-pid=\"SpSTbgay\">Day trader基本都是机构和职业投资/投机者，每天交易几十甚至几百次。需要紧盯盘面，快进快出，一般需要4个monitor监视不同的市场。这是一项压力很大的工作，淘汰率相当高，普通的业余投资者基本没可能做day trade。</p><p data-pid=\"bBaVTdkW\">Day trade 也可以用margin。很多券商给pattern day trader的margin比例比一般人更高，可以到25％，即放大4倍的资金。由于一般day trade在收盘前都要结清，而margin是隔夜才算利息，所以day trade实际上可以免费用margin，因为有了这个杠杆，你亏你赚的多，亏得也多。</p><p data-pid=\"LlzVKPMw\">既然Day trade基本是一项专业性很强的工作，所以和会计、程序员、建筑师等工作一样，也必然有相关的理论的支持和技巧、经验的积累。Day trade有以下的交易流派或方法：</p><p data-pid=\"_VuPPCDZ\">1、Trend following，国内一般叫“做趋势”，即判断股价在一段时间内会持续上升或下降，则相应的买入或卖空并持有一段时间来获利。</p><p data-pid=\"2vsrTfBu\">2、Range trading，“做波动”，即判断股价在Channel，即箱体内波动，则在此范围内低买高卖。</p><p data-pid=\"kuxXFQpa\">3、Contrarian Investing，反向操作，与市场唱反调。</p><p data-pid=\"A2R2EI8S\">4、Scalping，做差价或套利。因为市场上总有bid/ask价格的不同，其中的差距称为spread，则可以利用这个spread来获利。做scalping时是以bid价格买入，以ask价格卖出，得到spread。当市场比较稳定时适合做scalping。因为每次scalping得到的利润一般很少，所以必须通过很大的成交量和巨大的交易次数来得到可接受的利润。由于市场价格总是在不断变动，所以scalping的timeframe很短，可能是所有交易方式中最短的了，有时只是几秒的时间。</p><p data-pid=\"E-THxr18\">5、Rebate Trading，以ECN的返利作为利润来源。</p><p data-pid=\"J7Yug8Ks\">6、News Playing，好消息发布时买入（如果是见光死的消息则short)，坏消息相反，以此获利。 </p><p data-pid=\"ry1yg7_A\">专业做day trade不会用一般的retail broker，而是用direct access broker。后者直接接入ECN，交易速度更快。此外retail broker一般按交易次数收费，不管交易金额，而direct access broker则按交易金额收费，相对来说便宜很多。</p><p data-pid=\"ovslvkaV\">SEC认为day trade是高风险的交易style，对pattern day trader(连续5天内至少有4天发生intraday trading)有一些限制，比如帐户余额必须保持在US $25,000以上。可以看看下面的规定，我从我的broker 网站拷贝来的。我自己就拿多1万块玩过一阵，经常是在5天内交易到达4次，就不让做了，有时候明明机会很好，看着手痒，呵呵，不过由于这个限制，导致我的收益比较好，原因是只有很好的机会出现，我才买卖。</p><p data-pid=\"ZcPCUe98\">FINRA and the NYSE define a Pattern Day Trader (PDT) as one who effects four or more day trades (same day opening and closing of a given equity security (&#34;stock&#34;) or equity option) within a five business day period.</p><p data-pid=\"luevc1-1\">A potential pattern day trader error message means that an account has less than the SEC required $25,000 minimum Net Liquidation Value AND the number of available day trades (3) has already been used within the last five days.</p><p data-pid=\"YPEj8gH9\">IB is programmed to prohibit any further trades to be initiated in the account, regardless of the intent to day trade that position or not. This way, the accounts with less than $25,000 so the account would not “potentially” be flagged as a day trading account.</p><p data-pid=\"Yz_gO4Bt\">What happens if an account with less than $25,000 is flagged as a day trading account? Or, if the account is flagged as a PDT account, and the value subsequently falls below the SEC required $25,000 minimum (intraday included)?</p><p data-pid=\"tgDK34eu\">The customer has the following options:<br/>A. Deposit funds to bring the account’s equity up to the SEC required minimum of $25,000<br/>B. Wait the required 90 day period before any new positions can be initiated<br/>C. Request a PDT account reset</p><p data-pid=\"tpay-DB-\"><b>Day Trade的一些原则</b><br/>1、 坚决止损<br/>把亏损控制在既定范围内，否则纵然盈利的交易笔数大于亏损的交易笔数，依然很难赚到钱。<br/>能否作有效高效的止损是衡量一个Trader是否成熟最重要的标准之一。<br/>止损和交易是一对孪生兄弟，永远是紧密的在一起。更像是成本之于是利润，不可能被避免，但是可以被控制。</p><p data-pid=\"wIy5e2P1\">2、 坚持顺势操作</p><p data-pid=\"cw_amYzW\">3、 每天要有足够多的交易笔数<br/>只有保证足够多的交易次数才能够验证你目前的操作方法的正确率及有效率，而反过来，如果你的交易方法不是具备相应的正确率或者你无法去执行你的操作方法，那你的操作次数肯定上不去</p><p data-pid=\"HXe0scJO\">4、 保持良好的心态<br/>交易是残酷的，果断、信心、专注、自律这些珍贵的品格决定了交易的结果，这些珍贵的品格将会在超短线交易中，每一天、每一次交易中得到锤炼，无法通过这些的人将被无情的淘汰，市场永远不会同情弱者。</p><p data-pid=\"ZubY6SoQ\"><b>Day Trade 好处</b><br/>做波段不是每天都能赚到钱，但做Day Trader就像一份工作一样，每天都能赚到钱，如果不贪赚点生活费对于专业的Day Trader来说不是很难的事。Day Trader还有一个好处就是没有隔夜风险，每天晚上可以安心睡觉，如果用margin还没有隔夜利息。 </p><p data-pid=\"b3WXL0QR\"><b>别人的经验</b></p><p data-pid=\"JolU2jda\">1. 一个职业Day Trader的感受：<a href=\"https://link.zhihu.com/?target=http%3A//www.numgame.com/c_day_trader.html\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://www.</span><span class=\"visible\">numgame.com/c_day_trade</span><span class=\"invisible\">r.html</span><span class=\"ellipsis\"></span></a> </p><p data-pid=\"fn07FhFV\">2. 为什么我不做day trade和 短线： <a href=\"https://link.zhihu.com/?target=http%3A//8ok.com/bbs/200905/tzlc/3891.shtml\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">8ok.com/bbs/200905/tzlc</span><span class=\"invisible\">/3891.shtml</span><span class=\"ellipsis\"></span></a></p><p data-pid=\"0tYMGhFE\">3. 移民生活之理财—我学、做Day Trader的经历和感受: <a href=\"https://link.zhihu.com/?target=http%3A//blog.51.ca/u-137674/2009/03/17/%25E7%25A7%25BB%25E6%25B0%2591%25E7%2594%259F%25E6%25B4%25BB%25E4%25B9%258B%25E7%2590%2586%25E8%25B4%25A2%25E2%2580%2594%25E6%2588%2591%25E5%25AD%25A6%25E3%2580%2581%25E5%2581%259Aday-trader%25E7%259A%2584%25E7%25BB%258F%25E5%258E%2586%25E5%2592%258C/\" class=\" external\" target=\"_blank\" rel=\"nofollow noreferrer\"><span class=\"invisible\">http://</span><span class=\"visible\">blog.51.ca/u-137674/200</span><span class=\"invisible\">9/03/17/%E7%A7%BB%E6%B0%91%E7%94%9F%E6%B4%BB%E4%B9%8B%E7%90%86%E8%B4%A2%E2%80%94%E6%88%91%E5%AD%A6%E3%80%81%E5%81%9Aday-trader%E7%9A%84%E7%BB%8F%E5%8E%86%E5%92%8C/</span><span class=\"ellipsis\"></span></a></p>", "excerpt": "<b>首先声明一点：炒股有风险</b>美股不像A股，股票买了不能当天卖，而是可以做T+0。所以有一种交易方式就是当天买卖，迅速进出，持股时间以分钟计有时候甚至是秒计，原则上不持股过夜。有些交易者可能偶然的买了股票当天就卖出，是intraday trading，但是不算day trade的交易方式。 Day trader基本都是机构和职业投资/投机者，每天交易几十甚至几百次。需要紧盯盘面，快进快出，一般需要4个monitor监视不同的市场。这是一项压力很大的…", "excerpt_new": "<b>首先声明一点：炒股有风险</b>美股不像A股，股票买了不能当天卖，而是可以做T+0。所以有一种交易方式就是当天买卖，迅速进出，持股时间以分钟计有时候甚至是秒计，原则上不持股过夜。有些交易者可能偶然的买了股票当天就卖出，是intraday trading，但是不算day trade的交易方式。 Day trader基本都是机构和职业投资/投机者，每天交易几十甚至几百次。需要紧盯盘面，快进快出，一般需要4个monitor监视不同的市场。这是一项压力很大的…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1487383850, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1487379975481", "type": "feed", "target": {"id": "129507907", "type": "answer", "url": "https://api.zhihu.com/answers/129507907", "voteup_count": 4841, "thanks_count": 2392, "question": {"id": "20482563", "title": "投资美股应该如何入门？", "url": "https://api.zhihu.com/questions/20482563", "type": "question", "question_type": "normal", "created": 1347803696, "answer_count": 166, "comment_count": 7, "follower_count": 8399, "detail": "本题已经收录知乎圆桌<a href=\"https://www.zhihu.com/roundtable/invest-overseas\" class=\"internal\">海外投资初探</a>，更多相关话题欢迎关注讨论", "excerpt": "本题已经收录知乎圆桌<a href=\"https://www.zhihu.com/roundtable/invest-overseas\" class=\"internal\">海外投资初探</a>，更多相关话题欢迎关注讨论", "bound_topic_ids": [65777, 70390, 152402], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "6733f12c60e7e98ea7491f20de46f79e", "name": "周源", "headline": "知乎 001 号员工", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/zhouyuan", "url_token": "<PERSON><PERSON><PERSON>", "avatar_url": "https://pic1.zhimg.com/v2-12a47dcf1cf141556018d42d2a7d26f4_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [{"type": "best_answerer", "description": "优秀答主", "topic_names": ["创业"], "topics": []}, {"type": "identity", "description": "知乎 创始人 & CEO", "topic_names": [], "topics": []}], "badge_v2": {"detail_badges": null, "merged_badges": [{"badge_status": "passed", "description": "创业话题下的优秀答主", "detail_type": "best", "icon": "", "night_icon": "", "sources": [{"avatar_path": "v2-af063bec99e4decd9aa90c38d709e46f", "avatar_url": "https://pica.zhimg.com/v2-af063bec99e4decd9aa90c38d709e46f_720w.jpg?source=32738c0c", "description": "", "id": "19550560", "name": "创业", "priority": 0, "token": "19550560", "type": "topic", "url": "https://www.zhihu.com/topic/19550560"}], "title": "优秀答主", "type": "best", "url": "https://www.zhihu.com/question/48509984"}, {"badge_status": "passed", "description": "知乎 创始人 & CEO", "detail_type": "identity", "icon": "", "night_icon": "", "sources": [], "title": "认证", "type": "identity", "url": "https://zhuanlan.zhihu.com/p/96956163"}], "title": "创业话题下的优秀答主"}, "vip_info": {"is_vip": true, "vip_icon": {"url": "https://pica.zhimg.com/v2-4812630bc27d642f7cafcd6cdeca3d7a_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-c9686ff064ea3579730756ac6c289978_r.jpg?source=5a24d060"}, "target_url": ""}, "kvip_info": {"is_vip": true, "vip_icon": {"url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060", "night_mode_url": "https://picx.zhimg.com/v2-57fe7feb4813331d5eca02ef731e12c9_r.jpg?source=5a24d060"}, "target_url": "https://www.zhihu.com/kvip/purchase"}}, "reaction_instruction": null}, "updated_time": 1498218810, "created_time": 1478074945, "author": {"id": "135e2fac8f5458841169d6f5420bb61a", "name": "柳二", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/liuer001", "url_token": "liuer001", "avatar_url": "https://picx.zhimg.com/670664ead41115bbd89f520ec5d3b993_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": false, "comment_count": 582, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"c-RRLQXC\">很久没花时间逛知乎了，最近收到一些小伙伴的私信，抱歉很多都没有回复，大部分问的是入门这一块的，这里统一做个解答吧。</p><br/><p data-pid=\"HMO4ZeHH\"><b>只收藏不点赞，不开心。。。 (╯‵□′)╯︵ ┴─┴</b> </p><br/><p data-pid=\"EnQinbqN\"><b>一、树立正确的投资理念</b></p><br/><p data-pid=\"Wrb0g2Rt\">美股遵循「价值投资」原则。价值投资是“证券分析之父、价值投资鼻祖”本杰明·格雷厄姆提出的投资法则——“雪茄烟蒂式投资”。“你满地找雪茄烟蒂，终于找到一个已经湿透的，令人讨厌的烟蒂，看上去还能抽一口，这一口可是免费的。你把它捡起来，抽上最后一口，然后扔掉，接着找下一个。这样，你不用花钱就能抽上几口免费雪茄。”</p><p data-pid=\"2UveFxRm\">如果按这种风格投资，那投资组合中必然是大量残缺不全的雪茄烟蒂，投资者频繁收进各种烟蒂，并急于将它们脱手，其结果是，手中没有一件完整的好货，并且要被迫频繁交易，并急于将它们脱手。劣质股票、分散投资、短期持有变成重大缺陷。<b>正确做法是购买有很大安全边际的优质公司，集中投资，并且长期持有</b>。</p><p data-pid=\"u0yO17jL\">本杰明·格雷厄姆提出的「内在价值学说」和「安全边际原则」至今仍是价值投资理论的基石。</p><br/><p data-pid=\"JFKbEvPR\"><b>二、美股知识分类</b></p><br/><p data-pid=\"I30lfnzU\">美股知识也可按「硬件」和「软件」来分。硬件是规则性常识，软件是投资者的个人实力、投资眼光、投资能力等。</p><p data-pid=\"R1QKx2uc\">先来看硬件。由于美股交易品种宽泛，交易规则及术语驳杂，要记忆的部分很多，我将知识点串联起来，让大家少走弯路。</p><br/><p data-pid=\"_RY92F2G\"><b>1.美股开户</b>：需结合个人情况明确券商—开户方法—交易佣金等与费用相关的情况，为入门做好准备。具体参见：</p><a href=\"https://www.zhihu.com/question/19575320/answer/114800491\" class=\"internal\">炒美股如何开户？ - 柳二的回答</a><a href=\"https://www.zhihu.com/question/20498523/answer/121324974\" class=\"internal\">美股开户可以在网上办理所有手续吗？ - 柳二的回答</a><p data-pid=\"_G4D5D-c\">2.<b>交易规则</b>：具体参见：</p><p data-pid=\"ux7xOjqp\"><a href=\"https://www.zhihu.com/question/37324334/answer/121650840\" class=\"internal\">美股交易规则有哪些？哪家美股券商上手最方便？ - 柳二的回答</a> </p><br/><p data-pid=\"ArH0N-RL\">3.<b>基础知识：</b>网上关于美股知识的介绍比较散乱，可通过几家券商建立的美股学习平台进行了解：老虎证券：<a href=\"https://link.zhihu.com/?target=http%3A//www.tigerbrokers.com/college\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">股票学院 </a>、美豹金融：<a href=\"https://link.zhihu.com/?target=https%3A//www.usmeibao.com/knowledge.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">美股大学</a></p><br/><p data-pid=\"7Jg04Mzv\">4.<b>信息查询</b>：例如查看市场数据和动态、异动股票、涨跌幅股票排名，财报数据、分红等比较重要的信息，可通过以下渠道进行了解：MoneyDJ理財網、<a href=\"https://link.zhihu.com/?target=http%3A//www.meiguwiki.com/\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">美股维基百科</a>、<a href=\"https://link.zhihu.com/?target=https%3A//www.usmeibao.com/knowledge.html\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">美股大学</a></p><br/><p data-pid=\"o-vausjN\">5.<b>系统书籍</b>：书籍的重要性不言而喻，我分门别类整理出一些关于股票方面的书籍，也是我多年的经验和心血。</p><br/><p data-pid=\"izMU4C8B\"><b>构建金融投资知识体系</b></p><br/><figure><noscript><img src=\"https://pic3.zhimg.com/v2-16a8e117cdf9e61430a4d187025e56a6_b.png\" data-rawwidth=\"580\" data-rawheight=\"421\" data-original-token=\"v2-16a8e117cdf9e61430a4d187025e56a6\" class=\"origin_image zh-lightbox-thumb\" width=\"580\" data-original=\"https://pic3.zhimg.com/v2-16a8e117cdf9e61430a4d187025e56a6_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;580&#39; height=&#39;421&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"580\" data-rawheight=\"421\" data-original-token=\"v2-16a8e117cdf9e61430a4d187025e56a6\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"580\" data-original=\"https://pic3.zhimg.com/v2-16a8e117cdf9e61430a4d187025e56a6_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-16a8e117cdf9e61430a4d187025e56a6_b.png\"/></figure><br/><p data-pid=\"9angHIHA\"><b>底层理论</b></p><br/><p data-pid=\"eJl6CKh9\">有流派将技术分析的「三大假设」、「道氏理论」、「波浪理论」等认作是投资的底层理论。我有不同看法，以上理论毕竟属于技术分析层面，更像是配合武功招式的心法，与真正修炼内功的心法不在一个层面。</p><p data-pid=\"GokcniRU\">炒股最终面对的是人，是人的游戏，所以根本还是研究人！研究人的动机、行为以及相应的策略等。所以博弈论、行为金融学才是金融投资的底层理论。</p><p data-pid=\"b0UiCPlD\">入门比较适合读《股市博弈论》，如想深入了解，请阅读《博弈论》、《行为金融学与证券投资博弈》、《现代投资行为学》。</p><br/><p data-pid=\"-Yy7Kxo5\"><b>交易方法</b></p><br/><p data-pid=\"ccDf_yTv\">炒股这几年，我有珍藏的77本书籍，需要PDF文件的朋友可以私信我。（<b>书单将放在文章最后</b>）</p><br/><p data-pid=\"a9YwAQhr\">6．美股交流社区：美股投资可以跟一些大牛交流学习，进步也会更快，国内炒美股用的比较多的是：雪球网</p><br/><p data-pid=\"cy_QHyW7\"><b>三、美股投资关键点</b> </p><br/><p data-pid=\"EmnT_FSA\"><b>1. 美股入场顺序</b>：</p><br/><p data-pid=\"zskBYCEb\">由于美股中聚集了全球众多公司，覆盖各大行业，非常繁杂，同时美股有挂钩全球大宗商品、证券市场、行业公司类的ETF基金，也有债券、期权等产品，我总结出一套<b>“渐进式入场”的方法</b>，适合入门的小伙伴。</p><br/><p data-pid=\"JctfhS8P\"><b>小白用户：小额融资融券产品</b>——<b>中概股</b>——<b>ETF基金</b>（有跟踪证券市场走势或者大宗商品、外汇标的的ETF，哪个熟悉看哪个）——<b>具成长性的细分领域</b>——<b>期权</b></p><br/><p data-pid=\"OzVPiuEw\"><b>A股股民：小额融资融券产品</b>——<b>ETF基金</b>（有跟踪A股大盘的，如：CHAU）——<b>中概股</b>——<b>具成长性的细分领域</b>——<b>期权</b></p><br/><p data-pid=\"7NGn7Cwd\"><b>做过现货/期货/黄金/原油/外汇的：ETF基金</b>（有跟踪大宗商品、外汇走势的ETF）——<b>中概股</b>——<b>具成长性的细分领域</b>——<b>期权</b></p><br/><p data-pid=\"7cP1okE4\">如何进场？答案肯定是从自己熟悉的入手。</p><p data-pid=\"zBg6p-dr\">①中国有很多的公司在美国上市，如：百度、阿里巴巴、京东、新浪、微博、网易、搜狐、唯品会等，这些在家门口的公司，我们经常接触到他们的产品或服务，特别是互联网行业的从业者，很多的美股新手也都是从这些中概股开刀的。</p><p data-pid=\"HZ77Eam8\">②你可能是A股的老股民，对A股的大盘走势有一定的了解，你可以交易跟踪A股大盘走势的ETF基金，如：CHAD（做空A股沪深300指数）。有很多炒美股的人，此前都炒过A股，对大盘有自己的判断，很多人入门都是从这里开始的。</p><p data-pid=\"DrFg5TwY\">③你可能关注宏观经济，对黄金、原油、外汇等有自己的判断，或者有做过现货、期货之类的产品，美股也有很多跟踪这一块的走势的ETF基金，丰富到你意想不到，可以从这里进场。</p><p data-pid=\"BJJkHjF6\">④你可能什么都没有玩过，像很多人余额宝都没存过，只有银行存款的怎么办呢？这一类的人群想做美股的还比较少，如果真的要玩美股，建议可以从中概股了解开始上车。</p><br/><p data-pid=\"r-TNOccV\"><b>以小白用户入场顺序为例：</b></p><br/><p data-pid=\"k-4pgYkn\">●<b>小额融资融券产品</b></p><br/><p data-pid=\"asX_CPKW\">美股的融资融券功能是指杠杆交易，体验小额融资融券产品主要是熟悉美股比较重要的交易方式：</p><br/><p data-pid=\"1hufNMNE\">A、T+0，也即日内交易，当天买入当天卖出，且无涨跌幅限制。</p><br/><p data-pid=\"jnjfVUPM\">B、融券（做空），美股只要流动性好的股票，大于1美金以上的股票，交易所都会配额，所以一般都有做空机制。投资者可以融入证券卖出，以保证金作为抵押，在规定的时间内，再以证券归还。</p><br/><p data-pid=\"cBUqMqet\">C、融资（杠杆），融资是利用杠杆以小搏大的特性入市交易；美国清算所都会给股票指定杠杆，清算所定多少就是多少，不过一般都有5-6倍。正常配给海外（中国）市场账户一般是2倍，有券商可以做到5倍左右，则是借助清算行的优势。</p><br/><p data-pid=\"57WHRrhI\">美股有做多和做空两个方向，而A股没有做空机制，美股上手阶段建议尝试小额杠杆，浅显摸下美股市场，了解做多和做空两种模式的特性，尤其是以往没有接触过的做空模式。新手在熟悉了T+0、融资融券后，对美股市场的交易规则应该就比较熟悉了。</p><p data-pid=\"dhQePNjc\">炒股前期是交学费的阶段，我们目的是缩短这个周期，缩减这个成本。要真金白银的干才能体验到割肉的痛苦，模拟炒股不知道肉痛，你不会有扇自己两巴掌说再也不炒股了，再也不满仓了的念头。那么，你可以入金少一点啊，比如500-1000美金，玩一波来熟悉规则和波动。但我还是不建议20天内入金太多，毕竟大多数人（包括我）看到账户有钱都会控制不住，总感觉满仓才能赚钱。</p><p data-pid=\"Qb35xKdA\">小额玩交易来熟悉市场的规则，很多券商是0美金的入金门槛的，如：美豹金融、富途证券。如果用富途，其模拟炒股是我用过最好的。如果选择的是美豹金融，可以先小额入金玩玩美豹金融的多空杠杆，50美金就可以入场，用于新手熟悉规则是可以，但切记不要恋战，要想在多空杠杆赚钱，需要对标的股票非常熟悉，有非常强的盘感，你的目的是熟悉：T+0、融资、融券。</p><br/><p data-pid=\"07BSsBdX\">●<b>中概股</b></p><p data-pid=\"gRX1oER5\">中国概念股是外资看好中国经济成长而对所有在海外上市的中国股票的称呼。相信大部分知道美股的中国股民，都是从听说“新浪”、“搜狐”、“网易”、“唯品会”、“百度”、“京东”、“阿里巴巴”等赴美上市中国企业开始的，这类公司统称为“中概股”。（为什么买这类企业，前面有介绍，此处不赘述）</p><br/><p data-pid=\"fCQMSPzQ\">●<b>ETF基金</b></p><p data-pid=\"Py-Kuybs\">美股市场的ETF，主要是跟踪股市走势、大宗商品走势的基金，通俗点叫“一篮子股票”。</p><p data-pid=\"gzV779qP\">分析一支ETF基金给大家做参考。以“沪深300”为例，它由300支股票加权而得到走势，由一个大基金（专业团队）买下这300支股票，也即团购股票，进行专门管理，再拆分成小块卖给你。</p><p data-pid=\"HkJRb_fF\">管理这支ETF的基金公司会分析它的基本面，选择增持或减持。例如，黄金的走势比较强，则会高配；化工板块比较弱，则会减持改为持有现金。</p><p data-pid=\"uw9Q2DXA\">由于ETF基金公司会分析股票走势，散户只需跟随就好，相应会比较简单。因此入手阶段交易几支ETF基金，也有利于宏观面的分析和理解。如果你曾经做过期货、大宗商品、原油、黄金、白银，那美股市场同样有跟踪这些商品的ETF股票供你选择。</p><br/><p data-pid=\"827hOmR2\">●<b>具成长性的细分领域</b></p><p data-pid=\"7n2f3UJs\">美股有近万只股票产品投资供你选择，但记住我们的目的：赚钱。除了前面你投资的熟悉的行业或股票（之前熟悉的不一定有较高的成长性，如：搜狐、人人，为了盈利，你需要去探索有价值，值得投资的公司），还有很多比较有前景的行业，选择你认为较有前景的行业，吃透选择的行业，挑选几个公司长期关注研究，相信会有不小的收获。</p><br/><p data-pid=\"lWEH51mI\">●<b>期权</b></p><p data-pid=\"B2lXBYYh\">在美股市场，相对于股票和ETF基金而言，期权是相对复杂、风险较高的投资产品，有些老司机可能喜欢玩刺激的，但期权不适合新手入门时候投资了解，这里不做过多的介绍。</p><br/><p data-pid=\"LLT9Mzt3\">注：最后强调一点，美股入场顺序是让大家熟悉整个过程用的，仅供参考。但炒美股一定要非常熟悉规则，体验市场波动和估值方式。我们来股市的<b>目标只有一个——赚钱</b>。所以没有必要一直按照这个顺序去走到底，假设你很熟悉中概股，如果能在这里持续赚钱，走到这一步就足够了，其他不熟悉的领域或产品，没把握赚钱的别碰，强调的只有一个：<b>吃透你熟悉的公司和行业</b>。</p><br/><p data-pid=\"FITyRmWd\"><b>2. “如何选股”：怎样判断一支股票能不能买？</b></p><br/><p data-pid=\"rmEI3-Yz\">A、分析公司基本面，也即盈利模式、管理层团队、股权结构、财务报表等；</p><p data-pid=\"a-Ls1DG6\">假设整体环境无大变动，要投资一家公司时，简单问自己几个问题：</p><br/><p data-pid=\"_7Rvfjdw\"><b>①市场有多大？</b></p><p data-pid=\"idprHaM1\"><b>②这家公司能占领多大的市场份额？</b></p><p data-pid=\"SUspnyZA\"><b>③市场什么时候成熟？</b></p><p data-pid=\"VvtmaxMj\"><b>④该公司的领导者是谁？能带领团队保持稳定得增长吗？</b></p><p data-pid=\"f2e34Il3\"><b>⑤该公司是否有足够的条件维持未来的发展？</b></p><br/><p data-pid=\"NDqSQPeF\">以上是投资一个公司时对其主业务要了解的基础，接下来还有一个要关注的，如：亚马逊不仅B2C有不错的成绩，云计算也有绝对领先的地位；谷歌不仅搜索是全球霸主，还有YouTube、安卓、chrome等牛逼的产品，现在在发力研究人工智能。这里要表达的一点是：除了主业务，这个公司还在干什么？（这里要关注的是跨界市场探索的成败，同时再过一遍上面的⑤个点。）案例是百度的O2O、新浪的新浪微博。</p><br/><p data-pid=\"uO9JtrM2\">B、结合K线走势和行业属性，如果上市时间比较久，K线基本能反应出公司基本面，在脑海中形成一个大的轮廓；</p><br/><p data-pid=\"7eWOmUKO\">C、根据公司行业地位、成长性预期，最终做一个判断。</p><p data-pid=\"gLMcmQTk\">以“阿里巴巴”为例：电商行业龙头企业，公司管理层和管理效率是最大优势，财报健康，可以考虑买入，但是作为大盘股（市值大于500亿美金），弹性会差一些，会涨的慢一些。</p><p data-pid=\"5RLjvTgk\">再如“网易”：“网易”是中国最赚钱的互联网公司，连地产界大佬都羡慕不已。网易的盈利模式：“广告+在线网游”，来自游戏、广告和短信收入，加上收费邮箱、个人主页的补充，构成了网易的盈利模式。网易独自研发与运营网游《大话西游Online 2》和《梦幻西游》取得空前成功，创造了网络游戏的神话，网络游戏收入占其业务收入的比重已达到80%以上。</p><p data-pid=\"rIAsdJxt\">网易2016年第一季度净收入79.15亿元人民币（12.28亿美元），同比增长116.3%；净利润24.61亿元人民币（3.82亿美元），同比增长94.6%。这也直接影响到网易的股价一路攀升。</p><br/><figure><noscript><img src=\"https://pic3.zhimg.com/v2-316e44cbf6aabb82b9d7c6a1f430ccec_b.png\" data-rawwidth=\"1122\" data-rawheight=\"531\" data-original-token=\"v2-316e44cbf6aabb82b9d7c6a1f430ccec\" class=\"origin_image zh-lightbox-thumb\" width=\"1122\" data-original=\"https://pic3.zhimg.com/v2-316e44cbf6aabb82b9d7c6a1f430ccec_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1122&#39; height=&#39;531&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"1122\" data-rawheight=\"531\" data-original-token=\"v2-316e44cbf6aabb82b9d7c6a1f430ccec\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1122\" data-original=\"https://pic3.zhimg.com/v2-316e44cbf6aabb82b9d7c6a1f430ccec_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-316e44cbf6aabb82b9d7c6a1f430ccec_b.png\"/></figure><p data-pid=\"P1ZsuMTl\">网易2007年至今美股走势</p><br/><p data-pid=\"OSWfaHEp\">网易的实例恰好印证了投资界大神总结出的投资心法。</p><p data-pid=\"UvCyNZQE\">彼得·林奇在其《彼得·林奇的成功投资》中说——“最终决定一只股票的命运的还是收益。人们可能会把赌注押在股票短期波动上，但从长期来看公司收益的波动最终决定了股价的波动。”</p><p data-pid=\"xPOU3ubj\">格雷厄姆说——“短期而言，股票市场是一台投票机，反映了一个只需要金钱却不考虑智商高低和情绪稳定性好坏的选票登记比赛的结果，但是长期而言，股票市场却是一台称重机。”</p><p data-pid=\"W_LOG5AF\">价值投资最成功的股神巴菲特用其一生的投资实践证明——“市场可能会在一段时期内忽视公司的成功，但最终一定会用股价加以肯定。正如格雷厄姆所说：股市短期是一台投票机，但股市长期是一台称重机。”</p><br/><p data-pid=\"bJDsXtcG\"><b>四、无数大坑</b></p><br/><p data-pid=\"e-zKRi_p\">人们通常因为无法克服惯性思维而一再遭受损失，因此花在错误道路上的时间，往往比花在正确道路上的时间多得多。在此，结合我的投资心得，安利给大家一些投资规则。</p><br/><p data-pid=\"7UIbAc3r\"><b>了解你所持有的股票</b></p><p data-pid=\"FnWsbFKS\">有个现象我很纳闷，人们买冰箱前起码要对比10台以上，恨不得逛全城、全网的家电行。但不知道什么原因，他们对股市感到如此神秘，连一些士司机的小道消息都会左右他们，结果一买进去就亏个底儿掉。</p><p data-pid=\"arhEd_Or\">正确的做法是，你必须了解你要买进的股票，而不是拍脑袋认为它要涨或跌，更不可以撞大运式的选择。这听起来很简单，但能做到这一点的人少之又少，有一个简单的衡量标准，你必须在两分钟内，向一个12岁的孩子解释清楚你购买这支股票的原因，如果你无法做到这一点，那你的决策很危险。</p><br/><p data-pid=\"Rlg96dnv\"><b>五年级的数学足以满足投资所需，花15秒时间在资产负债表上</b></p><p data-pid=\"fiqWXCsl\">股市上用到的数学其实非常简单，五年级数学，已经可以在股市上做得很好。但你需要花点儿时间看看资产负债表，你看看左边，再看看右边，右边一团糟，左边很可疑。不用花太多时间你就知道这家公司不值得投资，如果你看不到任何债务，你清楚这家公司会相当令人满意。</p><br/><p data-pid=\"Cc8V5cT5\"><b>我能赔多少？股价只有3美元</b></p><p data-pid=\"ZYR7Mmpp\">这一点非常重要，请大家警惕，我们用基本的数学常识来做个算术吧，如果你买入两支股，一支30美元，一支3美元，你各投入1万美元，如果它们全都跌成0，你赔的钱完全一样！人们就是不相信这一点，请你算一算好吗？</p><p data-pid=\"G6dZ2E8s\">请大家注意观察那些做空的人，他们不可能在股价60、70美元并且仍然上涨的时候做空，他们只会在股价下跌的时候进场，在跌到3美元左右的时侯卖空。那又是谁在接盘这些做空的股票呢？就是那些说“股价只有3美元，还能跌到哪去”的人！</p><p data-pid=\"2VOZ5LOI\">美股有很多一天涨20%+的股票，甚至翻几倍的。众多人专门挑股价低的股票买，期待能暴涨。我只能说too young，重点警告，市值低于5亿美金的先别碰，股价低于2美金的千万别碰，除非你有内幕。</p><br/><p data-pid=\"yYlz74IO\"><b>一定要止损</b></p><p data-pid=\"SmFI839k\">千万不可抱着股价已经跌了这么多，还能跌多少的思路来炒美股，美股好的公司会越好，差的公司会越差，有些会退到粉单市场，而有些会直接退市，再也找不到。所以遇到差公司，千万不可抱有侥幸思维，要立即止损。</p><p data-pid=\"Zz5u50pR\">有些人说没事，跌下去的都会反弹回来，请问退市了还怎么反弹？一定要记住，差的只会更差！</p><br/><p data-pid=\"T_vL6-eg\"><b>因为没有买入我赔了多少钱？</b></p><p data-pid=\"oBNXgAEk\">这个情况很幼稚，很多人会说，我明明看好某支股，结果没买，你看我陪了多少钱？这个说法曾经也一直困扰着我。记住：如果你不持有某支上涨的股，赶快查你的银行账户，你没有损失一分钱！只有当你持有的股票下跌的时候，你才会赔钱。为踏空感到烦恼的人多得难以置信，如果股市一天之内上涨了50个点，就有人说我刚损失了280亿美元，这是不成立的～</p><br/><p data-pid=\"SUGY5MQD\"><b>股票涨了这么多了，可以做空了吧</b></p><p data-pid=\"j4RAJ4a6\">既然股价已经涨了这么多了，怎么可能还会涨得更高？来看沃尔玛今天的股价是72.09，比起1970年的80美分，上涨了90.1倍。我要说的是，不要卷入股票评论员对股票表现的技术分析，股票上涨，他们会说股价过于膨胀。但如果你喜欢这家公司，不应该对你造成干扰，股票的历史表现和未来表现无关，公司的绩效才与未来表现有关。</p><br/><p data-pid=\"YQVvl9az\">附：我收藏的77本投资相关的书。</p><br/><p data-pid=\"rlF22Ol_\"><b>（1）投机理论</b></p><br/><figure><noscript><img src=\"https://pic3.zhimg.com/v2-4a5629364491a14b05ac5695559142d0_b.png\" data-rawwidth=\"476\" data-rawheight=\"402\" data-original-token=\"v2-4a5629364491a14b05ac5695559142d0\" class=\"origin_image zh-lightbox-thumb\" width=\"476\" data-original=\"https://pic3.zhimg.com/v2-4a5629364491a14b05ac5695559142d0_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;476&#39; height=&#39;402&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"476\" data-rawheight=\"402\" data-original-token=\"v2-4a5629364491a14b05ac5695559142d0\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"476\" data-original=\"https://pic3.zhimg.com/v2-4a5629364491a14b05ac5695559142d0_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-4a5629364491a14b05ac5695559142d0_b.png\"/></figure><figure><noscript><img src=\"https://pic1.zhimg.com/v2-991d0632a6c6203d86af84f91da9075c_b.png\" data-rawwidth=\"484\" data-rawheight=\"215\" data-original-token=\"v2-991d0632a6c6203d86af84f91da9075c\" class=\"origin_image zh-lightbox-thumb\" width=\"484\" data-original=\"https://pic1.zhimg.com/v2-991d0632a6c6203d86af84f91da9075c_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;484&#39; height=&#39;215&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"484\" data-rawheight=\"215\" data-original-token=\"v2-991d0632a6c6203d86af84f91da9075c\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"484\" data-original=\"https://pic1.zhimg.com/v2-991d0632a6c6203d86af84f91da9075c_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-991d0632a6c6203d86af84f91da9075c_b.png\"/></figure><br/><p data-pid=\"w5boa5Oq\"><b>（2）交易策略</b></p><p data-pid=\"vEOQyvIi\">入门书籍非《聪明的投资者》莫属，想深入了解可以从下图中选择阅读。</p><br/><figure><noscript><img src=\"https://pic2.zhimg.com/v2-93846c06cbd1c0996b53187f520a82dd_b.jpg\" data-rawwidth=\"482\" data-rawheight=\"310\" data-original-token=\"v2-93846c06cbd1c0996b53187f520a82dd\" class=\"origin_image zh-lightbox-thumb\" width=\"482\" data-original=\"https://pic2.zhimg.com/v2-93846c06cbd1c0996b53187f520a82dd_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;482&#39; height=&#39;310&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"482\" data-rawheight=\"310\" data-original-token=\"v2-93846c06cbd1c0996b53187f520a82dd\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"482\" data-original=\"https://pic2.zhimg.com/v2-93846c06cbd1c0996b53187f520a82dd_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-93846c06cbd1c0996b53187f520a82dd_b.jpg\"/></figure><br/><p data-pid=\"Z8TItqdt\"><b>（3）技术战法</b></p><p data-pid=\"nMKwocYh\">这里推荐《筹码分布》、《交易员量价实战技法》。</p><br/><figure><noscript><img src=\"https://pic1.zhimg.com/v2-3096dd5976b6fdc189c84184927ac344_b.png\" data-rawwidth=\"480\" data-rawheight=\"406\" data-original-token=\"v2-3096dd5976b6fdc189c84184927ac344\" class=\"origin_image zh-lightbox-thumb\" width=\"480\" data-original=\"https://pic1.zhimg.com/v2-3096dd5976b6fdc189c84184927ac344_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;480&#39; height=&#39;406&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"480\" data-rawheight=\"406\" data-original-token=\"v2-3096dd5976b6fdc189c84184927ac344\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"480\" data-original=\"https://pic1.zhimg.com/v2-3096dd5976b6fdc189c84184927ac344_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-3096dd5976b6fdc189c84184927ac344_b.png\"/></figure><figure><noscript><img src=\"https://picx.zhimg.com/v2-3beb251f7d8d618f1ee5ec00a7ea456d_b.png\" data-rawwidth=\"480\" data-rawheight=\"397\" data-original-token=\"v2-3beb251f7d8d618f1ee5ec00a7ea456d\" class=\"origin_image zh-lightbox-thumb\" width=\"480\" data-original=\"https://picx.zhimg.com/v2-3beb251f7d8d618f1ee5ec00a7ea456d_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;480&#39; height=&#39;397&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"480\" data-rawheight=\"397\" data-original-token=\"v2-3beb251f7d8d618f1ee5ec00a7ea456d\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"480\" data-original=\"https://picx.zhimg.com/v2-3beb251f7d8d618f1ee5ec00a7ea456d_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-3beb251f7d8d618f1ee5ec00a7ea456d_b.png\"/></figure><br/><p data-pid=\"BchwR1nk\"><b>（4）交易系统</b></p><p data-pid=\"OuCS7RlN\">入门推荐《海龟交易法则》，这本书十分经典，没看过的强烈建议一定要看。</p><br/><figure><noscript><img src=\"https://pic3.zhimg.com/v2-a84065105698a4cb36187fb5441f8e4c_b.jpg\" data-rawwidth=\"482\" data-rawheight=\"184\" data-original-token=\"v2-a84065105698a4cb36187fb5441f8e4c\" class=\"origin_image zh-lightbox-thumb\" width=\"482\" data-original=\"https://pic3.zhimg.com/v2-a84065105698a4cb36187fb5441f8e4c_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;482&#39; height=&#39;184&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"482\" data-rawheight=\"184\" data-original-token=\"v2-a84065105698a4cb36187fb5441f8e4c\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"482\" data-original=\"https://pic3.zhimg.com/v2-a84065105698a4cb36187fb5441f8e4c_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-a84065105698a4cb36187fb5441f8e4c_b.jpg\"/></figure><br/><p data-pid=\"dr_oQTAC\"><b>（5）交易心理</b></p><p data-pid=\"bglvK8xb\">这一块的知识对任何交易者都非常重要，不亚于交易方法。通常可以从“提升交易者心态”和“研究市场心理”两个维度去阅读学习。</p><p data-pid=\"I5U7ZQjW\">提升心态方面，有兴趣的可以看《交易赢家的心理优势》。</p><p data-pid=\"Loefeiwl\">市场心理方面，《金融心理学》、《交易心理分析》、《股市心理学》、《股市心理博弈》，如果在底层理论学习中进行了深入研究，这方面学习速度应该非常快。</p><br/><figure><noscript><img src=\"https://pic4.zhimg.com/v2-b744ad184ce828c80714c884700d8975_b.png\" data-rawwidth=\"480\" data-rawheight=\"259\" data-original-token=\"v2-b744ad184ce828c80714c884700d8975\" class=\"origin_image zh-lightbox-thumb\" width=\"480\" data-original=\"https://pic4.zhimg.com/v2-b744ad184ce828c80714c884700d8975_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;480&#39; height=&#39;259&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"480\" data-rawheight=\"259\" data-original-token=\"v2-b744ad184ce828c80714c884700d8975\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"480\" data-original=\"https://pic4.zhimg.com/v2-b744ad184ce828c80714c884700d8975_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-b744ad184ce828c80714c884700d8975_b.png\"/></figure><br/><p data-pid=\"RHvwBAKx\"><b>（6）交易对手研究</b></p><p data-pid=\"GSaoJWK1\">这一块可分为“主力研究”和“散户研究”。由于前面「交易心理」的市场心理部分，大多数都是研究散户的心理行为，所以这里重点放在主力研究上面。但市面上这一类型书籍大多流于片面，内容大多来自作者的主观臆测，所以了解一下就好。例如《庄家那些事儿》、《主力行为盘口解密》等等。</p><br/><figure><noscript><img src=\"https://picx.zhimg.com/v2-2d1224238bf48fea97078c172fd85b6b_b.jpg\" data-rawwidth=\"481\" data-rawheight=\"126\" data-original-token=\"v2-2d1224238bf48fea97078c172fd85b6b\" class=\"origin_image zh-lightbox-thumb\" width=\"481\" data-original=\"https://picx.zhimg.com/v2-2d1224238bf48fea97078c172fd85b6b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;481&#39; height=&#39;126&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"481\" data-rawheight=\"126\" data-original-token=\"v2-2d1224238bf48fea97078c172fd85b6b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"481\" data-original=\"https://picx.zhimg.com/v2-2d1224238bf48fea97078c172fd85b6b_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-2d1224238bf48fea97078c172fd85b6b_b.jpg\"/></figure><br/><p data-pid=\"5vRn_K8t\"><b>（7）高手实战日志</b></p><p data-pid=\"KoTRpTm9\">读这个需要耐心，仔细阅读之后肯定有收获。特别适合有一定理论基础并经历过实战的读者。推荐《华尔街操盘手日记》、《多空交易日志》。</p><br/><figure><noscript><img src=\"https://pic2.zhimg.com/v2-da76e54de4488d6d246bd574bb846e2b_b.jpg\" data-rawwidth=\"480\" data-rawheight=\"91\" data-original-token=\"v2-da76e54de4488d6d246bd574bb846e2b\" class=\"origin_image zh-lightbox-thumb\" width=\"480\" data-original=\"https://pic2.zhimg.com/v2-da76e54de4488d6d246bd574bb846e2b_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;480&#39; height=&#39;91&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"480\" data-rawheight=\"91\" data-original-token=\"v2-da76e54de4488d6d246bd574bb846e2b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"480\" data-original=\"https://pic2.zhimg.com/v2-da76e54de4488d6d246bd574bb846e2b_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-da76e54de4488d6d246bd574bb846e2b_b.jpg\"/></figure><br/><p data-pid=\"dmmXOA3E\"><b>（8）投资大师经历</b></p><p data-pid=\"_cK0cMQJ\">市面上这类书主要有回忆录、传记、访谈等形式。《股票作手回忆录》是每个投机者必看的书。其余有兴趣的可以从下图中选择阅读。</p><br/><figure><noscript><img src=\"https://pic1.zhimg.com/v2-e3d61146eb6408b7cc6291f5822c65a0_b.jpg\" data-rawwidth=\"482\" data-rawheight=\"402\" data-original-token=\"v2-e3d61146eb6408b7cc6291f5822c65a0\" class=\"origin_image zh-lightbox-thumb\" width=\"482\" data-original=\"https://pic1.zhimg.com/v2-e3d61146eb6408b7cc6291f5822c65a0_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;482&#39; height=&#39;402&#39;&gt;&lt;/svg&gt;\" data-rawwidth=\"482\" data-rawheight=\"402\" data-original-token=\"v2-e3d61146eb6408b7cc6291f5822c65a0\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"482\" data-original=\"https://pic1.zhimg.com/v2-e3d61146eb6408b7cc6291f5822c65a0_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-e3d61146eb6408b7cc6291f5822c65a0_b.jpg\"/></figure><p data-pid=\"Yu2yZoST\"><b>私信要PDF也不点赞的，好忧桑 </b>╮(╯Д╰)╭  </p>", "excerpt": "很久没花时间逛知乎了，最近收到一些小伙伴的私信，抱歉很多都没有回复，大部分问的是入门这一块的，这里统一做个解答吧。 <b>只收藏不点赞，不开心。。。 (╯‵□′)╯︵ ┴─┴</b> <b>一、树立正确的投资理念</b> 美股遵循「价值投资」原则。价值投资是“证券分析之父、价值投资鼻祖”本杰明·格雷厄姆提出的投资法则——“雪茄烟蒂式投资”。“你满地找雪茄烟蒂，终于找到一个已经湿透的，令人讨厌的烟蒂，看上去还能抽一口，这一口可是免…", "excerpt_new": "很久没花时间逛知乎了，最近收到一些小伙伴的私信，抱歉很多都没有回复，大部分问的是入门这一块的，这里统一做个解答吧。 <b>只收藏不点赞，不开心。。。 (╯‵□′)╯︵ ┴─┴</b> <b>一、树立正确的投资理念</b> 美股遵循「价值投资」原则。价值投资是“证券分析之父、价值投资鼻祖”本杰明·格雷厄姆提出的投资法则——“雪茄烟蒂式投资”。“你满地找雪茄烟蒂，终于找到一个已经湿透的，令人讨厌的烟蒂，看上去还能抽一口，这一口可是免…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "disallowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1487379975, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}, {"id": "1486762056161", "type": "feed", "target": {"id": "18558231", "type": "answer", "url": "https://api.zhihu.com/answers/18558231", "voteup_count": 451, "thanks_count": 179, "question": {"id": "19983067", "title": "如何在工作的头三年里让自己变得强大？", "url": "https://api.zhihu.com/questions/19983067", "type": "question", "question_type": "normal", "created": 1324463209, "answer_count": 78, "comment_count": 2, "follower_count": 8853, "detail": "", "excerpt": "", "bound_topic_ids": [1537, 3479, 3946], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "23d98d141ef71164f8a453915d6aeef1", "name": "魏丽鹃", "headline": "嗨，我是一名在校生，想跟大家学习，了解下互联网，有问题互相交流~~学习，谢谢通过。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/wei-li-juan", "url_token": "wei-li-juan", "avatar_url": "https://picx.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": {}}, "updated_time": 1378705595, "created_time": 1377481200, "author": {"id": "92174acd59102bbff13649492de7b1ad", "name": "nightwish010", "headline": "……", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/ming-ming-51-18", "url_token": "ming-ming-51-18", "avatar_url": "https://pic1.zhimg.com/v2-0be56528896723bbfb16c9967eae9413_l.jpg?source=5a24d060&needBackground=1", "gender": 1, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 51, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"xUN6m7FI\">尽量简单来说，这事说起来能说到天黑。简单点来说</p><ol><li data-pid=\"DrMmsBmz\">安逸的工作（公司）不要做。</li><li data-pid=\"MA7V00p5\">可以累的跟孙子一样，但是一定要能学到东西。</li><li data-pid=\"zrNror8q\">有机会能进大公司（或你认为特别牛逼的公司），就是擦地板也要去。</li><li data-pid=\"EN2a2Adc\">控制脾气、一定要控制。</li><li data-pid=\"Ybh1m-eM\">理想要好高骛远，实现理想脚踏实地</li><li data-pid=\"gwBDPM05\">记住！公司里每个大爷都当过孙子的时候。</li><li data-pid=\"MTwH3EsC\">公司里别站队</li><li data-pid=\"EILrbfIM\">加班一定要做，加班费一定要拿（不给加班费的公司可以直接否掉）</li><li data-pid=\"ybEKqG73\">抓住在领导面前得瑟才能的机会，但是不要得瑟性格。</li></ol><p data-pid=\"X70sEj0c\">-------------------------------------------------------</p><p data-pid=\"398xA27D\">补充几点<strong>（为了60个赞而努力）</strong></p><ol><li data-pid=\"L0A4_lBw\">再不靠谱的领导，身上都有些值得学的东西（会关注领导的小细节，会把工作都分配出去让自己清闲）。这些不一定要自己以后用在别人身上，以后可以多开这些</li><li data-pid=\"pT9gROWO\">可以和同事交朋友，也可以交心。但是不要交流太多公司吐槽事情（比如吐槽领导为人，谁谁谁吃回扣）</li><li data-pid=\"utlSNZKC\">办公室恋爱很好，但是也很容易断送这个公司的前程。如果有最好低调</li><li data-pid=\"Kpdz7jt3\">公司不是你的家，领导不是你父母。别跟回家那么自在，也别没事跟领导抱怨。</li><li data-pid=\"5T423Ujy\">头三年（一般我认为是30岁以前）别惦记薪水，惦记职业规划和能学到多少东西。（能力有了，票子自然就来了。同理女人漂亮了，追的男人就多了，自然优胜劣汰了）</li></ol><p data-pid=\"4ZuWyOn3\">-------------------------------------------------------</p><p data-pid=\"xZZAk0Kx\">综述： 毕业生很少有心平气和，肯踏实工作的。大部分自我感觉可以改变全宇宙似地（往往这些基本没几个做得好的）。收起自己的性格，把才华都展示出来。同事也好领导也好，即使最后你离开了，以后也许会有人推荐你去他们朋友的公司或得到其他推荐的机会。</p><p data-pid=\"0PEY5k90\">---------------------------------------------</p><p data-pid=\"4ZajgVpv\">之前知乎有一个讲实习生的提问，你找找。里面有更多详细可用信息。</p>", "excerpt": "尽量简单来说，这事说起来能说到天黑。简单点来说 安逸的工作（公司）不要做。可以累的跟孙子一样，但是一定要能学到东西。有机会能进大公司（或你认为特别牛逼的公司），就是擦地板也要去。控制脾气、一定要控制。理想要好高骛远，实现理想脚踏实地记住！公司里每个大爷都当过孙子的时候。公司里别站队加班一定要做，加班费一定要拿（不给加班费的公司可以直接否掉）抓住在领导面前得瑟才能的机会，但是不要得瑟性格。---------…", "excerpt_new": "尽量简单来说，这事说起来能说到天黑。简单点来说 安逸的工作（公司）不要做。可以累的跟孙子一样，但是一定要能学到东西。有机会能进大公司（或你认为特别牛逼的公司），就是擦地板也要去。控制脾气、一定要控制。理想要好高骛远，实现理想脚踏实地记住！公司里每个大爷都当过孙子的时候。公司里别站队加班一定要做，加班费一定要拿（不给加班费的公司可以直接否掉）抓住在领导面前得瑟才能的机会，但是不要得瑟性格。---------…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_VOTEUP_ANSWER", "created_time": 1486762056, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pica.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "赞同了回答", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1486762056161&page_num=78"}}