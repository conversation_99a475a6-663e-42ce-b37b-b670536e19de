{"data": [{"id": "1689078608806", "type": "feed", "target": {"id": "3114130133", "type": "answer", "url": "https://api.zhihu.com/answers/3114130133", "voteup_count": 2, "thanks_count": 7, "question": {"id": "581556221", "title": "ChatGPT真的那么牛吗？", "url": "https://api.zhihu.com/questions/581556221", "type": "question", "question_type": "normal", "created": 1675176147, "answer_count": 3709, "comment_count": 83, "follower_count": 14711, "detail": "<p>我实际体验就是一、交了费还有广告；二、没事卡机不动；三、问问题不是傻缺答就是说塞车了稍后联系…不知道它怎么写论文的！难道还要考使用者的智商吗？</p>", "excerpt": "我实际体验就是一、交了费还有广告；二、没事卡机不动；三、问问题不是傻缺答就是说…", "bound_topic_ids": [350, 180476, 188574, 2396255], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "9961d4f0b6ff02a51166ed6744035546", "name": "<PERSON><PERSON><PERSON><PERSON>", "headline": "立天地之间，处人群之中，猜一字。", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/shula-last", "url_token": "shula-last", "avatar_url": "https://pic1.zhimg.com/v2-41b9a522b0188075686febe4f8caf2f2_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1689078607, "created_time": 1689078607, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p></p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-e4bd6f9e530818b0235416af62ec2f58_b.jpg\" data-size=\"normal\" data-rawwidth=\"900\" data-rawheight=\"383\" data-original-token=\"v2-7d8e1f7fd45883ab09c2456d6fec979b\" data-default-watermark-src=\"https://picx.zhimg.com/v2-64ec1f2d343af2e09caaa46530034c61_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"900\" data-original=\"https://pic1.zhimg.com/v2-e4bd6f9e530818b0235416af62ec2f58_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;900&#39; height=&#39;383&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"900\" data-rawheight=\"383\" data-original-token=\"v2-7d8e1f7fd45883ab09c2456d6fec979b\" data-default-watermark-src=\"https://picx.zhimg.com/v2-64ec1f2d343af2e09caaa46530034c61_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"900\" data-original=\"https://pic1.zhimg.com/v2-e4bd6f9e530818b0235416af62ec2f58_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-e4bd6f9e530818b0235416af62ec2f58_b.jpg\"/><figcaption>| 撰文：凯莉    编辑：路炀</figcaption></figure><p data-pid=\"6FBrlVhK\">家人们谁懂啊！今天，ChatGPT「代码解释器」（Code Interpreter）终于全面开放了！</p><p data-pid=\"1RpM17Am\"><b>就在7月9日，Code Interpreter 测试版正式向所有Plus用户开放。</b></p><p data-pid=\"rfPhy1_r\">为什么这是一个大消息呢？</p><h2><b>Code Interpreter 是什么？</b></h2><p data-pid=\"eibX_Oqb\">首先，你要知道这个东西到底是什么。</p><p data-pid=\"47ngqUJG\"><b>Code Interpreter 是一个官方的 ChatGPT 插件</b>，用于数据分析、图像转换、代码编辑等功能。它允许用户运行代码、上传数据并进行各种操作。</p><p data-pid=\"s4TG5GQt\">通过 Code Interpreter，用户可以执行和测试代码、处理和分析数据，以及进行其他与编程和数据处理相关的任务。 从 OpenAI 的初步用户研究中，已经发现特别适合 Code Interpreter 的<b>使用场景</b>，包括：</p><ul><li data-pid=\"Aa_yjFKx\"><b>解决数学问题，包括定量和定性分析</b></li><li data-pid=\"CsEIe2Bm\"><b>进行数据分析和可视化</b></li><li data-pid=\"xps_DDn8\"><b>在不同格式之间进行文件转换</b></li></ul><p data-pid=\"2L3iQ5sW\">这个新功能到底有多少强大功能，我估计大家还需要一些时间来发掘。</p><h2><b>怎么用？</b></h2><p data-pid=\"JXFPle-9\">首先，你需要通过设置来启用 Code Interpreter，才能在聊天中使用它。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-9c1645289c51e439f718254a081cd2b6_b.jpg\" data-size=\"normal\" data-rawwidth=\"677\" data-rawheight=\"431\" data-original-token=\"v2-7aa51b05cc5460ba8e319a9e656948fc\" data-default-watermark-src=\"https://pica.zhimg.com/v2-0e6c5926674d86886055e6af65660f2e_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"677\" data-original=\"https://pic3.zhimg.com/v2-9c1645289c51e439f718254a081cd2b6_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;677&#39; height=&#39;431&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"677\" data-rawheight=\"431\" data-original-token=\"v2-7aa51b05cc5460ba8e319a9e656948fc\" data-default-watermark-src=\"https://pica.zhimg.com/v2-0e6c5926674d86886055e6af65660f2e_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"677\" data-original=\"https://pic3.zhimg.com/v2-9c1645289c51e439f718254a081cd2b6_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-9c1645289c51e439f718254a081cd2b6_b.jpg\"/><figcaption>| 打开 “ Beta features ”</figcaption></figure><p data-pid=\"iX5TLyuX\">启用以后，创建一个新的聊天窗口，你就能使用这个新工具了。</p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-1a55ba723173e69f8e5f0a1d1083ccef_b.jpg\" data-size=\"normal\" data-rawwidth=\"509\" data-rawheight=\"495\" data-original-token=\"v2-4213d01050627c3b128892a252c5083b\" data-default-watermark-src=\"https://picx.zhimg.com/v2-47eea0bc7c27047a08292e052e6ae5bd_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"509\" data-original=\"https://picx.zhimg.com/v2-1a55ba723173e69f8e5f0a1d1083ccef_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;509&#39; height=&#39;495&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"509\" data-rawheight=\"495\" data-original-token=\"v2-4213d01050627c3b128892a252c5083b\" data-default-watermark-src=\"https://picx.zhimg.com/v2-47eea0bc7c27047a08292e052e6ae5bd_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"509\" data-original=\"https://picx.zhimg.com/v2-1a55ba723173e69f8e5f0a1d1083ccef_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-1a55ba723173e69f8e5f0a1d1083ccef_b.jpg\"/><figcaption>| 勾选 “ Code Interpreter ”</figcaption></figure><h2><b>应用案例</b></h2><p data-pid=\"3MD7fcna\">人们现在已经在用它做什么呢？</p><h3><b>一、数据分析</b></h3><p data-pid=\"7K3RzDbj\">有人用它大幅提高了数据分析和可视化的效率。数据科学家使用数据来理解和解释特定现象，以帮助个人和组织做出明智决策。</p><p data-pid=\"BMY-3ZJK\"><b>学会使用 Code Interpreter，ChatGPT 就成为了你的“随身数据科学家”。</b></p><p data-pid=\"J07RCSa8\">你只需要提供数据，然后写提示词告诉 ChatGPT，你需要对数据进行怎样的分析，然后它就会替你完成工作。</p><p data-pid=\"ngqnnb7X\">比如，<b>请 ChatGPT 做出世界人口地图：</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-c9e10441e6dde17b6ec141d4e0ea6f9f_b.jpg\" data-size=\"normal\" data-rawwidth=\"680\" data-rawheight=\"329\" data-original-token=\"v2-452bbeef30ccaf7bb1098ae35a65c79f\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-4fd324b3043e3560cb2a94fe69cf459e_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"680\" data-original=\"https://pic2.zhimg.com/v2-c9e10441e6dde17b6ec141d4e0ea6f9f_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;680&#39; height=&#39;329&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"680\" data-rawheight=\"329\" data-original-token=\"v2-452bbeef30ccaf7bb1098ae35a65c79f\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-4fd324b3043e3560cb2a94fe69cf459e_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"680\" data-original=\"https://pic2.zhimg.com/v2-c9e10441e6dde17b6ec141d4e0ea6f9f_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-c9e10441e6dde17b6ec141d4e0ea6f9f_b.jpg\"/><figcaption>| ChatGPT 做出的世界人口地图</figcaption></figure><p data-pid=\"PCBk5JMM\"><b>把美国的所有灯塔在地图上绘制出来：</b></p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-df896f1a094379f654260ca0fddeac11_b.jpg\" data-size=\"normal\" data-rawwidth=\"593\" data-rawheight=\"670\" data-original-token=\"v2-fdea9d8e9da988a5fbc99cef3d6067d9\" data-default-watermark-src=\"https://picx.zhimg.com/v2-6142f8e9cefa7022098a3a1af7f57bbd_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"593\" data-original=\"https://picx.zhimg.com/v2-df896f1a094379f654260ca0fddeac11_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;593&#39; height=&#39;670&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"593\" data-rawheight=\"670\" data-original-token=\"v2-fdea9d8e9da988a5fbc99cef3d6067d9\" data-default-watermark-src=\"https://picx.zhimg.com/v2-6142f8e9cefa7022098a3a1af7f57bbd_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"593\" data-original=\"https://picx.zhimg.com/v2-df896f1a094379f654260ca0fddeac11_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-df896f1a094379f654260ca0fddeac11_b.jpg\"/><figcaption>| ChatGPT 绘制的美国灯塔</figcaption></figure><p data-pid=\"sw8A46SJ\">还有数据科学家利用 Code Interpreter，来<b>给自己的机器学习模型调参</b>：</p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-1650e8b15ed4eb22dfbabc87ac48889f_b.jpg\" data-size=\"normal\" data-rawwidth=\"586\" data-rawheight=\"203\" data-original-token=\"v2-ce121830d615a1678b6c51fbe75b762e\" data-default-watermark-src=\"https://picx.zhimg.com/v2-c13de0132146dc6526a4dde34d9d31e1_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"586\" data-original=\"https://pic4.zhimg.com/v2-1650e8b15ed4eb22dfbabc87ac48889f_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;586&#39; height=&#39;203&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"586\" data-rawheight=\"203\" data-original-token=\"v2-ce121830d615a1678b6c51fbe75b762e\" data-default-watermark-src=\"https://picx.zhimg.com/v2-c13de0132146dc6526a4dde34d9d31e1_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"586\" data-original=\"https://pic4.zhimg.com/v2-1650e8b15ed4eb22dfbabc87ac48889f_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-1650e8b15ed4eb22dfbabc87ac48889f_b.jpg\"/><figcaption>| 举个例子（1）</figcaption></figure><figure data-size=\"normal\"><noscript><img src=\"https://pica.zhimg.com/v2-dc717e36008c0d8b120124b8fe3df08a_b.jpg\" data-size=\"normal\" data-rawwidth=\"599\" data-rawheight=\"507\" data-original-token=\"v2-6cad3ef2985c5282a0d5e82fea0ca3c7\" data-default-watermark-src=\"https://pic2.zhimg.com/v2-d3d1d2da6ef7de944504374953a04453_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"599\" data-original=\"https://pica.zhimg.com/v2-dc717e36008c0d8b120124b8fe3df08a_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;599&#39; height=&#39;507&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"599\" data-rawheight=\"507\" data-original-token=\"v2-6cad3ef2985c5282a0d5e82fea0ca3c7\" data-default-watermark-src=\"https://pic2.zhimg.com/v2-d3d1d2da6ef7de944504374953a04453_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"599\" data-original=\"https://pica.zhimg.com/v2-dc717e36008c0d8b120124b8fe3df08a_r.jpg\" data-actualsrc=\"https://pica.zhimg.com/v2-dc717e36008c0d8b120124b8fe3df08a_b.jpg\"/><figcaption>| 举个例子（2）</figcaption></figure><h3><b>二、图像转视频</b></h3><p data-pid=\"O_-MFbNK\">有人用 ChatGPT 把图片转换成视频：</p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-1b084f4d16ba29c4ba31cb06c417a9cf_b.gif\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"427\" data-original-token=\"v2-1b084f4d16ba29c4ba31cb06c417a9cf\" data-thumbnail=\"https://picx.zhimg.com/v2-1b084f4d16ba29c4ba31cb06c417a9cf_b.jpg\" data-default-watermark-src=\"https://picx.zhimg.com/v2-1b084f4d16ba29c4ba31cb06c417a9cf_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"640\" data-original=\"https://picx.zhimg.com/v2-1b084f4d16ba29c4ba31cb06c417a9cf_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;640&#39; height=&#39;427&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"640\" data-rawheight=\"427\" data-original-token=\"v2-1b084f4d16ba29c4ba31cb06c417a9cf\" data-thumbnail=\"https://picx.zhimg.com/v2-1b084f4d16ba29c4ba31cb06c417a9cf_b.jpg\" data-default-watermark-src=\"https://picx.zhimg.com/v2-1b084f4d16ba29c4ba31cb06c417a9cf_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"640\" data-original=\"https://picx.zhimg.com/v2-1b084f4d16ba29c4ba31cb06c417a9cf_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-1b084f4d16ba29c4ba31cb06c417a9cf_b.gif\"/><figcaption>| 效果图如上</figcaption></figure><p data-pid=\"5mBTJRcE\"><b>具体怎么做到的呢？</b></p><p data-pid=\"1pFmWmE2\">你只需要启用 Code Interpreter 功能，然后告诉 ChatGPT：请帮我把以下图片变成动画视频。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-c0e7a1c9adf97c998e20b21a3a443ce8_b.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"270\" data-original-token=\"v2-90b70e24ec91865a96c60c99b2fd290e\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-a052c4ad0348f561371da1662092135c_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-c0e7a1c9adf97c998e20b21a3a443ce8_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;270&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"270\" data-original-token=\"v2-90b70e24ec91865a96c60c99b2fd290e\" data-default-watermark-src=\"https://pic3.zhimg.com/v2-a052c4ad0348f561371da1662092135c_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-c0e7a1c9adf97c998e20b21a3a443ce8_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-c0e7a1c9adf97c998e20b21a3a443ce8_b.jpg\"/><figcaption>| 给到的原图</figcaption></figure><p data-pid=\"T6nN37Ed\">具体prompt如下：</p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-8594085925dd32516b9fe549165520b8_b.jpg\" data-size=\"normal\" data-rawwidth=\"1044\" data-rawheight=\"594\" data-original-token=\"v2-e795b37d5be827b76e039c905f5593dc\" data-default-watermark-src=\"https://picx.zhimg.com/v2-253b3d346155d4f44961fc1c7483f45b_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1044\" data-original=\"https://pic3.zhimg.com/v2-8594085925dd32516b9fe549165520b8_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1044&#39; height=&#39;594&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1044\" data-rawheight=\"594\" data-original-token=\"v2-e795b37d5be827b76e039c905f5593dc\" data-default-watermark-src=\"https://picx.zhimg.com/v2-253b3d346155d4f44961fc1c7483f45b_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1044\" data-original=\"https://pic3.zhimg.com/v2-8594085925dd32516b9fe549165520b8_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-8594085925dd32516b9fe549165520b8_b.jpg\"/><figcaption>| 具体prompt</figcaption></figure><p data-pid=\"RVLKw6cy\">不一会儿，你就得到了动画视频，而且ChatGPT还会给你一个下载视频的链接。</p><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-b85b72bc7ec32c39a1e519a19d7ca5cb_b.jpg\" data-size=\"normal\" data-rawwidth=\"969\" data-rawheight=\"566\" data-original-token=\"v2-d87a324a4bc12dfdc82eefb2a2ff257a\" data-default-watermark-src=\"https://pic2.zhimg.com/v2-7cb4705bd6b97dce081742202e218211_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"969\" data-original=\"https://picx.zhimg.com/v2-b85b72bc7ec32c39a1e519a19d7ca5cb_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;969&#39; height=&#39;566&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"969\" data-rawheight=\"566\" data-original-token=\"v2-d87a324a4bc12dfdc82eefb2a2ff257a\" data-default-watermark-src=\"https://pic2.zhimg.com/v2-7cb4705bd6b97dce081742202e218211_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"969\" data-original=\"https://picx.zhimg.com/v2-b85b72bc7ec32c39a1e519a19d7ca5cb_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-b85b72bc7ec32c39a1e519a19d7ca5cb_b.jpg\"/><figcaption>| 点击左下角即可下载</figcaption></figure><h3><b>三、辅助投资决策</b></h3><p data-pid=\"1A4z8xDB\">还有人用它辅助投资决策。</p><p data-pid=\"KNODg-vS\">比如，<b>用Code Interpreter来分析特斯拉公司的股票</b>：</p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-e0f50ccc025e027eab79e39aefb3b29f_b.jpg\" data-size=\"normal\" data-rawwidth=\"588\" data-rawheight=\"562\" data-original-token=\"v2-63a629eff2e777a896b8d5a4e3b0f8ce\" data-default-watermark-src=\"https://pica.zhimg.com/v2-df18ed5d3fb1589364bbcd632ccae940_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"588\" data-original=\"https://pic2.zhimg.com/v2-e0f50ccc025e027eab79e39aefb3b29f_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;588&#39; height=&#39;562&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"588\" data-rawheight=\"562\" data-original-token=\"v2-63a629eff2e777a896b8d5a4e3b0f8ce\" data-default-watermark-src=\"https://pica.zhimg.com/v2-df18ed5d3fb1589364bbcd632ccae940_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"588\" data-original=\"https://pic2.zhimg.com/v2-e0f50ccc025e027eab79e39aefb3b29f_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-e0f50ccc025e027eab79e39aefb3b29f_b.jpg\"/><figcaption>| 举个例子</figcaption></figure><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-951fa62702959c0c5c61a859b2614d45_b.jpg\" data-size=\"normal\" data-rawwidth=\"580\" data-rawheight=\"391\" data-original-token=\"v2-99f220e9fa81889235d649734223dd35\" data-default-watermark-src=\"https://pic1.zhimg.com/v2-ecc3c0326664e015368a152a8c855572_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"580\" data-original=\"https://pic4.zhimg.com/v2-951fa62702959c0c5c61a859b2614d45_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;580&#39; height=&#39;391&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"580\" data-rawheight=\"391\" data-original-token=\"v2-99f220e9fa81889235d649734223dd35\" data-default-watermark-src=\"https://pic1.zhimg.com/v2-ecc3c0326664e015368a152a8c855572_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"580\" data-original=\"https://pic4.zhimg.com/v2-951fa62702959c0c5c61a859b2614d45_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-951fa62702959c0c5c61a859b2614d45_b.jpg\"/><figcaption>| Code Interpreter 绘制的折线图</figcaption></figure><figure data-size=\"normal\"><noscript><img src=\"https://picx.zhimg.com/v2-c3f7dd046d0d1469a3f92202b86d96f1_b.jpg\" data-size=\"normal\" data-rawwidth=\"578\" data-rawheight=\"360\" data-original-token=\"v2-dc09ff83342a98c393c780267bf5a382\" data-default-watermark-src=\"https://pic2.zhimg.com/v2-bd19e07f40fe37143bf7ae5429f185b3_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"578\" data-original=\"https://picx.zhimg.com/v2-c3f7dd046d0d1469a3f92202b86d96f1_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;578&#39; height=&#39;360&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"578\" data-rawheight=\"360\" data-original-token=\"v2-dc09ff83342a98c393c780267bf5a382\" data-default-watermark-src=\"https://pic2.zhimg.com/v2-bd19e07f40fe37143bf7ae5429f185b3_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"578\" data-original=\"https://picx.zhimg.com/v2-c3f7dd046d0d1469a3f92202b86d96f1_r.jpg\" data-actualsrc=\"https://picx.zhimg.com/v2-c3f7dd046d0d1469a3f92202b86d96f1_b.jpg\"/><figcaption>| Code Interpreter 绘制的柱状图</figcaption></figure><p data-pid=\"i8nLipIw\"><b>分析苹果公司的期权并获得建议：</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-592dae39d18f7d49dc3a699c669a5219_b.jpg\" data-size=\"normal\" data-rawwidth=\"586\" data-rawheight=\"287\" data-original-token=\"v2-82ff9d50d303d34007aa510e034afaf0\" data-default-watermark-src=\"https://pic4.zhimg.com/v2-2309b703575f2ff61e88996fe9112c3f_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"586\" data-original=\"https://pic4.zhimg.com/v2-592dae39d18f7d49dc3a699c669a5219_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;586&#39; height=&#39;287&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"586\" data-rawheight=\"287\" data-original-token=\"v2-82ff9d50d303d34007aa510e034afaf0\" data-default-watermark-src=\"https://pic4.zhimg.com/v2-2309b703575f2ff61e88996fe9112c3f_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"586\" data-original=\"https://pic4.zhimg.com/v2-592dae39d18f7d49dc3a699c669a5219_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-592dae39d18f7d49dc3a699c669a5219_b.jpg\"/><figcaption>| 举个例子（1）</figcaption></figure><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-63d77094a7a61630861e972ac7d05162_b.jpg\" data-size=\"normal\" data-rawwidth=\"587\" data-rawheight=\"581\" data-original-token=\"v2-24c0c5ebc2ec406ba87e94f760638916\" data-default-watermark-src=\"https://pic4.zhimg.com/v2-27ce68492cef0715bf26b3386bd6f537_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"587\" data-original=\"https://pic3.zhimg.com/v2-63d77094a7a61630861e972ac7d05162_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;587&#39; height=&#39;581&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"587\" data-rawheight=\"581\" data-original-token=\"v2-24c0c5ebc2ec406ba87e94f760638916\" data-default-watermark-src=\"https://pic4.zhimg.com/v2-27ce68492cef0715bf26b3386bd6f537_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"587\" data-original=\"https://pic3.zhimg.com/v2-63d77094a7a61630861e972ac7d05162_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-63d77094a7a61630861e972ac7d05162_b.jpg\"/><figcaption>| 举个例子（2）</figcaption></figure><p data-pid=\"HhA-WCDs\">这些应用，都只是Code Interpreter的冰山一角。</p><p data-pid=\"BT36FmiQ\">很有可能，这将是迄今为止对白领们最有价值的AI工具。</p><p data-pid=\"7DF1yPg2\">同时也意味着，这些<b>知识工作者的工作形式最有可能被颠覆。</b></p><p data-pid=\"JuVweX05\">以往我们花几天、几个星期才能掌握的事情，现在AI可以在几秒钟内完成，而且通常比人类分析师更少出错。</p><p data-pid=\"8XEGMhBQ\">Code Interpreter会带来颠覆，<b>它会帮助我们摆脱工作中烦人、重复的部分，以便我们可以专注于更有价值的部分。</b></p><p data-pid=\"tSCcOqB2\">不仅是Code Interpreter对一部分工作有颠覆性的影响，可以预见的是，未来会有更多工具，将会进一步颠覆我们的工作方式。</p><p data-pid=\"Wd9ue2l9\">对于所有受AI影响的工作，<b>我们都应该思考如何借助这些最新最前沿的AI工具</b>，帮助我们把时用在更有意义的事情上、做真正有价值的事。</p><hr/><p data-pid=\"OH8A2Lui\">我是<a href=\"https://www.zhihu.com/search?q=%E5%87%AF%E8%8E%89%E5%BD%AD&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\">凯莉彭</a>，只分享有价值的思考，更多内容，欢迎链接kelly71017，备注知乎，领取《<a href=\"https://www.zhihu.com/search?q=%E5%86%85%E5%AE%B9%E5%8A%9B%E5%8F%98%E7%8E%B0%E6%8C%87%E5%8D%97&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\">内容力变现指南</a>》。</p><p data-pid=\"9Ni8HYA2\">最后，我经常在<a href=\"https://www.zhihu.com/search?q=%E7%BB%BF%E8%89%B2%E8%BD%AF%E4%BB%B6&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\">绿色软件</a>做直播，欢迎围观，相信一定会对你有帮助~</p>", "excerpt": "家人们谁懂啊！今天，ChatGPT「代码解释器」（Code Interpreter）终于全面开放了！ <b>就在7月9日，Code Interpreter 测试版正式向所有Plus用户开放。</b>为什么这是一个大消息呢？ <b>Code Interpreter 是什么？</b>首先，你要知道这个东西到底是什么。 <b>Code Interpreter 是一个官方的 ChatGPT 插件</b>，用于数据分析、图像转换、代码编辑等功能。它允许用户运行代码、上传数据并进行各种操作。通过 Code Interpreter，用户可以执行和测试代码、…", "excerpt_new": "家人们谁懂啊！今天，ChatGPT「代码解释器」（Code Interpreter）终于全面开放了！ <b>就在7月9日，Code Interpreter 测试版正式向所有Plus用户开放。</b>为什么这是一个大消息呢？ <b>Code Interpreter 是什么？</b>首先，你要知道这个东西到底是什么。 <b>Code Interpreter 是一个官方的 ChatGPT 插件</b>，用于数据分析、图像转换、代码编辑等功能。它允许用户运行代码、上传数据并进行各种操作。通过 Code Interpreter，用户可以执行和测试代码、…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1689078608, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}, {"id": "1688974598067", "type": "feed", "target": {"id": "642630259", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1688974597, "updated": 1688974597, "title": "为什么穷人不吃饭也要买彩票，越买越穷？", "excerpt_title": "", "content": "<p></p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-6d4469b68333e2251ef0c17da0eeac74_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"900\" data-rawheight=\"383\" class=\"origin_image zh-lightbox-thumb\" width=\"900\" data-original=\"https://pic3.zhimg.com/v2-6d4469b68333e2251ef0c17da0eeac74_r.jpg\" data-original-token=\"v2-dbcdcf67cc1cb057b1a02b97e2aff21e\"/><figcaption>| 撰文：凯莉    编辑：路炀</figcaption></figure><p data-pid=\"PB_tve0T\">为何很多低收入家庭中的人，尽管知道中奖概率低，却依然热衷于购买彩票？</p><p data-pid=\"KRnMHhBi\"><b>在美国，低收入家庭每年在彩票上花费411美元。</b></p><p data-pid=\"jVAuBknq\">与此同时，大约40%的所有家庭在紧急情况下都很难找到400美元。毫不奇怪，这40%主要由在彩票上花费最多的低收入群体组成。</p><p data-pid=\"Nn1qiGY0\"><b>这个现象可能被视为不理性的，因为彩票的期望回报是负的</b>：</p><p data-pid=\"9soN2lTW\">彩票公司通常只支付购买票据总金额的一半作为奖金。然而，低收入的人仍然选择买彩票。</p><p data-pid=\"Mou3mDAM\">这是因为，对他们来说，购买彩票不仅仅是一种投资。</p><p data-pid=\"jH7QedyQ\"><b>这还是他们看到的唯一逃脱贫困的机会，甚至是唯一的希望。</b></p><p data-pid=\"8uUMyAra\">即使概率微乎其微，他们也愿意冒这个风险。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-98061de1a777ab25946babb8b3a54679_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"698\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-98061de1a777ab25946babb8b3a54679_r.jpg\" data-original-token=\"v2-a6a4ae5e42d68511812d713cd8f7199c\"/><figcaption>| 图源 unsplash</figcaption></figure><p data-pid=\"iBzFKEYv\">这是金钱心理学的一个重要例子：<b>人们对待金钱的方式不仅取决于他们的财务状况，也取决于他们的人生经历和希望。</b></p><p data-pid=\"5WNymD0D\">如果你对金钱心理学感兴趣，我要给你推荐一本书，叫做<b>《金钱心理学》</b>。</p><p data-pid=\"3Jh5QLLX\">一句话总结，金钱心理学是理解我们与金钱的关系，以及我们如何做出财务决策的重要一步。</p><p data-pid=\"JaqqtC2k\"><b>无论我们的经济状况如何，我们的人生经历、信念和价值观都会影响我们如何看待和使用金钱。</b></p><p data-pid=\"tarWnJwh\">分享这本书中的三个核心观点：</p><h2><b>01过往经历影响我们的金钱观</b></h2><p data-pid=\"TMiGv8fM\"><b>我们的家庭、文化、个人历史和教育水平，都会塑造我们的金钱心理学。</b></p><p data-pid=\"92-KiMt9\">那些在金融危机时期长大的人，金钱观念往往更保守、更节俭。如果投资者的青少年和二十几岁期间通货膨胀率很高，他们很可能不会在以后的生活中投资债券。</p><p data-pid=\"4Ei8LOUT\">相反，如果这些形成时期的通货膨胀率很低，投资者在日后仍乐意将他们的资金投入债券市场 - 无论通货膨胀在此期间是否增长。</p><p data-pid=\"K7to4Cd-\"><b>这也就解释了为什么中国人都喜欢买房。</b>因为70后、80后和90后都经历了2000年初的房地产蓬勃发展的时期，基本上买房就能赚钱，所以现在中国人习惯于买遍全世界的房产。</p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-86395d3c95389186e2a7d16f54aef7bb_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"711\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic2.zhimg.com/v2-86395d3c95389186e2a7d16f54aef7bb_r.jpg\" data-original-token=\"v2-4b30a964fd5e9e41dc5c60a596489996\"/><figcaption>| 房地产经济蓬勃发展</figcaption></figure><h2><b>02运气是不可忽视的因素</b></h2><p data-pid=\"YbtJpsK0\"><b>幸运在财务成功中起到的作用比你想象的要大。</b></p><p data-pid=\"hS5yRxXx\">在询问别人的成功经验的时候，当我们听到别人说自己运气好，我们总觉得对方是在谦虚，但实际上这并不是谦虚。</p><p data-pid=\"Ud9SQm-j\">敢于承认运气好的人，往往是对事情可能有更加准确的认知，因为取得成功不仅是靠自己的能力，运气也发挥着不可忽视的作用。</p><p data-pid=\"fnefXSCF\"><b>我们通常会低估或高估运气在结果中的作用。</b>如果我们表现出色，那是因为我们努力工作；如果我们失败，那是因为我们运气不佳。</p><p data-pid=\"J0fiieA_\">然而，如果别人失败，我们就不这么认为了。在别人失败的时候，我们往往不将失败归因于坏运气，而是归因于他们的懒惰或短视等性格缺陷。</p><p data-pid=\"Z2-b4xTw\">不可否认，我们的文化对成功着迷。《福布斯》杂志并不会庆祝因为运气不佳而破产的聪明人，而它确实会庆祝那些因为运气好而发了一笔横财的人。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-6c9f8a9b7ff29938cc0f6e19544bcdf6_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"602\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic1.zhimg.com/v2-6c9f8a9b7ff29938cc0f6e19544bcdf6_r.jpg\" data-original-token=\"v2-a8dfa2611dc798f5d0ced271b1a9cbdf\"/><figcaption>| 图片源自影片剧照，侵删</figcaption></figure><p data-pid=\"Og_sTKFB\">那么，你应该如何在你的金融行为中考虑机会和运气呢？</p><p data-pid=\"D5HZnUA4\">这就要引入第三个核心观点：</p><h2><b>03专注于广泛的成功和失败模式</b></h2><p data-pid=\"22S2BhKX\">专注于广泛的成功和失败模式，而不是具体个例，可以帮助我们做出更好的决策。</p><p data-pid=\"Px_AEXqh\">当我们研究非常成功的人时，通常会选择一些离群值作为参考。比如那些改变世界的亿万富翁们，但是这可能会误导我们。</p><p data-pid=\"N2rK4MII\"><b>过于关注特定个人的例子容易误导我们。</b></p><p data-pid=\"sFEZ6hQT\">相反，研究更普遍的成功和失败模式，可以给我们提供更实用的指导，因为这些模式更适用于我们的生活和金融决策。</p><p data-pid=\"FcHnafhK\">以约翰·D·洛克菲勒为例，他是<b>历史上最成功的企业家之一</b>。</p><figure data-size=\"normal\"><img src=\"https://pica.zhimg.com/v2-9ed2f7af715c9cb996f69f6a7b3d9198_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"703\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pica.zhimg.com/v2-9ed2f7af715c9cb996f69f6a7b3d9198_r.jpg\" data-original-token=\"v2-1695dd92e9c9c09b90ad1e3c75a5485d\"/><figcaption>| 约翰·D·洛克菲勒照片</figcaption></figure><p data-pid=\"M5x1YC71\">当他开始建立石油产业时，他面临一个问题。美国的法律不允许他做他想做的事情。</p><p data-pid=\"Dvb8JVQN\">他的解决方案很简单——忽视法律。</p><p data-pid=\"TCWuO59c\">由于他对法律条款过分不尊重，以至于有一位法官说他的企业行为“不比普通小偷好到哪里去”。</p><p data-pid=\"mCRX6s-m\"><b>洛克菲勒的成功影响了我们对违法行为的看法。</b></p><p data-pid=\"Y8_8WbfT\">回顾过去，我们很容易赞美他的远见，并夸奖他拒绝让过时的法律阻碍创新。</p><p data-pid=\"f-3imCqw\"><b>但是，如果他失败了，我们则会认为洛克菲勒的例子不是我们应该追随的。</b></p><p data-pid=\"MYKVwUJf\">我们最多会将他视为一个不成功的罪犯，告诫后人他的失败教会了我们不应该怎么做。</p><p data-pid=\"yfIhp7yb\">如果你深入研究，你会发现<b>这两种结果之间的区别就是运气。</b></p><p data-pid=\"xee6VoKB\">一个不同的判决结果，又或许是政治环境的变化，都有可能改变洛克菲勒的命运。</p><h2><b>写在最后</b></h2><p data-pid=\"NGOS-C8n\">1、在现实世界中，金融决策比经济学教科书中描述的更加复杂。</p><p data-pid=\"efqCAPNO\">许多决策并不是理性的，比如在身无分文时购买彩票，但它们在某种程度上是合理的。</p><p data-pid=\"aTzTbd5l\">同样，投资选择通常受到个人年轻时对经济的经历的影响，而不是对当前市场情况的冷静评估。</p><p data-pid=\"IKyWFntD\"><b>简而言之，金融决策与心理因素密切相关。</b></p><p data-pid=\"F-OnVwMf\">2、<b>运气在财务成功中的作用比你想象的要大。</b></p><p data-pid=\"N4BVBSaz\">个人的财务状况往往与他们家庭背景中的幸运或不幸有关，但我们常常低估了运气在成功中的作用。</p><p data-pid=\"UKYCH8lA\">同时，我们往往将他人的失败归因于个人品质缺陷，而不是归因于坏运气。</p><p data-pid=\"IBX0_4um\">了解运气的作用可以帮助我们更好地处理金钱。</p><p data-pid=\"5RFci9Mf\">3、<b>专注于广泛的成功和失败模式，而不是具体个例</b>，可以帮助我们做出更好的决策。</p><p data-pid=\"kZ2OdJ55\">过于关注特定个人的例子容易误导我们。</p><p data-pid=\"E8SZaI0u\"><b>相反，研究更普遍的成功和失败模式，可以提供更实用的指导，</b>因为这些模式更可能适用于我们的生活和金融决策。</p><p data-pid=\"bjTcWRsD\">理解个人经历的重要性、认识到运气的作用、专注于广泛模式，可以帮助我们在金钱方面做出更明智的决策。</p><p data-pid=\"bjfiufkj\"><b>如果你还没有读过这本书，推荐你读一读。</b></p><hr/><p data-pid=\"OH8A2Lui\">我是<a href=\"https://www.zhihu.com/search?q=%E5%87%AF%E8%8E%89%E5%BD%AD&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\" target=\"_blank\">凯莉彭</a>，只分享有价值的思考，更多内容，欢迎链接kelly71017，备注知乎，领取《<a href=\"https://www.zhihu.com/search?q=%E5%86%85%E5%AE%B9%E5%8A%9B%E5%8F%98%E7%8E%B0%E6%8C%87%E5%8D%97&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\" target=\"_blank\">内容力变现指南</a>》。</p><p data-pid=\"9Ni8HYA2\">最后，我经常在<a href=\"https://www.zhihu.com/search?q=%E7%BB%BF%E8%89%B2%E8%BD%AF%E4%BB%B6&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\" target=\"_blank\">绿色软件</a>做直播，欢迎围观，相信一定会对你有帮助~</p><p></p>", "excerpt": "| 撰文：凯莉 编辑：路炀为何很多低收入家庭中的人，尽管知道中奖概率低，却依然热衷于购买彩票？ <b>在美国，低收入家庭每年在彩票上花费411美元。</b>与此同时，大约40%的所有家庭在紧急情况下都很难找到400美元。毫不奇怪，这40%主要由在彩票上花费最多的低收入群体组成。 <b>这个现象可能被视为不理性的，因为彩票的期望回报是负的</b>：彩票公司通常只支付购买票据总金额的一半作为奖金。然而，低收入的人仍然选择买彩票。 这是因为，对他…", "excerpt_new": "| 撰文：凯莉 编辑：路炀为何很多低收入家庭中的人，尽管知道中奖概率低，却依然热衷于购买彩票？ <b>在美国，低收入家庭每年在彩票上花费411美元。</b>与此同时，大约40%的所有家庭在紧急情况下都很难找到400美元。毫不奇怪，这40%主要由在彩票上花费最多的低收入群体组成。 <b>这个现象可能被视为不理性的，因为彩票的期望回报是负的</b>：彩票公司通常只支付购买票据总金额的一半作为奖金。然而，低收入的人仍然选择买彩票。 这是因为，对他…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/642630259", "comment_permission": "all", "voteup_count": 0, "comment_count": 0, "image_url": "", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1688974598, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1684036441076", "type": "feed", "target": {"id": "3027145628", "type": "answer", "url": "https://api.zhihu.com/answers/3027145628", "voteup_count": 0, "thanks_count": 2, "question": {"id": "46563853", "title": "人工智能达到了什么程度？", "url": "https://api.zhihu.com/questions/46563853", "type": "question", "question_type": "normal", "created": 1463752551, "answer_count": 1257, "comment_count": 48, "follower_count": 22461, "detail": "<p><b>本题已加入知乎圆桌<a href=\"https://www.zhihu.com/roundtable/robot2019\" class=\"internal\">进击吧，机器人</a>»，更多相关话题欢迎关注圆桌参与讨论，一同了解机器人、人工智能和我们现在、未来相关的那些事。</b></p><p>2019.2.22 跟进进度</p><p>现在的人工智能能否对单一问题进行最佳决策？</p><p>2018.4.19 跟进进度</p><p>强人工智能方面有什么进展么？达到了什么程度？</p><p class=\"ztext-empty-paragraph\"><br/></p><p>2018.1.3 跟进进度<br/>回答过的答主有新的答案及时更新<br/>谢谢<br/>#######2017.10.5 跟进进度</p><p>实时跟进进度 这是未来的革命<br/>强人工智能 弱人工智能<br/>机器学习 自然语言等</p><p><b>本题已加入圆桌 »</b> <a href=\"https://www.zhihu.com/roundtable/kejie-vs-alphago\" class=\"internal\">人机对弈终章</a><b>，更多「AlphaGo（阿尔法围棋）」相关的话题欢迎关注讨论</b></p>", "excerpt": "<b>本题已加入知乎圆桌<a href=\"https://www.zhihu.com/roundtable/robot2019\" class=\"internal\">进击吧，机器人</a>»，更多相关话题欢迎关注圆桌参与讨论，一同了解…</b>", "bound_topic_ids": [350, 2084, 10033, 20858, 188574], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "c60b12e48726b47bf0a357cc038d7660", "name": "Close", "headline": "他什么也没说", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/rosireity", "url_token": "rosireity", "avatar_url": "https://pic1.zhimg.com/v2-877dc9d3bc339b17ec760685293d8457_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1684036440, "created_time": 1684036440, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"C8gUP7FQ\">过去三天,人工智能圈子里可是发生了太多事了，一天一个变化，而且还都是大变化。</p><p data-pid=\"EMzCk74R\">5月10号，Google I/O 2023，大会上，Google CEO 劈柴哥和其他发言人，在为期两小时的演示中，大约提到了143次人工智能，<b>平均每分钟提及1.153次人工智能。</b></p><p data-pid=\"khmMOyuk\">此次大会，Google 宣布了一系列新功能的发布，很多都和AI有关，比如：</p><p data-pid=\"zNzzcV0z\">Gmail 发布新功能<b>「帮我写」</b>（Help Me Write），根据邮件内容，用自然语言命令 AI 撰写对应的回复。</p><p data-pid=\"i_8qS8II\">Google Photos 推出<b>「魔法编辑器」</b>（Magic Editor），可以把照片上的元素随便移动，然后原本缺失的部分自动补足。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><noscript><img src=\"https://pic2.zhimg.com/v2-51bf7ffe8501c1d4044bd257d470d9f3_b.gif\" data-size=\"normal\" data-rawwidth=\"1079\" data-rawheight=\"607\" data-original-token=\"v2-51bf7ffe8501c1d4044bd257d470d9f3\" data-thumbnail=\"https://pic2.zhimg.com/v2-51bf7ffe8501c1d4044bd257d470d9f3_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1079\" data-original=\"https://pic2.zhimg.com/v2-51bf7ffe8501c1d4044bd257d470d9f3_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1079&#39; height=&#39;607&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1079\" data-rawheight=\"607\" data-original-token=\"v2-51bf7ffe8501c1d4044bd257d470d9f3\" data-thumbnail=\"https://pic2.zhimg.com/v2-51bf7ffe8501c1d4044bd257d470d9f3_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1079\" data-original=\"https://pic2.zhimg.com/v2-51bf7ffe8501c1d4044bd257d470d9f3_r.jpg\" data-actualsrc=\"https://pic2.zhimg.com/v2-51bf7ffe8501c1d4044bd257d470d9f3_b.gif\"/><figcaption>| Google Photos Magic Editor 演示</figcaption></figure><p data-pid=\"vpozlfDs\">另外，Google 最新最强的大语言模型 PaLM2 终于露出了庐山真面目。相比上一代，在数学、编码、推理、多语言翻译和自然语言生成上的表现都更加出色。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-0caec4a03daf4cfdb7242d41bed19154_b.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"607\" data-original-token=\"v2-c0d13e21f38d1a755e33e459ef704a8e\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-0caec4a03daf4cfdb7242d41bed19154_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;607&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"607\" data-original-token=\"v2-c0d13e21f38d1a755e33e459ef704a8e\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-0caec4a03daf4cfdb7242d41bed19154_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-0caec4a03daf4cfdb7242d41bed19154_b.jpg\"/><figcaption>| Google CEO Sundar Pichai 演示</figcaption></figure><p data-pid=\"Zf7mkzMM\">Google I/O 2023 的狂欢热度还没过去，紧接着，5月11号，OpenAI 的“最强竞争对手”，Anthropic公司宣布旗下的<b>Claude 现在可以一次性阅读长达10万个 token</b>。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic3.zhimg.com/v2-9dec0833208e7582f8e4055fdb118a9a_b.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1029\" data-original-token=\"v2-a581ad6a51ad5d04ce95243aac6be16b\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-9dec0833208e7582f8e4055fdb118a9a_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;1029&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1029\" data-original-token=\"v2-a581ad6a51ad5d04ce95243aac6be16b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-9dec0833208e7582f8e4055fdb118a9a_r.jpg\" data-actualsrc=\"https://pic3.zhimg.com/v2-9dec0833208e7582f8e4055fdb118a9a_b.jpg\"/><figcaption>| Anthropic公司官宣100k Context Windows</figcaption></figure><p data-pid=\"J3I8-B5d\"><b>10万个token是什么概念呢？</b>你要知道，GPT 3.5，只能够一次性处理4,096个 token，也就是差不多2,000个汉字吧。GPT 4.0，一次性处理的上限是8,000个 token，也就差不多个4,000个汉字。但是 Claude 现在厉害了，它可以一次性处理10万个 token，也就是差不多5万个汉字。这意味着什么？这意味着他可以在<b>一分钟之内读完整整一本书！</b>一个普通人，需要大约5个小时来阅读10万个 token 的文本，然后他们可能需要更长的时间来消化、记住和分析这些信息。而 Claude 可以在不到60秒内完成这个过程！在官网发布的示例里，Anthropic 公司将《了不起的盖茨比》这本小说的完整文本加载到了 Claude 里，这本书的文本数量，一共是7.2万个token。接着，他们在这7.2万个 token 的文本中修改了一行文字，然后要求模型找出两个版本的小说有什么不同之处。<b>只用了22秒钟，Claude 就读完这本书，并发现了修改的地方。</b></p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-1948ae875634c7c6ae26efeaff67b771_b.gif\" data-size=\"normal\" data-rawwidth=\"1079\" data-rawheight=\"530\" data-original-token=\"v2-1948ae875634c7c6ae26efeaff67b771\" data-thumbnail=\"https://pic4.zhimg.com/v2-1948ae875634c7c6ae26efeaff67b771_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1079\" data-original=\"https://pic4.zhimg.com/v2-1948ae875634c7c6ae26efeaff67b771_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1079&#39; height=&#39;530&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1079\" data-rawheight=\"530\" data-original-token=\"v2-1948ae875634c7c6ae26efeaff67b771\" data-thumbnail=\"https://pic4.zhimg.com/v2-1948ae875634c7c6ae26efeaff67b771_b.jpg\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1079\" data-original=\"https://pic4.zhimg.com/v2-1948ae875634c7c6ae26efeaff67b771_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-1948ae875634c7c6ae26efeaff67b771_b.gif\"/><figcaption>| Claude 可以在一分钟内处理完几十页的公司财务文档</figcaption></figure><p data-pid=\"LSbITboy\">除此之外，<b>Claude 还可以很快的读完一系列的公司文档，提炼出文档里的核心信息，整理成一份专业报告。</b>咨询师和分析师们，会不会感到背后有丝丝凉意？不仅如此，有了10万 token 的加持，Claude 现在还可以<b>把自己本来还不会的概念，在一分钟之内迅速学会</b>，并且运用这个新概念帮你开发软件程序。是不是非常惊人？不过你放心，这个功能暂时还没有对你开放，你得去申请 Claude 的API，还要再等批准。等申请到以后，你可以付费使用。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic1.zhimg.com/v2-3a65b67be30530af8d2e0ec51ef224dc_b.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"750\" data-rawheight=\"500\" data-original-token=\"v2-f610f7b37a923b69669152ba965c8762\" class=\"origin_image zh-lightbox-thumb\" width=\"750\" data-original=\"https://pic1.zhimg.com/v2-3a65b67be30530af8d2e0ec51ef224dc_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;750&#39; height=&#39;500&#39;&gt;&lt;/svg&gt;\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"750\" data-rawheight=\"500\" data-original-token=\"v2-f610f7b37a923b69669152ba965c8762\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"750\" data-original=\"https://pic1.zhimg.com/v2-3a65b67be30530af8d2e0ec51ef224dc_r.jpg\" data-actualsrc=\"https://pic1.zhimg.com/v2-3a65b67be30530af8d2e0ec51ef224dc_b.jpg\"/></figure><p data-pid=\"4kmEWHvs\">10万个 token 的狂欢还没有过去多久，紧接着在5月12号，OpenAI公司坐不住了，宣布<b>ChatGPT Plus 的用户马上可以使用 plugins</b>了，也就是可以使用插件了。其实插件这玩意儿，在OpenAI 3月份发布GPT 4.0的时候就官宣了，但是后来一直在“小范围测试”，并没有给到用户。说时迟那时快，突然就在5月12号，说“我们现在把插件给到大家了哈，你们可以用起来啦”。</p><figure data-size=\"normal\"><noscript><img src=\"https://pic4.zhimg.com/v2-f27378ba2f8f4bc2ee586b0a1aa702ed_b.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"727\" data-original-token=\"v2-e718605c9b9669067b4c34fe17c8a92b\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-f27378ba2f8f4bc2ee586b0a1aa702ed_r.jpg\"/></noscript><img src=\"data:image/svg+xml;utf8,&lt;svg xmlns=&#39;http://www.w3.org/2000/svg&#39; width=&#39;1080&#39; height=&#39;727&#39;&gt;&lt;/svg&gt;\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"727\" data-original-token=\"v2-e718605c9b9669067b4c34fe17c8a92b\" class=\"origin_image zh-lightbox-thumb lazy\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-f27378ba2f8f4bc2ee586b0a1aa702ed_r.jpg\" data-actualsrc=\"https://pic4.zhimg.com/v2-f27378ba2f8f4bc2ee586b0a1aa702ed_b.jpg\"/><figcaption>| 图片来自网络</figcaption></figure><p data-pid=\"fiiBPv7z\">你说说看，10号、11号、12号，他们是都商量好了日子确保不撞车的吗？</p><p data-pid=\"WS_ztWar\">这几家公司的新产品发布，怎么就这么一天挨着一天发生了呢？</p><p data-pid=\"2iVy-hKb\">感觉这些前沿科技公司，就像是在牌桌上打牌，每家公司手里握着一手牌，看对手出什么牌，再决定自己什么时候亮什么牌。</p><p data-pid=\"zJraXw8G\">另一方面，他们又像是在搞军备竞赛，磨刀霍霍，你追我赶，生怕让竞争对手抢了先机，所以时刻警惕，随时准备发布新功能。</p><p data-pid=\"VVxAP00j\">我有些学员感叹说，“<b>唉呀，我们作为用户可真是太幸运了，每天都有新的工具可以用，工具不断在变得更好，我们的效率在不断提升。”</b></p><p data-pid=\"7iFjOUMk\">但是同时，这会不会又是一大不幸呢？</p><p data-pid=\"ytEWRNhZ\"><b>我们真的需要一分钟读完一本书吗？</b></p><p data-pid=\"hudMkfc3\"><b>会不会我们正在加速的奔向一个悬崖？</b></p><p data-pid=\"LG_OG5fJ\">等真正到了悬崖边上的那一天，我们能不能刹得住车呢？</p><p data-pid=\"ks53yn47\">无论说什么，我们都不知道是对是错，也不知道未来会发生什么事情，作为一个普通用户，只希望人工智能这一块的监管能抓紧跟上，不然这样“军备竞赛”下去，大概率结局不会太好。</p><p data-pid=\"I9MmloZw\"><b>而我们每个普通人能做的，就是跟上时代的步伐，拥抱变化，驾驭工具。</b></p><p data-pid=\"bokPGiAD\">不要成为一个“无用的人”，不要让自己先掉下了悬崖。</p><p data-pid=\"cYd9I1p5\">如果你不愿意“摸黑”行路，想拥有一个行路指南针，和一群人一起前行，欢迎链接kelly71017，备注知乎，领取《<a href=\"https://www.zhihu.com/search?q=%E5%86%85%E5%AE%B9%E5%8A%9B%E5%8F%98%E7%8E%B0%E6%8C%87%E5%8D%97&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\">内容力变现指南</a>》。</p>", "excerpt": "过去三天,人工智能圈子里可是发生了太多事了，一天一个变化，而且还都是大变化。 5月10号，Google I/O 2023，大会上，Google CEO 劈柴哥和其他发言人，在为期两小时的演示中，大约提到了143次人工智能， <b>平均每分钟提及1.153次人工智能。</b>此次大会，Google 宣布了一系列新功能的发布，很多都和AI有关，比如： Gmail 发布新功能 <b>「帮我写」</b>（Help Me Write），根据邮件内容，用自然语言命令 AI 撰写对应的回复。Google Photos 推出 <b>…</b>", "excerpt_new": "过去三天,人工智能圈子里可是发生了太多事了，一天一个变化，而且还都是大变化。 5月10号，Google I/O 2023，大会上，Google CEO 劈柴哥和其他发言人，在为期两小时的演示中，大约提到了143次人工智能， <b>平均每分钟提及1.153次人工智能。</b>此次大会，Google 宣布了一系列新功能的发布，很多都和AI有关，比如： Gmail 发布新功能 <b>「帮我写」</b>（Help Me Write），根据邮件内容，用自然语言命令 AI 撰写对应的回复。Google Photos 推出 <b>…</b>", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1684036441, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}, {"id": "1684036440241", "type": "feed", "target": {"id": "629247256", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1684036440, "updated": 1684036443, "title": "这两天，AI圈子发布了几个大消息", "excerpt_title": "", "content": "<p data-pid=\"aaCHiFTF\">过去三天,人工智能圈子里可是发生了太多事了，一天一个变化，而且还都是大变化。</p><p data-pid=\"nCscahaK\">5月10号，Google I/O 2023，大会上，Google CEO 劈柴哥和其他发言人，在为期两小时的演示中，大约提到了143次人工智能，<b>平均每分钟提及1.153次人工智能。</b></p><p data-pid=\"3n5ZZoaD\">此次大会，Google 宣布了一系列新功能的发布，很多都和AI有关，比如：</p><p data-pid=\"CoKAE-aa\">Gmail 发布新功能<b>「帮我写」</b>（Help Me Write），根据邮件内容，用自然语言命令 AI 撰写对应的回复。</p><p data-pid=\"0XtRaUgr\">Google Photos 推出<b>「魔法编辑器」</b>（Magic Editor），可以把照片上的元素随便移动，然后原本缺失的部分自动补足。</p><p class=\"ztext-empty-paragraph\"><br/></p><figure data-size=\"normal\"><img src=\"https://pic2.zhimg.com/v2-51bf7ffe8501c1d4044bd257d470d9f3_1440w.gif\" data-size=\"normal\" data-rawwidth=\"1079\" data-rawheight=\"607\" data-thumbnail=\"https://pic2.zhimg.com/v2-51bf7ffe8501c1d4044bd257d470d9f3_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1079\" data-original=\"https://pic2.zhimg.com/v2-51bf7ffe8501c1d4044bd257d470d9f3_r.jpg\" data-original-token=\"v2-51bf7ffe8501c1d4044bd257d470d9f3\"/><figcaption>| Google Photos Magic Editor 演示</figcaption></figure><p data-pid=\"9lAepE1m\">另外，Google 最新最强的大语言模型 PaLM2 终于露出了庐山真面目。相比上一代，在数学、编码、推理、多语言翻译和自然语言生成上的表现都更加出色。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-0caec4a03daf4cfdb7242d41bed19154_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"607\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-0caec4a03daf4cfdb7242d41bed19154_r.jpg\" data-original-token=\"v2-c0d13e21f38d1a755e33e459ef704a8e\"/><figcaption>| Google CEO Sundar Pichai 演示</figcaption></figure><p data-pid=\"Pvd91VE8\">Google I/O 2023 的狂欢热度还没过去，紧接着，5月11号，OpenAI 的“最强竞争对手”，Anthropic公司宣布旗下的<b>Claude 现在可以一次性阅读长达10万个 token</b>。</p><figure data-size=\"normal\"><img src=\"https://pic3.zhimg.com/v2-9dec0833208e7582f8e4055fdb118a9a_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"1029\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic3.zhimg.com/v2-9dec0833208e7582f8e4055fdb118a9a_r.jpg\" data-original-token=\"v2-a581ad6a51ad5d04ce95243aac6be16b\"/><figcaption>| Anthropic公司官宣100k Context Windows</figcaption></figure><p data-pid=\"OLP2-PHC\"><b>10万个token是什么概念呢？</b>你要知道，GPT 3.5，只能够一次性处理4,096个 token，也就是差不多2,000个汉字吧。GPT 4.0，一次性处理的上限是8,000个 token，也就差不多个4,000个汉字。但是 Claude 现在厉害了，它可以一次性处理10万个 token，也就是差不多5万个汉字。这意味着什么？这意味着他可以在<b>一分钟之内读完整整一本书！</b>一个普通人，需要大约5个小时来阅读10万个 token 的文本，然后他们可能需要更长的时间来消化、记住和分析这些信息。而 Claude 可以在不到60秒内完成这个过程！在官网发布的示例里，Anthropic 公司将《了不起的盖茨比》这本小说的完整文本加载到了 Claude 里，这本书的文本数量，一共是7.2万个token。接着，他们在这7.2万个 token 的文本中修改了一行文字，然后要求模型找出两个版本的小说有什么不同之处。<b>只用了22秒钟，Claude 就读完这本书，并发现了修改的地方。</b></p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-1948ae875634c7c6ae26efeaff67b771_1440w.gif\" data-size=\"normal\" data-rawwidth=\"1079\" data-rawheight=\"530\" data-thumbnail=\"https://pic4.zhimg.com/v2-1948ae875634c7c6ae26efeaff67b771_b.jpg\" class=\"origin_image zh-lightbox-thumb\" width=\"1079\" data-original=\"https://pic4.zhimg.com/v2-1948ae875634c7c6ae26efeaff67b771_r.jpg\" data-original-token=\"v2-1948ae875634c7c6ae26efeaff67b771\"/><figcaption>| Claude 可以在一分钟内处理完几十页的公司财务文档</figcaption></figure><p data-pid=\"hHdYkW0W\">除此之外，<b>Claude 还可以很快的读完一系列的公司文档，提炼出文档里的核心信息，整理成一份专业报告。</b>咨询师和分析师们，会不会感到背后有丝丝凉意？不仅如此，有了10万 token 的加持，Claude 现在还可以<b>把自己本来还不会的概念，在一分钟之内迅速学会</b>，并且运用这个新概念帮你开发软件程序。是不是非常惊人？不过你放心，这个功能暂时还没有对你开放，你得去申请 Claude 的API，还要再等批准。等申请到以后，你可以付费使用。</p><figure data-size=\"normal\"><img src=\"https://pic1.zhimg.com/v2-3a65b67be30530af8d2e0ec51ef224dc_1440w.jpg\" data-caption=\"\" data-size=\"normal\" data-rawwidth=\"750\" data-rawheight=\"500\" class=\"origin_image zh-lightbox-thumb\" width=\"750\" data-original=\"https://pic1.zhimg.com/v2-3a65b67be30530af8d2e0ec51ef224dc_r.jpg\" data-original-token=\"v2-f610f7b37a923b69669152ba965c8762\"/></figure><p data-pid=\"-cxo-E_R\">10万个 token 的狂欢还没有过去多久，紧接着在5月12号，OpenAI公司坐不住了，宣布<b>ChatGPT Plus 的用户马上可以使用 plugins</b>了，也就是可以使用插件了。其实插件这玩意儿，在OpenAI 3月份发布GPT 4.0的时候就官宣了，但是后来一直在“小范围测试”，并没有给到用户。说时迟那时快，突然就在5月12号，说“我们现在把插件给到大家了哈，你们可以用起来啦”。</p><figure data-size=\"normal\"><img src=\"https://pic4.zhimg.com/v2-f27378ba2f8f4bc2ee586b0a1aa702ed_1440w.jpg\" data-size=\"normal\" data-rawwidth=\"1080\" data-rawheight=\"727\" class=\"origin_image zh-lightbox-thumb\" width=\"1080\" data-original=\"https://pic4.zhimg.com/v2-f27378ba2f8f4bc2ee586b0a1aa702ed_r.jpg\" data-original-token=\"v2-e718605c9b9669067b4c34fe17c8a92b\"/><figcaption>| 图片来自网络</figcaption></figure><p data-pid=\"Z74kEsrt\">你说说看，10号、11号、12号，他们是都商量好了日子确保不撞车的吗？</p><p data-pid=\"fjMNGdNR\">这几家公司的新产品发布，怎么就这么一天挨着一天发生了呢？</p><p data-pid=\"4HbgXRJO\">感觉这些前沿科技公司，就像是在牌桌上打牌，每家公司手里握着一手牌，看对手出什么牌，再决定自己什么时候亮什么牌。</p><p data-pid=\"1HKSDsRW\">另一方面，他们又像是在搞军备竞赛，磨刀霍霍，你追我赶，生怕让竞争对手抢了先机，所以时刻警惕，随时准备发布新功能。</p><p data-pid=\"ZqM-m4nh\">我有些学员感叹说，“<b>唉呀，我们作为用户可真是太幸运了，每天都有新的工具可以用，工具不断在变得更好，我们的效率在不断提升。”</b></p><p data-pid=\"iy12c2_K\">但是同时，这会不会又是一大不幸呢？</p><p data-pid=\"5XBc8AAe\"><b>我们真的需要一分钟读完一本书吗？</b></p><p data-pid=\"5gqZu2gB\"><b>会不会我们正在加速的奔向一个悬崖？</b></p><p data-pid=\"oFG6md1H\">等真正到了悬崖边上的那一天，我们能不能刹得住车呢？</p><p data-pid=\"AhbtCIWP\">无论说什么，我们都不知道是对是错，也不知道未来会发生什么事情，作为一个普通用户，只希望人工智能这一块的监管能抓紧跟上，不然这样“军备竞赛”下去，大概率结局不会太好。</p><p data-pid=\"ZI3CwkaB\"><b>而我们每个普通人能做的，就是跟上时代的步伐，拥抱变化，驾驭工具。</b></p><p data-pid=\"wGh9_Bi8\">不要成为一个“无用的人”，不要让自己先掉下了悬崖。</p><p data-pid=\"Aizoi2pI\">如果你不愿意“摸黑”行路，想拥有一个行路指南针，和一群人一起前行，欢迎链接kelly71017，备注知乎，领取《<a href=\"https://www.zhihu.com/search?q=%E5%86%85%E5%AE%B9%E5%8A%9B%E5%8F%98%E7%8E%B0%E6%8C%87%E5%8D%97&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\" target=\"_blank\">内容力变现指南</a>》。</p>", "excerpt": "过去三天,人工智能圈子里可是发生了太多事了，一天一个变化，而且还都是大变化。 5月10号，Google I/O 2023，大会上，Google CEO 劈柴哥和其他发言人，在为期两小时的演示中，大约提到了143次人工智能， <b>平均每分钟提及1.153次人工智能。</b>此次大会，Google 宣布了一系列新功能的发布，很多都和AI有关，比如： Gmail 发布新功能 <b>「帮我写」</b>（Help Me Write），根据邮件内容，用自然语言命令 AI 撰写对应的回复。Google Photos 推出 <b>…</b>", "excerpt_new": "过去三天,人工智能圈子里可是发生了太多事了，一天一个变化，而且还都是大变化。 5月10号，Google I/O 2023，大会上，Google CEO 劈柴哥和其他发言人，在为期两小时的演示中，大约提到了143次人工智能， <b>平均每分钟提及1.153次人工智能。</b>此次大会，Google 宣布了一系列新功能的发布，很多都和AI有关，比如： Gmail 发布新功能 <b>「帮我写」</b>（Help Me Write），根据邮件内容，用自然语言命令 AI 撰写对应的回复。Google Photos 推出 <b>…</b>", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/629247256", "comment_permission": "all", "voteup_count": 0, "comment_count": 0, "image_url": "https://picx.zhimg.com/v2-f11062b3022e72b2b3ce02525ad0d802_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1684036440, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1683796888685", "type": "feed", "target": {"id": "3023387920", "type": "answer", "url": "https://api.zhihu.com/answers/3023387920", "voteup_count": 0, "thanks_count": 1, "question": {"id": "516660808", "title": "OpenAI 首席科学家称 AI 或有点自主意识了，其中有哪些值得关注的信息？", "url": "https://api.zhihu.com/questions/516660808", "type": "question", "question_type": "normal", "created": 1644893425, "answer_count": 82, "comment_count": 16, "follower_count": 413, "detail": "<p>影视剧《西部世界》中AI角色<br/>「现在的大型神经网络可能已经有了点自主意识。」近日，OpenAI 首席科学家 Ilya Sutskever 推文中的一句话激起千层浪。</p><p>关于AI能否有自主意识的大型争论在学术界已经停歇好久，但目前 Sutskever的这则推文已经「引战」了学术界的多位大佬。其中最被关注的是图灵奖得主、Meta 首席科学家杨立昆（Yann LeCun）的回应。</p><p>立昆直截了当反驳，「不。就算是对‘轻微意识’的‘轻微’和对‘大型神经网络’的‘大型’来说都不对。我认为你需要一种当下神经网络都不具备的特定宏架构。」<br/><br/>这不是立昆第一次回应类似的问题。<br/><br/>2018 年立昆说，人工智能缺乏对世界的基本认识，甚至还不如家猫认知水平。<br/><br/>2022年立昆认为，人工智能依然没有达到猫的水平。立昆近日在 Lex Fridman 的采访中表示，「尽管只有 8 亿个神经元，但猫的大脑远远领先于任何大型人工神经网络。」</p><a href=\"https://link.zhihu.com/?target=https%3A//www.thepaper.cn/newsDetail_forward_16692076\" data-draft-node=\"block\" data-draft-type=\"link-card\" data-image=\"https://pic2.zhimg.com/v2-62e66f59894b9797a07e54a15a228d83.jpg\" data-image-width=\"832\" data-image-height=\"468\" class=\" wrap external\" target=\"_blank\" rel=\"nofollow noreferrer\">OpenAI首席科学家称AI或有点自主意识了，杨立昆怒怼</a><p></p>", "excerpt": "影视剧《西部世界》中AI角色 「现在的大型神经网络可能已经有了点自主意识。」近日…", "bound_topic_ids": [350, 2143, 4616, 23174, 180476], "relationship": {"is_author": false}, "is_following": false, "author": {"id": "40123d9ad431381a044b579c0a36d8c2", "name": "肚子饿了", "headline": "", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/du-zi-e-liao-13-87", "url_token": "du-zi-e-liao-13-87", "avatar_url": "https://pic1.zhimg.com/v2-abed1a8c04700ba7d72b45195223e0ff_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": false, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "reaction_instruction": null}, "updated_time": 1683796887, "created_time": 1683796887, "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "comment_permission": "all", "is_copyable": true, "comment_count": 0, "can_comment": {"status": false, "reason": "只有作者关注的人才可以评论"}, "content": "<p data-pid=\"KZkpYAJX\">人类发现了永生的秘密，但是永生的不是人类。</p><p data-pid=\"pYw_nmn2\">人工智能之父Geoffrey Hinton警告AI可能摧毁人类。</p><p data-pid=\"87eQWTpG\"><b>几天前，Hinton公开官宣从Google辞职，原因是希望能够更自由的谈论ai的危险。</b></p><p data-pid=\"y80pgsOy\">作为“AI教父”三位之一，Hinton因其在深度学习方面的工作，而在2018年获得ACM图灵奖——计算机领域的“诺贝尔奖”。</p><p data-pid=\"4gE_h7Ag\">他曾期待AI能帮助人类更好地了解人类大脑，如今却为人工智能感到担忧。</p><p data-pid=\"L7wtbLN4\"><b>Hinton为什么感到担忧呢？</b></p><p data-pid=\"NYP1XGUj\">他认为，像我们这样的“生物智能”，可以在低功耗下运行，“即使你在思考时，也只消耗30瓦”，“每个大脑都略有不同”，这意味着<b>我们通过模仿其他人来学习</b>。</p><p data-pid=\"WsnVzL5S\">但这种方法在信息传递方面“非常低效”。<b>相比之下，数字智能具有巨大的优势：在多个副本之间共享信息非常容易。</b></p><p data-pid=\"GFkiBTt5\">“你需要付出巨大的能量代价，但当其中一个学会了什么，所有人都知道了，你可以轻松存储更多的副本。所以<b>好消息是，我们发现了永生的秘密。坏消息是，它不是为我们准备的。</b>”</p><p data-pid=\"_8GZ8kpw\">当Hinton意识到，我们正在建立可能超越人类思维的智能时，更令人担忧的问题随之而来：这一天将会在什么时候到来？</p><p data-pid=\"LaMZAD3U\">Hinton曾经认为，我们还有足够的时间，这一天将会在30到50年之后来到。然而现在，他不再这么认为。根据目前人工智能的发展速度，Hinton担心<b>在未来5到20年内，具有超人智慧的AI将通过互联网学会操控人类。</b></p><p data-pid=\"BG_SNWa7\">关于“具有超人智慧的AI“诞生的时间，Hinton还说：“我不排除1-2年的时间，我也不排除100年的可能性——只是我关于‘这件事不会在很长一段时间内发生‘的信心，已经被动摇了，因为生物智能和数字智能是非常不同的，数字智能可能更好。”</p><p data-pid=\"O1lMu5ZH\">那么，<b>什么是“具有超人智慧的AI”呢？</b></p><p data-pid=\"dg7IgbET\">为了理解这个概念，我们需要想象一种比我们聪明得多的存在。</p><p data-pid=\"xb_rFi79\"><b>我们与“具有超人智慧的AI”的差距，就像我们与昆虫的智力差距一样大。</b></p><p data-pid=\"_rKRmIkc\">这种超级智能将从互联网上学习所有人类知识，阅读每一本关于如何操纵人类的书籍，并将其付诸实践。尽管有可能AI的潜力被夸大，但灾难发生的可能性难以忽视。一旦出现超级智能，人类就很难控制它。</p><p data-pid=\"tVuPMNLP\"><b>回顾历史，我们找不到任何一个例子证明，一个聪明的实体，会被不如它聪明的实体所控制。</b>在巨大的潜在利益面前，人们不太可能停下开发更强大的AI的步伐。</p><p data-pid=\"2iKSezrk\">但是，我们最好开始重视这个警告。但是事实是否完全无望呢？记者问他有没有存在希望的理由，Hinton说“经常有人从看似无望的局面中走出来，并且没事了。</p><p data-pid=\"SBgjyBrP\">比如，核武器。这些强大的武器在冷战时期似乎是一个非常糟糕的情况。但<b>人们在事情发生之前就已经看到了它</b>，并且在今天看来当时可能有点反应过度，但是这远比不够重视要好得多。”</p><p data-pid=\"uOtBttg6\">我们所生活的这个世纪，可能是<b>人类历史上最令人兴奋的一个世纪</b>，也是至关重要的一个世纪。</p><p data-pid=\"hcG8pHUR\"><b>与其恐惧畏缩停滞不前，不如一起拥抱变化、适应变化。</b></p><p data-pid=\"ZjJJ8BeG\"><b>未来已来，我们拭目以待。</b></p>", "excerpt": "人类发现了永生的秘密，但是永生的不是人类。 人工智能之父Geoffrey Hinton警告AI可能摧毁人类。 <b>几天前，Hinton公开官宣从Google辞职，原因是希望能够更自由的谈论ai的危险。</b>作为“AI教父”三位之一，Hinton因其在深度学习方面的工作，而在2018年获得ACM图灵奖——计算机领域的“诺贝尔奖”。 他曾期待AI能帮助人类更好地了解人类大脑，如今却为人工智能感到担忧。 <b>Hinton为什么感到担忧呢？</b>他认为，像我们这样的“生物智能”，…", "excerpt_new": "人类发现了永生的秘密，但是永生的不是人类。 人工智能之父Geoffrey Hinton警告AI可能摧毁人类。 <b>几天前，Hinton公开官宣从Google辞职，原因是希望能够更自由的谈论ai的危险。</b>作为“AI教父”三位之一，Hinton因其在深度学习方面的工作，而在2018年获得ACM图灵奖——计算机领域的“诺贝尔奖”。 他曾期待AI能帮助人类更好地了解人类大脑，如今却为人工智能感到担忧。 <b>Hinton为什么感到担忧呢？</b>他认为，像我们这样的“生物智能”，…", "preview_type": "default", "preview_text": "", "relationship": {"voting": 0, "is_thanked": false, "is_nothelp": false, "upvoted_followee_ids": null}, "reaction_instruction": {}, "relevant_info": {"relevant_type": "", "is_relevant": false, "relevant_text": ""}, "reshipment_settings": "allowed", "answer_type": "normal", "paid_info": {"type": "", "content": "", "has_purchased": false}, "attachment": null, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote", "mark_infos": [], "content_need_truncated": false, "force_login_when_click_read_more": false}, "verb": "MEMBER_ANSWER_QUESTION", "created_time": 1683796888, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "回答了问题", "is_sticky": false}, {"id": "1683796888675", "type": "feed", "target": {"id": "628648588", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1683796887, "updated": 1683796887, "title": "AI之父Hinton：我们发现了永生的秘密，但是……", "excerpt_title": "", "content": "<p data-pid=\"h3aLhmhE\">人类发现了永生的秘密，但是永生的不是人类。</p><p data-pid=\"cr40jhUC\">人工智能之父Geoffrey Hinton警告AI可能摧毁人类。</p><p data-pid=\"Z8d3URqQ\"><b>几天前，Hinton公开官宣从Google辞职，原因是希望能够更自由的谈论ai的危险。</b></p><p data-pid=\"vemccIpa\">作为“AI教父”三位之一，Hinton因其在深度学习方面的工作，而在2018年获得ACM图灵奖——计算机领域的“诺贝尔奖”。</p><p data-pid=\"Chh7g-CV\">他曾期待AI能帮助人类更好地了解人类大脑，如今却为人工智能感到担忧。</p><p data-pid=\"nt2E9B9x\"><b>Hinton为什么感到担忧呢？</b></p><p data-pid=\"F0imW2NF\">他认为，像我们这样的“生物智能”，可以在低功耗下运行，“即使你在思考时，也只消耗30瓦”，“每个大脑都略有不同”，这意味着<b>我们通过模仿其他人来学习</b>。</p><p data-pid=\"vh7GMS0Z\">但这种方法在信息传递方面“非常低效”。<b>相比之下，数字智能具有巨大的优势：在多个副本之间共享信息非常容易。</b></p><p data-pid=\"Mb0p1RJF\">“你需要付出巨大的能量代价，但当其中一个学会了什么，所有人都知道了，你可以轻松存储更多的副本。所以<b>好消息是，我们发现了永生的秘密。坏消息是，它不是为我们准备的。</b>”</p><p data-pid=\"RnsGTfKO\">当Hinton意识到，我们正在建立可能超越人类思维的智能时，更令人担忧的问题随之而来：这一天将会在什么时候到来？</p><p data-pid=\"B00HORTw\">Hinton曾经认为，我们还有足够的时间，这一天将会在30到50年之后来到。然而现在，他不再这么认为。根据目前人工智能的发展速度，Hinton担心<b>在未来5到20年内，具有超人智慧的AI将通过互联网学会操控人类。</b></p><p data-pid=\"AAayI6u9\">关于“具有超人智慧的AI“诞生的时间，Hinton还说：“我不排除1-2年的时间，我也不排除100年的可能性——只是我关于‘这件事不会在很长一段时间内发生‘的信心，已经被动摇了，因为生物智能和数字智能是非常不同的，数字智能可能更好。”</p><p data-pid=\"A1UU7KW0\">那么，<b>什么是“具有超人智慧的AI”呢？</b></p><p data-pid=\"J1RzYgk1\">为了理解这个概念，我们需要想象一种比我们聪明得多的存在。</p><p data-pid=\"ydulFvDY\"><b>我们与“具有超人智慧的AI”的差距，就像我们与昆虫的智力差距一样大。</b></p><p data-pid=\"Bxi6aPhp\">这种超级智能将从互联网上学习所有人类知识，阅读每一本关于如何操纵人类的书籍，并将其付诸实践。尽管有可能AI的潜力被夸大，但灾难发生的可能性难以忽视。一旦出现超级智能，人类就很难控制它。</p><p data-pid=\"AUu29Fmt\"><b>回顾历史，我们找不到任何一个例子证明，一个聪明的实体，会被不如它聪明的实体所控制。</b>在巨大的潜在利益面前，人们不太可能停下开发更强大的AI的步伐。</p><p data-pid=\"fYCplABv\">但是，我们最好开始重视这个警告。但是事实是否完全无望呢？记者问他有没有存在希望的理由，Hinton说“经常有人从看似无望的局面中走出来，并且没事了。</p><p data-pid=\"p0R5i3kD\">比如，核武器。这些强大的武器在冷战时期似乎是一个非常糟糕的情况。但<b>人们在事情发生之前就已经看到了它</b>，并且在今天看来当时可能有点反应过度，但是这远比不够重视要好得多。”</p><p data-pid=\"FHzuBWKd\">我们所生活的这个世纪，可能是<b>人类历史上最令人兴奋的一个世纪</b>，也是至关重要的一个世纪。</p><p data-pid=\"xRoZpQu7\"><b>与其恐惧畏缩停滞不前，不如一起拥抱变化、适应变化。</b></p><p data-pid=\"PIu_79Sb\"><b>未来已来，我们拭目以待。</b></p>", "excerpt": "人类发现了永生的秘密，但是永生的不是人类。 人工智能之父Geoffrey Hinton警告AI可能摧毁人类。 <b>几天前，Hinton公开官宣从Google辞职，原因是希望能够更自由的谈论ai的危险。</b>作为“AI教父”三位之一，Hinton因其在深度学习方面的工作，而在2018年获得ACM图灵奖——计算机领域的“诺贝尔奖”。 他曾期待AI能帮助人类更好地了解人类大脑，如今却为人工智能感到担忧。 <b>Hinton为什么感到担忧呢？</b>他认为，像我们这样的“生物智能”，…", "excerpt_new": "人类发现了永生的秘密，但是永生的不是人类。 人工智能之父Geoffrey Hinton警告AI可能摧毁人类。 <b>几天前，Hinton公开官宣从Google辞职，原因是希望能够更自由的谈论ai的危险。</b>作为“AI教父”三位之一，Hinton因其在深度学习方面的工作，而在2018年获得ACM图灵奖——计算机领域的“诺贝尔奖”。 他曾期待AI能帮助人类更好地了解人类大脑，如今却为人工智能感到担忧。 <b>Hinton为什么感到担忧呢？</b>他认为，像我们这样的“生物智能”，…", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/628648588", "comment_permission": "all", "voteup_count": 2, "comment_count": 0, "image_url": "", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1683796888, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://pic1.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}, {"id": "1681431727911", "type": "feed", "target": {"id": "621731304", "type": "article", "author": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "created": 1681431726, "updated": 1681431726, "title": "裸辞之前，存多少钱才够？", "excerpt_title": "", "content": "<p data-pid=\"vOqVMcIA\">前几天我有幸受邀作为嘉宾，参加为友的年度个人增值峰会，分享裸辞转型路上的心路历程、经验思考。</p><p data-pid=\"c1Yf0taq\">小伙伴们的提问特别好，有一个问题我想展开说说，那就是<b>裸辞前的财务准备。</b></p><p data-pid=\"vatf6m7x\">每当提到财务问题的时候，如果有谁说“我存了足够多的钱”，总会有人问“存了多少钱才算够”。</p><p data-pid=\"7an6Q3oV\"><b>多少钱才算够，永远是相对于我们的支出水平的。</b></p><p data-pid=\"JaZqHpGg\">这里我给了一个建议，引用了来自纳瓦尔的一句话：<b>“People who are living far below their means enjoy a freedom that people busy upgrading their lifestyles just can’t fathom.”</b></p><p data-pid=\"tHr7ZzdI\">翻译过来是：<b>“生活水平远低于他们实际收入的人，享受着那些忙于提升生活品质的人无法想象的自由。”</b></p><p data-pid=\"n1d1fViY\">我在2017年的时候，裸辞回学校深造，出于被动的原因，不得不让生活方式大大降级，<b>从住豪华公寓，降级到睡了三年客厅。</b></p><p data-pid=\"u8zUxgXR\">后来虽然没有继续睡客厅了，但是我的<b>支出水平一直远低于收入水平</b>，每个月至少存下50%工资。这样做了几年，确实让我有了更大的自由。</p><p data-pid=\"Wji5iNM1\">于是在2022年的时候，能够没有太多顾虑，裸辞 all in 自媒体创业。</p><p data-pid=\"Huoqj6UT\">所以我特别认同纳瓦尔的观点话，今天想跟大家分享他的完整发言，也许能够启发到更多人——</p><p data-pid=\"17F_LDt3\"><b>第一，不要把时间租给别人；</b></p><p data-pid=\"XDR8yJ_d\"><b>第二，生活方式升级的速度不宜过快。</b></p><p data-pid=\"-qEcFWii\">生活水平远低于他们实际收入的人，享受着那些忙于提升生活品质的人无法想象的自由。</p><p data-pid=\"Bxjh7IIs\">我认为这一点非常重要，就是不要总是升级你的生活方式。要保持你的自由。这给你带来了行动的自由。</p><p data-pid=\"S8aEdqas\">当你赚到一点钱时，不要急着升级房子、生活方式和其他所有东西。</p><p data-pid=\"veahSlO-\"><b>最危险的东西是海洛因和月薪</b></p><p data-pid=\"gJ0bfpwe\">比如说，你的时薪是1000美元。问题是，你不会突然从每小时20美元变为每小时1000美元。这是一个漫长职业生涯的发展过程。</p><p data-pid=\"Lmb0TV0e\">随着这个过程的发展，一个微妙的问题是，<b>当你赚到越来越多的钱时，你会逐步升级你的生活方式。</b></p><p data-pid=\"B6o9a3vI\">而这种生活方式的升级，会让你不断提高自己对于财富的定义<b>，使你陷入工资奴隶陷阱。</b></p><p data-pid=\"9BeVzRmB\">“最危险的东西是海洛因和月薪。” 因为它们具有很强的上瘾性。你想要变富有的方式是，你要一直穷着，一直工作，一直努力。</p><p data-pid=\"U73VQJSy\"><b>理想情况下，你的财富应该是分散在不同时间段获得的</b></p><p data-pid=\"RJ2A1GoU\">科技行业的工作方式是，你在前十年可能一分钱都赚不到，然后在第十一个年头，你可能会有一笔巨额收入。</p><p data-pid=\"C41bgkV8\">顺便说一句，这就是为什么那些所谓富人的高边际税率是有缺陷的原因之一，因为在那些最需要承担风险、最有创造力的职业中，<b>你在前十年里面可能会亏本，承担巨大的风险，一路流血。</b></p><p data-pid=\"GbDVZb0y\"><b>然后突然在第十一个年头，或者第十五个年头，你可能会有一次巨大的收入。</b></p><p data-pid=\"WXaKdSgj\">当然，山姆大叔（美国政府）会出现，说：“嘿，你知道吗，你今年赚了很多钱。所以，你很富有。因此，你是邪恶的，你必须把所有钱交给我们。”</p><p data-pid=\"5R6xhwKU\">所以，这种做法会扼杀那些具有创造性和冒险精神的职业。</p><p data-pid=\"CKxqw-Ah\"><b>理想情况下，你希望在相隔很长时间的不同时间段内，以离散的方式赚到钱，</b>这样你的生活方式就没有机会快速适应，然后你就可以说：</p><p data-pid=\"0k3eVSCK\">“好了，我完成了。现在我退休了。现在我自由了。我还会继续工作，但我只会做我想做的事，在我想做的时候做。”</p><p data-pid=\"9ivgmXbs\">这样你就有了更多的创造性的表达，而不那么关注金钱。</p><p data-pid=\"0dxzxQ7Y\"><b>所以，关键是要保持谦逊和自制。</b>不要让成功冲昏头脑，过快升级你的生活方式。因为这可能会导致你失去自由和创造力。</p><p data-pid=\"gnjKBeih\">在你的职业生涯中，<b>尽量让收入分散在不同的时间段</b>，以避免陷入拿工资的奴隶陷阱。</p><p data-pid=\"2b0L7czs\">当然，这并不意味着你不能享受生活。适度地享受来之不易的成功也是必要的。</p><p data-pid=\"HKtrXOmZ\">但是，在追求财富的过程中，我们要始终记住，<b>真正的富有不仅仅是金钱，更是自由、创造力和对生活的热爱。</b>我们要努力工作，但也要留出时间去关注家庭、朋友和兴趣爱好。</p><p data-pid=\"Cm_YN6wR\">最后，为了实现财务自由，我们需要合理地规划财务，<b>学会理财，让我们的钱为我们工作</b>，而不是成为我们生活的负担。</p><p data-pid=\"qVQUkCdg\">这样，即使在退休之后，我们仍然可以享受美好的生活，有足够的时间和精力去追求自己的梦想。</p><p data-pid=\"HaSmFmLn\">以上，希望今天的分享对你有启发。</p><hr/><p data-pid=\"MX0lLhYF\">我是<a href=\"https://www.zhihu.com/search?q=%E5%87%AF%E8%8E%89%E5%BD%AD&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\" target=\"_blank\">凯莉彭</a>，关注我，我只分享有价值的思考。</p><p data-pid=\"37-Cu25k\">更多内容，欢迎链接kelly7117，备注知乎，领取《<a href=\"https://www.zhihu.com/search?q=%E5%86%85%E5%AE%B9%E5%8A%9B%E5%8F%98%E7%8E%B0%E6%8C%87%E5%8D%97&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\" target=\"_blank\">内容力变现指南</a>》。</p><p data-pid=\"l_FypnZ2\">最后，我经常在<a href=\"https://www.zhihu.com/search?q=%E7%BB%BF%E8%89%B2%E8%BD%AF%E4%BB%B6&amp;search_source=Entity&amp;hybrid_search_source=Entity&amp;hybrid_search_extra=%7B%22sourceType%22%3A%22answer%22%2C%22sourceId%22%3A2793140750%7D\" class=\"internal\" target=\"_blank\">绿色软件</a>做直播，欢迎围观，相信一定会对你有帮助~</p>", "excerpt": "前几天我有幸受邀作为嘉宾，参加为友的年度个人增值峰会，分享裸辞转型路上的心路历程、经验思考。 小伙伴们的提问特别好，有一个问题我想展开说说，那就是 <b>裸辞前的财务准备。</b>每当提到财务问题的时候，如果有谁说“我存了足够多的钱”，总会有人问“存了多少钱才算够”。 <b>多少钱才算够，永远是相对于我们的支出水平的。</b>这里我给了一个建议，引用了来自纳瓦尔的一句话： <b>“People who are living far below their means enjoy a …</b>", "excerpt_new": "前几天我有幸受邀作为嘉宾，参加为友的年度个人增值峰会，分享裸辞转型路上的心路历程、经验思考。 小伙伴们的提问特别好，有一个问题我想展开说说，那就是 <b>裸辞前的财务准备。</b>每当提到财务问题的时候，如果有谁说“我存了足够多的钱”，总会有人问“存了多少钱才算够”。 <b>多少钱才算够，永远是相对于我们的支出水平的。</b>这里我给了一个建议，引用了来自纳瓦尔的一句话： <b>“People who are living far below their means enjoy a …</b>", "preview_type": "default", "preview_text": "", "url": "https://zhuanlan.zhihu.com/p/621731304", "comment_permission": "all", "voteup_count": 0, "comment_count": 0, "image_url": "https://picx.zhimg.com/v2-d3d92926c607a7775cc67d3af53a19e7_r.jpg?source=172ae18b", "linkbox": {"url": "", "category": "", "pic": "", "title": ""}, "reaction_instruction": {}, "upvoted_followees": [], "voting": 0, "content_need_truncated": false, "force_login_when_click_read_more": false, "is_navigator": false, "navigator_vote": false, "vote_next_step": "vote"}, "verb": "MEMBER_CREATE_ARTICLE", "created_time": 1681431727, "interaction": {"can_show_sticker": true}, "actor": {"id": "e20fa7eabb77d1c852164133a6d51e69", "name": "凯莉彭", "headline": "新媒体实战导师 | 前硅谷商业分析、数据科学专家", "type": "people", "user_type": "people", "url": "https://www.zhihu.com/people/kai-li-peng", "url_token": "kai-li-peng", "avatar_url": "https://picx.zhimg.com/v2-06fc184922f1d1005075df91de982d3a_l.jpg?source=5a24d060&needBackground=1", "gender": 0, "is_following": true, "is_followed": false, "is_org": false, "badge": [], "badge_v2": {"detail_badges": null, "merged_badges": [], "title": ""}, "vip_info": {"is_vip": false, "target_url": ""}, "kvip_info": {"is_vip": false, "target_url": ""}}, "action_text": "发表了文章", "is_sticky": false}], "paging": {"is_end": false, "need_force_login": false, "next": "https://www.zhihu.com/api/v3/moments/kai-li-peng/activities?offset=1681431727911&page_num=33"}}